package com.memberintegral.unifiedmanagementplatform.service.impl;

import com.google.common.collect.Lists;
import com.kuwo.commercialization.common.resp.PageResult;
import com.memberintegral.unifiedmanagementplatform.dao.statistics.SongLimitPopMapper;
import com.memberintegral.unifiedmanagementplatform.model.dto.SongLimitPopDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * <p>Description: SongLimitPop ServiceImpl</p>
 * @date 2024-05-23 21:28:40
 */
@Service(value = "songLimitPopService")
public class SongLimitPopService {

    @Autowired
    private SongLimitPopMapper songLimitPopMapper;

    public void insertBatch(List<SongLimitPopDTO> dtos) {
        songLimitPopMapper.insertBatch(dtos);
    }
    
    public boolean insert(SongLimitPopDTO songLimitPopDTO) {
        return songLimitPopMapper.insert(songLimitPopDTO) > 0;
    }

    
    public boolean deleteById(Long id, String groupId) {
        if (Objects.nonNull(id)) {
            songLimitPopMapper.deleteById(id);
        }
        if (StringUtils.isNotBlank(groupId)) {
            songLimitPopMapper.deleteByGroupId(groupId);
        }
        return true;
    }

    public int deleteBySongIds(List<Long> groupIds) {
        return songLimitPopMapper.deleteBySongIds(groupIds);
    }

    
    public boolean updateById(SongLimitPopDTO songLimitPopDTO) {
        return songLimitPopMapper.updateById(songLimitPopDTO) > 0;
    }

    
    public SongLimitPopDTO selectById(Long id) {
        return songLimitPopMapper.selectById(id);
    }

    
    public List<SongLimitPopDTO> selectByQuery(SongLimitPopDTO query) {
        return songLimitPopMapper.selectByQuery(query);
    }

    
    public PageResult<SongLimitPopDTO> selectPageByQuery(SongLimitPopDTO query) {
        PageResult result = new PageResult();
        //groupId维度总页数
        int totalCount = songLimitPopMapper.selectCountGroupByQuery(query);
        int totalPage = (totalCount + query.getPageSize() - 1) / query.getPageSize();
        int pageNum = query.getPageIndex();
        int pageSize = query.getPageSize();
        Integer offset = (pageNum - 1) * pageSize;
        query.setOffset(offset);
        query.setLimit(pageSize);

        //groupId维度分页
        List<SongLimitPopDTO> groupList = songLimitPopMapper.selectPageGroupByQuery(query);

        if (CollectionUtils.isEmpty(groupList)) {
            result.setCurrentPage(pageNum);
            result.setPageSize(pageSize);
            result.setPages(totalPage);
            result.setTotalCount(totalCount);
            result.setDatas(Lists.newArrayList());
            return result;
        }
        
        SongLimitPopDTO songLimitPopDTO = new SongLimitPopDTO();
        songLimitPopDTO.setGroupIds(groupList.stream().map(SongLimitPopDTO::getGroupId).collect(Collectors.toList()));

        //songId维度详细信息
        List<SongLimitPopDTO> list = songLimitPopMapper.selectByQuery(songLimitPopDTO);

        //groupId维度详细信息
        List<SongLimitPopDTO> resultList = list.stream().collect(() -> new HashMap<String, SongLimitPopDTO>(), (a, b) -> {
            String key = b.getGroupId() + b.getType() + b.getTitle() + b.getCoverUrl();
            if (b.getUpdateTime() == null) {
                b.setUpdateTime(new Date());
            }
            if (!a.containsKey(key) || a.get(key).getUpdateTime().getTime() < b.getUpdateTime().getTime()) {
                b.setSourceIds(Lists.newArrayList(b.getSourceId()));
                b.setSourceId(null);
                a.put(key, b);
            } else {
                a.get(key).getSourceIds().add(b.getSourceId());
            }
        }, (a1, a2) -> a1.putAll(a2)).values().stream().sorted(Comparator.comparingLong(e -> ((SongLimitPopDTO) e).getUpdateTime().getTime()).reversed()).collect(Collectors.toList());

        result.setCurrentPage(pageNum);
        result.setPageSize(pageSize);
        result.setPages(totalPage);
        result.setTotalCount(totalCount);
        result.setDatas(resultList);
        return result;
    }

}
