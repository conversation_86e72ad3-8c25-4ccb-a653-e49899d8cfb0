spring:
  cloud:
    nacos:
      discovery:
        server-addr: ************:8848
        namespace1: ad3cc90f-b0b6-46fc-9ea5-dcc2e5d4e768
    sentinel:
      transport:
        dashboard: *************:8080

redis:
  config:
    main:
      address: redis://************:6379
      password: 2019yyFF$%
      selectDb: 0
      userStringCodec: true
    old_ui_cache_1:
      address: redis://***********:6382
      password: 2019yyFF$%
      selectDb: 0
      userStringCodec: true
    old_ui_cache_2:
      address: redis://************:6380
      password: 2019yyFF$%
      selectDb: 0
      userStringCodec: true
    vehicle_cache:
      address: redis://***********:6380
      password: 2019yyFF$%
      selectDb: 0
      userStringCodec: true
  names: main,old_ui_cache_1,old_ui_cache_2,vehicle_cache

base:
  domain: https://vip1.kuwo.cn/commercia/s

dubbo:
  application:
    name: ui-service-provider
  protocol:
    name: dubbo
    port: -1
  registries:
    p_1:
      address: nacos://************:8848?namespace=4566ca54-8e14-4749-a4c5-4ffc028d6377
      group: prod

  scan:
    base-packages: com.commercal.uiservice.service
  provider:
    filter: tracing
  consumer:
    filter: tracing
  cloud:
    subscribed-services: ui-canal-kafka-service-provider


kuwo:
  monitor:
    enabled: true
    sentinel:
      address: ************:8848
      namespace: df1616de-b65e-44b6-ab0e-debdc7cca419
      groupId: prod
      dataId: ${spring.application.name}-flow-rules
      degradeRulesDataId: ${spring.application.name}-degrade-rules
      flowRulesDataId: ${spring.application.name}-flow-rules
      paramRulesDataId: ${spring.application.name}-param-rules
      systemRulesDataId: ${spring.application.name}-system-rules

vip:
  domain: http://vip1.kuwo.cn