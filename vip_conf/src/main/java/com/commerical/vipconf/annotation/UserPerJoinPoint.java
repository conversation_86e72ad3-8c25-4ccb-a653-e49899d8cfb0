package com.commerical.vipconf.annotation;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.commerical.vipconf.domain.ProjectSpace;
import com.commerical.vipconf.service.ProjectSpaceService;
import com.commerical.vipconf.service.UserSpaceApplyService;
import com.kuwo.commercialization.common.message.MessageModel;
import com.kuwo.commercialization.common.message.SystemCodeErrorConstant;
import org.apache.commons.beanutils.BeanUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.List;

@Aspect
@Component
public class UserPerJoinPoint {

    private Logger logger = LoggerFactory.getLogger(UserPerJoinPoint.class);

    @Autowired
    private UserSpaceApplyService userSpaceApplyService;

    @Autowired
    private ProjectSpaceService projectSpaceService;


    @Pointcut("@annotation(com.commerical.vipconf.annotation.UserPermission)")
    public void aopPoint() {
    }

    @Around("aopPoint()")
    public Object doRouter(ProceedingJoinPoint jp) throws Throwable {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        //获取内容
        Method method = getMethod(jp);
        UserPermission userPermission = method.getAnnotation(UserPermission.class);
        //获取字段值
        String type = userPermission.type();
        String ename = request.getParameter("ename");//getFiledValue(userPermission.ename(), jp.getArgs());
        ProjectSpace projectSpace = null;
        if(userPermission.codeType().equals("projectSpaceId")){
           String id =  request.getParameter("id");//getFiledValue("id", jp.getArgs());
            projectSpace= projectSpaceService.getProjectSpaceInfoById(Integer.parseInt(id));
        }
        if (ObjectUtils.isNotEmpty(projectSpace) && ObjectUtils.isNotEmpty(ename) && ename.equals(projectSpace.getCreateEname())) {
            return jp.proceed();
        }
        // todo 确认code是空间code
        String code = ObjectUtils.isNotEmpty(projectSpace)?projectSpace.getCode():"";
        List<Integer> types= userSpaceApplyService.getUserApplyPass(ename,code);
        if (types.contains(Integer.parseInt(type))|| StringUtils.isBlank(code)) {
            return jp.proceed();
        }
        return returnObject();
    }

    private Method getMethod(JoinPoint jp) throws NoSuchMethodException {
        Signature sig = jp.getSignature();
        MethodSignature methodSignature = (MethodSignature) sig;
        return getClass(jp).getMethod(methodSignature.getName(), methodSignature.getParameterTypes());
    }

    private Class<? extends Object> getClass(JoinPoint jp) throws NoSuchMethodException {
        return jp.getTarget().getClass();
    }

    //返回对象
    private Object returnObject() {
        return new MessageModel(SystemCodeErrorConstant.UNAUTHORIZED);
    }

    //获取属性值
    //todo 对于多值存在问题   影响com.commerical.vipconf.controller.ProjectSpaceController.getProjectSpaceInfoById接口
    private String getFiledValue(String filed, Object[] args) {
        String filedValue = null;
        for (Object arg : args) {
            try {
                if (null == filedValue || "".equals(filedValue)) {
                    filedValue = BeanUtils.getProperty(arg, filed);
                } else {
                    break;
                }
            } catch (Exception e) {
                if (args.length == 1) {
                    return args[0].toString();
                }
            }
        }
        return null;
    }

}
