package com.commerical.vipconf.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;


@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface UserPermission {

    String type() default "1";

    String ename() default "ename";

    String codeType() default "code";

    String returnJson() default "";

}
