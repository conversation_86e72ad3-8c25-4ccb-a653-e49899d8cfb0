package com.commerical.vipconf.annotation;

import com.commerical.vipconf.nacos.VipConfConfigNacos;
import com.kuwo.commercialization.common.message.MessageModel;
import com.kuwo.commercialization.common.message.SystemCodeErrorConstant;
import com.kuwo.commercialization.common.utill.IpUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Aspect
@Component
@Slf4j
public class CheckUserPoint {

    private Logger logger = LoggerFactory.getLogger(CheckUserPoint.class);

    @Autowired
    private HttpServletRequest request;


    @Autowired
    private VipConfConfigNacos vipConfConfigNacos;

    @Pointcut("@annotation(com.commerical.vipconf.annotation.CheckUser)")
    public void aopPoint() {
    }

    @Around("aopPoint()")
    public Object doRouter(ProceedingJoinPoint jp) throws Throwable {
        logger.info("enter cookie");
        Cookie[] cookies= request.getCookies();
        String userIp = IpUtil.getUserIp(request);
        List<String> ips= vipConfConfigNacos.getVipConfBO().getIps();
        logger.info("enter cookie userIp={}",userIp);
        if(!ips.contains(userIp)){
            logger.info("enter cookie not valid userIp={}",userIp);
            return returnObject();
        }

//        if (cookies != null) {
//            try{
//                for(Cookie cookie:cookies){
//                    if(cookie.getName().equals("user")){
//                        logger.info("cookie cookie={}",cookie.getValue());
//                        String value= URL.decode(cookie.getValue());
//                        if(value.contains("cname")){
//                             JSONObject object= JSONUtil.parseObj(value);
//                             DressCenterConstant.userLocal.set(object.getStr("cname"));
//                            logger.info("cookie cname={}",object.getStr("cname"));
//                            return jp.proceed();
//                        }
//                    }
//                }
//            }catch (Exception e){
//                log.error("CheckUserPoint has error",e);
//            }
//        }
        return jp.proceed();
    }

    private Object returnObject() {
        return new MessageModel(SystemCodeErrorConstant.UNAUTHORIZED);
    }

}
