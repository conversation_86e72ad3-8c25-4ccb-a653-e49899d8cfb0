package com.commerical.vipconf.controller;


import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.commerical.vipconf.annotation.CheckUser;
import com.commerical.vipconf.config.exception.ServiceException;
import com.commerical.vipconf.domain.Gear;
import com.commerical.vipconf.domain.PayDesk;
import com.commerical.vipconf.domain.vo.PayDeskQueryVO;
import com.commerical.vipconf.domain.vo.PayDeskVO;
import com.commerical.vipconf.service.PayDeskService;
import com.kuwo.commercialization.common.message.MessageModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;


/**
 * <p>
 *   前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-26
 */
@RestController
@RequestMapping("/payDesk")
public class PayDeskController extends BaseController{

    private final static Logger logger = LoggerFactory.getLogger(PayDeskController.class);

    @Autowired
    private PayDeskService payDeskService;

    /**
     * 添加收银台
     *
     * @param payDeskVO
     * @return
     */
    @CheckUser
    @RequestMapping("/addOrUpdatePayDesk")
    public MessageModel addOrUpdatePayDesk(@Valid PayDeskVO payDeskVO){
        logger.info("addOrUpdatePayDesk param={}", JSONUtil.toJsonStr(payDeskVO));
        PayDesk payDesk= payDeskService.addOrUpdatePayDesk(payDeskVO);
        return success(payDesk.getId());
    }

    /**
     * 删除收银台
     *
     * @param
     * @return
     */
    @CheckUser
    @RequestMapping("/deletePayDeskById")
    public MessageModel deletePayDeskById(Integer id,Boolean isTrue,Integer isDelete){
        if(ObjectUtils.isNull(id)||ObjectUtils.isNull(isTrue)||ObjectUtils.isNull(isDelete)){
            logger.error("deletePayDeskById param  has empty value!,id={}",id);
            return failed();
        }
        payDeskService.deletePayDeskById(id,isTrue,isDelete);
        return success();
    }

    /**
     * 收银台列表查询
     *
     * @param
     * @return
     */
    @RequestMapping("/getPayDeskList")
    public MessageModel getPayDeskList(@Valid PayDeskQueryVO payDeskQueryVO){
        IPage<PayDesk> payDeskVOList= payDeskService.getPayDeskList(payDeskQueryVO);
        return success(payDeskVOList);
    }

    /**
     * 收银台详情查询
     *
     * @param
     * @return
     */
    @RequestMapping("/getPayDeskInfoById")
    public MessageModel getPayDeskInfoById(Integer id){
        PayDesk payDesk= payDeskService.getPayDeskInfoById(id);
        return success(payDesk);
    }

    @RequestMapping("/getAllPayDesk")
    public MessageModel getAllPayDesk(String id) throws ServiceException {
        if(!id.equals("6bc5a449-8e1d-43d9-a019-530d26ccdc9e")){
            return new MessageModel();
        }
        List<PayDesk> payDeskList= payDeskService.getAllPayDesk();
        return success(payDeskList);
    }

}

