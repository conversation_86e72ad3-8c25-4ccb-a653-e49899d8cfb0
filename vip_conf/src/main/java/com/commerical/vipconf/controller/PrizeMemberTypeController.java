package com.commerical.vipconf.controller;


import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.commerical.vipconf.annotation.CheckUser;
import com.commerical.vipconf.config.constant.DressCenterConstant;
import com.commerical.vipconf.domain.DressCenterPic;
import com.commerical.vipconf.domain.PrizeMemberType;
import com.commerical.vipconf.domain.vo.DressCenterPicQueryVO;
import com.commerical.vipconf.domain.vo.DressCenterPicVO;
import com.commerical.vipconf.domain.vo.PrizeMemberTypeQueryVo;
import com.commerical.vipconf.service.DressCenterPicService;
import com.commerical.vipconf.service.PrizeMemberTypeService;
import com.commerical.vipconf.service.PubDressCenterPicService;
import com.kuwo.commercialization.common.message.MessageModel;
import com.kuwo.commercialization.common.message.SystemCodeErrorConstant;
import com.kuwo.commercialization.common.utill.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Arrays;

/**
 * <p>
 * 支付结果页优化抽奖分类配置
 * </p>
 */
@RestController
@RequestMapping("/prizeMemberType")
@Slf4j
public class PrizeMemberTypeController extends BaseController {

    @Autowired
    private PrizeMemberTypeService prizeMemberTypeService;

    /**
     * 添加/修改
     *
     * @param prizeMemberType
     * @return
     */
    @RequestMapping("/addOrUpdate")
    public MessageModel addOrUpdate(ServletRequest servletRequest, ServletResponse servletResponse,@Valid PrizeMemberType prizeMemberType){
        if(ipLimit(servletRequest,servletResponse)){
            return failed(SystemCodeErrorConstant.UNAUTHORIZED);
        }

        log.info("addOrUpdate param={}", JSONUtil.toJsonStr(prizeMemberType));
        PrizeMemberType obj=prizeMemberTypeService.addOrUpdate(prizeMemberType);
        return success(obj.getId()) ;
    }

    /**
     * 删除
     *
     * @param
     * @return
     */
    @RequestMapping("/deleteById")
    public MessageModel deleteById(ServletRequest servletRequest, ServletResponse servletResponse,Integer id){
        if(ipLimit(servletRequest,servletResponse)){
            return failed(SystemCodeErrorConstant.UNAUTHORIZED);
        }

        log.info("deleteById id={}",id);
        if(ObjectUtils.isNull(id)){
            log.error("deleteById param  has empty value!,id={}",id);
            return failed();
        }
        prizeMemberTypeService.deleteById(id);
        return success();
    }

    /**
     * 列表查询
     *
     * @param
     * @return
     */
    @RequestMapping("/getList")
    public MessageModel getList(@Valid PrizeMemberTypeQueryVo prizeMemberTypeQueryVo){
        IPage<PrizeMemberType> list= prizeMemberTypeService.getList(prizeMemberTypeQueryVo);
        return success(list);
    }

    /**
     * 详情查询
     *
     * @param
     * @return
     */
    @RequestMapping("/getOneById")
    public MessageModel getOneById(Integer id){
        if(ObjectUtils.isNull(id)){
            log.error("getOneById id empty value！");
            return failed();
        }
        PrizeMemberType prizeMemberType= prizeMemberTypeService.getOneById(id);
        return success(prizeMemberType);
    }

    private boolean vaild(HttpServletRequest request) {
        try {
            if (request.getCookies()==null || request.getCookies().length == 0) {
                return Boolean.FALSE;
            }
            if (!Arrays.asList(request.getCookies()).stream().anyMatch(e -> "user".equals(e.getName()))) {
                return Boolean.FALSE;
            }
            Cookie cookie = Arrays.asList(request.getCookies()).stream().filter(e -> "user".equals(e.getName())).findFirst().orElse(null);
            if (cookie == null || org.apache.commons.lang3.StringUtils.isBlank(cookie.getValue())) {
                return Boolean.FALSE;
            }
            String decode = URLDecoder.decode(cookie.getValue(), "UTF-8");
            JSONObject jsonObject = JSONObject.parseObject(decode);
            if (!jsonObject.containsKey("ename")) {
                return Boolean.FALSE;
            }
            String ename = jsonObject.getString("ename");
            if (StringUtils.isBlank(ename)) {
                return Boolean.FALSE;
            }
            log.info("old_server_vaild_cookie ename: {}, url: {}, method: {}, content-type: {}, param: {}", ename, request.getRequestURI(), request.getMethod(), request.getContentType(), JSONObject.toJSONString(request.getParameterMap()));
            return Boolean.TRUE;
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return Boolean.FALSE;
    }
    private boolean ipLimit(ServletRequest servletRequest, ServletResponse servletResponse){
        boolean bl=false;
        String ipAddr = IpLimitUtil.getIpAddr((HttpServletRequest) servletRequest);
        log.info("req user ipaddr is : " + ipAddr);
        HttpServletRequest request = (HttpServletRequest)servletRequest;
        if (EnvUtil.isOnline() && !vaild((HttpServletRequest) servletRequest)) {
            bl=true;
        }
        log.info("old server request msg ::: url: {}, method: {}, content-type: {}, param: {}", request.getRequestURI(), request.getMethod(), request.getContentType(), JSONObject.toJSONString(request.getParameterMap()));
        if (!InternalIpUtil.internalIp(ipAddr)) {
            bl=true;
        }
        return bl;
    }
}

