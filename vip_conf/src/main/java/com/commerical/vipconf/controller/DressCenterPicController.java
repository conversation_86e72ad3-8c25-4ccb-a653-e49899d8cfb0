package com.commerical.vipconf.controller;


import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.commerical.vipconf.annotation.CheckUser;
import com.commerical.vipconf.config.constant.DressCenterConstant;
import com.commerical.vipconf.domain.DressCenterPic;
import com.commerical.vipconf.domain.vo.DressCenterPicQueryVO;
import com.commerical.vipconf.domain.vo.DressCenterPicVO;
import com.commerical.vipconf.service.DressCenterPicService;
import com.commerical.vipconf.service.PubDressCenterBannerService;
import com.commerical.vipconf.service.PubDressCenterPicService;
import com.kuwo.commercialization.common.message.MessageModel;
import com.kuwo.commercialization.common.message.SystemCodeErrorConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <p>
 * 桌面图标配置 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-24
 */
@RestController
@RequestMapping("/dressCenterPic")
@Slf4j
public class DressCenterPicController extends BaseController {

    @Autowired
    private DressCenterPicService dressCenterPicService;

    @Autowired
    private PubDressCenterPicService pubDressCenterPicService;

    /**
     * 添加/修改
     *
     * @param DressCenterPicVO
     * @return
     */
    @CheckUser
    @RequestMapping("/addOrUpdateDressCenterPic")
    public MessageModel addOrUpdateDressCenterPic(@Valid DressCenterPicVO DressCenterPicVO){
        log.info("addOrUpdateDressCenterPic param={}", JSONUtil.toJsonStr(DressCenterPicVO));
        DressCenterPic DressCenterPic=dressCenterPicService.addOrUpdateDressCenterPic(DressCenterPicVO);
        return success(DressCenterPic.getCode()) ;
    }

    /**
     * 删除
     *
     * @param
     * @return
     */
    @CheckUser
    @RequestMapping("/deleteDressCenterPicById")
    public MessageModel deleteDressCenterPicById(Integer id,Boolean isTrue,Integer isDelete){
        log.info("deleteDressCenterPicById id={} isTrue={} isDelete={}",id,isTrue,isDelete);
        if(ObjectUtils.isNull(id)||ObjectUtils.isNull(isTrue)||ObjectUtils.isNull(isDelete)){
            log.error("deleteDressCenterPicById param  has empty value!,id={}",id);
            return failed();
        }
        dressCenterPicService.deleteDressCenterPicById(id,isTrue,isDelete);
        return success();
    }

    /**
     * 列表查询
     *
     * @param
     * @return
     */
    @RequestMapping("/getDressCenterPicList")
    public MessageModel getDressCenterPicList(@Valid DressCenterPicQueryVO DressCenterPicQueryVO){
        IPage<DressCenterPic> DressCenterPicVOList= dressCenterPicService.getDressCenterPicList(DressCenterPicQueryVO);
        return success(DressCenterPicVOList);
    }

    /**
     * 详情查询
     *
     * @param
     * @return
     */
    @RequestMapping("/getDressCenterPicInfoById")
    public MessageModel getDressCenterPicInfoById(Integer id){
        if(ObjectUtils.isNull(id)){
            log.error("deleteDressCenterPicById id empty value！");
            return failed();
        }
        DressCenterPic DressCenterPic= dressCenterPicService.getDressCenterPicInfoById(id);
        return success(DressCenterPic);
    }

    /**
     * 发布pic
     *
     * @param code
     * @return
     */
    @CheckUser
    @RequestMapping("/pubDressCenterPic")
    public MessageModel pubDressCenterPic(String code){
        if(StringUtils.isBlank(code)){
            log.error("pubDressCenterPic code empty value！");
            return failed(SystemCodeErrorConstant.CODE_ERROR);
        }
        String name= DressCenterConstant.userLocal.get();
        log.info("pubDressCenterPic code={},name={}", code,name);
        SystemCodeErrorConstant systemCodeErrorConstant= pubDressCenterPicService.pubDressCenterPic(code);
        if(systemCodeErrorConstant== SystemCodeErrorConstant.SUCCESS){
            return success();
        }
        return failed(systemCodeErrorConstant) ;
    }
}

