package com.commerical.vipconf.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.commerical.vipconf.domain.ProjectTemplate;
import com.commerical.vipconf.domain.vo.ProjectTemplateQueryVO;
import com.commerical.vipconf.domain.vo.ProjectTemplateVO;
import com.commerical.vipconf.service.ProjectTemplateService;
import com.kuwo.commercialization.common.message.MessageModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <p>
 *  项目模板 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-01
 */
@RestController
@RequestMapping("/projectTemplate")
public class ProjectTemplateController extends BaseController{

    private final static Logger logger = LoggerFactory.getLogger(ProjectTemplateController.class);

    @Autowired
    private ProjectTemplateService projectTemplateService;

    /**
     * 添加项目模板
     *
     * @param projectTemplateVO
     * @return
     */
    @RequestMapping("/addOrUpdateProjectTemplate")
    public MessageModel addOrUpdateProjectTemplate(@Valid ProjectTemplateVO projectTemplateVO){
        ProjectTemplate projectTemplate= projectTemplateService.addOrUpdateProjectTemplate(projectTemplateVO);
        return success(projectTemplate.getCode());
    }

    /**
     * 删除项目模板
     *
     * @param
     * @return
     */
    @RequestMapping("/deleteProjectTemplateById")
    public MessageModel deleteProjectTemplateById(Integer id,Boolean isTrue,Integer isDelete){
        if(ObjectUtils.isNull(id)||ObjectUtils.isNull(isTrue)||ObjectUtils.isNull(isDelete)){
            logger.error("deleteProjectTemplateById param has empty value!,id={}",id);
            return failed();
        }
        projectTemplateService.deleteProjectTemplateById(id,isTrue,isDelete);
        return success();
    }

    /**
     * 项目模板列表查询
     *
     * @param
     * @return
     */
    @RequestMapping("/getProjectTemplateList")
    public MessageModel getProjectTemplateList(@Valid ProjectTemplateQueryVO projectTemplateQueryVO){
        IPage<ProjectTemplate> projectTemplateVOList= projectTemplateService.getProjectTemplateList(projectTemplateQueryVO);
        return success(projectTemplateVOList);
    }

    /**
     * 项目模板详情查询
     *
     * @param
     * @return
     */
    @RequestMapping("/getProjectTemplateInfoById")
    public MessageModel getProjectTemplateInfoById(Integer id){
        ProjectTemplate projectTemplate= projectTemplateService.getProjectTemplateInfoById(id);
        return success(projectTemplate);
    }

}

