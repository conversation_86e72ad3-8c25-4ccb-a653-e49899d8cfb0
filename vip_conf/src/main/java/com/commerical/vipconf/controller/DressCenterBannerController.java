package com.commerical.vipconf.controller;


import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.commerical.vipconf.annotation.CheckUser;
import com.commerical.vipconf.config.constant.DressCenterConstant;
import com.commerical.vipconf.domain.DressCenterBanner;
import com.commerical.vipconf.domain.vo.DressCenterBannerQueryVO;
import com.commerical.vipconf.domain.vo.DressCenterBannerVO;
import com.commerical.vipconf.service.DressCenterBannerService;
import com.commerical.vipconf.service.PubDressCenterBannerService;
import com.kuwo.commercialization.common.message.MessageModel;
import com.kuwo.commercialization.common.message.SystemCodeErrorConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * <p>
 * 装扮中心 banner配置 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-23
 */
@RestController
@RequestMapping("/banner")
@Slf4j
public class DressCenterBannerController extends BaseController{

    @Autowired
    private DressCenterBannerService dressCenterBannerService;

    @Autowired
    private PubDressCenterBannerService pubDressCenterBannerService;

    /**
     * 添加/修改
     *
     * @param dressCenterBannerVO
     * @return
     */
    @CheckUser
    @RequestMapping("/addOrUpdateDressCenterBanner")
    public MessageModel addOrUpdatedressCenterBanner(@Valid DressCenterBannerVO dressCenterBannerVO){
        log.info("addOrUpdatedressCenterBanner param={}", JSONUtil.toJsonStr(dressCenterBannerVO));
        DressCenterBanner dressCenterBanner=dressCenterBannerService.addOrUpdateDressCenterBanner(dressCenterBannerVO);
        return success(dressCenterBanner.getId()) ;
    }

    /**
     * 删除
     *
     * @param
     * @return
     */
    @CheckUser
    @RequestMapping("/deleteDressCenterBannerById")
    public MessageModel deletedressCenterBannerById(Integer id,Boolean isTrue,Integer isDelete){
        if(ObjectUtils.isNull(id)||ObjectUtils.isNull(isTrue)||ObjectUtils.isNull(isDelete)){
            log.error("deletedressCenterBannerById param  has empty value!,id={}",id);
            return failed();
        }
        log.info("deleteDressCenterBannerById param={}", id);
        dressCenterBannerService.deleteDressCenterBannerById(id,isTrue,isDelete);
        return success();
    }

    /**
     * 列表查询
     *
     * @param
     * @return
     */
    @RequestMapping("/getDressCenterBannerList")
    public MessageModel getdressCenterBannerList(@Valid DressCenterBannerQueryVO dressCenterBannerQueryVO, HttpServletRequest request){
        Cookie[] cookies = request.getCookies();
        if(null!=cookies){
            for(Cookie cookie : cookies){
                log.info("cookie name={},value={}",cookie.getName(),cookie);
            }
        }
        IPage<DressCenterBanner> dressCenterBannerVOList= dressCenterBannerService.getDressCenterBannerList(dressCenterBannerQueryVO);;
        return success(dressCenterBannerVOList);
    }

    /**
     * 详情查询
     *
     * @param
     * @return
     */
    @RequestMapping("/getdressCenterBannerInfoById")
    public MessageModel getdressCenterBannerInfoById(Integer id){
        if(ObjectUtils.isNull(id)){
            log.error("deletedressCenterBannerById id empty value！");
            return failed();
        }
        DressCenterBanner dressCenterBanner= dressCenterBannerService.getDressCenterBannerInfoById(id);
        return success(dressCenterBanner);
    }

    /**
     * 发布banner
     *
     * @param code code标识
     * @return
     */
    @CheckUser
    @RequestMapping("/pubDressCenterBanner")
    public MessageModel pubDressCenterBanner(String code){
        if(StringUtils.isBlank(code)){
            log.error("pubDressCenterBanner code empty value！");
            return failed(SystemCodeErrorConstant.CODE_ERROR);
        }
        String name= DressCenterConstant.userLocal.get();
        log.info("pubDressCenterBanner code={},name={}", code,name);
        SystemCodeErrorConstant systemCodeErrorConstant =pubDressCenterBannerService.pubDressCenterBanner(code);
        if(systemCodeErrorConstant==SystemCodeErrorConstant.SUCCESS){
            return success();
        }
        return failed(systemCodeErrorConstant);
    }

}

