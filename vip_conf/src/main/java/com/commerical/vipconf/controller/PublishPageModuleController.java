package com.commerical.vipconf.controller;


import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.commerical.vipconf.domain.bo.ProjectPageBO;
import com.commerical.vipconf.domain.bo.PublishProjectPageBO;
import com.commerical.vipconf.service.PublishPageModuleService;
import com.kuwo.commercialization.common.message.MessageModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 发布后的页面模块 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-20
 */
@RestController
@RequestMapping("/publishPageModule")
public class PublishPageModuleController extends BaseController{

    @Autowired
    PublishPageModuleService publishPageModuleService;

    /**
     * 项目页面模块
     *
     * @param
     * @return
     */
    @RequestMapping("/getPublishPageContent")
    public MessageModel getPageContent(String projectPageCode){
        if(StringUtils.isBlank(projectPageCode)){
            return failed();
        }
        PublishProjectPageBO publishProjectPageBO= publishPageModuleService.getPublishPageContent(projectPageCode);
        return success(publishProjectPageBO);
    }

}

