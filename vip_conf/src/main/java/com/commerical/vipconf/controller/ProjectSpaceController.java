package com.commerical.vipconf.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.commerical.vipconf.annotation.UserPermission;
import com.commerical.vipconf.domain.ProjectSpace;
import com.commerical.vipconf.domain.vo.ProjectSpaceQueryVO;
import com.commerical.vipconf.domain.vo.ProjectSpaceVO;
import com.commerical.vipconf.service.ProjectSpaceService;
import com.kuwo.commercialization.common.message.MessageModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <p>
 * 项目空间 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-01
 */
@RestController
@RequestMapping("/projectSpace")
public class ProjectSpaceController extends BaseController {


    private final static Logger logger = LoggerFactory.getLogger(ProjectSpaceController.class);

    @Autowired
    private ProjectSpaceService projectSpaceService;

    /**
     * 添加项目空间
     *
     * <p>
     *  @UserPermission(type="2")
     *  type=2验证当前访问用户ename是否有编辑权限
     * </p>
     * @param projectSpaceVO 参数
     * @return 添加/修改 空间code
     */
    @UserPermission(type="2")// 验证是否有编辑权限
    @RequestMapping("/addOrUpdateProjectSpace")
    public MessageModel addOrUpdateProjectSpace(@Valid ProjectSpaceVO projectSpaceVO){
        ProjectSpace projectSpace= projectSpaceService.addOrUpdateProjectSpace(projectSpaceVO);
        return success(projectSpace.getCode());
    }

    /**
     * 删除项目空间
     *
     * @param
     * @return
     */
    @RequestMapping("/deleteProjectSpaceById")
    public MessageModel deleteProjectSpaceById(Integer id,Boolean isTrue,Integer isDelete){
        if(ObjectUtils.isNull(id)||ObjectUtils.isNull(isTrue)||ObjectUtils.isNull(isDelete)){
            logger.error("deleteProjectSpaceById param  has empty value!,id={}",id);
            return failed();
        }
        projectSpaceService.deleteProjectSpaceById(id,isTrue,isDelete);
        return success();
    }

    /**
     * 项目空间列表查询
     *
     * @param
     * @return
     */
  //  @UserPermission(type="1")
    @RequestMapping("/getProjectSpaceList")
    public MessageModel getProjectSpaceList(@Valid ProjectSpaceQueryVO ProjectSpaceQueryVO){
        IPage<ProjectSpace> ProjectSpaceVOList= projectSpaceService.getProjectSpaceList(ProjectSpaceQueryVO);
        return success(ProjectSpaceVOList);
    }

    /**
     * 项目空间详情查询
     *
     * @param
     * @return
     */
    @UserPermission(type="1",codeType = "projectSpaceId")
    @RequestMapping("/getProjectSpaceInfoById")
    public MessageModel getProjectSpaceInfoById(Integer id){
        ProjectSpace ProjectSpace= projectSpaceService.getProjectSpaceInfoById(id);
        return success(ProjectSpace);
    }

}

