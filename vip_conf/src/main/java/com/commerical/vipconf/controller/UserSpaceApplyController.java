package com.commerical.vipconf.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.commerical.vipconf.domain.UserSpaceApply;
import com.commerical.vipconf.domain.vo.UserSpaceApplyQueryVO;
import com.commerical.vipconf.domain.vo.UserSpaceApplyVO;
import com.commerical.vipconf.service.UserSpaceApplyService;
import com.kuwo.commercialization.common.message.MessageModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <p>
 * 用户空间权限申请 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-13
 */
@RestController
@RequestMapping("/userApply")
public class UserSpaceApplyController extends BaseController{

    private final static Logger logger = LoggerFactory.getLogger(UserSpaceApplyController.class);

    @Autowired
    private UserSpaceApplyService userSpaceApplyService;

    /**
     * 添加权限申请
     *
     * @param userSpaceApplyVO
     * @return
     */
    @RequestMapping("/applyUserSpace")
    public MessageModel addOrUpdateUserSpaceApply(@Valid UserSpaceApplyVO userSpaceApplyVO){
        UserSpaceApply UserSpaceApply= userSpaceApplyService.addOrUpdateUserSpaceApply(userSpaceApplyVO);
        return success(UserSpaceApply.getId());
    }

    /**
     * 更新申请状态
     *
     * @param
     * @return
     */
    @RequestMapping("/updateUserApplyStatus")
    public MessageModel updateUserApplyStatus(@Valid UserSpaceApplyVO userSpaceApplyVO){
        userSpaceApplyService.updateUserApplyStatus(userSpaceApplyVO);
        return success();
    }

    /**
     * 权限申请列表查询
     *
     * @param
     * @return
     */
    @RequestMapping("/getUserSpaceApplyList")
    public MessageModel getUserSpaceApplyList(@Valid UserSpaceApplyQueryVO userSpaceApplyQueryVO){
        IPage<UserSpaceApply> UserSpaceApplyVOList= userSpaceApplyService.getUserSpaceApplyList(userSpaceApplyQueryVO);
        return success(UserSpaceApplyVOList);
    }

    /**
     * 权限申请详情查询
     *
     * @param
     * @return
     */
    @RequestMapping("/getUserSpaceApplyInfoById")
    public MessageModel getUserSpaceApplyInfoById(Integer id){
        UserSpaceApply UserSpaceApply= userSpaceApplyService.getUserSpaceApplyInfoById(id);
        return success(UserSpaceApply);
    }

    /**
     * 权限申请详情查询
     *
     * @param
     * @return
     */
    @RequestMapping("/authUserSpace")
    public MessageModel authUserSpace(@RequestBody String projectSpaceCode,String applyNames ,String approveName,Integer type){
        if(StringUtils.isBlank(projectSpaceCode)||StringUtils.isBlank(applyNames)||type==null){
            return failed();
        }
       userSpaceApplyService.authUserSpace(projectSpaceCode, applyNames,approveName, type);
       return success();
    }
}

