package com.commerical.vipconf.controller;


import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.commerical.vipconf.annotation.CheckUser;
import com.commerical.vipconf.config.constant.DressCenterConstant;
import com.commerical.vipconf.domain.DressCenterToptab;
import com.commerical.vipconf.domain.vo.DressCenterToptabQueryVO;
import com.commerical.vipconf.domain.vo.DressCenterToptabVO;
import com.commerical.vipconf.service.DressCenterToptabService;
import com.commerical.vipconf.service.PubDressCenterPicService;
import com.commerical.vipconf.service.PubDressCenterToptabService;
import com.kuwo.commercialization.common.message.MessageModel;
import com.kuwo.commercialization.common.message.SystemCodeErrorConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <p>
 * 装扮中心顶部导航栏配置 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-23
 */
@RestController
@RequestMapping("/dressCenterToptab")
@Slf4j
public class DressCenterToptabController extends BaseController {


    @Autowired
    private DressCenterToptabService dressCenterToptabService;


    @Autowired
    private PubDressCenterToptabService pubDressCenterToptabService;

    /**
     * 添加/修改
     *
     * @param DressCenterToptabVO
     * @return
     */
    @CheckUser
    @RequestMapping("/addOrUpdateDressCenterToptab")
    public MessageModel addOrUpdateDressCenterToptab(@Valid DressCenterToptabVO DressCenterToptabVO){
        log.info("addOrUpdateDressCenterToptab param={}", JSONUtil.toJsonStr(DressCenterToptabVO));
        DressCenterToptab DressCenterToptab=dressCenterToptabService.addOrUpdateDressCenterToptab(DressCenterToptabVO);
        return success(DressCenterToptab.getCode()) ;
    }

    /**
     * 删除
     *
     * @param
     * @return
     */
    @CheckUser
    @RequestMapping("/deleteDressCenterToptabById")
    public MessageModel deleteDressCenterToptabById(Integer id,Boolean isTrue,Integer isDelete){
        if(ObjectUtils.isNull(id)||ObjectUtils.isNull(isTrue)||ObjectUtils.isNull(isDelete)){
            log.error("deleteDressCenterToptabById param  has empty value!,id={}",id);
            return failed();
        }
        log.info("deleteDressCenterToptabById param={}", id);
        dressCenterToptabService.deleteDressCenterToptabById(id,isTrue,isDelete);
        return success();
    }

    /**
     * 列表查询
     *
     * @param
     * @return
     */
    @RequestMapping("/getDressCenterToptabList")
    public MessageModel getDressCenterToptabList(@Valid DressCenterToptabQueryVO DressCenterToptabQueryVO){
        IPage<DressCenterToptab> DressCenterToptabVOList= dressCenterToptabService.getDressCenterToptabList(DressCenterToptabQueryVO);;
        return success(DressCenterToptabVOList);
    }

    /**
     * 详情查询
     *
     * @param
     * @return
     */
    @RequestMapping("/getDressCenterToptabInfoById")
    public MessageModel getDressCenterToptabInfoById(Integer id){
        if(ObjectUtils.isNull(id)){
            log.error("deleteDressCenterToptabById id empty value！");
            return failed();
        }
        DressCenterToptab DressCenterToptab= dressCenterToptabService.getDressCenterToptabInfoById(id);
        return success(DressCenterToptab);
    }

    /**
     * 发布topTab
     *
     * @param code
     * @return
     */
    @CheckUser
    @RequestMapping("/pubDressCenterToptab")
    public MessageModel pubDressCenterToptab(String code){
        if(StringUtils.isBlank(code)){
            log.error("pubDressCenterToptab code empty value！");
            return failed(SystemCodeErrorConstant.CODE_ERROR);
        }
        String name= DressCenterConstant.userLocal.get();
        log.info("pubDressCenterToptab code={},name={}", code,name);
        SystemCodeErrorConstant systemCodeErrorConstant= pubDressCenterToptabService.pubDressCenterToptab(code);
        if(systemCodeErrorConstant== SystemCodeErrorConstant.SUCCESS){
            return success();
        }
        return failed(systemCodeErrorConstant);
    }
}

