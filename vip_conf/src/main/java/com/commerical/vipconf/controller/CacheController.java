package com.commerical.vipconf.controller;

import com.commerical.vipconf.config.cache.FilterCaffeineCache;
import com.kuwo.commercialization.common.message.MessageModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * Cache Management Controller
 * Provides cache status viewing and manual refresh functions
 */
@Slf4j
@RestController
@RequestMapping("/api/cache")
public class CacheController extends BaseController {


    @Autowired
    private FilterCaffeineCache caffeineCacheUtils;

    /**
     * Get cache status
     */
    @GetMapping("/status")
    public MessageModel getCacheStatus() {
        try {
            Map<String, Object> result = new HashMap<>();
            result.put("caffeineCache", caffeineCacheUtils.getCacheStats());
            return success(result);
        } catch (Exception e) {
            log.error("Failed to get cache status", e);
            return failed();
        }
    }

    /**
     * Refresh cache
     */
    @GetMapping("/refresh")
    public MessageModel refreshCache() {
        try {
            caffeineCacheUtils.refreshAllCache();
            return success("Cache refresh successful");
        } catch (Exception e) {
            log.error("Failed to refresh cache", e);
            return failed();
        }
    }
} 