package com.commerical.vipconf.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.commerical.vipconf.domain.PageModule;
import com.commerical.vipconf.domain.vo.PageModuleQueryVO;
import com.commerical.vipconf.domain.vo.PageModuleVO;
import com.commerical.vipconf.service.PageModuleService;
import com.kuwo.commercialization.common.message.MessageModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <p>
 * 页面模块 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-03
 */
@RestController
@RequestMapping("/pageModule")
public class PageModuleController extends BaseController{

    private final static Logger logger = LoggerFactory.getLogger(PageModuleController.class);

    @Autowired
    private PageModuleService pageModuleService;

    /**
     * 添加/修改 页面模块
     *
     * @param pageModuleVO
     * @return
     */
    @RequestMapping("/addOrUpdatePageModule")
    public MessageModel addOrUpdatePageModule(@Valid PageModuleVO pageModuleVO){
        PageModule pageModule= pageModuleService.addOrUpdatePageModule(pageModuleVO);
        return success(pageModule.getId());
    }

    /**
     * 删除页面模块
     *
     * @param
     * @return
     */
    @RequestMapping("/deletePageModuleById")
    public MessageModel deletePageModuleById(Integer id,Boolean isTrue,Integer isDelete){
        if(ObjectUtils.isNull(id)||ObjectUtils.isNull(isTrue)||ObjectUtils.isNull(isDelete)){
            logger.error("deletePageModuleById param  has empty value!,id={}",id);
            return failed();
        }
        pageModuleService.deletePageModuleById(id,isTrue,isDelete);
        return success();
    }

    /**
     * 页面模块列表查询
     *
     * @param
     * @return
     */
    @RequestMapping("/getPageModuleList")
    public MessageModel getPageModuleList(@Valid PageModuleQueryVO pageModuleQueryVO){
        IPage<PageModule> PageModuleVOList= pageModuleService.getPageModuleList(pageModuleQueryVO);
        return success(PageModuleVOList);
    }

    /**
     * 页面模块详情查询
     *
     * @param
     * @return
     */
    @RequestMapping("/getPageModuleInfoById")
    public MessageModel getPageModuleInfoById(Integer id){
        PageModule PageModule= pageModuleService.getPageModuleInfoById(id);
        return success(PageModule);
    }

}

