package com.commerical.vipconf.controller;

import com.commerical.vipconf.config.redis.DynamicChoiceRedissonClient;
import com.commerical.vipconf.service.impl.AsyncCombineCashService;
import com.commerical.vipconf.service.impl.LimitPopServiceImpl;
import com.kuwo.commercialization.common.message.MessageModel;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Desc: 刷新redis
 * @date 2024-07-10 21:19:36
 */
@RestController
@RequestMapping("/refresh")
public class RefreshController extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(RefreshController.class);
    @Autowired
    private DynamicChoiceRedissonClient dynamicChoiceRedissonClient;

    @GetMapping("/clearNonModePop")
    public MessageModel clearNonModePop(String userId, String vers) {
        if (!"RweJ39SKwJs1hPKkRl4CD7jEdMFo56we".equals(vers)) {
            return failed();
        }

        try {
            RedissonClient redissonClient = dynamicChoiceRedissonClient.getClient("d1");

            String triggeredKey = String.format(LimitPopServiceImpl.TRIGGERED_KEY, userId);
            String todayCountKey = String.format(LimitPopServiceImpl.TODAY_COUNT_KEY, userId);
            String exposuresKey = String.format(LimitPopServiceImpl.EXPOSURES_KEY, userId);

            redissonClient.getBucket(triggeredKey).delete();
            redissonClient.getBucket(todayCountKey).delete();
            redissonClient.getBucket(exposuresKey).delete();
        } catch (Exception e) {
            log.error("clearNonModePop error is ", e);
            return failed();
        }
        return success();
    }

    @GetMapping("/clearSingleFreeMode")
    public MessageModel clearSingleFreeMode(String userId, String vers) {
        if (!"RweJ39SKwJs1hPKkRl4CD7jEdMFo56we".equals(vers)) {
            return failed();
        }

        try {
            RedissonClient redissonClient = dynamicChoiceRedissonClient.getClient("d1");

            String triggeredKey = String.format(LimitPopServiceImpl.SINGLE_TRIGGERED_KEY, userId);
            String todayCountKey = String.format(LimitPopServiceImpl.SINGLE_TODAY_COUNT_KEY, userId);

            redissonClient.getBucket(triggeredKey).delete();
            redissonClient.getBucket(todayCountKey).delete();
        } catch (Exception e) {
            log.error("clearSingleFreeMode error is ", e);
            return failed();
        }
        return success();
    }

    @GetMapping("/clearInitPopupInfo")
    public MessageModel clearInitPopupInfo(String userId, String vers) {
        if (!"RweJ39SKwJs1hPKkRl4CD7jEdMFo56we".equals(vers)) {
            return failed();
        }

        try {
            RedissonClient redissonClient = dynamicChoiceRedissonClient.getClient("d1");
            String key = String.format(AsyncCombineCashService.MEMBER_CENTER_ALERT_NUMBER, userId);
            redissonClient.getBucket(key).delete();
        } catch (Exception e) {
            log.error("clearInitPopupInfo error is ", e);
            return failed();
        }
        return success();
    }
}