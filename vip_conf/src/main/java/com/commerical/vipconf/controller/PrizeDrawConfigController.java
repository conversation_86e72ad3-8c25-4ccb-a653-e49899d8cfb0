package com.commerical.vipconf.controller;


import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.commerical.vipconf.annotation.CheckUser;
import com.commerical.vipconf.domain.PrizeDrawConfig;
import com.commerical.vipconf.domain.PrizeMemberType;
import com.commerical.vipconf.domain.vo.PrizeDrawConfigQueryVo;
import com.commerical.vipconf.domain.vo.PrizeMemberTypeQueryVo;
import com.commerical.vipconf.service.PrizeDrawConfigService;
import com.commerical.vipconf.service.PrizeMemberTypeService;
import com.commerical.vipconf.service.cashstragety.IOSApprovedVersionService;
import com.kuwo.commercialization.common.message.MessageModel;
import com.kuwo.commercialization.common.message.SystemCodeErrorConstant;
import com.kuwo.commercialization.common.utill.EnvUtil;
import com.kuwo.commercialization.common.utill.InternalIpUtil;
import com.kuwo.commercialization.common.utill.IpLimitUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

/**
 * <p>
 * 支付结果页优化抽奖奖品配置
 * </p>
 */
@RestController
@RequestMapping("/prizeDrawConfig")
@Slf4j
public class PrizeDrawConfigController extends BaseController {

    @Autowired
    private PrizeDrawConfigService prizeDrawConfigService;

    @Autowired
    private IOSApprovedVersionService iosApprovedVersionService;

    /**
     * 添加/修改
     *
     * @param prizeDrawConfig
     * @return
     */
    @RequestMapping("/addOrUpdate")
    public MessageModel addOrUpdate(HttpServletRequest servletRequest, ServletResponse servletResponse,@Valid PrizeDrawConfig prizeDrawConfig) throws UnsupportedEncodingException {
        if(ipLimit(servletRequest,servletResponse)){
            return failed(SystemCodeErrorConstant.UNAUTHORIZED);
        }
        if (servletRequest.getCookies()!=null && servletRequest.getCookies().length > 0) {
            if (Arrays.asList(servletRequest.getCookies()).stream().anyMatch(e -> "user".equals(e.getName()))) {
                Cookie cookie = Arrays.asList(servletRequest.getCookies()).stream().filter(e -> "user".equals(e.getName())).findFirst().orElse(null);
                if (cookie != null && !StringUtils.isBlank(cookie.getValue())) {
                    String decode = URLDecoder.decode(cookie.getValue(), "UTF-8");
                    JSONObject jsonObject = JSONObject.parseObject(decode);
                    if (jsonObject.containsKey("ename")) {
                        String cname = jsonObject.getString("cname");
                        String ename = jsonObject.getString("ename");
                        prizeDrawConfig.setExt1(cname+"("+ename+")");
                    }

                }
            }
        }
        
        log.info("addOrUpdate param={}", JSONUtil.toJsonStr(prizeDrawConfig));
        PrizeDrawConfig obj=prizeDrawConfigService.addOrUpdate(prizeDrawConfig);
        return success(obj.getId()) ;
    }

    /**
     * 删除
     *
     * @param
     * @return
     */
    @RequestMapping("/deleteById")
    public MessageModel deleteById(ServletRequest servletRequest, ServletResponse servletResponse,Integer id){
        if(ipLimit(servletRequest,servletResponse)){
            return failed(SystemCodeErrorConstant.UNAUTHORIZED);
        }

        log.info("deleteById id={}",id);
        if(ObjectUtils.isNull(id)){
            log.error("deleteById param  has empty value!,id={}",id);
            return failed();
        }
        prizeDrawConfigService.deleteById(id);
        return success();
    }

    /**
     * 列表查询
     *
     * @param
     * @return
     */
    @RequestMapping("/getList")
    public MessageModel getList(@Valid PrizeDrawConfigQueryVo prizeDrawConfigQueryVo){
        IPage<PrizeDrawConfig> list= prizeDrawConfigService.getList(prizeDrawConfigQueryVo);
        return success(list);
    }

    /**
     * 详情查询
     *
     * @param
     * @return
     */
    @RequestMapping("/getOneById")
    public MessageModel getOneById(Integer id){
        if(ObjectUtils.isNull(id)){
            log.error("getOneById id empty value！");
            return failed();
        }
        PrizeDrawConfig prizeDrawConfig= prizeDrawConfigService.getOneById(id);
        return success(prizeDrawConfig);
    }


    /**
     * 查询奖品列表
     *
     * @param
     * @return
     */
    @RequestMapping("/getListByPid")
    public MessageModel getListByPid(Integer pid,Long userId,String platform, String source){
        if(ObjectUtils.isNull(pid)){
            log.error("getListByPid pid empty value！");
            return failed();
        }
        if(ObjectUtils.isNull(platform)){
            log.error("getListByPid platform empty value！");
            return failed();
        }
        if(ObjectUtils.isNull(userId)){
            log.error("getListByPid userId empty value！");
            return failed();
        }
        if(userId<=0){
            log.error("getListByPid userId err 0！");
            return failed();
        }
        if (iosApprovedVersionService.isApproved(source, platform)) {
            log.info("iOS审核版不下发");
            return success();
        }
        List<PrizeDrawConfig> prizeDrawConfigList= prizeDrawConfigService.getListByPid(pid,userId,platform);
        return success(prizeDrawConfigList);
    }

    @RequestMapping("/getPrizeDrawConfigByPidAndProbability")
    public MessageModel getPrizeDrawConfigByPidAndProbability(Integer pid,String userid,Long orderId,String platform){
        if(ObjectUtils.isNull(pid)){
            log.error("getPrizeDrawConfigByPidAndProbability pid empty value！");
            return failed();
        }
        if(ObjectUtils.isNull(platform)){
            log.error("getPrizeDrawConfigByPidAndProbability platform empty value！");
            return failed();
        }
        Long uid=null;
        if(userid!=null && !"".equals(userid)){
            uid=Long.parseLong(userid);
        }
        if(uid==null || uid<=0){
            log.error("getPrizeDrawConfigByPidAndProbability userId empty value！");
            return failed();
        }
        JSONObject retJson = prizeDrawConfigService.getPrizeDrawConfigByPidAndProbability(pid, uid, orderId, platform);
        int code = retJson.getIntValue("code");
        if(code==200){
            PrizeDrawConfig prizeDrawConfig = retJson.getObject("prizeDrawConfig", PrizeDrawConfig.class);
            return success(prizeDrawConfig);
        }else if(code==SystemCodeErrorConstant.ACT_TIPS_4000001.getCode()){
            return failed(SystemCodeErrorConstant.ACT_TIPS_4000001);
        }else if(code==SystemCodeErrorConstant.ACT_TIPS_4000002.getCode()) {
            return failed(SystemCodeErrorConstant.ACT_TIPS_4000002);
        }else{
            return failed(SystemCodeErrorConstant.UNAUTHORIZED);
        }
    }


    private boolean vaild(HttpServletRequest request) {
        try {
            if (request.getCookies()==null || request.getCookies().length == 0) {
                return Boolean.FALSE;
            }
            if (!Arrays.asList(request.getCookies()).stream().anyMatch(e -> "user".equals(e.getName()))) {
                return Boolean.FALSE;
            }
            Cookie cookie = Arrays.asList(request.getCookies()).stream().filter(e -> "user".equals(e.getName())).findFirst().orElse(null);
            if (cookie == null || org.apache.commons.lang3.StringUtils.isBlank(cookie.getValue())) {
                return Boolean.FALSE;
            }
            String decode = URLDecoder.decode(cookie.getValue(), "UTF-8");
            JSONObject jsonObject = JSONObject.parseObject(decode);
            if (!jsonObject.containsKey("ename")) {
                return Boolean.FALSE;
            }
            String ename = jsonObject.getString("ename");
            if (StringUtils.isBlank(ename)) {
                return Boolean.FALSE;
            }
            log.info("vip_conf_server_vaild_cookie ename: {}, url: {}, method: {}, content-type: {}, param: {}", ename, request.getRequestURI(), request.getMethod(), request.getContentType(), JSONObject.toJSONString(request.getParameterMap()));
            return Boolean.TRUE;
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return Boolean.FALSE;
    }
    private boolean ipLimit(ServletRequest servletRequest, ServletResponse servletResponse){
        boolean bl=false;
        String ipAddr = IpLimitUtil.getIpAddr((HttpServletRequest) servletRequest);
        log.info("req user ipaddr is : " + ipAddr);
        HttpServletRequest request = (HttpServletRequest)servletRequest;
        if (EnvUtil.isOnline() && !vaild((HttpServletRequest) servletRequest)) {
            bl=true;
        }
        log.info("vip_conf server request msg ::: url: {}, method: {}, content-type: {}, param: {}", request.getRequestURI(), request.getMethod(), request.getContentType(), JSONObject.toJSONString(request.getParameterMap()));
        if (!InternalIpUtil.internalIp(ipAddr)) {
            bl=true;
        }
        return bl;
    }
}

