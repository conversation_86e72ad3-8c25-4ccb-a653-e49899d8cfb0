package com.commerical.vipconf.controller;

import cn.hutool.core.convert.Convert;
import cn.hutool.json.JSONUtil;
import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.commercal.uiservice.enity.VipInfo;
import com.commercal.uiservice.service.UiInfoService;
import com.commerical.abserviceapi.domain.UserInfoBO;
import com.commerical.mointor.config.annotation.SentinelAlarm;
import com.commerical.musicplayerservice.enity.TitleInfoResp;
import com.commerical.vipconf.config.sentinel.LimitHandler;
import com.commerical.vipconf.domain.bo.CashBO;
import com.commerical.vipconf.domain.bo.InterceptResultBO;
import com.commerical.vipconf.domain.bo.ResultBO;
import com.commerical.vipconf.domain.bo.VipConfBO;
import com.commerical.vipconf.domain.context.CalculateContext;
import com.commerical.vipconf.domain.req.SingleSongReq;
import com.commerical.vipconf.domain.vo.CashVO;
import com.commerical.vipconf.domain.vo.RecallMultipleGearVO;
import com.commerical.vipconf.domain.vo.SongPriceGearInfoVO;
import com.commerical.vipconf.enums.AdEnum;
import com.commerical.vipconf.nacos.RecallJointGearSrcNacos;
import com.commerical.vipconf.nacos.VipConfConfigNacos;
import com.commerical.vipconf.service.CashService;
import com.commerical.vipconf.service.SinglePriceService;
import com.commerical.vipconf.service.SongGearService;
import com.commerical.vipconf.service.impl.AsyncCombineCashService;
import com.commerical.vipconf.service.impl.DcRemoteService;
import com.commerical.vipconf.service.impl.FreeModelService;
import com.commerical.vipconf.service.impl.LimitPopServiceImpl;
import com.commerical.vipconf.service.netearn.VipCashBackService;
import com.commerical.vipconf.util.AdVipUtil;
import com.commerical.vipconf.util.AdVipUtil;
import com.commerical.vipconf.util.CarMonitor;
import com.commerical.vipconf.util.IPUtils;
import com.commerical.vipconf.util.InvokeHttpForUserInfo;
import com.kuwo.commercialization.common.message.MessageModel;
import com.kuwo.commercialization.common.utill.IpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Slf4j
@RestController
@RequestMapping("/cash")
public class CashController extends BaseController {

    @Autowired
    private AsyncCombineCashService asyncCombineCashService;

    @Autowired
    private InvokeHttpForUserInfo invokeHttpForUserInfo;

    @Autowired
    private CashService cashService;

    @DubboReference(timeout = 1000)
    private UiInfoService uiInfoService;

    @Autowired
    private CarMonitor carMonitor;

    @Autowired
    private LimitPopServiceImpl limitPop;

    @Autowired
    private FreeModelService freeModelService;

    @Autowired
    private SinglePriceService singlePriceService;

    @Autowired
    private VipConfConfigNacos vipConfConfigNacos;

    @Autowired
    private HttpServletRequest request;

    @Autowired
    private IPUtils ipLocationUtil;

    @Autowired
    private RecallJointGearSrcNacos recallJointGearSrcNacos;

    @Autowired
    private SongGearService songGearService;
    @Autowired
    private VipCashBackService vipCashBackService;

    @Autowired
    private DcRemoteService dcRemoteService;

    @Autowired
    private AdVipUtil adVipUtil;
    /**
     * 会员中心
     *
     * @param cashVO
     * @return
     */
    @RequestMapping("/vipcenter")
    @SentinelResource(value = "vipcenter",
            blockHandlerClass ={LimitHandler.class},
            blockHandler = "vipcenterHandler")
    @SentinelAlarm(alertThreshold = 30d, limitThreshold = 50d)
    MessageModel getVipCenterCashInfo(@Valid CashVO cashVO){
        ResultBO resultBO=new ResultBO();
        try{
            log.info("vipCenter input param logJson:{}",JSONUtil.toJsonStr(cashVO));
            String payDeskSign=StringUtils.isBlank(cashVO.getPayDeskSign())?"membercenter_cash":cashVO.getPayDeskSign();
            payDeskSign= invokeHttpForUserInfo.invokerAllUserPayDesk(cashVO.getUid(),payDeskSign);
            log.info("vipCenter payDeskSign:{}", payDeskSign);
            carMonitor.cashCount.increment();
            // ui信息
           // VipInfo newUIVipInfo=new VipInfo();
            CompletableFuture<VipInfo> newUIVipInfoFuture= uiInfoService.getAndValidateUIAllInfo(cashVO.getUid());
            VipInfo newUIVipInfo= newUIVipInfoFuture.get(2, TimeUnit.SECONDS);
            log.info("vipCenter newUIVipInfo logJson:{}",JSONUtil.toJsonStr(newUIVipInfo));
            checkMemRegion(cashVO);
            // 挽留
            CompletableFuture<Object> retainPopupAsync =asyncCombineCashService.callRetainInfo(cashVO);
            // mcInfo
            CompletableFuture<Object> mcInfoAsync =asyncCombineCashService.callMcInfo(cashVO,newUIVipInfo);
            cashVO.setPayDeskSign(payDeskSign);
            CompletableFuture<Object> mainGearAsync= asyncCombineCashService.callCashInfo(cashVO,newUIVipInfo);
            CashVO tuiTanVO=new CashVO();
            BeanUtils.copyProperties(cashVO,tuiTanVO);
            tuiTanVO.setPayDeskSign("membercenter_cash_tuitan");
            // 收银台
            Object tuiTan=null;
            if(!StringUtils.equals("0",cashVO.getUid())){
                CompletableFuture<Object> tuiTanAsync= asyncCombineCashService.callCashInfo(tuiTanVO,newUIVipInfo);
                tuiTan =tuiTanAsync.get(2, TimeUnit.SECONDS);
            }
            CompletableFuture<Void> combinedFuture = CompletableFuture.allOf(retainPopupAsync, mcInfoAsync);
            combinedFuture.get(2, TimeUnit.SECONDS);
            Object retainPopup = retainPopupAsync.get();
            Object mcInfo = mcInfoAsync.get();
            Object mainGear =mainGearAsync.get(2, TimeUnit.SECONDS);
            resultBO.setInitPopup(asyncCombineCashService.initPopupInfo(mainGear, cashVO, newUIVipInfo));
            resultBO.setRetainPopup(retainPopup);
            resultBO.setMcInfo(mcInfo);
            resultBO.setMainGear(mainGear);
            resultBO.setTuiTanGear(tuiTan);
            checkEmpty(retainPopup, mcInfo, mainGear, tuiTan );
            log.info("vipCenter result logJson:{}",JSONUtil.toJsonStr(resultBO));
            return success(resultBO);
        }catch (Exception e){
            carMonitor.cashCountError.increment();
            log.error("getVipCenterCashInfo has error!",e);
        }
        return success(resultBO);
    }

    public void checkEmpty(Object retainPopup,Object mcInfo,Object mainGear,Object tuiTan ){
        if(retainPopup==null){
            log.error("getVipCenterCashInfo empty retainPopup empty!");
        }
        if(mcInfo==null){
            log.error("getVipCenterCashInfo empty mcInfo empty!");
        }
        if(mainGear==null) {
            log.error("getVipCenterCashInfo empty mainGear empty!");
        }
        if(tuiTan==null) {
            log.error("getVipCenterCashInfo empty tuiTan empty!");
        }
    }

    /**
     * 会员中心-切换tab
     *
     * @param cashVO
     * @return
     */
    @RequestMapping("/vipcenter/viptype")
    @SentinelResource(value = "viptype",
            blockHandlerClass ={LimitHandler.class},
            blockHandler = "viptypeHandler")
    @SentinelAlarm(alertThreshold = 20d, limitThreshold = 40d)
    MessageModel getVipCenterTabCash(@Valid CashVO cashVO){
        CashBO cashBO=null;
        try{
            //VipInfo newUIVipInfo=new VipInfo();
            log.info("getVipCenterTabCash cashVO={}", JSONUtil.toJsonStr(cashVO));
            String payDeskSign=StringUtils.isBlank(cashVO.getPayDeskSign())?"membercenter_cash":cashVO.getPayDeskSign();
            payDeskSign= invokeHttpForUserInfo.invokerAllUserPayDesk(cashVO.getUid(),payDeskSign);
            carMonitor.vipTypeGearCount.increment();
            CompletableFuture<VipInfo> newUIVipInfoFuture= uiInfoService.getAndValidateUIAllInfo(cashVO.getUid());
            VipInfo newUIVipInfo= newUIVipInfoFuture.get(2, TimeUnit.SECONDS);
            checkMemRegion(cashVO);
            log.info("getVipCenterTabCash newUIVipInfo={}", JSONUtil.toJsonStr(newUIVipInfo));
            cashVO.setPayDeskSign(payDeskSign);
            cashBO= cashService.getCashInfo(cashVO,newUIVipInfo);
            log.info("getVipCenterTabCash cashBO={}", JSONUtil.toJsonStr(cashBO));
            log.info("cash tab uid={}", cashVO.getUid());
            return success(cashBO);
        }catch (Exception e){
            carMonitor.vipTypeGearError.increment();
            log.error("getVipCenterTabCash has error!",e);
        }
        return success(cashBO);
    }

    /**
     * 拦截弹窗收银台
     *
     * @param cashVO 参数
     * @return
     */
    @RequestMapping("/interceptPopup")
    MessageModel interceptPopup(@Valid CashVO cashVO){
        InterceptResultBO resultBO=new InterceptResultBO();
        UserInfoBO userInfoBO=new UserInfoBO();
        CalculateContext calculateContext=new CalculateContext();
        VipInfo newUIVipInfo=getVipInfo(cashVO,"interceptPopup");
        String payDeskSign= invokeHttpForUserInfo.invokerAllUserPayDesk(cashVO.getUid(),"limitpop");
        log.info("interceptPopup payDeskSign={}", payDeskSign);
        try{
            log.info("interceptPopup cashVO={}", JSONUtil.toJsonStr(cashVO));
            carMonitor.interceptCount.increment();
            invokeHttpForUserInfo.invokeInterceptUserInfo(cashVO,userInfoBO,calculateContext,newUIVipInfo);
            log.info("interceptPopup newUIVipInfo={} userInfoBO={}", JSONUtil.toJsonStr(newUIVipInfo),JSONUtil.toJsonStr(userInfoBO));
            //特定fromsrc判断歌曲是否4.0
            dcRemoteService.svipSongJudge(cashVO.getFromsrc(),cashVO.getSongId(),cashVO.getSource(),calculateContext);
            Tuple2<Integer, Object> popStyle=limitPop.popStyle(cashVO.getUid(),cashVO.getDeviceId(),cashVO.getPlatform(),cashVO.getSource(),cashVO.getSongId(),cashVO.getPayDeskSign(),cashVO.getFromsrc(),newUIVipInfo,cashVO.getGlobalPaymentStatus(), cashVO.getApiv(), cashVO.getRecId());
            //Tuple2<Integer, Object> popStyle= Tuples.of(cashVO.getPopStyle(), 1);
            log.info("interceptPopup popStyle={}", popStyle.getT1());
//            if(VipConstant.popPayDeskSigns.contains(payDeskSign)){
//                invokeHttpForUserInfo.checkMultiTagStarUser(cashVO.getUid(),calculateContext);
//            }
            // 广告会员状态
            int adStatus= adVipUtil.checkAdvStatus(cashVO.getUid(),payDeskSign, cashVO.getSource(), cashVO.getPlatform(), cashVO.getDeviceId(), cashVO.getApiv(),cashVO.getApiversion(), userInfoBO,  calculateContext,cashVO.getGlobalPaymentStatus(),cashVO.getFromsrc());
            if(cashVO.getLowtest()==1){
                popStyle= Tuples.of(1, false);
            }
            if(calculateContext.isSvipSong()){
                popStyle= Tuples.of(0, false);
            }
            if(popStyle.getT1()==0){
                CalculateContext tuiTancalculateContext=new CalculateContext();
                UserInfoBO tuiTanUserInfoBO=new UserInfoBO();
                CashVO tuiTanVO=new CashVO();
                //BeanUtils.copyProperties(calculateContext,tuiTancalculateContext);
                copyIfNecessary(calculateContext,tuiTancalculateContext);
                BeanUtils.copyProperties(userInfoBO,tuiTanUserInfoBO);
                BeanUtils.copyProperties(cashVO,tuiTanVO);
                tuiTanVO.setPayDeskSign("limitpopTuitan");
                // mainGear 拦截弹窗
               // checkRegion(cashVO);
                cashVO.setPayDeskSign(payDeskSign);
                checkRegion(cashVO);
                CompletableFuture<Object> mainGearAsync= asyncCombineCashService.callInterceptCashInfo(cashVO,calculateContext,userInfoBO);
                // retainPopup
                CompletableFuture<Object> retainPopupAsync =asyncCombineCashService.callInterceptRetainInfo(cashVO);
                // tuiTanGear 拦截弹窗退弹
                CompletableFuture<Object> tuiTanAsync=CompletableFuture.completedFuture(null);
                if(!StringUtils.equals("0",cashVO.getUid())){
                    tuiTanAsync= asyncCombineCashService.callInterceptCashInfo(tuiTanVO,tuiTancalculateContext,tuiTanUserInfoBO);
                }
                CompletableFuture<Void> combinedFuture;
                CompletableFuture<Object> freeModelAsync=null;
                if(cashVO.getGlobalPaymentStatus()==0&&adStatus!=AdEnum.NO_FREE.getStatus()&&!(calculateContext.isSvipSong()&&FreeModelService.noFreeModel4List.contains(cashVO.getFromsrc()))){
                    // freeModelInfo
                    freeModelAsync =asyncCombineCashService.asyncFreeModelTask(cashVO,newUIVipInfo);
                    combinedFuture = CompletableFuture.allOf(retainPopupAsync,freeModelAsync,mainGearAsync,tuiTanAsync);
                }else{
                    combinedFuture = CompletableFuture.allOf(retainPopupAsync,mainGearAsync,tuiTanAsync);
                }
                String successText=limitPop.paySuccessText(cashVO.getFromsrc());


                combinedFuture.get(2, TimeUnit.SECONDS);
                Object retainPopup = retainPopupAsync.get();
                Object mainGear =mainGearAsync.get();
                Object tuiTanGear =tuiTanAsync.get();
                if(cashVO.getGlobalPaymentStatus()==0&&freeModelAsync!=null){
                    Object freeModel =freeModelAsync.get();
                    resultBO.setFreeModelInfo(freeModel);
                }
                Object title=null;
                if(calculateContext.isMultiUser()){
                    log.info("isMultiUser getMultiPlatformType={}",calculateContext.getMultiPlatformType());
                    if(StringUtils.isNotBlank(calculateContext.getMultiPlatformType())){
                        resultBO.setIsMultiPlatformUser(1);
                        TitleInfoResp titleResult = new TitleInfoResp();
                        titleResult.setTitle(calculateContext.getMultiTitle());
                        title=titleResult;
                    }else{
                        title=limitPop.getSongInfo(newUIVipInfo, cashVO.getUid(), cashVO.getDeviceId(), cashVO.getPlatform(), cashVO.getFromsrc(),cashVO.getSource(),
                                cashVO.getSongId(), cashVO.getOverseasQuality(), cashVO.getMusic_quality(), cashVO.getPop_id(), cashVO.getSongQuality(),
                                cashVO.getAuditionJumpSuper(), cashVO.getGlobalPaymentStatus());
                    }
                }else{
                     title=limitPop.getSongInfo(newUIVipInfo, cashVO.getUid(), cashVO.getDeviceId(), cashVO.getPlatform(), cashVO.getFromsrc(),cashVO.getSource(),
                            cashVO.getSongId(), cashVO.getOverseasQuality(), cashVO.getMusic_quality(), cashVO.getPop_id(), cashVO.getSongQuality(),
                            cashVO.getAuditionJumpSuper(), cashVO.getGlobalPaymentStatus());
                }
                resultBO.setMainGear(mainGear);
                resultBO.setTuiTanGear(tuiTanGear);
                resultBO.setRetainPopup(retainPopup);
                resultBO.setOrderSucessText(successText);
                if (vipCashBackService.showVipCashBackBanner(mainGear)) {
                    String limitPopCashSuccText = limitPop.paySuccessText("cashBackSuccText");
                    resultBO.setCashBackSuccText(limitPopCashSuccText);
                }
                resultBO.setTitleInfo(title);
                resultBO.setVipInfo(newUIVipInfo.getBasicUInfo());
                resultBO.setNowTime(System.currentTimeMillis());
                if(mainGear==null){
                    log.error("mainGear has error!");
                    carMonitor.interceptCashError.increment();
                }
            }
            else if(popStyle.getT1()==1){
                // personality
                Object personality =popStyle.getT2();
                resultBO.setPersonality(personality);
                // lowPriceGear
                CompletableFuture<Object> lowPriceGearAsync = asyncCombineCashService.asyncLowPriceThreadPoolTask(cashVO, calculateContext,userInfoBO);
                Object lowGear=lowPriceGearAsync.get(2, TimeUnit.SECONDS);
                resultBO.setLowPriceGear(lowGear);
            }
            else if(popStyle.getT1()==2){
                // newFreeModelInfo
                Object newFreeModelInfo=popStyle.getT2();
                resultBO.setNewFreeModelInfo(newFreeModelInfo);
                // lowPriceGear
                CompletableFuture<Object> lowPriceGearAsync = asyncCombineCashService.asyncLowPriceThreadPoolTask(cashVO, calculateContext,userInfoBO);
                Object lowGear=lowPriceGearAsync.get(2, TimeUnit.SECONDS);
                resultBO.setLowPriceGear(lowGear);
            } else if (popStyle.getT1() == 3) {
                Object popText = popStyle.getT2();
                resultBO.setPopText(popText);
                // lowPriceGear
                CompletableFuture<Object> lowPriceGearAsync = asyncCombineCashService.asyncLowPriceThreadPoolTask(cashVO, calculateContext, userInfoBO);
                Object lowGear = lowPriceGearAsync.get(2, TimeUnit.SECONDS);
                resultBO.setLowPriceGear(lowGear);
            }
            adVipUtil.checkValidAd(userInfoBO,cashVO,calculateContext,resultBO,popStyle.getT1());
            resultBO.setPopStyle(popStyle.getT1());
            afterHandler(resultBO,cashVO,calculateContext);
            return success(resultBO);
        }catch (Exception e){
            errorHandle(resultBO, cashVO,calculateContext,userInfoBO);
            log.error("interceptPopup has error!",e);
        }
        return success(resultBO);
    }

    /**
     * 召回弹窗展示多档位，逻辑和拦截弹窗3.0一致
     *
     * @param cashVO
     * @return
     */
    @GetMapping("/recallPop")
    public MessageModel recallPopGearInfo(@Valid CashVO cashVO) {
        log.info("recallPopGearInfo cashVO={}", JSONUtil.toJsonStr(cashVO));
        if (StringUtils.isNotBlank(cashVO.getUserId())) {
            cashVO.setUid(cashVO.getUserId());
        }
        if (StringUtils.isBlank(cashVO.getUid()) || Long.parseLong(cashVO.getUid()) <= 0) {
            return success(getDefaultMultipleGear(cashVO.getPlatform()));
        }
        RecallMultipleGearVO recallMultipleGearVO = new RecallMultipleGearVO();
        UserInfoBO userInfoBO = new UserInfoBO();
        CalculateContext calculateContext = new CalculateContext();
        try {
            cashVO.setGlobalPaymentStatus(1);
            VipInfo newUIVipInfo = getVipInfo(cashVO, "recallPopGearInfo");
            log.info("recallPopGearInfo cashVO={}", JSONUtil.toJsonStr(cashVO));
            carMonitor.recallPopUpCount.increment();
            invokeHttpForUserInfo.invokeInterceptUserInfo(cashVO, userInfoBO, calculateContext, newUIVipInfo);
            log.info("recallPopGearInfo newUIVipInfo={} userInfoBO={}", JSONUtil.toJsonStr(newUIVipInfo), JSONUtil.toJsonStr(userInfoBO));
            cashVO.setPayDeskSign("limitpop");
            CompletableFuture<Object> mainGearAsync = asyncCombineCashService.callInterceptCashInfo(cashVO, calculateContext, userInfoBO);
            Object mainGear = mainGearAsync.get(2, TimeUnit.SECONDS);

            JSONObject mainGearInfo = JSONObject.parseObject(JSONObject.toJSONString(mainGear));
            if (Objects.isNull(mainGearInfo) || Objects.isNull(mainGearInfo.getString("list"))) {
                return success(getDefaultMultipleGear(cashVO.getPlatform()));
            }
            JSONArray gearList = JSONObject.parseArray(mainGearInfo.getString("list"));
            if (CollectionUtils.isEmpty(gearList)) {
                return success(getDefaultMultipleGear(cashVO.getPlatform()));
            }

            List<Object> multipleGearList = gearList.stream().collect(Collectors.toList());
            RecallMultipleGearVO.MainGear mainGearVO = new RecallMultipleGearVO.MainGear();
            mainGearVO.setList(multipleGearList);
            mainGearVO.setShowType(mainGearInfo.getString("showType"));
            recallMultipleGearVO.setMainGear(mainGearVO);
            recallMultipleGearVO.convert(recallMultipleGearVO,recallJointGearSrcNacos);
            return success(recallMultipleGearVO);
        } catch (Exception e) {
            recalErrorHandle(recallMultipleGearVO, cashVO, calculateContext, userInfoBO);
            log.error("recallPopGearInfo has error!", e);
        }
        return success(recallMultipleGearVO);
    }

    /**
     * 单曲购买送超会档位
     * @param req
     * @return
     */
    @GetMapping(value = "/singleSongGear")
    public MessageModel getSingleSVIPGear(@Valid SingleSongReq req) {
        SongPriceGearInfoVO singleSongInfo = songGearService.getSingleSongAndGearInfo(req);
        return new MessageModel(singleSongInfo);
    }

    /**
     * 地区核查
     *
     * @param cashVO
     */
    public void checkRegion(CashVO cashVO){
        try{
            int onOff=vipConfConfigNacos.getVipConfBO().getAutoPayTypeON();
            if(onOff==2){
                String ip = IpUtil.getUserIp(request);
                Boolean isBeijing = ipLocationUtil.isFromBeijing(ip);
                if (isBeijing) {
                    cashVO.setIsOFFAutoPay(1);
                }
          }
        }catch (Exception e){
            log.error("checkRegion has error!",e);
        }
    }

    /**
     * 地区核查
     *
     * @param cashVO
     */
    public void checkMemRegion(CashVO cashVO){
        try{
            String ip = IpUtil.getUserIp(request);
            boolean isValidCity = ipLocationUtil.isValidCity(ip,cashVO);
            if (isValidCity) {
                cashVO.setIsOFFAutoPay(1);
            }
        }catch (Exception e){
            log.error("checkMemRegion has error!",e);
        }
    }

    public void copyIfNecessary(CalculateContext calculateContext ,CalculateContext tuiTanCalculateContext){
        tuiTanCalculateContext.setAutoPay(calculateContext.isAutoPay());
        tuiTanCalculateContext.setFirstLuxPay(calculateContext.isFirstLuxPay());
        tuiTanCalculateContext.setFirstLuxRenewPay(calculateContext.isFirstLuxRenewPay());
        tuiTanCalculateContext.setFirstSvipPay(calculateContext.isFirstSvipPay());
        tuiTanCalculateContext.setFirstSvipRenewalPay(calculateContext.isFirstSvipRenewalPay());
    }

    /**
     * 会员信息获取
     *
     * @param cashVO
     * @param method
     * @return
     */
    public VipInfo getVipInfo(CashVO cashVO,String method){
        try{
            CompletableFuture<VipInfo> newUIVipInfoFuture= uiInfoService.getAndValidateUIAllInfo(cashVO.getUid());
            return newUIVipInfoFuture.get(2, TimeUnit.SECONDS);
        }catch (Exception e){
            carMonitor.vipInfoError.increment();
            log.error("getVipInfo has error! method={}",method,e);
        }
        return null;
    }

    public void errorHandle(InterceptResultBO resultBO,CashVO cashVO,CalculateContext calculateContext,UserInfoBO userInfoBO){
       try{
           carMonitor.interceptCashError.increment();
           cashVO.setPayDeskSign("limitpop");
           CompletableFuture<Object> mainGearAsync= asyncCombineCashService.callInterceptCashInfo(cashVO,calculateContext,userInfoBO);
           resultBO.setMainGear(mainGearAsync.get(1, TimeUnit.SECONDS));
           resultBO.setPopStyle(0);
       }catch (Exception e){
           log.error("errorHandle has error!",e);
       }
    }

    @RequestMapping("/aliPayConsult")
    MessageModel aliPayConsult(String platform, String uid, String sid, String price){
        if (!platform.equals("ar") || StringUtils.isEmpty(uid) || StringUtils.isEmpty(sid) || Convert.toBigDecimal(price, new BigDecimal(0)).compareTo(new BigDecimal(0)) <= 0) {
            return success("参数错误");
        }

        return success(cashService.getAliPrePayInfo(platform, uid, sid, price));
    }

    public void recalErrorHandle(RecallMultipleGearVO recallMultipleGearVO, CashVO cashVO, CalculateContext calculateContext, UserInfoBO userInfoBO) {
        carMonitor.recallPopUpError.increment();
        cashVO.setPayDeskSign("limitpop");
        try {
            CompletableFuture<Object> mainGearAsync = asyncCombineCashService.callInterceptCashInfo(cashVO, calculateContext, userInfoBO);
            recallMultipleGearVO.setMainGear((RecallMultipleGearVO.MainGear) mainGearAsync.get(2, TimeUnit.SECONDS));
        } catch (Exception e) {
            log.error("recalErrorHandle has error!", e);
        }
    }

    public Object getDefaultMultipleGear(String platform) {
        String arDefaultGear = "{\"mainGear\":{\"showType\":1,\"data\":null,\"list\":[{\"id\":\"vip_7-1-0-12\",\"vipType\":\"vip_7\",\"vipTypeId\":12,\"vipTypeName\":\"豪华VIP\",\"month\":1,\"act\":\"OPEN_VIP\",\"lastTime\":null,\"price\":15,\"autoPayPrice\":null,\"src\":\"vip7ar1_15_1671977255\",\"orderType\":1,\"isFirstRenewal\":null,\"isIosDiscounts\":null,\"autoPay\":0,\"payTypeList\":[],\"filterId\":null,\"filterOrderRank\":null,\"gearOrderRank\":4,\"isNewUser\":false,\"expire\":null,\"gearId\":null,\"freeAdd\":null,\"addBuyList\":null,\"limitPriceAddPay\":0,\"btnText\":\"立即购买\",\"isSrcInCode\":\"\",\"autoPayBtnShow\":false,\"topTag\":\"\",\"bottomTag\":\"\",\"exitPopupDuration\":\"\",\"maxNum\":\"\",\"title\":\"豪华VIP\",\"subTitle\":\"月卡\",\"autoPayDesc\":\"\",\"isAddPayShow\":0,\"exitPopupTime\":\"\",\"descImg\":\"\",\"tip\":\"购买成功\",\"style\":\"\",\"exitPopupCountdown\":\"\",\"oPrice\":15}," +
                "{\"id\":\"vip_7-3-0-12\",\"vipType\":\"vip_7\",\"vipTypeId\":12,\"vipTypeName\":\"豪华VIP\",\"month\":3,\"act\":\"OPEN_VIP\",\"lastTime\":null,\"price\":45,\"autoPayPrice\":null,\"src\":\"vip7ar3_45_1671978192\",\"orderType\":1,\"isFirstRenewal\":null,\"isIosDiscounts\":null,\"autoPay\":0,\"payTypeList\":[],\"filterId\":null,\"filterOrderRank\":null,\"gearOrderRank\":3,\"isNewUser\":false,\"expire\":null,\"gearId\":null,\"freeAdd\":null,\"addBuyList\":null,\"limitPriceAddPay\":0,\"btnText\":\"立即购买\",\"isSrcInCode\":\"\",\"autoPayBtnShow\":false,\"topTag\":\"\",\"bottomTag\":\"\",\"exitPopupDuration\":\"\",\"maxNum\":\"\",\"title\":\"豪华VIP\",\"subTitle\":\"季卡\",\"autoPayDesc\":\"\",\"isAddPayShow\":0,\"exitPopupTime\":\"\",\"descImg\":\"\",\"tip\":\"购买成功\",\"style\":\"\",\"exitPopupCountdown\":\"\",\"oPrice\":45}," +
                "{\"id\":\"vip_7-12-0-12\",\"vipType\":\"vip_7\",\"vipTypeId\":12,\"vipTypeName\":\"豪华VIP\",\"month\":12,\"act\":\"OPEN_VIP\",\"lastTime\":null,\"price\":180,\"autoPayPrice\":null,\"src\":\"vip7ar12_180_1671978341\",\"orderType\":1,\"isFirstRenewal\":null,\"isIosDiscounts\":null,\"autoPay\":0,\"payTypeList\":[],\"filterId\":null,\"filterOrderRank\":null,\"gearOrderRank\":2,\"isNewUser\":false,\"expire\":null,\"gearId\":null,\"freeAdd\":null,\"addBuyList\":null,\"limitPriceAddPay\":0,\"btnText\":\"立即购买\",\"isSrcInCode\":\"\",\"autoPayBtnShow\":false,\"topTag\":\"\",\"bottomTag\":\"\",\"exitPopupDuration\":\"\",\"maxNum\":\"\",\"title\":\"豪华VIP\",\"subTitle\":\"年卡\",\"autoPayDesc\":\"\",\"isAddPayShow\":0,\"exitPopupTime\":\"\",\"descImg\":\"\",\"tip\":\"购买成功\",\"style\":\"\",\"exitPopupCountdown\":\"\",\"oPrice\":180}]}}";

        String iosDefaultGear = "{\"mainGear\":{\"showType\":1,\"data\":null,\"list\":[{\"id\":\"vip_7-1-0-12\",\"vipType\":\"vip_7\",\"vipTypeId\":12,\"vipTypeName\":\"豪华VIP\",\"month\":1,\"act\":\"OPEN_VIP\",\"lastTime\":null,\"price\":18,\"autoPayPrice\":null,\"src\":\"vip7ios1_18_1671977301\",\"orderType\":1,\"pid\":\"tj_kuwo_supervip_18\",\"isFirstRenewal\":null,\"isIosDiscounts\":null,\"autoPay\":0,\"payTypeList\":[],\"filterId\":null,\"filterOrderRank\":null,\"gearOrderRank\":4,\"isNewUser\":false,\"expire\":null,\"gearId\":null,\"freeAdd\":null,\"addBuyList\":null,\"limitPriceAddPay\":0,\"btnText\":\"立即购买\",\"isSrcInCode\":\"\",\"autoPayBtnShow\":false,\"topTag\":\"\",\"bottomTag\":\"\",\"exitPopupDuration\":\"\",\"maxNum\":\"\",\"title\":\"豪华VIP\",\"subTitle\":\"月卡\",\"autoPayDesc\":\"\",\"isAddPayShow\":0,\"exitPopupTime\":\"\",\"descImg\":\"\",\"tip\":\"购买成功\",\"style\":\"\",\"exitPopupCountdown\":\"\",\"oPrice\":18}," +
                "{\"id\":\"vip_7-3-0-12\",\"vipType\":\"vip_7\",\"vipTypeId\":12,\"vipTypeName\":\"豪华VIP\",\"month\":3,\"act\":\"OPEN_VIP\",\"lastTime\":null,\"price\":45,\"autoPayPrice\":null,\"src\":\"vip7ios3_45_1671978266\",\"orderType\":1,\"pid\":\"tj_kuwo_supervip_45\",\"isFirstRenewal\":null,\"isIosDiscounts\":null,\"autoPay\":0,\"payTypeList\":[],\"filterId\":null,\"filterOrderRank\":null,\"gearOrderRank\":3,\"isNewUser\":false,\"expire\":null,\"gearId\":null,\"freeAdd\":null,\"addBuyList\":null,\"limitPriceAddPay\":0,\"btnText\":\"立即购买\",\"isSrcInCode\":\"\",\"autoPayBtnShow\":false,\"topTag\":\"\",\"bottomTag\":\"\",\"exitPopupDuration\":\"\",\"maxNum\":\"\",\"title\":\"豪华VIP\",\"subTitle\":\"季卡\",\"autoPayDesc\":\"\",\"isAddPayShow\":0,\"exitPopupTime\":\"\",\"descImg\":\"\",\"tip\":\"购买成功\",\"style\":\"\",\"exitPopupCountdown\":\"\",\"oPrice\":54}," +
                "{\"id\":\"vip_7-12-0-12\",\"vipType\":\"vip_7\",\"vipTypeId\":12,\"vipTypeName\":\"豪华VIP\",\"month\":12,\"act\":\"OPEN_VIP\",\"lastTime\":null,\"price\":168,\"autoPayPrice\":null,\"src\":\"vip7ios12_168_1671978432\",\"orderType\":1,\"pid\":\"tj_kuwo_supervip_148\",\"isFirstRenewal\":null,\"isIosDiscounts\":null,\"autoPay\":0,\"payTypeList\":[],\"filterId\":null,\"filterOrderRank\":null,\"gearOrderRank\":2,\"isNewUser\":false,\"expire\":null,\"gearId\":null,\"freeAdd\":null,\"addBuyList\":null,\"limitPriceAddPay\":0,\"btnText\":\"立即购买\",\"isSrcInCode\":\"\",\"autoPayBtnShow\":false,\"topTag\":\"\",\"bottomTag\":\"\",\"exitPopupDuration\":\"\",\"maxNum\":\"\",\"title\":\"豪华VIP\",\"subTitle\":\"年卡\",\"autoPayDesc\":\"\",\"isAddPayShow\":0,\"exitPopupTime\":\"\",\"descImg\":\"\",\"tip\":\"购买成功\",\"style\":\"\",\"exitPopupCountdown\":\"\",\"oPrice\":216}]}}";
        if (Objects.equals(platform, "ar")) {
            return JSONObject.parseObject(arDefaultGear);
        }
        if (Objects.equals(platform, "ios")) {
            return JSONObject.parseObject(iosDefaultGear);
        }
        return null;
    }

    /**
     * 星画 人群判断
     *
     * @param userId
     * @return
     */
    @RequestMapping("/checkTagStar")
    public MessageModel checkTagStar(String userId,Integer type){
        if(StringUtils.isBlank(userId)){
            return new MessageModel(false);
        }
        Object checkTagStart=cashService.checkTagStar(userId,type);
        return new MessageModel(checkTagStart);
    }

    private void afterHandler(InterceptResultBO resultBO,CashVO cashVO,CalculateContext calculateContext){
        VipConfBO vipConfBO = vipConfConfigNacos.getVipConfBO();
        if(Objects.isNull(vipConfBO)){
            return;
        }
        if (!calculateContext.isSvipSong()&&Objects.equals(cashVO.getFromsrc(),"box_noPic_download") && Objects.equals(cashVO.getApiv(), 2)) {

            TitleInfoResp titleInfoResp = new TitleInfoResp();
            titleInfoResp.setTitle(vipConfBO.getSongDownloadGearTitle());
            resultBO.setTitleInfo(titleInfoResp);
            resultBO.setSongDownloadGearPicture(vipConfBO.getSongDownloadGearPicture());
            resultBO.setPopStyle(4);
        }

        if(calculateContext.isSvipSong() && Objects.equals(cashVO.getFromsrc(),"box_noPic_download")){
            TitleInfoResp titleInfoResp = new TitleInfoResp();
            titleInfoResp.setTitle(vipConfBO.getSvip4DownloadGearTitle());
            resultBO.setTitleInfo(titleInfoResp);
        }

        if(calculateContext.isSvipSong() && (Objects.equals(cashVO.getFromsrc(),"auditionPopUpH5")
                || Objects.equals(cashVO.getFromsrc(),"BarClick")
                || Objects.equals(cashVO.getFromsrc(),"PageChange"))){
            TitleInfoResp titleInfoResp = new TitleInfoResp();
            titleInfoResp.setTitle(vipConfBO.getSvip4PlayGearTitle());
            resultBO.setTitleInfo(titleInfoResp);
        }
    }
}
