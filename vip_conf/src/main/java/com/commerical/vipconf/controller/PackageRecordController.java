package com.commerical.vipconf.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.commerical.vipconf.annotation.CheckUser;
import com.commerical.vipconf.config.exception.ServiceException;
import com.commerical.vipconf.domain.PackageRecord;
import com.commerical.vipconf.domain.vo.PackageRecordQueryVO;
import com.commerical.vipconf.domain.vo.PackageRecordVO;
import com.commerical.vipconf.service.PackageRecordService;
import com.kuwo.commercialization.common.message.MessageModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-09
 */
@RestController
@RequestMapping("/packageRecord")
@Slf4j
public class PackageRecordController extends BaseController{

    @Autowired
    private PackageRecordService packageRecordService;

    /**
     * 添加 key value
     *
     * @param packageRecordVO
     * @return
     * @throws ServiceException
     */
    @CheckUser
    @RequestMapping("/addPackageRecord")
    public MessageModel addPackageRecord(@Valid PackageRecordVO packageRecordVO) throws ServiceException {
        PackageRecord packageRecord= packageRecordService.addPackageRecord(packageRecordVO);
        return success(packageRecord.getId());
    }

    /**
     * 添加 key value
     *
     * @param packageRecordQueryVO
     * @return
     * @throws ServiceException
     */
    @RequestMapping("/getPackageRecord")
    public MessageModel getPackageRecord(@Valid PackageRecordQueryVO packageRecordQueryVO) throws ServiceException {
        IPage<PackageRecord> packageRecord= packageRecordService.getPackageRecord(packageRecordQueryVO);
        return success(packageRecord);
    }

    /**
     * 删除PackageRecord
     *
     * @param
     * @return
     */
    @CheckUser
    @RequestMapping("/deletePackageRecordById")
    public MessageModel deletePackageRecordById(Integer id){
        if(ObjectUtils.isNull(id)){
            log.error("deletePackageRecordById  param  has empty value!,id={}",id);
            return failed();
        }
        packageRecordService.deletePackageRecordById(id);
        return success();
    }

}

