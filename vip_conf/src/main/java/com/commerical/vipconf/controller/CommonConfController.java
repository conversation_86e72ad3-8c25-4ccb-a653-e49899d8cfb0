package com.commerical.vipconf.controller;

import cn.hutool.core.util.StrUtil;
import com.commerical.vipconf.config.exception.ServiceException;
import com.commerical.vipconf.domain.CommonConf;
import com.commerical.vipconf.domain.res.CommonConfRes;
import com.commerical.vipconf.domain.vo.CommonConfQueryVO;
import com.commerical.vipconf.domain.vo.PriceGearVO;
import com.commerical.vipconf.service.CommonConfService;
import com.commerical.vipconf.service.FilterService;
import com.commerical.vipconf.service.cashstragety.IOSApprovedVersionService;
import com.commerical.vipconf.service.netearn.VipCashBackService;
import com.commerical.vipconf.util.PageInfo;
import com.commerical.vipconf.util.TagStarUtils;
import com.kuwo.commercialization.common.message.MessageModel;
import com.kuwo.commercialization.common.message.SystemCodeErrorConstant;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Objects;

@RestController
@RequestMapping("commonConf")
public class CommonConfController {
    private Logger logger = LoggerFactory.getLogger(CommonConfController.class);
    @Autowired
    private CommonConfService commonConfService;

    @Autowired
    private FilterService filterService;

    @Autowired
    private IOSApprovedVersionService iosApprovedVersionService;

    @Autowired
    private VipCashBackService vipCashBackService;

    @RequestMapping(value = "addOrUpdateBack")
    public MessageModel addOrUpdateBack(CommonConf commonConf, String sign) {
        //sign错误，提示参数异常。
        if (StringUtils.isEmpty(sign) || !"NQN7LDZVN0A4DSC".equals(sign)) {
            return new MessageModel(SystemCodeErrorConstant.PARAM_CHECK_ERROR);
        }
        //参数校验
        if (commonConf.getConfType() == null
                || StringUtils.isEmpty(commonConf.getPlatform())
                || StringUtils.isEmpty(commonConf.getConfText())
                || (!"ar".equals(commonConf.getPlatform()) && !"ip".equals(commonConf.getPlatform()))
                || commonConf.getJumpType() == null
                || StringUtils.isEmpty(commonConf.getJumpUrl())
                || commonConf.getStartTime() == null
                || commonConf.getEndTime() == null
                || commonConf.getOperator() == null
        ) {
            return new MessageModel(SystemCodeErrorConstant.PARAM_CHECK_ERROR);
        }
        return commonConfService.saveOrUpdateBack(commonConf);
    }

    @RequestMapping(value = "deleteBack")
    public MessageModel deleteBack(Long id, String sign, String operator) {
        //sign错误，提示参数异常。
        if (StringUtils.isEmpty(sign) || !"TZJGRBOLFYCTZUJ".equals(sign)) {
            return new MessageModel(SystemCodeErrorConstant.PARAM_CHECK_ERROR);
        }
        if (id == null || StringUtils.isEmpty(operator)) {
            return new MessageModel(SystemCodeErrorConstant.PARAM_CHECK_ERROR);
        }
        return commonConfService.deleteById(id, operator);
    }

    @RequestMapping("getByIdBack")
    public MessageModel getByIdBack(Long id) {
        if (id == null) {
            return new MessageModel(SystemCodeErrorConstant.PARAM_CHECK_ERROR);
        }
        MessageModel messageModel = new MessageModel();
        try {
            CommonConf commonConf = commonConfService.getConfigById(id);
            messageModel.setData(commonConf);
            return messageModel;
        } catch (Exception e) {
            // e.printStackTrace();
            logger.error("查询ID支付结果页配置异常", e);
        }
        return null;
    }

    @RequestMapping("getListBack")
    public MessageModel getListBack(CommonConfQueryVO query) {
        MessageModel messageModel = new MessageModel();
        if (query == null
                || query.getConfTypeStr() == null
                || query.getPage() == null
                || query.getPageSize() == null
        ) {
            return new MessageModel(SystemCodeErrorConstant.PARAM_CHECK_ERROR);
        }
        try {
            logger.info("query is {}", query);
            PageInfo<CommonConf> data = commonConfService.getListBack(query);
            messageModel.setData(data);
            return messageModel;
        } catch (Exception e) {
            // e.printStackTrace();
            logger.error("查询支付结果页异常", e);
        }
        return null;
    }

    @RequestMapping("getButtonConf")
    public MessageModel getButtonConf(CommonConfQueryVO reqCommonConfigVo) {
        // modelType，不传或者传0，返回标准模式【之前的】；传1，返回全局模式的【新的配置】。
        MessageModel messageModel = new MessageModel();
        String platform = reqCommonConfigVo.getPlatform();
        if (StringUtils.isEmpty(platform) || (!"ar".equals(platform) && !"ip".equals(platform))) {
            return new MessageModel(SystemCodeErrorConstant.PARAM_CHECK_ERROR);
        }
        // 如果modelType没传，默认modelType=0
        if (Objects.isNull(reqCommonConfigVo.getModelType())) {
            reqCommonConfigVo.setModelType(0);
        }
        try {
            CommonConfRes data = commonConfService.getButtonConf(reqCommonConfigVo);
            // 添加N元返现按钮
            if (Objects.nonNull(data)) {
                String loginUid = reqCommonConfigVo.getLoginUid();
                if (StrUtil.isBlankIfStr(loginUid)) {
                    loginUid = reqCommonConfigVo.getUserId();
                }
                Boolean inActivity = vipCashBackService.inActivity(loginUid, platform);
                Boolean hitShowBannerAbt = vipCashBackService.hitShowBannerAbt(loginUid, platform);
                if (BooleanUtils.isTrue(inActivity) && BooleanUtils.isTrue(hitShowBannerAbt)) {
                    data.setCashBackBtn(1);
                }
                if (BooleanUtils.isNotTrue(inActivity)) {
                    data.setCashBackBtn(0);
                }
            }
            // ios 审核版校验
            if (iosApprovedVersionService.isApproved(reqCommonConfigVo.getSource(), platform) && Objects.nonNull(data)) {
                data.setJumpType(2);
                data.setConfText("前往会员精选");
                data.setJumpUrl("kwapp://rootTab/member");
            }
            messageModel.setData(data);
            return messageModel;
        } catch (Exception e) {
            // e.printStackTrace();

            logger.error("获取支付结果页按钮异常", e);
        }
        return null;
    }

    /**
     * 获取支付结果配置
     *
     * @param
     * @return
     */
    @RequestMapping("/getPayConf")
    public MessageModel getPayConf(HttpServletRequest request, @Valid PriceGearVO priceGearVO) throws ServiceException {
        MessageModel messageModel = new MessageModel();
        CommonConfRes commonConfRes = filterService.getPriceGearInfoSim(request, priceGearVO);
        messageModel.setData(commonConfRes);
        return messageModel;
    }

    @GetMapping(value = "/logStatistic")
    public MessageModel getLogStatistic(CommonConfQueryVO reqCommonConfigVo) {
        return commonConfService.getActivityReportCount(reqCommonConfigVo);
    }

    @GetMapping(value = "/ruleCheck/{starId}/{uid}")
    public MessageModel getRuleCheck(@PathVariable("starId") String starId, @PathVariable("uid") String uid) {
        if (StringUtils.isBlank(starId) || StringUtils.isBlank(uid)) {
            return new MessageModel(SystemCodeErrorConstant.PARAM_CHECK_ERROR);
        }
        return new MessageModel(TagStarUtils.getMultiTagStar(uid, starId.split(",")));
    }


}
