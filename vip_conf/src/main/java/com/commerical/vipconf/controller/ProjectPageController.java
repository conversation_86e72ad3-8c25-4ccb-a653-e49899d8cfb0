package com.commerical.vipconf.controller;


import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.commerical.vipconf.domain.MyOADTO;
import com.commerical.vipconf.domain.ProjectPage;
import com.commerical.vipconf.domain.PublishPageModule;
import com.commerical.vipconf.domain.PublishProjectPage;
import com.commerical.vipconf.domain.bo.ProjectPageBO;
import com.commerical.vipconf.domain.bo.PublishProjectPageBO;
import com.commerical.vipconf.domain.vo.ProjectPageQueryVO;
import com.commerical.vipconf.domain.vo.ProjectPageVO;
import com.commerical.vipconf.service.ProjectPageService;
import com.commerical.vipconf.service.PublishProjectPageService;
import com.google.common.collect.Maps;
import com.commerical.vipconf.service.PublishProjectPageService;
import com.kuwo.commercialization.common.message.MessageModel;
import com.kuwo.commercialization.common.resp.BasicResponse;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 项目页面 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-01
 */
@RestController
@RequestMapping("/projectPage")
public class ProjectPageController  extends BaseController {

    private final static Logger logger = LoggerFactory.getLogger(ProjectPageController.class);

    @Autowired
    private ProjectPageService projectPageService;

    @Autowired
    private PublishProjectPageService publishProjectPageService;
    
    @Value("${spring.profiles.active}")
    private String active;
    
    /**
     * 添加项目页面
     *
     * @param projectPageVO
     * @return
     */
    @RequestMapping("/addOrUpdateProjectPage")
    public MessageModel addOrUpdateProjectPage(@Valid ProjectPageVO projectPageVO){
        ProjectPage projectPage= projectPageService.addOrUpdateProjectPage(projectPageVO);
        return success(projectPage.getCode());
    }

    /**
     * 删除项目页面
     *
     * @param
     * @return
     */
    @RequestMapping("/deleteProjectPageById")
    public MessageModel deleteProjectPageById(Integer id,Boolean isTrue,Integer isDelete){
        if(ObjectUtils.isNull(id)||ObjectUtils.isNull(isTrue)||ObjectUtils.isNull(isDelete)){
            logger.error("deleteProjectPageById param  has empty value!,id={}",id);
            return failed();
        }
        projectPageService.deleteProjectPageById(id,isTrue,isDelete);
        return success();
    }

    /**
     * 项目页面列表查询
     *
     * @param
     * @return
     */
    @RequestMapping("/getProjectPageList")
    public MessageModel getProjectPageList(@Valid ProjectPageQueryVO projectPageQueryVO){
        IPage<ProjectPage> ProjectPageVOList= projectPageService.getProjectPageList(projectPageQueryVO);
        return success(ProjectPageVOList);
    }

    /**
     * 项目页面详情查询
     *
     * @param
     * @return
     */
    @RequestMapping("/getProjectPageInfoById")
    public MessageModel getProjectPageInfoById(Integer id){
        ProjectPage ProjectPage= projectPageService.getProjectPageInfoById(id);
        return success(ProjectPage);
    }

    /**
     * 项目页面模块
     *
     * @param
     * @return
     */
    @RequestMapping("/getPageContent")
    public MessageModel getPageContent(String projectPageCode){
        if(StringUtils.isBlank(projectPageCode)){
            return failed();
        }
         ProjectPageBO projectPageBO= projectPageService.getPageContent(projectPageCode);
        return success(projectPageBO);
    }

    /**
     * 项目页面模块
     *
     * @param
     * @return
     */
    @RequestMapping("/getPubPageContent")
    public MessageModel getPubPageContent(String projectPageCode){
        if(StringUtils.isBlank(projectPageCode)){
            return failed();
        }
        PublishProjectPageBO publishProjectPageBO = publishProjectPageService.getPageContent(projectPageCode);
        return success(publishProjectPageBO);
    }

    /**
     * 项目页面发布
     *
     * @param
     * @return
     */
    @RequestMapping("/publicPage")
    public MessageModel publicPage(String projectPageCode){
        if(StringUtils.isBlank(projectPageCode)){
            return failed();
        }
        boolean isSuccess=projectPageService.publicPage(projectPageCode);
        return success(isSuccess);
    }

    /**
     * 项目页面发布
     *
     * @param
     * @return
     */
    @RequestMapping("/publicPageByMyoa")
    public BasicResponse publicPageByMyoa(@RequestBody MyOADTO myOADTO){
        logger.info("publicPageByMyoa param: {}", JSONObject.toJSONString(myOADTO));
        BasicResponse response = new BasicResponse();
        if(myOADTO == null || myOADTO.getExtData().isEmpty()){
            response.setCode(1000);
            response.setMsg("参数错误");
            return response;
        }
        
        String projectPageCode = null;
        String cname = null;
        String ename = null;
        List<String> processInstIdList = null;
        for (MyOADTO.ExtDataDTO extDatum : myOADTO.getExtData()) {
            if (CollectionUtils.isEmpty(extDatum.getValue()) && StringUtils.isBlank(extDatum.getValue().get(0))) {
                continue;
            }
            if (MyOADTO.projectPageCodeKey.equals(extDatum.getKey())) {
                projectPageCode = extDatum.getValue().get(0);
            }
            if (MyOADTO.submitCnameKey.equals(extDatum.getKey())) {
                cname = extDatum.getValue().get(0);
            }
            if (MyOADTO.submitEnameKey.equals(extDatum.getKey())) {
                ename = extDatum.getValue().get(0);
            }
            if (MyOADTO.allProcessInstIdKey.equals(extDatum.getKey())) {
                processInstIdList = extDatum.getValue();
            }
        }

        if (!closeWorkitem(myOADTO, processInstIdList)) {
            logger.info("关闭审批单失败, param: {}", JSONObject.toJSONString(myOADTO));
            response.setCode(1000);
            response.setMsg("关闭审批单失败");
            return response;
        }
        
        if (!"approval".equals(myOADTO.getSubmitAction())) {
            logger.info("审批未通过, param: {}", JSONObject.toJSONString(myOADTO));
            response.setCode(0);
            response.setMsg("ok");
            return response;
        }
        boolean isSuccess=projectPageService.publicPage(projectPageCode);

        sendNigtify(myOADTO, projectPageCode, ename, cname);

        response.setCode(0);
        response.setMsg("ok");
        return response;
    }

    /**
     * 根据channel获取数据
     *
     */
    @RequestMapping("/getProjectPageByChannel")
    public MessageModel getProjectPageByChannel(String channel){
        if(StringUtils.isBlank(channel)){
            return failed();
        }
        ProjectPage projectPage=projectPageService.getProjectPageByChannel(channel);
        return success(projectPage);
    }

    /**
     * 根据channel获取数据
     *
     */
    @RequestMapping("/getPubProjectPageByChannel")
    public MessageModel getPubProjectPageByChannel(String channel){
        if(StringUtils.isBlank(channel)){
            return failed();
        }
        PublishProjectPage projectPage=publishProjectPageService.getProjectPageByChannel(channel);
        return success(projectPage);
    }
    
    private boolean closeWorkitem(MyOADTO myOADTO, List<String> processInstIdList) {
        if (CollectionUtils.isEmpty(processInstIdList)) {
            return true;
        }
        for (String processInstId : processInstIdList) {
            try {
                if (StringUtils.isBlank(processInstId) || processInstId.equals(myOADTO.getProcessInstId())) {
                    continue;
                }
                //文档见：https://tpp.tmeoa.com/systems/tpp-docs/apps/docs/preview/609d146cdae4d935f0612a15  第5.3条
                long now = System.currentTimeMillis()/1000;
                String url = "http://api.myoa.tmeoa.com/workitem/discard";
                String key = active.equals("prod")?"6204ac80d8503fe4f01e56bb2befaf35":"dd6d78c7dc6c6001122fbb247e9d553c";
                String secret = active.equals("prod")?"53774589d0c5f4716d62c9736ac71947":"17030cb7897bb9a2b82cebdc62722daa";
                String source = now + secret + now;
                String signature = DigestUtils.sha1Hex(source.getBytes());
                // 构建请求头
                Map<String, String> headers = Maps.newHashMap();
                headers.put("Content-Type", "application/json");
                headers.put("X-APPKEY", key);
                headers.put("X-SIGNATURE", signature);
                headers.put("X-TIMESTAMP", String.valueOf(now));

                JSONObject param = new JSONObject();
                param.put("category", myOADTO.getCategory());
                param.put("process_key", myOADTO.getProcessKey());
                param.put("process_inst_id", processInstId);
                param.put("submit_opinion", String.format("%s(%s) 已审批: %s", myOADTO.getHandler(), myOADTO.getHandlerName(), myOADTO.getSubmitActionName()));
//                param.put("activity", myOADTO.getActivity());
//                param.put("handler", myOADTO.getHandler());

                HttpRequest request = HttpRequest.post(url);
                request.addHeaders(headers);
                request.body(param.toJSONString());
                String response = request.execute().body();
                logger.info("call Myoa to close workitem, response: {}", response);
            } catch (Exception e) {
                logger.error("call Myoa to close workitem fail", e);
            }
        }
        return true;
    }
    
    private void sendNigtify(MyOADTO myOADTO, String projectPageCode, String ename, String cname) {
        String url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=bb370b96-ac10-45c1-b498-311a2472aa58";
        String template = "" +
                "{\n" +
                "    \"msgtype\": \"markdown\",\n" +
                "    \"markdown\": {\n" +
                "        \"content\": \"# 配置发布审批通知-【%s】\n" +
                "         >活动code:<font color=\\\"comment\\\">%s</font>\n" +
                "         >提交人:<font color=\\\"comment\\\">%s(%s)</font>\n" +
                "         >提交时间:<font color=\\\"comment\\\">%s</font>\n" +
                "         >审批人:<font color=\\\"comment\\\">%s(%s)</font>\n" +
                "         >审批状态:<font color=\\\"%s\\\">%s(%s)</font>\n" +
                "         >审批意见:<font color=\\\"comment\\\">%s</font>\"\n" +
                "    }\n" +
                "}";
        String serverModel = active.equals("prod")?"正式环境":"测试环境";
        String color = "approval".equals(myOADTO.getSubmitAction())?"info":"warning";
        String formatDate = DateFormatUtils.format(new Date(myOADTO.getSubmitTime()*1000L), "yyyy-MM-dd HH:mm:ss");
        String approveMsg = String.format(template, serverModel, projectPageCode, ename, cname, formatDate, myOADTO.getHandler(), myOADTO.getHandlerName(), color, myOADTO.getSubmitAction(), myOADTO.getSubmitActionName(), myOADTO.getSubmitOpinion());
        HttpRequest request = HttpRequest.post(url);
        request.body(approveMsg);
        String response = request.execute().body();
        logger.info("call wxwork rebot, response: {}", response);
    }

}

