package com.commerical.vipconf.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.commerical.vipconf.annotation.CheckUser;
import com.commerical.vipconf.domain.GearFilter;
import com.commerical.vipconf.domain.vo.GearFilterQueryVO;
import com.commerical.vipconf.domain.vo.GearFilterVO;
import com.commerical.vipconf.service.GearFilterService;
import com.kuwo.commercialization.common.message.MessageModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-19
 */
@RestController
@RequestMapping("/gearFilter")
public class GearFilterController extends BaseController {

    @Autowired
    private GearFilterService gearFilterService;

    /**
     * 档位列表查询
     *
     * @param
     * @return
     */
    @RequestMapping("/getGearFilterList")
    public MessageModel getGearFilterList(@Valid GearFilterQueryVO gearFilterQueryVO){
        IPage<GearFilter> GearVOList= gearFilterService.getGearFilterList(gearFilterQueryVO);
        return success(GearVOList);
    }
    @CheckUser
    @RequestMapping("/addGearFilter")
    public MessageModel addGearFilter(@Valid GearFilterVO gearFilterVO){
        gearFilterService.addGearFilter(gearFilterVO);
        return success();
    }
    @CheckUser
    @RequestMapping("/updateGearFilter")
    public MessageModel updateGearFilter(@Valid GearFilterVO gearFilterVO){
        boolean isSuccess= gearFilterService.updateGearFilter(gearFilterVO);
        if(isSuccess){
            return success();
        }
        return failed();
    }
    @CheckUser
    @RequestMapping("/deleteGearFilterById")
    public MessageModel deleteGearFilterById(@Valid GearFilterVO gearFilterVO){
        int i= gearFilterService.deleteGearFilterById(gearFilterVO);
        if(i>0){
            return success();
        }
        return failed();
    }
    @RequestMapping("/getGearFilterById")
    public MessageModel getGearFilterById(@Valid GearFilterVO gearFilterVO){
        GearFilter filter= gearFilterService.getGearFilterById(gearFilterVO);
        return success(filter);
    }
}

