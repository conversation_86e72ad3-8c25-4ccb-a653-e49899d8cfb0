package com.commerical.vipconf.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.commerical.vipconf.annotation.CheckUser;
import com.commerical.vipconf.config.exception.ServiceException;
import com.commerical.vipconf.domain.Filter;
import com.commerical.vipconf.domain.Gear;
import com.commerical.vipconf.domain.vo.GearQueryVO;
import com.commerical.vipconf.domain.vo.GearVO;
import com.commerical.vipconf.service.GearService;
import com.kuwo.commercialization.common.message.MessageModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 *  档位
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-26
 */
@RestController
@RequestMapping("/gear")
public class GearController extends BaseController{

    private final static Logger logger = LoggerFactory.getLogger(GearController.class);

    @Autowired
    private GearService gearService;

    /**
     * 添加挡位信息
     *
     * @param gearVO
     * @return
     */
    @CheckUser
    @RequestMapping("/addOrUpdateGear")
    public MessageModel addOrUpdateGear(@Valid GearVO gearVO){
        Gear gear=gearService.addOrUpdateGear(gearVO);;
        return success(gear.getId());
    }

    /**
     * 删除档位
     *
     * @param
     * @return
     */
    @CheckUser
    @RequestMapping("/deleteGearById")
    public MessageModel deleteGearById(Integer id,Boolean isTrue,Integer isDelete){
        if(ObjectUtils.isNull(id)||ObjectUtils.isNull(isTrue)||ObjectUtils.isNull(isDelete)){
            logger.error("deleteGearById param  has empty value!,id={}",id);
            return failed();
        }
        gearService.deleteGearById( id,isTrue,isDelete);
        return success();
    }

    /**
     * 档位列表查询
     *
     * @param
     * @return
     */
    @RequestMapping("/getGearList")
    public MessageModel getGearList(@Valid GearQueryVO gearQueryVO){
        IPage<Gear> GearVOList= gearService.getGearList(gearQueryVO);
        return success(GearVOList);
    }

    /**
     * 档位详情查询
     *
     * @param
     * @return
     */
    @RequestMapping("/getGearInfoById")
    public MessageModel getGearInfoById(Integer id){
        Gear gear=gearService.getGearInfoById(id);
        return success(gear);
    }

    @RequestMapping("/getAllGear")
    public MessageModel getAllGear(String id) throws ServiceException {
        if(!id.equals("6bc5a449-8e1d-43d9-a019-530d26ccdc9e")){
            return new MessageModel();
        }
        List<Gear> gearList= gearService.getAllGear();
        return success(gearList);
    }

}

