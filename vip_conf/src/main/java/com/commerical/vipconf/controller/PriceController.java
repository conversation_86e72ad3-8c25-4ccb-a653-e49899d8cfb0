package com.commerical.vipconf.controller;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.commerical.vipconf.config.exception.ServiceException;
import com.commerical.vipconf.config.sentinel.PriceExceptionHandler;

import com.commerical.vipconf.domain.req.SingleSVIPInfoReq;
import com.commerical.vipconf.domain.bo.GearBO;
import com.commerical.vipconf.domain.vo.PriceGearVO;
import com.commerical.vipconf.domain.vo.SinglePriceGearVO;
import com.commerical.vipconf.domain.vo.SingleSVIPInfoVO;
import com.commerical.vipconf.nacos.VipConfConfigNacos;
import com.commerical.vipconf.service.GearService;
import com.commerical.vipconf.service.NewSinglePriceService;
import com.commerical.vipconf.service.SinglePriceService;
import com.commerical.vipconf.util.CarMonitor;
import com.commerical.vipconf.util.SingleGearBoUtil;
import com.kuwo.commercialization.common.message.MessageModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.Objects;


@RestController
@RequestMapping("/price")
@Slf4j
public class PriceController extends BaseController {

    @Autowired
    private SinglePriceService singlePriceService;

    @Autowired
    private VipConfConfigNacos vipConfConfigNacos;

    @Autowired
    private NewSinglePriceService newSinglePriceService;
    @Autowired
    private GearService gearService;

    @Autowired
    private CarMonitor carMonitor;


    /**
     * 获取单档位 豪华VIP价格
     *
     * @param priceGearVO 档位请求参数
     * @return
     * @throws ServiceException
     */
    @RequestMapping("/getSinglePriceInfo")
    @SentinelResource(value = "getSinglePriceInfo", fallbackClass ={PriceExceptionHandler.class}, fallback = "singleBlockExceptionHandler")
    public MessageModel getSinglePriceInfo(SinglePriceGearVO priceGearVO) throws ServiceException {
        // userId合法性判断
        if(!priceGearVO.checkValidUser()){
            log.error("getSinglePriceInfo user not valid!priceGearVO={}",JSONUtil.toJsonStr(priceGearVO));
           return success();
        }
        if(priceGearVO.getType()==0){
            Object price=singlePriceService.getRedisSinglePriceInfo(priceGearVO);
            log.info("getSinglePriceInfo cache redis param={},price={}", JSONUtil.toJsonStr(priceGearVO),price);
            return success(price);
        }
        Object ob= newSinglePriceService.getSinglePriceInfo(priceGearVO);
        log.info("getNewSinglePriceInfo param={},price={}", JSONUtil.toJsonStr(priceGearVO),ob);
        return success(ob);
    }

    @RequestMapping("/getSingleLuxSVIPPrice")
    public MessageModel getSingleLuxSVIPPrice(SinglePriceGearVO priceGearVO) throws ServiceException {
        // userId合法性判断
        if(!priceGearVO.checkValidUser()){
            log.error("getSingleLuxSVIPPrice user not valid!priceGearVO={}",JSONUtil.toJsonStr(priceGearVO));
            return success();
        }
        Map<String,String> resultMap= newSinglePriceService.getSingleLuxSVIPPrice(priceGearVO);
       // log.info("getSingleLuxSVIPPrice param={},resultMap={}", JSONUtil.toJsonStr(priceGearVO),JSONUtil.toJsonStr(resultMap));
        return success(resultMap);
    }

    @RequestMapping("/getLuxSvipLowPriceInfo")
    public MessageModel getLuxSvipLowPriceInfo(SinglePriceGearVO priceGearVO) throws ServiceException {
        // userId合法性判断
        if(!priceGearVO.checkValidUser()){
            log.error("getSinglePriceInfo user not valid!priceGearVO={}", JSONUtil.toJsonStr(priceGearVO));
            return success();
        }
        Object ob= newSinglePriceService.getLuxSvipLowPriceInfo(priceGearVO);
        return success(ob);
    }

    @RequestMapping("/getSinglePriceInfo2")
    public MessageModel getSinglePriceConfInfo2(SinglePriceGearVO priceGearVO) throws ServiceException {
        // userId合法性判断
        if(!priceGearVO.checkValidUser()){
            log.error("getSinglePriceConfInfo2 user not valid!priceGearVO={}",JSONUtil.toJsonStr(priceGearVO));
            return success();
        }
        Long now = System.currentTimeMillis();
        Map<String, Object> simplePriceGearBOMap = singlePriceService.getSinglePriceInfo2(priceGearVO);
        log.info("getSinglePriceConfInfo2 param={},response={}, cost={}", JSONUtil.toJsonStr(priceGearVO), JSONUtil.toJsonStr(simplePriceGearBOMap), System.currentTimeMillis() - now);
        return success(simplePriceGearBOMap);
    }

    /**
     * 连接设备推音质，获取超会档位
     * @param req
     * @return
     * @throws ServiceException
     */
    @GetMapping(value = "singleSVIPInfo")
    public MessageModel getSingelSVIPInfo(@Valid SingleSVIPInfoReq req) throws ServiceException {
        SinglePriceGearVO priceGearVO = new SinglePriceGearVO();
        BeanUtils.copyProperties(req,priceGearVO);
        priceGearVO.setDeviceName(req.getName());
        priceGearVO.setEarphonePopSign(1);

        Long now = System.currentTimeMillis();
        SingleSVIPInfoVO singleSVIPInfo = newSinglePriceService.getSingleSVIPInfo(priceGearVO);

        if (Objects.isNull(singleSVIPInfo) || Objects.isNull(singleSVIPInfo.getGearInfo())) {
            carMonitor.hiResAudioError.increment();
            log.error("getSingelSVIPInfo gear info is null,req={}", JSONUtil.toJsonStr(req));
        }
        log.info("getSingelSVIPInfo param={},response={}, cost={}", JSONUtil.toJsonStr(req), JSONUtil.toJsonStr(singleSVIPInfo), System.currentTimeMillis() - now);
        return success(singleSVIPInfo);
    }

    /**
     * 获取用户的超会升级档位
     * @param priceGearVO
     * @return
     */
    @RequestMapping("/getSingleSVIPUpPrice")
    public MessageModel getSingleUpgradeSVIP(SinglePriceGearVO priceGearVO) throws ServiceException {
        Long now = System.currentTimeMillis();
        GearBO singleUpgradeSVIPGear = newSinglePriceService.getSingleUpgradeSVIPGear(priceGearVO);
        log.info("getSingleUpgradeSVIP param={},response={},cost={}",JSONUtil.toJsonStr(priceGearVO),JSONUtil.toJsonStr(singleUpgradeSVIPGear),System.currentTimeMillis() - now);
        return success(singleUpgradeSVIPGear);
    }

    @RequestMapping("/getRecallUpgradeSvip")
    public MessageModel getRecallUpgradeSvip(SinglePriceGearVO priceGearVO) throws ServiceException {
        if (StringUtils.equals("ios", priceGearVO.getPlatform())) {
            GearBO gearBO = new GearBO();
            gearBO.setHitRecallUp(0);
            return success(gearBO);
        }

        long now = System.currentTimeMillis();
        GearBO singleUpgradeSVIPGear = newSinglePriceService.getSingleUpgradeSVIPGear(priceGearVO);
        log.info("getRecallUpgradeSVIP param={},response={},cost={}",JSONUtil.toJsonStr(priceGearVO),JSONUtil.toJsonStr(singleUpgradeSVIPGear),System.currentTimeMillis() - now);
        if (Objects.nonNull(singleUpgradeSVIPGear)) {
            try {
                JSONObject docJson = JSONUtil.parseObj(JSONUtil.parseArray(singleUpgradeSVIPGear.getAbData()).get(0)).getJSONObject("doc");
                String src = docJson.getStr("src");
                // 大于31天多档位
                if (StringUtils.equals("luxUpgradeSvip_payPage_whn_2", src)) {
                    GearBO gearBO = new GearBO();
                    gearBO.setHitRecallUp(0);
                    return success(gearBO);
                }
                singleUpgradeSVIPGear.setHitRecallUp(1);
                return success(singleUpgradeSVIPGear);
            } catch (Exception e) {
                log.error("判断档位异常", e);
            }
        }
        singleUpgradeSVIPGear = new GearBO();
        singleUpgradeSVIPGear.setHitRecallUp(0);
        return success(singleUpgradeSVIPGear);
    }

    /**
     * 灰度userId
     *
     * @param userId
     * @return
     */
    public boolean newSingleUser(String userId){
        try{
            if(StringUtils.isBlank(userId)||userId.equals("0")){
                return false;
            }
            List<String> newSingleUserIds=vipConfConfigNacos.getVipConfBO().getNewUserIds();
            if(CollectionUtils.isEmpty(newSingleUserIds)){
                return false;
            }
            if(!CollectionUtils.isEmpty(newSingleUserIds)){
                for(String keyStr:newSingleUserIds){
                    if(userId.endsWith(keyStr)){
                        return true;
                    }
                }
            }
            return false;
        }catch (Exception e){
            log.error("newSingleUser error",e);
        }
        return false;
    }

    @GetMapping(value = "/recallMusicCard")
    public MessageModel getVipMusicRecallCard(PriceGearVO priceGearBO) {
        MessageModel messageModel = new MessageModel();
        try {
            messageModel.setData(newSinglePriceService.getRecallMusicCard(priceGearBO));
        } catch (Exception e) {
            log.error("getVipMusicRecallCard error", e);
            messageModel.setData(SingleGearBoUtil.getDefaultSeasonLuxCard(priceGearBO.getPlatform()));
        }
        return messageModel;
    }

    @GetMapping(value = "/getMemInfo")
    public MessageModel getMemInfo(String uid ,String token) {
        MessageModel messageModel =null;
        if(!StringUtils.equals(token,"eyJhbGciOiJIUzI1NiJ9")||StringUtils.isBlank(uid)){
            return messageModel;
        }
        try {
            messageModel=singlePriceService.getMemInfo(uid);
        } catch (Exception e) {
            log.error("getMemInfo error", e);
        }
        return messageModel;
    }

    @GetMapping(value = "/recallYearGear")
    public MessageModel getSVipRecallYearGear(PriceGearVO priceGearBO) {
        MessageModel messageModel = new MessageModel();
        try {
            messageModel.setData(newSinglePriceService.getSVipRecallYearGear(priceGearBO));
        } catch (Exception e) {
            log.error("getSVipRecallYearGear error", e);
            messageModel.setData(SingleGearBoUtil.getDefaultSeasonLuxCard(priceGearBO.getPlatform()));
        }
        return messageModel;
    }

}
