package com.commerical.vipconf.controller;

import com.commerical.vipconf.service.PriceValidateService;
import com.commerical.vipconf.util.MyNumberUtils;
import com.kuwo.commercialization.common.message.MessageModel;
import com.kuwo.commercialization.common.message.SystemCodeErrorConstant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Desc:
 * @date 2024-07-15 22:45:02
 */
@RestController
@RequestMapping("/price/validate")
public class PriceSrcValidateController extends BaseController{

    private PriceValidateService priceValidateService;

    public PriceSrcValidateController(PriceValidateService priceValidateService) {
        this.priceValidateService = priceValidateService;
    }

    @RequestMapping("/uid/add")
    public MessageModel addValidateUidSrc(String userId, String srcList) {
        long uidL = MyNumberUtils.toLONG(userId);
        if (uidL ==0  || StringUtils.isBlank(srcList)) {
            return failed(SystemCodeErrorConstant.PARAM_CHECK_ERROR);
        }
        String[] srcLA = srcList.split(",");
        List<String> collect = Arrays.stream(srcLA).collect(Collectors.toList());
        return success(priceValidateService.addValidateUidSrc(userId, collect));
    }

    @RequestMapping("/uid/in")
    public MessageModel isInValidateUidSrcSet(String userId, String src) {
        long uidL = MyNumberUtils.toLONG(userId);
        if (uidL ==0 || StringUtils.isBlank(src)) {
            return failed(SystemCodeErrorConstant.PARAM_CHECK_ERROR);
        }
        return success(priceValidateService.isInValidateUidSrcSet(userId, src));
    }

    @RequestMapping("/uid/clear")
    public MessageModel clearValidateUid(String userId) {
        long uidL = MyNumberUtils.toLONG(userId);
        if (uidL ==0) {
            return failed(SystemCodeErrorConstant.PARAM_CHECK_ERROR);
        }
        return success(priceValidateService.clearValidateUid(userId));
    }

    @RequestMapping("/vid/add")
    public MessageModel addValidateVirtualUidSrc(String virtualUid, String srcList) {
        long vidL = MyNumberUtils.toLONG(virtualUid);
        if (vidL ==0  || StringUtils.isBlank(srcList)) {
            return failed(SystemCodeErrorConstant.PARAM_CHECK_ERROR);
        }

        String[] srcLA = srcList.split(",");
        List<String> collect = Arrays.stream(srcLA).collect(Collectors.toList());
        return success(priceValidateService.addValidateVirtualUidSrc(virtualUid, collect));
    }

    @RequestMapping("/vid/in")
    public MessageModel isInValidateSrcSet(String virtualUid, String src) {
        long uidL = MyNumberUtils.toLONG(virtualUid);
        if (uidL ==0 || StringUtils.isBlank(src)) {
            return failed(SystemCodeErrorConstant.PARAM_CHECK_ERROR);
        }
        return success(priceValidateService.isInValidateSrcSet(virtualUid, src));
    }


    @RequestMapping("/vid/clear")
    public MessageModel clearValidateVirtualUid(String virtualUid) {
        long vidL = MyNumberUtils.toLONG(virtualUid);
        if (vidL ==0) {
            return failed(SystemCodeErrorConstant.PARAM_CHECK_ERROR);
        }
        return success(priceValidateService.clearValidateVirtualUid(virtualUid));
    }
}
