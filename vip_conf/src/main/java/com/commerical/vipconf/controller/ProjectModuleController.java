package com.commerical.vipconf.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.commerical.vipconf.domain.ProjectModule;
import com.commerical.vipconf.domain.vo.ProjectModuleQueryVO;
import com.commerical.vipconf.domain.vo.ProjectModuleVO;
import com.commerical.vipconf.service.ProjectModuleService;
import com.kuwo.commercialization.common.message.MessageModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <p>
 * 项目模块 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-03
 */
@RestController
@RequestMapping("/projectModule")
public class ProjectModuleController extends BaseController {

    private final static Logger logger = LoggerFactory.getLogger(ProjectModuleController.class);

    @Autowired
    private ProjectModuleService projectModuleService;

    /**
     * 添加项目模块
     *
     * @param projectModuleVO
     * @return
     */
    @RequestMapping("/addOrUpdateProjectModule")
    public MessageModel addOrUpdateProjectModule(@Valid ProjectModuleVO projectModuleVO){
        ProjectModule projectModule= projectModuleService.addOrUpdateProjectModule(projectModuleVO);
        return success(projectModule.getCode());
    }

    /**
     * 删除项目模块
     *
     * @param
     * @return
     */
    @RequestMapping("/deleteProjectModuleById")
    public MessageModel deleteProjectModuleById(Integer id,Boolean isTrue,Integer isDelete){
        if(ObjectUtils.isNull(id)||ObjectUtils.isNull(isTrue)||ObjectUtils.isNull(isDelete)){
            logger.error("deleteProjectModuleById param  has empty value!,id={}",id);
            return failed();
        }
        projectModuleService.deleteProjectModuleById(id,isTrue,isDelete);
        return success();
    }

    /**
     * 项目模块列表查询
     *
     * @param
     * @return
     */
    @RequestMapping("/getProjectModuleList")
    public MessageModel getProjectModuleList(@Valid ProjectModuleQueryVO projectModuleQueryVO){
        IPage<ProjectModule> ProjectModuleVOList= projectModuleService.getProjectModuleList(projectModuleQueryVO);
        return success(ProjectModuleVOList);
    }

    /**
     * 项目模块详情查询
     *
     * @param
     * @return
     */
    @RequestMapping("/getProjectModuleInfoById")
    public MessageModel getProjectModuleInfoById(Integer id){
        ProjectModule ProjectModule= projectModuleService.getProjectModuleInfoById(id);
        return success(ProjectModule);
    }
}

