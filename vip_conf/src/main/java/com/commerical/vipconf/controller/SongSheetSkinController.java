package com.commerical.vipconf.controller;
import com.commerical.vipconf.domain.SongSheetSkin;
import com.commerical.vipconf.service.SongSheetSkinService;
import com.kuwo.commercialization.common.message.MessageModel;
import com.kuwo.commercialization.common.message.SystemCodeErrorConstant;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("songSheetSkin")
public class SongSheetSkinController {
	private Logger logger = LoggerFactory.getLogger(SongSheetSkinController.class);
	@Autowired
    private SongSheetSkinService songSheetSkinService;

    @RequestMapping(value = "addOrUpdateBack")
    public MessageModel addOrUpdateBack(SongSheetSkin songSheetSkin,String sign) {
		//有参数为空，提示参数异常。
		if(StringUtils.isEmpty(sign)||!"AEX4D4K4FUHN6U1".equals(sign)){
			return new MessageModel(SystemCodeErrorConstant.PARAM_CHECK_ERROR);
		}
        return songSheetSkinService.saveOrUpdateBack(songSheetSkin);
    }

    @RequestMapping(value = "deleteBack")
    public MessageModel deleteBack(Long id,String sign) {
		if(StringUtils.isEmpty(sign)||!"85XL4G3ZF3V9MDG".equals(sign)){
			return new MessageModel(SystemCodeErrorConstant.PARAM_CHECK_ERROR);
		}
		return songSheetSkinService.deleteById(id);
    }

	@RequestMapping("getByIdBack")
	public MessageModel getByIdBack(Long id) {
		MessageModel messageModel = new MessageModel();
		try {
			SongSheetSkin songSheetSkin = songSheetSkinService.getById(id);
			messageModel.setData(songSheetSkin);
			return messageModel;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	@RequestMapping("getListBack")
	public MessageModel getALlBack() {
		MessageModel messageModel = new MessageModel();
		try {
			List<SongSheetSkin> data = songSheetSkinService.getAll();
			messageModel.setData(data);
			return messageModel;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

}
