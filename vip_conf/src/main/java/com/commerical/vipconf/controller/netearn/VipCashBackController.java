package com.commerical.vipconf.controller.netearn;

import com.commerical.vipconf.domain.vo.CashVO;
import com.commerical.vipconf.service.netearn.VipCashBackService;
import com.kuwo.commercialization.common.message.MessageModel;
import com.kuwo.commercialization.common.message.SystemCodeErrorConstant;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 网赚付N元，看广告打卡返现
 *
 * <AUTHOR>
 * <p>
 * 2025年4月19日
 */
@RestController
@RequestMapping(value = "/vipCashBack")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class VipCashBackController {
    private final VipCashBackService vipCashBackService;

    @GetMapping(value = "/bought")
    public MessageModel boughtVipCard(CashVO cashVO) {
        try {
            Boolean inActivity = vipCashBackService.inActivity(cashVO.getUid(), cashVO.getPlatform());
            return new MessageModel(inActivity ? 1 :0);
        } catch (Exception e) {
            return new MessageModel(SystemCodeErrorConstant.FAIL);
        }
    }

    /**
     * 获取档位价格（h5）
     *
     * @param cashVO cashVo
     * @return 档位价格信息
     */
    @GetMapping(value = "/actPage")
    public MessageModel gearVipCard(CashVO cashVO) {
        return new MessageModel(vipCashBackService.getActivityPage(cashVO));
    }
}
