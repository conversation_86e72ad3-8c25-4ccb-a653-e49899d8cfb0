package com.commerical.vipconf.controller;

import com.alibaba.fastjson.JSONObject;
import com.commercal.uiservice.enity.VipInfo;
import com.commerical.musicplayerservice.enity.TitleInfoResp;
import com.commerical.vipconf.service.LimitPopService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.util.function.Tuple2;

import java.util.concurrent.ExecutionException;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.commerical.vipconf.controller
 * @date:2024/8/17
 */

@RestController
@RequestMapping("/test")
@Slf4j
public class TestController extends BaseController {

    @Autowired
    private LimitPopService limitPopService;

    @RequestMapping("/1")
    public Object test1(String userId, String deviceId, String platform, String source, String songId, String payDeskSign, String fromsrc, String vipInfo) {
        VipInfo vipInfoDTO = null;
        if (StringUtils.isNotBlank(vipInfo)) {
            try {
                vipInfoDTO = JSONObject.parseObject(vipInfo, VipInfo.class);
            } catch (Exception e) {
                log.error("parse vipInfo is error, ", e);
            }
        }
        Tuple2<Integer, Object> objects = limitPopService.popStyle(userId, deviceId, platform, source, songId, payDeskSign, fromsrc, vipInfoDTO, 0, 1, null);
        return success(objects);
    }

    @RequestMapping("/2")
    public Object test2(String vipInfo, String userId, String platform, String fromsrc, String source, String songId, String overseasQuality, String music_quality, String pop_id, String songQuality, String auditionJumpSuper) {
        VipInfo vipInfoDTO = null;
        if (StringUtils.isNotBlank(vipInfo)) {
            try {
                vipInfoDTO = JSONObject.parseObject(vipInfo, VipInfo.class);
            } catch (Exception e) {
                log.error("parse vipInfo is error, ", e);
            }
        }
        TitleInfoResp songInfo = limitPopService.getSongInfo(vipInfoDTO, userId, null, platform, fromsrc, source, songId, overseasQuality, music_quality, pop_id, songQuality, auditionJumpSuper, 1);
        return success(songInfo);
    }

    @RequestMapping("/3")
    public Object test3(String fromsrc) throws ExecutionException {
        String s = limitPopService.paySuccessText(fromsrc);
        return success(s);
    }

    @RequestMapping("/4")
    public Object test4() {
        limitPopService.load();
        return success("1");
    }
}
