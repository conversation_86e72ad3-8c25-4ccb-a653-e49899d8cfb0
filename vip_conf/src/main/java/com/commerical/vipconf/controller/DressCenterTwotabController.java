package com.commerical.vipconf.controller;


import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.commerical.vipconf.annotation.CheckUser;
import com.commerical.vipconf.config.constant.DressCenterConstant;
import com.commerical.vipconf.config.exception.ServiceException;
import com.commerical.vipconf.domain.DressCenterTwotab;
import com.commerical.vipconf.domain.vo.DressCenterTwotabQueryVO;
import com.commerical.vipconf.domain.vo.DressCenterTwotabVO;
import com.commerical.vipconf.service.DressCenterTwotabService;
import com.commerical.vipconf.service.PubDressCenterTwotabService;
import com.kuwo.commercialization.common.message.MessageModel;
import com.kuwo.commercialization.common.message.SystemCodeErrorConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <p>
 * 装扮中心二级tab 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-23
 */
@RestController
@RequestMapping("/dressCenterTwotab")
@Slf4j
public class DressCenterTwotabController extends BaseController{

    @Autowired
    private DressCenterTwotabService dressCenterTwotabService;

    @Autowired
    private PubDressCenterTwotabService pubDressCenterTwotabService;

    /**
     * 添加/修改
     *
     * @param DressCenterTwotabVO
     * @return
     */
    @CheckUser
    @RequestMapping("/addOrUpdateDressCenterTwotab")
    public MessageModel addOrUpdateDressCenterTwotab(@Valid DressCenterTwotabVO DressCenterTwotabVO){
        log.info("addOrUpdateDressCenterTwotab param={}", JSONUtil.toJsonStr(DressCenterTwotabVO));
        DressCenterTwotab DressCenterTwotab=dressCenterTwotabService.addOrUpdateDressCenterTwotab(DressCenterTwotabVO);
        return success(DressCenterTwotab.getCode()) ;
    }

    /**
     * 删除
     *
     * @param
     * @return
     */
    @CheckUser
    @RequestMapping("/deleteDressCenterTwotabById")
    public MessageModel deleteDressCenterTwotabById(Integer id,Boolean isTrue,Integer isDelete){
        if(ObjectUtils.isNull(id)||ObjectUtils.isNull(isTrue)||ObjectUtils.isNull(isDelete)){
            log.error("deleteDressCenterTwotabById param  has empty value!,id={}",id);
            return failed();
        }
        log.info("deleteDressCenterTwotabById param={}", id);
        dressCenterTwotabService.deleteDressCenterTwotabById(id,isTrue,isDelete);
        return success();
    }

    /**
     * 列表查询
     *
     * @param
     * @return
     */
    @RequestMapping("/getDressCenterTwotabList")
    public MessageModel getDressCenterTwotabList(@Valid DressCenterTwotabQueryVO DressCenterTwotabQueryVO) throws ServiceException {
        IPage<DressCenterTwotab> DressCenterTwotabVOList= dressCenterTwotabService.getDressCenterTwotabList(DressCenterTwotabQueryVO);;
        return success(DressCenterTwotabVOList);
    }

    /**
     * 详情查询
     *
     * @param id 主键id
     * @return
     */
    @RequestMapping("/getDressCenterTwotabInfoById")
    public MessageModel getDressCenterTwotabInfoById(Integer id){
        if(ObjectUtils.isNull(id)){
            log.error("deleteDressCenterTwotabById id empty value！");
            return failed();
        }
        DressCenterTwotab DressCenterTwotab= dressCenterTwotabService.getDressCenterTwotabInfoById(id);
        return success(DressCenterTwotab);
    }

    /**
     * 发布topTab
     *
     * @param code
     * @return
     */
    @CheckUser
    @RequestMapping("/pubDressCenterTwotab")
    public MessageModel pubDressCenterTwotab(String code){
        if(StringUtils.isBlank(code)){
            log.error("pubDressCenterTwotab code empty value！");
            return failed(SystemCodeErrorConstant.CODE_ERROR);
        }
        String name= DressCenterConstant.userLocal.get();
        log.info("pubDressCenterTwotab code={},name={}", code,name);
        return success(pubDressCenterTwotabService.pubDressCenterTwotab(code));
    }

}

