package com.commerical.vipconf;

import feign.Logger;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.client.RestTemplate;

@EnableDiscoveryClient
@SpringBootApplication
@EnableScheduling
@MapperScan("com.commerical.vipconf.mapper")
@EnableFeignClients(basePackages = {"com.commerical.vipconf.feign"})
public class VipConfApplication {

	public static void main(String[] args) {
		SpringApplication.run(VipConfApplication.class, args);
	}

	@Bean
	Logger.Level feignLoggerLevel(){
		return Logger.Level.BASIC;
	}

	@Bean
	@LoadBalanced
	RestTemplate restTemplate() {
		return new RestTemplate();
	}

}
