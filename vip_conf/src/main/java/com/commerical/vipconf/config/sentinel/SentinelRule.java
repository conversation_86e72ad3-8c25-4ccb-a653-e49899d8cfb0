package com.commerical.vipconf.config.sentinel;

import com.alibaba.csp.sentinel.datasource.ReadableDataSource;
import com.alibaba.csp.sentinel.datasource.nacos.NacosDataSource;
import com.alibaba.csp.sentinel.property.PropertyListener;
import com.alibaba.csp.sentinel.property.SentinelProperty;
import com.alibaba.csp.sentinel.slots.block.degrade.DegradeRule;
import com.alibaba.csp.sentinel.slots.block.degrade.DegradeRuleManager;
import com.alibaba.csp.sentinel.slots.block.flow.FlowRule;
import com.alibaba.csp.sentinel.slots.block.flow.FlowRuleManager;
import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;
import java.util.List;
import java.util.Properties;

@Component
public class SentinelRule implements InitializingBean {

    private Logger log = LoggerFactory.getLogger(SentinelRule.class);

    @Value("${spring.cloud.nacos.discovery.server-addr}")
    private String address;

    @Value("${sentinel.nacos.namespace}")
    private String namespace;

    @Value("${sentinel.nacos.groupId}")
    private String groupId;

    @Value("${sentinel.nacos.vip-rule-dataId}")
    private String dataId;

    @Value("${sentinel.nacos.vip-Degrade-dataId}")
    private String deGradeId;

    private ConfigService configService;

    private Gson gson = new GsonBuilder().registerTypeAdapter(FlowRule.class, new FlowRuleDeserialize()).create();
    private Gson degradeJson = new GsonBuilder().registerTypeAdapter(DegradeRule.class, new DegradeRuleDeserialize()).create();
    public void  initRule(){
        Properties properties = new Properties();
        properties.setProperty("serverAddr", address);
        if (StringUtils.isNotBlank(namespace)){
            properties.setProperty("namespace", namespace);
        }
        Type setType = new TypeToken<List<FlowRule>>(){}.getType();
        ReadableDataSource<String, List<FlowRule>> vipServiceFlowRuleDataSource =  new NacosDataSource<>(properties, groupId, dataId, source -> {
            try {
                log.info("[sentinel vipService flow] update rule -> "+source);
                return gson.fromJson(source, setType);
            } catch (Exception e) {
                log.error("sentinel rule convert fail!", e);
            }
            return null;
        });

        Type setDegradeType = new TypeToken<List<DegradeRule>>(){}.getType();
        ReadableDataSource<String, List<DegradeRule>> vipServicDegradeDataSource =  new NacosDataSource<>(properties, groupId, deGradeId, source -> {
            try {
                log.info("[sentinel vipService degrade flow] update rule -> "+source);
                return degradeJson.fromJson(source, setDegradeType);
            } catch (Exception e) {
                log.error("sentinel degrade rule convert fail!", e);
            }
            return null;
        });

        SentinelProperty<List<FlowRule>> vipServiceFlowRuleProperty = vipServiceFlowRuleDataSource.getProperty();
        vipServiceFlowRuleProperty.addListener(new PropertyListener<List<FlowRule>>() {
            @Override
            public void configUpdate(List<FlowRule> value) {
                log.info("[sentinel vipService flow] config update ->" + value);
                try {
                  //  configService.publishConfig(dataId, groupId,  gson.toJson(value));
                } catch (Exception e) {
                    log.error("sentinel rule publish fail!", e);
                }
            }

            @Override
            public void configLoad(List<FlowRule> value) {
                log.info("[sentinel vipService flow] rule config load ->" + value);
            }
        });
        SentinelProperty<List<DegradeRule>> vipServiceDegradeProperty = vipServicDegradeDataSource.getProperty();
        vipServiceDegradeProperty.addListener(new PropertyListener<List<DegradeRule>>() {
            @Override
            public void configUpdate(List<DegradeRule> value) {
                log.info("[sentinel vipService degrade] config update ->" + value);
                try {
                   // configService.publishConfig(deGradeId, groupId,  degradeJson.toJson(value));
                } catch (Exception e) {
                    log.error("sentinel degrade rule publish fail!", e);
                }
            }
            @Override
            public void configLoad(List<DegradeRule> value) {
                log.info("[sentinel vipService degrade] rule config load ->" + value);
            }
        });

        FlowRuleManager.register2Property(vipServiceFlowRuleProperty);
        DegradeRuleManager.register2Property(vipServiceDegradeProperty);
        log.info("[sentinel rule load finish]");
    }




    @Override
    public void afterPropertiesSet() throws Exception {
        Properties properties = new Properties();
        properties.setProperty("serverAddr", address);
        if (StringUtils.isNotBlank(namespace)){
            properties.setProperty("namespace", namespace);
        }
        configService = NacosFactory.createConfigService(properties);
    }
}
