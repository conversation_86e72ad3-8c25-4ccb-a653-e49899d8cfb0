package com.commerical.vipconf.domain.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.gson.Gson;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class FilterVO implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 策略名
     */
    @NotNull(message = "策略filterName不能为空")
    private String filterName;

    /**
     * 价格
     */
    @NotNull(message = "策略price不能为空")
    private BigDecimal price;

    /**
     * 续费价格
     */
    private BigDecimal renewalPrice;

    private String doc;

    /**
     * 档位id
     */
   //@NotNull(message = "策略档位gearId不能为空")
    private Integer gearId;

    private Integer isDelete;

    /**
     * 城市包
     */
    private String filterCityPackageId;

    /**
     * device id 匹配
     */
    private String filterDevice;

    /**
     * 是否为首开
     */
    private Integer filterFirstPay;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 剩余在期时间
     */
    private String filterPeriodLeft;

    /**
     * 过期时间
     */
    private String filterOverDue;

    /**
     * user_id尾号
     */
    private String filterUser;

    /**
     * virtual_user尾号
     */
    private String filterVitrualUser;

    /**
     * 人群包
     */
    private String filterPersonPackageId;

    /**
     * 类型
     */
    @NotNull(message = "type不能为空")
    private Integer type;

    /**
     * 排序
     */
    private Integer orderRank;

    /**
     * 是否为首续
     */
    private Integer filterFirstRenew;

    /**
     * 编辑人
     */
    private String editorName;

    /**
     * 支付方式
     */
    private String payWay;


    private String unionId;

    private String threeMemberId;

    /**
     * 拓展
     */
    private String extend;

    /**
     * fromsrc
     */
    private String filterFromsrc;

    /**
     * 是否会员
     */
    private Integer filterIsMember;

    /**
     * 30天听过长音频
     *  1 听过
     *  0 否
     */
    private Integer filter30DayTing;

    /**
     * 渠道
     */
    private String filterChannel;

    /**
     * 策略类型
     */
    private String filterType;

    /**
     * 平台 ar安卓，ios，pc，car，web
     */
    private String platform;

    /**
     * 会员类型 音乐包1，豪华vip：12，超会 34
     */
    private String vipType;

    /**
     * 是否自动续费 1是 0否
     */
    private Integer autoPay;


    /**
     * 挡位类型 月卡:1,季卡:3,半年卡：6，年卡：12，联合会员：13，运营位：14
     */
    private Integer gearType;

    /**
     * 是否活动
     */
    private Integer isActivity;

    /**
     * 屏蔽渠道
     */
    private String blockedChannel;

    /**
     * 活动开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @TableField(value = "activity_start_time", fill = FieldFill.INSERT_UPDATE)
    private Date  activityStartTime;

    /**
     * 活动结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @TableField(value = "activity_end_time", fill = FieldFill.INSERT_UPDATE)
    private Date  activityEndTime;

    /**
     * 登录状态
     */
    private Integer  loginStatus;

    public String parseOtherConditionsToJson(Gson gson){
        if (filter30DayTing!=null){
            Map otherConditions = new HashMap();
            otherConditions.put("filter30DayTing", filter30DayTing);
            return gson.toJson(otherConditions);
        }
        return "";
    }
}
