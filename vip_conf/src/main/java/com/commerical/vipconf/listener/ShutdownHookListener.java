package com.commerical.vipconf.listener;

import com.alibaba.cloud.nacos.registry.NacosAutoServiceRegistration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ShutdownHookListener implements ApplicationListener<ContextClosedEvent> {

    @Autowired
    private NacosAutoServiceRegistration nacosAutoServiceRegistration;

    @Override
    public void onApplicationEvent(ContextClosedEvent event) {
        log.info("receive context close event, spring shutdown");
        nacosAutoServiceRegistration.stop();
    }
}
