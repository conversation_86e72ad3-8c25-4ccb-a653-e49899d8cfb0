package com.commerical.vipconf.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.commerical.vipconf.domain.ProjectSpace;
import com.commerical.vipconf.domain.ProjectTemplate;
import com.commerical.vipconf.domain.ProjectTemplate;
import com.baomidou.mybatisplus.extension.service.IService;
import com.commerical.vipconf.domain.vo.ProjectTemplateQueryVO;
import com.commerical.vipconf.domain.vo.ProjectTemplateVO;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-01
 */
public interface ProjectTemplateService extends IService<ProjectTemplate> {
    
    ProjectTemplate addOrUpdateProjectTemplate(ProjectTemplateVO projectTemplateVO);

    void deleteProjectTemplateById(Integer id,Boolean isTrue,Integer isDelete);

    IPage<ProjectTemplate> getProjectTemplateList(ProjectTemplateQueryVO projectTemplateQueryVO);

    ProjectTemplate getProjectTemplateInfoById(Integer id);

    ProjectTemplate getProjectTemplateInfoByCode(String code);

    List<ProjectTemplate> getProjectSpaceALlList(String projectSpaceCode);
}
