package com.commerical.vipconf.service.netearn;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.net.URLDecoder;
import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.commerical.vipconf.config.redis.DynamicChoiceRedissonClient;
import com.commerical.vipconf.domain.bo.CashBO;
import com.commerical.vipconf.domain.bo.CashFilterBO;
import com.commerical.vipconf.domain.bo.CashVipTypeBO;
import com.commerical.vipconf.domain.vo.CashVO;
import com.commerical.vipconf.domain.vo.netearn.LuxuryCashBackVo;
import com.commerical.vipconf.enums.VipTypeEnum;
import com.commerical.vipconf.nacos.NetEarnLuxVipNacos;
import com.commerical.vipconf.util.AbtUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class VipCashBackService {
    private final String vipCenterTypeUrl;
    private final NetEarnLuxVipNacos netEarnLuxVipNacos;
    private final DynamicChoiceRedissonClient dynamicChoiceRedissonClient;

    public VipCashBackService(@Value("${spring.profiles.active:dev}") String activeProfile,
                              NetEarnLuxVipNacos netEarnLuxVipNacos,
                              DynamicChoiceRedissonClient dynamicChoiceRedissonClient) {
        this.dynamicChoiceRedissonClient = dynamicChoiceRedissonClient;
        this.netEarnLuxVipNacos = netEarnLuxVipNacos;

        if (StringUtils.equals("prod", activeProfile)) {
            log.info("初始化正式环境");
            vipCenterTypeUrl = "http://vip1.kuwo.cn/commercia/vipconf/cash/vipcenter/viptype";
        } else {
            log.info("初始化测试环境");
            vipCenterTypeUrl = "http://testvip.kuwo.cn/commercia/vipconf/cash/vipcenter/viptype";
        }
        log.info("初始化环境完成");
    }

    private interface BannerAbtConfig {
        Integer channel = 336;
        String moduleKey = "WatchADWithdraw";
        // 获取分组信息的key
        String groupKey = "group";
        // 实验组的分组信息
        String hitGroup = "1";
        String bannerKey = "bannerurl";
        String showBannerKey = "bannershow";
        String showBannerValue = "1";
    }

    private interface NetEarnConfig {
        // 活动是否开始
        interface ActStart {
            Integer yes = 1;
            Integer no = 0;
        }

        // 使用ab实验, 产品要求在，ab实验可控制，即当全量验证好后，不调用abt了，节约时间
        interface UseAbt {
            Integer yes = 1;
            Integer no = 0;
        }

        // 收银台标识， 对应收银台标识的才设置
        Set<String> payDeskSigns = new HashSet<String>() {{
            // 全局 拦截弹窗
            add("Allpay_limitpop");
            // 拦截弹窗
            add("limitpop");
            // 会员中心
            add("membercenter_cash");
            // 全局 会员中心
            add("Allpay_membercenter_cash");
        }};

        // 价格策略
        interface PriceReference {
            int lowPriceLine = 3;
            BigDecimal priceIfLessLine = BigDecimal.valueOf(4.8);
            String lowPriceSrc = "wangzhuan30CashBack4.8";
            Integer monthGearFlag = 1;
            Integer autoPayFlag = 1;
            String unionMonthGearStr = "union";
        }

        // redis
        interface RedisReference {
            String clientKey = "cashback";
            String currentKeyPattern = "netearn:cash:luxury:curr:%s";
            String nextKeyPattern = "netearn:cash:luxury:next:%s";
            long expireDays = 186; // 过期时间(186天) 会员半年卡的时间
        }

        LuxuryCashBackVo errorVo = LuxuryCashBackVo.builder()
                .showType(0)
                .desc("活动太火爆啦，请稍后再试~")
                .build();

        /**
         * redis中存储的订单数据
         */
        @Data
        class OrderData {
            private BigDecimal price;
            private Long startTime;
            private Long expireTime;
            private Long orderId;
        }

    }

    public Boolean hitShowBannerAbt(String userId, String platform) {
        JSONObject abtMatcher = AbtUtil.getAbtMatcher(userId, "", platform, BannerAbtConfig.channel, BannerAbtConfig.moduleKey)
                .getJSONObject(BannerAbtConfig.moduleKey);
        if (MapUtil.isEmpty(abtMatcher)) {
            return false;
        }
        String group = abtMatcher.getStr(BannerAbtConfig.groupKey);
        return StringUtils.equals(BannerAbtConfig.hitGroup, group);
    }

    /**
     * 判断当前用户有没有购买0元打卡返现档位
     * 用于用户首次打卡使用
     *
     * @param userId   uid
     * @param platform 平台
     * @return int
     */
    public Boolean inActivity(String userId, String platform) {
        if (StringUtils.isBlank(userId) || !NumberUtil.isNumber(userId)) {
            return false;
        }
        if (StringUtils.isBlank(platform) || !StrUtil.equalsAnyIgnoreCase(platform, "ar", "android", "an")) {
            return false;
        }

        String currentRedisKey = String.format(NetEarnConfig.RedisReference.currentKeyPattern, userId);
        Map<Object, Object> currOrderMap = dynamicChoiceRedissonClient.getClient(NetEarnConfig.RedisReference.clientKey)
                .getMap(currentRedisKey)
                .readAllMap();
        if (MapUtil.isEmpty(currOrderMap)) {
            return false;
        }
        long currentTimeMillis = System.currentTimeMillis();
        NetEarnConfig.OrderData orderData = buildOrderData(currOrderMap);
        return currentTimeMillis >= orderData.getStartTime() && currentTimeMillis < orderData.getExpireTime();
    }

    /**
     * 获取活动页数据
     *
     * @param cashVO cashVo
     * @return LuxuryCashBackVo
     */
    public LuxuryCashBackVo getActivityPage(CashVO cashVO) {
        if (Objects.equals(NetEarnConfig.ActStart.no, netEarnLuxVipNacos.getStart())) {
            log.info("活动未开始");
            return NetEarnConfig.errorVo;
        }
        String userId = cashVO.getUid();
        if (CollUtil.isNotEmpty(netEarnLuxVipNacos.getTestAccount()) && !netEarnLuxVipNacos.getTestAccount().contains(userId)) {
            log.info("当前用户{}不是活动测试账号，无法获取0元打卡返现活动页数据", userId);
            return NetEarnConfig.errorVo;
        }
        if (!StrUtil.equalsAnyIgnoreCase(cashVO.getPlatform(), "ar", "android", "an")) {
            log.info("仅限安卓用户参与");
            return NetEarnConfig.errorVo;
        }
        try {
            String redisCurrentKey = String.format(NetEarnConfig.RedisReference.currentKeyPattern, userId);
            RedissonClient client = dynamicChoiceRedissonClient.getClient(NetEarnConfig.RedisReference.clientKey);
            RMap<Object, Object> currClientMap = client.getMap(redisCurrentKey, StringCodec.INSTANCE);
            Map<Object, Object> currOrderMap = currClientMap.readAllMap();
            long currentTimeMillis = System.currentTimeMillis();
            // 无订单数据
            if (MapUtil.isEmpty(currOrderMap)) {
                return getNextOrderGear(cashVO, currClientMap, client);
            }

            NetEarnConfig.OrderData currOrderData = buildOrderData(currOrderMap);
            if (currentTimeMillis < currOrderData.getStartTime()) {
                log.info("userId: {} 当前订单未开始， 返回档位", userId);
                return getActivityPageGear(cashVO);
            }
            if (currentTimeMillis > currOrderData.getExpireTime()) {
                return getNextOrderGear(cashVO, currClientMap, client);
            }
            return getNetEarnSignData(currOrderData);

        } catch (Exception e) {
            log.error("获取0元打卡返现活动页数据异常", e);
            return NetEarnConfig.errorVo;
        }
    }

    /**
     * 设置会员中心豪华vip tab页的banner图
     *
     * @param userId uid
     * @param cashBO CashBO
     */
    public void setVipCenterBanner(String userId, String platform, CashBO cashBO, String payDeskSign) {
        try {
            if (BooleanUtils.isNotTrue(preSetBannerCheck(userId, payDeskSign, platform, cashBO))) {
                return;
            }
            // 对应的档位信息 强转因为 CashFilterBo 使用了 @JsonSerialize(using = FilterBOHandle.FilterSerializer.class) json 反序列化无法将属性全部设置进来
            @SuppressWarnings("unchecked")
            List<CashVipTypeBO> cashVipTypeBOS = (List<CashVipTypeBO>) cashBO.getData();
            if (CollUtil.isEmpty(cashVipTypeBOS)) {
                log.info("userId: {} cashVipTypeBOS is empty", userId);
                return;
            }
            // 筛选档位中的豪V连包月、月卡
            List<CashVipTypeBO> luxCashVipTypeBos = cashVipTypeBOS.stream()
                    .filter(cashVipTypeBO -> StringUtils.equals("vip_7", cashVipTypeBO.getType()))
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(luxCashVipTypeBos)) {
                if (log.isDebugEnabled()) {
                    log.debug("userId: {} 无豪华VIP连包月卡", userId);
                }
                return;
            }
            // 筛选豪华vip连包档位
            List<CashFilterBO> luxuryAutoPayCashFilterBos = luxCashVipTypeBos.stream()
                    .flatMap(cashVipTypeBO -> {
                        Object listInCashVipTypeBo = cashVipTypeBO.getList();
                        if (Objects.isNull(listInCashVipTypeBo) || !(listInCashVipTypeBo instanceof List)) {
                            return null;
                        }
                        try {
                            // 强转因为 CashFilterBo @JsonSerialize(using = FilterBOHandle.FilterSerializer.class)
                            @SuppressWarnings("unchecked")
                            List<CashFilterBO> cashFilterBOS = (List<CashFilterBO>) listInCashVipTypeBo;
                            if (CollUtil.isEmpty(cashFilterBOS)) {
                                return null;
                            }
                            return Stream.of(cashFilterBOS.toArray(new CashFilterBO[0]));
                        } catch (Exception e) {
                            log.error("userId: {} cashVipTypeBo 转化档位异常", userId, e);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .filter(filterLuxuryMonthAutoPay())
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(luxuryAutoPayCashFilterBos)) {
                if (log.isDebugEnabled()) {
                    log.debug("userId: {} 无连包档位", userId);
                }
                return;
            }

            luxuryAutoPayCashFilterBos = luxuryAutoPayCashFilterBos.stream().filter(filterPriceAndDescImg(userId)).collect(Collectors.toList());
            if (CollUtil.isEmpty(luxuryAutoPayCashFilterBos)) {
                return;
            }
            String bannerUrl;
            // 需求中 要求预留出对abt下线后的控制
            if (Objects.equals(NetEarnConfig.UseAbt.yes, netEarnLuxVipNacos.getUseAbt())) {
                JSONObject abtMatcher = AbtUtil.getAbtMatcher(userId, "", "ar", BannerAbtConfig.channel, BannerAbtConfig.moduleKey)
                        .getJSONObject(BannerAbtConfig.moduleKey);
                if (MapUtil.isEmpty(abtMatcher)) {
                    return;
                }
                String abtGroup = abtMatcher.getStr(BannerAbtConfig.groupKey);
                String showBanner = abtMatcher.getStr(BannerAbtConfig.showBannerKey);
                if (!StringUtils.equals(BannerAbtConfig.hitGroup, abtGroup) || !StringUtils.equals(BannerAbtConfig.showBannerValue, showBanner)) {
                    if (log.isDebugEnabled()) {
                        log.debug("userId: {} abt not match", userId);
                    }
                    return;
                }
                bannerUrl = URLDecoder.decode(abtMatcher.getStr(BannerAbtConfig.bannerKey), StandardCharsets.UTF_8);
            } else {
                bannerUrl = netEarnLuxVipNacos.getBannerUrl();
            }
            luxuryAutoPayCashFilterBos.forEach(cashFilterBO -> {
                Map<String, Object> extendMap = cashFilterBO.getExtendMap();
                if (MapUtil.isEmpty(extendMap)) {
                    extendMap = new HashMap<>();
                    cashFilterBO.setExtendMap(extendMap);
                }
                extendMap.put("descImg", bannerUrl);
                extendMap.put("cashBackFlag", 1);
                cashBO.setShowCashBackBanner(1);
            });
        } catch (Exception e) {
            log.error("设置banner异常", e);
        }
    }

    /**
     * 设置拦截弹窗豪华VIP banner
     *
     * @param userId uid
     * @param cashBO cashBO
     */
    public void setInterceptBanner(String userId, String platform, CashBO cashBO, String payDeskSign) {
        try {
            if (BooleanUtils.isNotTrue(preSetBannerCheck(userId, payDeskSign, platform, cashBO))) {
                return;
            }
            // 对应的档位信息 强转因为 CashFilterBo 使用了 @JsonSerialize(using = FilterBOHandle.FilterSerializer.class) json 反序列化无法将属性全部设置进来
            @SuppressWarnings("unchecked")
            List<CashFilterBO> cashFilterBOS = (List<CashFilterBO>) cashBO.getList();
            if (CollUtil.isEmpty(cashFilterBOS)) {
                log.error("userId: {} cashFilterBOS 为空", userId);
                return;
            }
            List<CashFilterBO> luxuryAutoPayCashFilterBos = cashFilterBOS.stream()
                    .filter(Objects::nonNull)
                    .filter(filterLuxuryMonthAutoPay())
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(luxuryAutoPayCashFilterBos)) {
                return;
            }

            luxuryAutoPayCashFilterBos = luxuryAutoPayCashFilterBos.stream().filter(filterPriceAndDescImg(userId)).collect(Collectors.toList());
            if (CollUtil.isEmpty(luxuryAutoPayCashFilterBos)) {
                return;
            }

            String bannerUrl;
            if (Objects.equals(NetEarnConfig.UseAbt.yes, netEarnLuxVipNacos.getUseAbt())) {
                JSONObject abMatcher = AbtUtil.getAbtMatcher(userId, "", "ar", BannerAbtConfig.channel, BannerAbtConfig.moduleKey)
                        .getJSONObject(BannerAbtConfig.moduleKey);
                if (MapUtil.isEmpty(abMatcher)) {
                    return;
                }
                String group = abMatcher.getStr(BannerAbtConfig.groupKey);
                String showBanner = abMatcher.getStr(BannerAbtConfig.showBannerKey);
                if (!StringUtils.equals(BannerAbtConfig.hitGroup, group) || !StringUtils.equals(BannerAbtConfig.showBannerValue, showBanner)) {
                    return;
                }
                bannerUrl = URLDecoder.decode(abMatcher.getStr(BannerAbtConfig.bannerKey), StandardCharsets.UTF_8);
            } else {
                bannerUrl = netEarnLuxVipNacos.getBannerUrl();
            }
            luxuryAutoPayCashFilterBos.forEach(cashFilterBO -> {
                Map<String, Object> extendMap = cashFilterBO.getExtendMap();
                if (MapUtil.isEmpty(extendMap)) {
                    extendMap = new HashMap<>();
                    cashFilterBO.setExtendMap(extendMap);
                }
                extendMap.put("descImg", bannerUrl);
                extendMap.put("cashBackFlag", 1);
            });
        } catch (Exception e) {
            log.error("设置拦截弹窗banner异常", e);
        }

    }

    public Boolean showVipCashBackBanner(Object mainGear) {
        if (!(mainGear instanceof CashBO)) {
            return false;
        }
        CashBO cashBO = (CashBO) mainGear;
        return Objects.equals(1, cashBO.getShowCashBackBanner());
    }
    /**
     * 设置banner 前校验参数，以及档位环境
     *
     * @param userId      userId
     * @param payDeskSign 收银台标识
     * @param platform    平台
     * @param cashBO      档位信息
     * @return bool
     */
    private Boolean preSetBannerCheck(String userId, String payDeskSign, String platform, CashBO cashBO) {
        if (!NetEarnConfig.payDeskSigns.contains(payDeskSign)) {
            if (log.isDebugEnabled()) {
                log.debug("userId: {}, payDeskSign: {} 不满足", userId, payDeskSign);
            }
            return false;
        }
        if (Objects.equals(NetEarnConfig.ActStart.no, netEarnLuxVipNacos.getStart())) {
            return false;
        }
        if (CollUtil.isNotEmpty(netEarnLuxVipNacos.getTestAccount()) && !netEarnLuxVipNacos.getTestAccount().contains(userId)) {
            return false;
        }
        if (!StrUtil.equalsAnyIgnoreCase(platform, "ar", "android", "an")) {
            return false;
        }
        if (Objects.isNull(cashBO)) {
            log.warn("userId:{} cashBo is null", userId);
            return false;
        }
        if (BooleanUtils.isTrue(inActivity(userId, platform))) {
            if (log.isDebugEnabled()) {
                log.debug("userId: {} 在活动中，不设置", userId);
            }
            return false;
        }
        Object cashBOData = cashBO.getData();
        if (Objects.isNull(cashBOData)) {
            cashBOData = cashBO.getList();
        }
        if (Objects.isNull(cashBOData) || !(cashBOData instanceof List)) {
            log.info("userId: {} cashBO.getList() not list , list：{}", userId, cashBOData);
            return false;
        }
        return true;
    }

    /**
     * 过滤豪华VIP连包月卡
     *
     * @return predict
     */
    private Predicate<CashFilterBO> filterLuxuryMonthAutoPay() {
        return cashFilterBO -> StringUtils.equals(VipTypeEnum.VIP_LUXURY.getType(), cashFilterBO.getVipType()) &&
                Objects.equals(NetEarnConfig.PriceReference.monthGearFlag, cashFilterBO.getMonth()) &&
                Objects.equals(NetEarnConfig.PriceReference.autoPayFlag, cashFilterBO.getAutoPay()) &&
                // 联合会员的src 包含union 也是月卡
                !StringUtils.contains(cashFilterBO.getSrc(), NetEarnConfig.PriceReference.unionMonthGearStr);
    }

    /**
     * 过滤价格和图片
     *
     * @param userId uid
     * @return predict
     */
    private Predicate<CashFilterBO> filterPriceAndDescImg(String userId) {
        return cashFilterBO -> {
            BigDecimal price = cashFilterBO.getPrice();
            if (price.intValue() < NetEarnConfig.PriceReference.lowPriceLine) {
                if (log.isDebugEnabled()) {
                    log.debug("userId: {} luxury auto month price is {} , lower than {}", userId, price.intValue(), NetEarnConfig.PriceReference.lowPriceLine);
                }
                return false;
            }
            Map<String, Object> extendMap = cashFilterBO.getExtendMap();
            if (MapUtil.isEmpty(extendMap)) {
                if (log.isDebugEnabled()) {
                    log.debug("userId: {} extendMap is empty", userId);
                }
                return false;
            }
            Object descImg = extendMap.get("descImg");
            if (Objects.isNull(descImg) || StringUtils.isBlank(descImg.toString())) {
                return true;
            }
            if (log.isDebugEnabled()) {
                log.debug("userId: {} descImg is not blank", userId);
            }
            return false;
        };
    }

    /**
     * 调用会员中心接口获取豪华VIP档位
     *
     * @param cashVO cashVO
     * @return cashVipTypeJSONObj
     */
    private com.alibaba.fastjson.JSONObject getLuxuryTabGears(CashVO cashVO) {
        cashVO.setVipType(VipTypeEnum.VIP_LUXURY.getType());
        String platform = cashVO.getPlatform();
        if (StrUtil.equalsAnyIgnoreCase(platform, "ar", "android", "an")) {
            cashVO.setPlatform("ar");
        }
        JSONObject cashVo2JSONObj = JSONUtil.parseObj(cashVO);
        if (MapUtil.isEmpty(cashVo2JSONObj)) {
            log.warn("userId: {} 为传递参数", cashVO.getUid());
            return null;
        }
        UrlBuilder urlBuilder = UrlBuilder.of(vipCenterTypeUrl);
        cashVo2JSONObj.forEach(urlBuilder::addQuery);
        String body = HttpUtil.get(urlBuilder.build(), 5000);
        log.info("getLuxuryTabGears url={} body={}",urlBuilder.build(),body);
        if (StringUtils.isBlank(body) || !JSONUtil.isTypeJSON(body)) {
            log.warn("userId: {} 会员中心档位接口 返回空", cashVO.getUid());
            return null;
        }
        com.alibaba.fastjson.JSONObject bodyJSONObj = JSON.parseObject(body);
        if (MapUtil.isEmpty(bodyJSONObj)) {
            log.warn("userId: {} bodyJSONObj is empty", cashVO.getUid());
            return null;
        }

        if (!Objects.equals(200, bodyJSONObj.getInteger("code"))) {
            log.warn("userId: {} 会员中心档位返回异常", cashVO.getUid());
            return null;
        }

        return bodyJSONObj.getJSONObject("data");
    }

    /**
     * 根据档位获取用户活动档位
     *
     * @param userId            UID
     * @param httpVipCenterData HTTP调用的数据
     * @return 用户活动档位  com.alibaba.fastjson.JSONObject 返回的时候可以序列化， cn.hutool.json.JSONObject 这个不可以序列化
     */
    private com.alibaba.fastjson.JSONObject getActivityGear(String userId, com.alibaba.fastjson.JSONObject httpVipCenterData) {
        if (MapUtil.isEmpty(httpVipCenterData)) {
            log.warn("userId: {} httpVipCenterData is empty", userId);
            return null;
        }
        com.alibaba.fastjson.JSONObject cashVipTypeJSONObj = httpVipCenterData.getJSONArray("data").getJSONObject(0);
        if (MapUtil.isEmpty(cashVipTypeJSONObj)) {
            log.warn("userId: {} cashVipTypeJSONObj is empty", userId);
            return null;
        }
        com.alibaba.fastjson.JSONArray vipCenterGearsJSONArr = cashVipTypeJSONObj.getJSONArray("list");
        if (CollUtil.isEmpty(vipCenterGearsJSONArr)) {
            log.warn("userId: {} vipCenterGearsJSONArr is empty", userId);
            return null;
        }
        List<com.alibaba.fastjson.JSONObject> vipCenterGears = vipCenterGearsJSONArr.toJavaList(com.alibaba.fastjson.JSONObject.class);
        vipCenterGears.removeIf(vipGearJSONObj -> {
            if (MapUtil.isEmpty(vipGearJSONObj)) {
                return true;
            }
            // 过滤 非豪华VIP
            if (!StringUtils.equals(VipTypeEnum.VIP_LUXURY.getType(), vipGearJSONObj.getString("vipType"))) {
                return true;
            }
            // 过滤非 月卡
            if (!Objects.equals(1, vipGearJSONObj.getInteger("month"))) {
                return true;
            }
            // 过滤paySrc 捆绑超会
            if (Objects.nonNull(vipGearJSONObj.getInteger("paySrc"))) {
                return true;
            }
            String src = vipGearJSONObj.getString("src");
            if (StringUtils.isBlank(src)) {
                return true;
            }
            return src.contains(NetEarnConfig.PriceReference.unionMonthGearStr);
        });

        if (CollUtil.isEmpty(vipCenterGears)) {
            log.warn("userId: {} vipCenterGearsJSONArr is empty", userId);
            return null;
        }

        com.alibaba.fastjson.JSONObject luxuryMonthGear = vipCenterGears.get(0);
        if (MapUtil.isEmpty(luxuryMonthGear)) {
            log.warn("userId: {} luxuryMonthGear is empty", userId);
            return null;
        }
        // 校验价格
        BigDecimal price = luxuryMonthGear.getBigDecimal("price");
        if (price.intValue() < NetEarnConfig.PriceReference.lowPriceLine) {
            log.info("userId: {} vip center price is {} lower than {}, and set it to {}", userId, price.intValue(),
                    NetEarnConfig.PriceReference.lowPriceLine, NetEarnConfig.PriceReference.priceIfLessLine);
            luxuryMonthGear.put("price", NetEarnConfig.PriceReference.priceIfLessLine);
            luxuryMonthGear.put("src", NetEarnConfig.PriceReference.lowPriceSrc);
        }

        return luxuryMonthGear;
    }

    /**
     * 构建订单类
     *
     * @param redisHash redisHash
     * @return NetEarnConfig.OrderData
     */
    private NetEarnConfig.OrderData buildOrderData(Map<Object, Object> redisHash) {
        if (MapUtil.isEmpty(redisHash)) {
            throw new IllegalArgumentException("redisHash is empty");
        }
        NetEarnConfig.OrderData orderData = new NetEarnConfig.OrderData();
        orderData.setOrderId(Convert.toLong(redisHash.get("orderId")));
        orderData.setPrice(Convert.toBigDecimal(redisHash.get("price")));
        orderData.setStartTime(Convert.toLong(redisHash.get("startTime"), 0L));
        orderData.setExpireTime(Convert.toLong(redisHash.get("expireTime"), 0L));
        return orderData;
    }

    /**
     * 构造活动页档位
     *
     * @param cashVO cashVo
     * @return 档位
     */
    private LuxuryCashBackVo getActivityPageGear(CashVO cashVO) {
        com.alibaba.fastjson.JSONObject luxuryTabGears = getLuxuryTabGears(cashVO);
        com.alibaba.fastjson.JSONObject activityGear = getActivityGear(cashVO.getUid(), luxuryTabGears);
        if (MapUtil.isEmpty(activityGear)) {
            throw new IllegalArgumentException("activityGear is empty");
        }
        return LuxuryCashBackVo.builder()
                .showType(1)
                .showData(activityGear)
                .desc("档位")
                .build();
    }

    /**
     * 构造网赚签到数据
     *
     * @param orderData 订单数据
     * @return 签到数据
     */
    private LuxuryCashBackVo getNetEarnSignData(NetEarnConfig.OrderData orderData) {
        return LuxuryCashBackVo.builder()
                .showType(2)
                .showData(new HashMap<String, Object>() {{
                    put("totalPrice", orderData.getPrice());
                    put("startDate", LocalDateTimeUtil.formatNormal(
                            LocalDateTime.ofInstant(Instant.ofEpochMilli(orderData.getStartTime()), ZoneId.systemDefault()).toLocalDate()
                    ));
                }})
                .desc("打卡")
                .build();
    }

    /**
     * 获取续费订单数据
     *
     * @param cashVO        cashVo
     * @param currClientMap 当前redis数据
     * @param client        client
     * @return luxuryCashBackVo
     */
    private LuxuryCashBackVo getNextOrderGear(CashVO cashVO, RMap<Object, Object> currClientMap, RedissonClient client) {
        String userId = cashVO.getUid();
        // 查询续费订单数据，续费可能延时，也可能提前
        String redisNextKey = String.format(NetEarnConfig.RedisReference.nextKeyPattern, userId);
        RMap<Object, Object> nextClientMap = client.getMap(redisNextKey, StringCodec.INSTANCE);
        Map<Object, Object> nextOrderMap = nextClientMap.readAllMap();
        // 无续费订单
        if (MapUtil.isEmpty(nextOrderMap)) {
            log.info("userId: {} 当前无正在使用的订单，且无续费订单，返回档位", userId);
            return getActivityPageGear(cashVO);
        }

        NetEarnConfig.OrderData nextOrderData = buildOrderData(nextOrderMap);
        long currentTimeMillis = System.currentTimeMillis();
        // 判断当前续费订单是否生效(vip正在使用中)
        if (currentTimeMillis < nextOrderData.getStartTime()) {
            log.info("userId: {} 续费订单未生效, 返回活动单位", userId);
            return getActivityPageGear(cashVO);
        }
        if (currentTimeMillis > nextOrderData.getExpireTime()) {
            log.info("userId: {} 续费订单已过期，返回活动档位", userId);
            return getActivityPageGear(cashVO);
        }
        log.info("userId: {} 续费订单生效，返回打卡签到数据", userId);
        currClientMap.putAll(nextOrderMap);
        currClientMap.expire(NetEarnConfig.RedisReference.expireDays, TimeUnit.DAYS);
        nextClientMap.delete();
        return getNetEarnSignData(nextOrderData);
    }

}
