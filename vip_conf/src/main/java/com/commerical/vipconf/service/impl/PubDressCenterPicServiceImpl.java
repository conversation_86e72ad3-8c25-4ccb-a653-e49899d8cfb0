package com.commerical.vipconf.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.commerical.vipconf.domain.DressCenterBanner;
import com.commerical.vipconf.domain.DressCenterPic;
import com.commerical.vipconf.domain.PubDressCenterPic;
import com.commerical.vipconf.mapper.PubDressCenterPicMapper;
import com.commerical.vipconf.service.DressCenterPicService;
import com.commerical.vipconf.service.PubDressCenterPicService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kuwo.commercialization.common.message.SystemCodeErrorConstant;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <p>
 * 桌面图标配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-29
 */
@Service
public class PubDressCenterPicServiceImpl extends ServiceImpl<PubDressCenterPicMapper, PubDressCenterPic> implements PubDressCenterPicService {

    @Autowired
    private PubDressCenterPicMapper pubDressCentePicMapper;

    @Autowired
    private DressCenterPicService dressCenterPicService;

    /**
     * 添加/修改
     *
     * @param
     * @return
     */
    @Override
    public SystemCodeErrorConstant pubDressCenterPic(String code) {
        DressCenterPic dressCenterPic=dressCenterPicService.getDressCenterPicInfoByCode(code);
        if(dressCenterPic!=null&&dressCenterPic.getIsPub()!=null&&dressCenterPic.getIsPub()==1){
            return SystemCodeErrorConstant.PUB_ERROR;
        }
        PubDressCenterPic pubDressCenterPic= this.getDressCenterPicInfoByCode(code);
        if(pubDressCenterPic==null){
            PubDressCenterPic pubDressCenter=PubDressCenterPic.builder().build();
            BeanUtils.copyProperties(dressCenterPic, pubDressCenter);
            pubDressCenter.setCreateTime(new Date());
            pubDressCenter.setUpdateTime(new Date());
            this.save(pubDressCenter);
        }else{
            // 修改
            LambdaUpdateWrapper<PubDressCenterPic> wrapper= Wrappers.lambdaUpdate();
            wrapper.set(PubDressCenterPic::getName,dressCenterPic.getName())
                    .set(PubDressCenterPic::getAppPicName,dressCenterPic.getAppPicName())
                    .set(PubDressCenterPic::getPic,dressCenterPic.getPic())
                    .set(PubDressCenterPic::getInitNum,dressCenterPic.getInitNum())
                    .set(PubDressCenterPic::getAddNum,dressCenterPic.getAddNum())
                    .set(PubDressCenterPic::getSubNum,dressCenterPic.getSubNum())
                    .set(PubDressCenterPic::getDescDetail,dressCenterPic.getDescDetail())
                    .set(PubDressCenterPic::getIsDelete,dressCenterPic.getIsDelete())
                    .set(PubDressCenterPic::getSort,dressCenterPic.getSort())
                    .set(PubDressCenterPic::getUpdateTime,new Date())
                    .set(PubDressCenterPic::getEditorName,dressCenterPic.getEditorName())
                    .set(PubDressCenterPic::getLimitType,dressCenterPic.getLimitType())
                    .set(PubDressCenterPic::getIsShowNew,dressCenterPic.getIsShowNew())
                    .eq(PubDressCenterPic::getCode,dressCenterPic.getCode());
            this.update(wrapper);

        }
        dressCenterPicService.lambdaUpdate().set(DressCenterPic::getIsPub,1).eq(DressCenterPic::getCode,code).update();
        return  SystemCodeErrorConstant.SUCCESS;
    }

    @Override
    public int deleteDressCenterPicByCode(String code) {
        LambdaQueryWrapper<PubDressCenterPic> wrapper= Wrappers.lambdaQuery();
        wrapper.eq(PubDressCenterPic::getCode,code);
        return this.getBaseMapper().delete(wrapper);
    }

    /**
     * 装扮中心Pic详情获取
     *
     * @param code code
     * @return PubDressCenterPic 详情
     */
    public PubDressCenterPic getDressCenterPicInfoByCode(String code) {
        LambdaQueryWrapper<PubDressCenterPic> wrapper= Wrappers.lambdaQuery();
        wrapper.eq(PubDressCenterPic::getCode,code);
        return pubDressCentePicMapper.selectOne(wrapper);
    }

}
