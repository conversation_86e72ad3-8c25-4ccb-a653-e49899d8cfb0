package com.commerical.vipconf.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.commerical.vipconf.config.constant.DressCenterConstant;
import com.commerical.vipconf.domain.DressCenterBanner;
import com.commerical.vipconf.domain.DressCenterToptab;
import com.commerical.vipconf.domain.vo.DressCenterToptabQueryVO;
import com.commerical.vipconf.domain.vo.DressCenterToptabVO;
import com.commerical.vipconf.mapper.DressCenterToptabMapper;
import com.commerical.vipconf.service.DressCenterToptabService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.commerical.vipconf.service.PubDressCenterToptabService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <p>
 * 装扮中心顶部导航栏配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-23
 */
@Service
public class DressCenterToptabServiceImpl extends ServiceImpl<DressCenterToptabMapper, DressCenterToptab> implements DressCenterToptabService {

    @Autowired
    private DressCenterToptabMapper dressCenterToptabMapper;

    @Autowired
    private PubDressCenterToptabService pubDressCenterToptabService;

    /**
     * 添加/修改
     *
     * @param dressCenterToptabVO
     * @return
     */
    @Override
    public DressCenterToptab addOrUpdateDressCenterToptab(DressCenterToptabVO dressCenterToptabVO) {
        DressCenterToptab dressCenterToptab= DressCenterToptab.builder().build();
        BeanUtils.copyProperties(dressCenterToptabVO, dressCenterToptab);
        dressCenterToptab.setUpdateTime(new Date());
        String name= DressCenterConstant.userLocal.get();
        // id为空或者code为空表示添加数据操作
        if(StringUtils.isBlank(dressCenterToptabVO.getCode())){
            dressCenterToptab.setCreateTime(new Date());
            dressCenterToptab.setIsDelete(1);
            dressCenterToptab.setCode(StringUtils.isBlank(dressCenterToptabVO.getCode())? IdUtil.simpleUUID():dressCenterToptabVO.getCode());
            dressCenterToptab.setCreateName(name);
            dressCenterToptab.setEditorName(name);
            dressCenterToptab.setIsPub(0);
            this.save(dressCenterToptab);
        }else{
            // 修改
            LambdaUpdateWrapper<DressCenterToptab> wrapper= Wrappers.lambdaUpdate();
            wrapper.set(dressCenterToptabVO.getName()!=null,DressCenterToptab::getName,dressCenterToptabVO.getName())
                    .set(dressCenterToptabVO.getDescDetail()!=null,DressCenterToptab::getDescDetail,dressCenterToptabVO.getDescDetail())
                    .set(dressCenterToptabVO.getIcon()!=null,DressCenterToptab::getIcon,dressCenterToptabVO.getIcon())
                    .set(dressCenterToptabVO.getSort()!=null,DressCenterToptab::getSort,dressCenterToptabVO.getSort())
                    .set(dressCenterToptabVO.getActiveIcon()!=null,DressCenterToptab::getActiveIcon,dressCenterToptabVO.getActiveIcon())
                    .set(dressCenterToptabVO.getNewIcon()!=null,DressCenterToptab::getNewIcon,dressCenterToptabVO.getNewIcon())
                    .set(dressCenterToptabVO.getType()!=null,DressCenterToptab::getType,dressCenterToptabVO.getType())
                    .set(dressCenterToptabVO.getPlatform()!=null,DressCenterToptab::getPlatform,dressCenterToptabVO.getPlatform())
                    .set(DressCenterToptab::getIsPub,0)
                    .set(DressCenterToptab::getUpdateTime,new Date())
                    .set(StringUtils.isNotBlank(name),DressCenterToptab::getEditorName,name)
                    .eq(DressCenterToptab::getCode,dressCenterToptabVO.getCode());
            this.update(wrapper);
        }
        return dressCenterToptab;
    }

    /**
     * 装扮中心删除
     *
     * @param id 主键id
     * @param isTrue true为真删除，false 逻辑删除
     * @param isDelete 删除与参数 1/0
     */
    @Override
    public void deleteDressCenterToptabById(Integer id, Boolean isTrue, Integer isDelete) {
        if(id==null||id==0){
            log.error("DressCenterToptab:deleteDressCenterToptabById id is empty!");
            return;
        }
        String name= DressCenterConstant.userLocal.get();
        if(isTrue){
            DressCenterToptab dressCenterToptab= this.getById(id);
            dressCenterToptabMapper.deleteById(id);
            pubDressCenterToptabService.deleteDressCenterToptabByCode(dressCenterToptab.getCode());
        }else{
            LambdaUpdateWrapper<DressCenterToptab> wrapper= Wrappers.lambdaUpdate();
            wrapper.set(DressCenterToptab::getIsDelete,isDelete)
                    .set(DressCenterToptab::getIsPub,0)
                    .set(DressCenterToptab::getCreateName,name)
                    .eq(DressCenterToptab::getId,id);
            this.update(wrapper);
        }
    }

    /**
     * 查询列表
     *
     * @param dressCenterToptabQueryVO 查询参数
     * @return 查询列表
     */
    @Override
    public IPage<DressCenterToptab> getDressCenterToptabList(DressCenterToptabQueryVO dressCenterToptabQueryVO) {
        IPage<DressCenterToptab> page = new Page<>(dressCenterToptabQueryVO.getPage(), dressCenterToptabQueryVO.getPageSize());
        LambdaQueryWrapper<DressCenterToptab> wrapper= Wrappers.lambdaQuery();
        wrapper.eq(ObjectUtils.isNotEmpty(dressCenterToptabQueryVO.getEditorName()),DressCenterToptab::getEditorName,dressCenterToptabQueryVO.getEditorName())
                .like(ObjectUtils.isNotEmpty(dressCenterToptabQueryVO.getName()),DressCenterToptab::getName,dressCenterToptabQueryVO.getName())
                .eq(ObjectUtils.isNotEmpty(dressCenterToptabQueryVO.getType()),DressCenterToptab::getType,dressCenterToptabQueryVO.getType())
                .eq(ObjectUtils.isNotEmpty(dressCenterToptabQueryVO.getIsDelete()),DressCenterToptab::getIsDelete,dressCenterToptabQueryVO.getIsDelete())
                .orderByAsc(DressCenterToptab::getIsDelete)
                .orderByDesc(DressCenterToptab::getSort);
        return dressCenterToptabMapper.selectPage(page,wrapper);
    }

    /**
     * 装扮中心banner详情获取
     *
     * @param id 主键id
     * @return banner详情
     */
    @Override
    public DressCenterToptab getDressCenterToptabInfoById(Integer id) {
        return this.getById(id);
    }

    /**
     * 查询info
     *
     * @param code
     * @return
     */
    @Override
    public DressCenterToptab getDressCenterToptabInfoByCode(String code) {
        LambdaQueryWrapper<DressCenterToptab> wrapper= Wrappers.lambdaQuery();
        wrapper.eq(DressCenterToptab::getCode,code);
        return this.baseMapper.selectOne(wrapper);
    }
}
