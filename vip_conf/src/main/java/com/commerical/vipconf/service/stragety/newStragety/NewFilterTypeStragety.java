package com.commerical.vipconf.service.stragety.newStragety;

import com.commerical.abserviceapi.domain.UserInfoBO;
import com.commerical.vipconf.domain.Filter;
import com.commerical.vipconf.domain.Gear;
import com.commerical.vipconf.domain.bo.ABDataBO;
import com.commerical.vipconf.domain.bo.FreeDataBO;
import com.commerical.vipconf.domain.bo.GearBO;
import com.commerical.vipconf.domain.bo.PayDataBO;
import com.commerical.vipconf.domain.vo.PriceGearVO;

import java.util.List;

public  interface NewFilterTypeStragety {

    /**
     * 普通filter过滤
     */
     void parseFilter(Filter filter, Gear gear, GearBO gearBO, PriceGearVO priceGearVO, List<ABDataBO> adDataList, List<PayDataBO> payDataList, List<FreeDataBO> freeDataList, String vipType, UserInfoBO userInfoBO);

}
