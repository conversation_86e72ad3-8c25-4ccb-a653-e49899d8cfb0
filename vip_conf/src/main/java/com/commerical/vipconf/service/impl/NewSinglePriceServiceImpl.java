package com.commerical.vipconf.service.impl;

import cn.hutool.crypto.digest.MD5;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.commercal.uiservice.enity.BasicUInfo;
import com.commercal.uiservice.enity.SignStateInfo;
import com.commercal.uiservice.enity.VipInfo;
import com.commercal.uiservice.service.UiInfoService;
import com.commerical.abserviceapi.domain.UserInfoBO;
import com.commerical.abserviceapi.req.AbRequest;
import com.commerical.abserviceapi.resp.AbResponse;
import com.commerical.abserviceapi.service.AbRuleService;
import com.commerical.vipconf.config.cache.CacheAdapter;
import com.commerical.vipconf.config.exception.ServiceException;
import com.commerical.vipconf.config.redis.DynamicChoiceRedissonClient;
import com.commerical.vipconf.domain.*;
import com.commerical.vipconf.domain.bo.*;
import com.commerical.vipconf.domain.context.SingleContext;
import com.commerical.vipconf.domain.remote.AudioConfigResponse;
import com.commerical.vipconf.domain.vo.CashVO;
import com.commerical.vipconf.domain.vo.PriceGearVO;
import com.commerical.vipconf.domain.vo.SinglePriceGearVO;
import com.commerical.vipconf.domain.vo.SingleSVIPInfoVO;
import com.commerical.vipconf.enums.GearTypeEnum;
import com.commerical.vipconf.enums.SingleTypeEnum;
import com.commerical.vipconf.enums.VipTypeEnum;
import com.commerical.vipconf.mapper.FilterMapper;
import com.commerical.vipconf.nacos.VipConfConfigNacos;
import com.commerical.vipconf.service.GearService;
import com.commerical.vipconf.service.NewSinglePriceService;
import com.commerical.vipconf.service.PayDeskService;
import com.commerical.vipconf.service.PriceValidateService;
import com.commerical.vipconf.util.*;
import com.google.common.util.concurrent.ListeningExecutorService;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.kuwo.commercialization.common.message.SystemCodeErrorConstant;
import com.kuwo.commercialization.common.utill.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import reactor.util.function.Tuples;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class NewSinglePriceServiceImpl implements NewSinglePriceService {

    @Autowired
    private NewFilterServiceImpl newFilterService;

    @Autowired
    private PayDeskService payDeskService;

    @Autowired
    private GearService gearService;

    @Autowired
    private FilterMapper filterMapper;

    @DubboReference
    private AbRuleService abRuleService;

    @Autowired
    private HttpServletRequest request;

    @Autowired
    private PriceValidateService priceValidateService;

    @DubboReference(timeout = 1000)
    private UiInfoService uiInfoService;

    @Autowired
    private CarMonitor carMonitor;

    @Value("${vip.domain}")
    private String vipDomain;

    @Autowired
    private CashServiceImpl cashService;

    @Autowired
    private CacheUtils cacheUtils;

    @Autowired
    private VipConfConfigNacos vipConfConfigNacos;

    @Autowired
    private InvokeHttpForUserInfo invokeHttpForUserInfo;

    @Autowired
    private CalculeteUpgradePrice calculeteUpgradePrice;

    @Autowired
    private InvokingRemoteUtil invokingRemoteUtil;

    @Autowired
    private DynamicChoiceRedissonClient dynamicChoiceRedissonClient;

    @Autowired
    private HttpServletRequest httpServletRequest;

    @Autowired
    private SinglePriceServiceImpl singlePriceServiceImpl;

    @Autowired
    private CacheAdapter cacheAdapter;

    private static ThreadPoolExecutor executor = new ThreadPoolExecutor(5, 10, 10L, TimeUnit.SECONDS, new ArrayBlockingQueue<>(100), new ThreadFactoryBuilder().setNameFormat("-vip_conf-%d").build());
    private static ListeningExecutorService listeningExecutor = MoreExecutors.listeningDecorator(executor);

    /**
     * 获取档位价格(连包月卡豪华VIP)
     *
     * @param priceGearVO 请求参数
     * @return
     * @throws ServiceException
     */
    @Override
    public Object getSinglePriceInfo(SinglePriceGearVO priceGearVO) throws ServiceException {
        try{
            // 1 参数验证
            if(priceGearVO.getPlatform().equals("an")){
                priceGearVO.setPlatform("ar");
            }
            if(priceGearVO.getPlatform().equals("ios")&&(StringUtils.isBlank(priceGearVO.getDeviceId())
                    ||StringUtils.equals(priceGearVO.getDeviceId(),"0")||
                    StringUtils.equals(priceGearVO.getDeviceId(),"null"))){
                return "";
            }
            String payDeskSign=priceGearVO.getSinglePayDeskSign();
            String  paySign =invokeHttpForUserInfo.invokerAllUserPayDesk(priceGearVO.getUserId(),payDeskSign);
            priceGearVO.setPayDeskSign(paySign);
            // 2.收银台信息获取
            PayDesk payDesk =cacheUtils.getPayDeskCache.get(priceGearVO.getSinglePayDeskSign());
            if (ObjectUtils.isNull(payDesk)) {
                log.error("paydesk: getSinglePriceInfo payDesk is empty! payDeskId={}", payDesk.getId());
                throw new ServiceException(SystemCodeErrorConstant.PAY_DESK_EMPTY);
            }
            UserInfoBO userInfoBO=new UserInfoBO();
            SingleContext singleContext=new SingleContext();
            invokeSingleUserInfo(priceGearVO, userInfoBO, singleContext);
            // 5.（豪华、超会）单挡位价格
            if (priceGearVO.getType()==SingleTypeEnum.LUX_SVIP.getType()) {
                return parseActivityH5SingleGearPrice(payDesk, priceGearVO,singleContext.getAutoPay(),userInfoBO);
            }
            // 6.豪华 超会
            Gear gear=cashService.getLuxLowGear(payDesk.getId(),priceGearVO.getPlatform(), singleContext.getAutoPay()==1,VipTypeEnum.VIP_LUXURY.getType());
//            if(StringUtils.isBlank(priceGearVO.getPayDeskSign())){
//                priceGearVO.setPayDeskSign("limitpop");
//            }
            List<Filter> filters = invokeValidFilter(priceGearVO, Collections.singletonList(gear),userInfoBO);
            if (CollectionUtils.isEmpty(filters)) {
                log.error("paydesk: getSinglePriceInfo filters is empty,priceGearVO={}",JSONUtil.toJsonStr(priceGearVO));
                throw new ServiceException(SystemCodeErrorConstant.FILTER_EMPTY);
            }
            Collections.sort(filters);
            // 7.挡位获取
            if (priceGearVO.getType()==SingleTypeEnum.LUX.getType()) {
                return parseH5SingleGearPrice(filters, payDesk, gear, priceGearVO,singleContext.getAutoPay());
            }
            // 8.低价
            return parseFilterPrice(gear,filters,priceGearVO);
        }catch (Exception e){
            log.error("getSinglePriceInfo has error!",e);
        }
        return "";
    }


    @Override
    public  Map<String,String> getSingleLuxSVIPPrice(SinglePriceGearVO priceGearVO)  {
        Map<String,String> resultMap = new HashMap<>();
        try{
            // 1. 参数验证与规范化
            if (!validateAndNormalizeParams(priceGearVO)) {
                log.warn("getSingleLuxSVIPPrice parameter validation failed, userId={}, platform={}",
                        priceGearVO.getUserId(), priceGearVO.getPlatform());
                return resultMap;
            }
            // 2. 获取收银台信息并验证
            String payDeskSign = invokeHttpForUserInfo.invokerAllUserPayDesk(priceGearVO.getUserId(), "membercenter_cash");
            PayDesk payDesk = cacheAdapter.getPayDeskBySign(payDeskSign, priceGearVO.getUserId());
            if (ObjectUtils.isNull(payDesk)) {
                log.error("PayDesk not found for sign={}, userId={}", payDeskSign, priceGearVO.getUserId());
                return resultMap;
            }

            // 3. 获取用户信息和上下文
            UserInfoBO userInfoBO = new UserInfoBO();
            SingleContext singleContext = new SingleContext();
            invokeInterceptUserInfo(priceGearVO, userInfoBO, singleContext);

            // 4. 获取并处理档位信息
            Map<String, List<Filter>> filteredGearMap = getFilteredGearMap(payDesk, priceGearVO, singleContext, userInfoBO);
            if (filteredGearMap.isEmpty()) {
                log.error("No valid filters found, userId={}", priceGearVO.getUserId());
                return resultMap;
            }

            // 5. 解析各类型的价格
            processPriceResults(filteredGearMap, resultMap, singleContext, priceGearVO);

            // 6. 获取升级价格信息
            getUpgradePriceInfo(resultMap, priceGearVO, userInfoBO, payDeskSign);
        }catch (Exception e){

            log.error("getSingleLuxSVIPPrice error, userId={}, platform={} error={}", priceGearVO.getUserId(), priceGearVO.getPlatform(),   LogUtil.getExceptionStackTrace(e),e);
            carMonitor.luxSviplowPriceError.increment();
        }
        return resultMap;
    }


    /**
     * 验证并规范化请求参数
     * @param priceGearVO 价格参数对象
     * @return 验证是否通过
     */
    private boolean validateAndNormalizeParams(SinglePriceGearVO priceGearVO) {
        // 平台转换和标准化
        if ("an".equals(priceGearVO.getPlatform())) {
            priceGearVO.setPlatform("ar");
        }
//        if(StringUtils.equals(priceGearVO.getPlatform(),"ios")&&StringUtils.isNotBlank(priceGearVO.getDeviceId())&&priceGearVO.getDeviceId().contains(",")){
//            log.info("validateAndNormalizeParams priceGearVO={}",JSONUtil.toJsonStr(priceGearVO));
//            String[] deviceIds = priceGearVO.getDeviceId().split(",");
//            if (deviceIds.length > 0) {
//                priceGearVO.setDeviceId(deviceIds[0]); // 设置为第一个 deviceId
//            }
//            log.info("validateAndNormalizeParams deviceId={}",JSONUtil.toJsonStr(priceGearVO.getDeviceId()));
//        }

        // IOS平台需要有效的设备ID
        if ("ios".equals(priceGearVO.getPlatform()) &&
                (StringUtils.isBlank(priceGearVO.getDeviceId()) ||
                        "0".equals(priceGearVO.getDeviceId()) ||
                        "null".equals(priceGearVO.getDeviceId()))) {
            return false;
        }

        return true;
    }

    /**
     * 获取过滤后的档位Map
     * @return 按档位ID分组的Filter列表Map
     */
    private Map<String, List<Filter>> getFilteredGearMap(PayDesk payDesk, SinglePriceGearVO priceGearVO,
                                                         SingleContext singleContext, UserInfoBO userInfoBO) {
        Map<String, List<Filter>> result = new HashMap<>();

        try {
            // 获取低价档位列表
            List<Gear> gears = cacheAdapter.getAllLowGear(
                    payDesk.getId(),
                    priceGearVO.getPlatform(),
                    singleContext.getAutoPay() == 1,
                    priceGearVO.getUserId(),
                    singleContext.getCarAutoPay() == 1,
                    singleContext.getAdAutoPay() == 1
            );

            if (CollectionUtils.isEmpty(gears)) {
                log.warn("No gears found for payDeskId={}, platform={}",
                        payDesk.getId(), priceGearVO.getPlatform());
                return result;
            }
            // 车载挡位处理
          //  cacheAdapter.processGearForPayDeskSign(priceGearVO,  payDesk,  userInfoBO, gears);
            // 创建VIP类型到Gear ID的映射
            Map<String, Integer> vipTypeToGearIdMap = gears.stream()
                    .collect(Collectors.toMap(
                            Gear::getVipType,
                            Gear::getId,
                            (oldValue, newValue) -> newValue
                    ));

            if (vipTypeToGearIdMap.size() < 3) {
                log.warn("Insufficient gear types, expected 3, got {}", vipTypeToGearIdMap.size());
            }

            // 获取过滤后的filters
            List<Filter> filters = invokeValidFilter(priceGearVO, gears, userInfoBO);
            if (CollectionUtils.isEmpty(filters)) {
                return result;
            }

            // 按档位ID分组
            Map<Integer, List<Filter>> filtersByGearId = filters.stream()
                    .collect(Collectors.groupingBy(Filter::getGearId, LinkedHashMap::new, Collectors.toList()));

            // 获取各类型档位的filters并排序
            String[] vipTypes = {
                    VipTypeEnum.VIP_LUXURY.getType(),
                    VipTypeEnum.SUPER_VIP.getType(),
                    VipTypeEnum.VEHICLE_VIP.getType(),
                    VipTypeEnum.AD_VIP.getType()
            };

            for (String vipType : vipTypes) {
                Integer gearId = vipTypeToGearIdMap.get(vipType);
                if (gearId != null && filtersByGearId.containsKey(gearId)) {
                    List<Filter> typeFilters = filtersByGearId.get(gearId);
                    if (!CollectionUtils.isEmpty(typeFilters)) {
                        Collections.sort(typeFilters);
                        result.put(vipType, typeFilters);
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error getting filtered gear map", e);
        }

        return result;
    }

    /**
     * 处理各类型会员的价格结果
     */
    private void processPriceResults(Map<String, List<Filter>> filteredGearMap, Map<String, String> resultMap,
                                     SingleContext singleContext, SinglePriceGearVO priceGearVO) {
        // 处理豪华VIP价格
        if (filteredGearMap.containsKey(VipTypeEnum.VIP_LUXURY.getType())) {
            String lowLuxPrice = lowLuxParse(
                    filteredGearMap.get(VipTypeEnum.VIP_LUXURY.getType()),
                    singleContext.getAutoPay(),
                    priceGearVO
            );
            resultMap.put(VipTypeEnum.VIP_LUXURY.getType(), lowLuxPrice);
        }else{
        resultMap.put(VipTypeEnum.VIP_LUXURY.getType(), "");
        }

        // 处理超级会员价格
        if (filteredGearMap.containsKey(VipTypeEnum.SUPER_VIP.getType())) {
            String lowSvipPrice = lowSvipParse(
                    filteredGearMap.get(VipTypeEnum.SUPER_VIP.getType()),
                    singleContext.getAutoPay(),
                    priceGearVO
            );
            resultMap.put(VipTypeEnum.SUPER_VIP.getType(), lowSvipPrice);
        }else{
            resultMap.put(VipTypeEnum.SUPER_VIP.getType(), "");
        }

        // 处理车载会员价格
        if (filteredGearMap.containsKey(VipTypeEnum.VEHICLE_VIP.getType())) {
            String lowVehiclePrice = lowVehicleParse(
                    filteredGearMap.get(VipTypeEnum.VEHICLE_VIP.getType()),
                    singleContext.getAutoPay(),
                    priceGearVO
            );
            resultMap.put(VipTypeEnum.VEHICLE_VIP.getType(), lowVehiclePrice);
        }else{
            resultMap.put(VipTypeEnum.VEHICLE_VIP.getType(), "");
        }

        // 处理广告会员价格
        if (filteredGearMap.containsKey(VipTypeEnum.AD_VIP.getType())) {
            String lowAdPrice = lowAdParse(
                    filteredGearMap.get(VipTypeEnum.AD_VIP.getType()),
                    singleContext.getAdAutoPay(),
                    priceGearVO
            );
            resultMap.put(VipTypeEnum.AD_VIP.getType(), lowAdPrice);
        }else{
            resultMap.put(VipTypeEnum.AD_VIP.getType(), "");
        }
    }

    /**
     * 车载低价
     *
     * @param filters
     * @param autoPay
     * @param priceGearVO
     * @return
     */
    public String lowAdParse(List<Filter> filters,int autoPay,PriceGearVO priceGearVO){
        for(Filter filter: filters ){
            DecimalFormat decimalFormat = new DecimalFormat("###################.###########");
            String formatPrice=decimalFormat.format(filter.getPrice());
            if(filter.getPrice().compareTo(new BigDecimal(10))<=0){
                log.info("lowAdParse low price out show price,userId={},price={},plat={} filter={}", priceGearVO.getUserId(), filter.getPrice(),priceGearVO.getPlatform(),JSONUtil.toJsonStr(filter));
            }
            return formatPrice;
        }
        return "";
    }

    /**
     * 车载低价
     *
     * @param filters
     * @param autoPay
     * @param priceGearVO
     * @return
     */
    public String lowVehicleParse(List<Filter> filters,int autoPay,PriceGearVO priceGearVO){
        for(Filter filter: filters ){
            if(filter.getType()!=1){
                continue;
            }
            boolean filterCheckForIos = filterNewVehicleCheckForIos(filter,autoPay, priceGearVO);
            if (filterCheckForIos) {
                continue;
            }
            DecimalFormat decimalFormat = new DecimalFormat("###################.###########");
            String formatPrice=decimalFormat.format(filter.getPrice());
            if(filter.getPrice().compareTo(new BigDecimal(10))<=0){
               // log.info("lowVehicleParse low price out show price,userId={},price={},plat={} filter={}", priceGearVO.getUserId(), filter.getPrice(),priceGearVO.getPlatform(),JSONUtil.toJsonStr(filter));
            }
            return formatPrice;
        }
        return "";
    }

    /**
     * ios 车载购买判断
     *
     * @param filter
     * @param autoPay
     * @param priceGearVO
     * @return
     */
    public boolean filterNewVehicleCheckForIos(Filter filter, int autoPay,PriceGearVO priceGearVO) {
        try{
            if ("ios".equals(priceGearVO.getPlatform())&autoPay==0) {
                if (filter.getFilterFirstPay() != null
                        && filter.getFilterFirstPay().equals(1)
                        && !priceGearVO.getFirstVehiclePay() && filter.getOrderRank() != 1) {
                    return true;
                }
                if ((filter.getFilterFirstPay() == null
                        || !filter.getFilterFirstPay().equals(1))
                        && priceGearVO.getFirstVehiclePay() && filter.getOrderRank() != 1) {
                    return true;
                }
                if (filter.getFilterFirstRenew() != null
                        && (filter.getFilterFirstRenew().equals(1)
                        || filter.getFilterFirstRenew().equals(4)
                        || filter.getFilterFirstRenew().equals(5))
                        && !priceGearVO.getVehicleFirstRenewal() && filter.getOrderRank() != 1) {
                    return true;
                }
            }
        }catch (Exception e){
            log.error("filterNewVehicleCheckForIos error! filter={} priceGearVO={}",JSONUtil.toJsonStr(filter),JSONUtil.toJsonStr(priceGearVO), e);
        }
        return false;
    }

    /**
     * 获取升级价格信息
     */
    private void getUpgradePriceInfo(Map<String, String> resultMap, SinglePriceGearVO priceGearVO,
                                     UserInfoBO userInfoBO, String payDeskSign) {
        try {
            // 创建CashVO对象准备计算升级价格
            CashVO cashVO = new CashVO();
            cashVO.setUid(priceGearVO.getUserId());
            cashVO.setPlatform(priceGearVO.getPlatform());
            cashVO.setPayDeskSign(payDeskSign);
            cashVO.setDeviceId(priceGearVO.getDeviceId());
            cashVO.setIsOFFAutoPay(priceGearVO.getIsOFFAutoPay());

            // 计算三种升级价格
            CashFilterBO cashFilterBO= calculeteUpgradePrice.calculateUpgradeSvipPriceCache(priceGearVO.getUserId(), priceGearVO.getPlatform(), userInfoBO, "limitpop", cashVO,"lowPrice");

            CashFilterBO cashMusicBO =calculeteUpgradePrice.calculateUpgradeMusicPrice(cashVO, userInfoBO);

            CashFilterBO cashCarBO =calculeteUpgradePrice.checkCarToSVIP(priceGearVO.getUserId(), userInfoBO, priceGearVO.getPlatform(), cashVO,null);


            DecimalFormat decimalFormat = new DecimalFormat("###################.###########");

            // 处理豪华VIP升级到超级会员的价格
            if (cashFilterBO != null && cashFilterBO.getPrice() != null) {
                String luxPrice = decimalFormat.format(cashFilterBO.getPrice());
                resultMap.put("luxUpgradeSvip", luxPrice);
            } else {
                resultMap.put("luxUpgradeSvip", "");
            }

            // 处理音乐会员升级到豪华VIP的价格
            if (cashMusicBO != null && cashMusicBO.getPrice() != null) {
                String musicPrice = decimalFormat.format(cashMusicBO.getPrice());
                resultMap.put("musicUpgradeLux", musicPrice);
            } else {
                resultMap.put("musicUpgradeLux", "");
            }

            // 处理车载会员升级到超级会员的价格
            if (cashCarBO != null && cashCarBO.getPrice() != null) {
                String vehiclePrice = decimalFormat.format(cashCarBO.getPrice());
                resultMap.put("vehicleUpgradeSvip", vehiclePrice);
            } else {
                resultMap.put("vehicleUpgradeSvip", "");
            }
        } catch (Exception e) {
            log.error("Error getting upgrade price info", e);
            resultMap.put("luxUpgradeSvip", "");
            resultMap.put("musicUpgradeLux", "");
            resultMap.put("vehicleUpgradeSvip", "");
        }
    }

    @Override
    public Object getLuxSvipLowPriceInfo(SinglePriceGearVO priceGearVO) throws ServiceException {
        try{
            // 1 参数验证
            if(priceGearVO.getPlatform().equals("an")){
                priceGearVO.setPlatform("ar");
            }
            if(priceGearVO.getPlatform().equals("ios")&&(StringUtils.isBlank(priceGearVO.getDeviceId())
                    ||StringUtils.equals(priceGearVO.getDeviceId(),"0")||
                    StringUtils.equals(priceGearVO.getDeviceId(),"null"))){
                return "";
            }
            String  payDeskSign =invokeHttpForUserInfo.invokerAllUserPayDesk(priceGearVO.getUserId(),"membercenter_cash");
            priceGearVO.setPayDeskSign(payDeskSign);
            // 2.收银台信息获取
            PayDesk payDesk =cacheUtils.getPayDeskCache.get(priceGearVO.getPayDeskSign());
            if (ObjectUtils.isNull(payDesk)) {
                log.error("paydesk: getSinglePriceInfo payDesk is empty! payDeskId={}", payDesk.getId());
                throw new ServiceException(SystemCodeErrorConstant.PAY_DESK_EMPTY);
            }
            UserInfoBO userInfoBO=new UserInfoBO();
            SingleContext singleContext=new SingleContext();
            invokeInterceptUserInfo(priceGearVO, userInfoBO, singleContext);

            String vipType=VipTypeEnum.VIP_LUXURY.getType();
            if(StringUtils.equals("vip_34",priceGearVO.getLowPriceType())){
                vipType=VipTypeEnum.SUPER_VIP.getType();
            }
            if(StringUtils.equals("vip_ad_1",priceGearVO.getLowPriceType())){
                vipType=VipTypeEnum.AD_VIP.getType();
                long adExpireTime=userInfoBO.getVipAdExpireTime();
                if(adExpireTime>System.currentTimeMillis()){
                    return "";
                }
                if(singleContext.getAdAutoPay()==1){
                    singleContext.setAutoPay(1);
                }
            }
            // 6.豪华 超会
            Gear gear=cashService.getLuxLowGear(payDesk.getId(),priceGearVO.getPlatform(), singleContext.getAutoPay()==1,vipType);
            log.info("getSinglePriceInfo vipType={} lowPriceVipType={} gear={}",vipType,priceGearVO.getLowPriceType(),JSONUtil.toJsonStr(gear) );
            List<Filter> filters = invokeValidFilter(priceGearVO, Collections.singletonList(gear),userInfoBO);
            if (CollectionUtils.isEmpty(filters)) {
                log.error("paydesk: getSinglePriceInfo filters is empty,priceGearVO={}",JSONUtil.toJsonStr(priceGearVO));
                throw new ServiceException(SystemCodeErrorConstant.FILTER_EMPTY);
            }
            Collections.sort(filters);
            return parseH5SingleGearPrice(filters, payDesk, gear, priceGearVO, singleContext.getAutoPay());
        } catch (Exception e) {
            log.error("getSinglePriceInfo has error!", e);
        }
        return "";
    }

    @Override
    public SingleSVIPInfoVO getSingleSVIPInfo(SinglePriceGearVO priceGearVO) throws ServiceException {
        SingleSVIPInfoVO singleSVIPInfoVO = new SingleSVIPInfoVO();
        String platform = priceGearVO.getPlatform();
        String deviceId = priceGearVO.getDeviceId();
        if (Objects.equals(platform, "an")) {
            priceGearVO.setPlatform("ar");
        }
        if (Objects.equals(platform, "ios") && (StringUtils.isBlank(deviceId)
                || Objects.equals(deviceId, "0") ||
                Objects.equals(deviceId, "null"))) {
            return null;
        }
        priceGearVO.setPayDeskSign("membercenter_cash");
        try {
            // 2.收银台信息获取
            PayDesk payDesk = cacheUtils.getPayDeskCache.get(priceGearVO.getPayDeskSign());
            if (ObjectUtils.isNull(payDesk)) {
                log.error("paydesk: getSingleSVIPInfo payDesk is empty! payDeskId={}", payDesk.getId());
                throw new ServiceException(SystemCodeErrorConstant.PAY_DESK_EMPTY);
            }
            UserInfoBO userInfoBO = new UserInfoBO();
            SingleContext singleContext = new SingleContext();
            invokeInterceptUserInfo(priceGearVO, userInfoBO, singleContext);

            // 升级档位，如果有直接返回
            GearBO svipGearsPrice = newFilterService.getSvipGearsPrice(userInfoBO, platform, priceGearVO.getUserId(), priceGearVO.getPayDeskSign(), priceGearVO.getPayDeskSign(),null);
            AudioConfigResponse audioConfig;
            if (Objects.nonNull(svipGearsPrice)) {
                singleSVIPInfoVO.setGearInfo(svipGearsPrice);
                // 存储升级档位src和uid到redis
                listeningExecutor.submit(()-> newFilterService.upgradeGearSaveToRedis(svipGearsPrice,priceGearVO.getUserId()));
                if (Objects.isNull(priceGearVO.getId())) {
                    return singleSVIPInfoVO;
                }
                audioConfig = invokingRemoteUtil.getAudioConfig(String.valueOf(priceGearVO.getId()),priceGearVO.getDeviceName(), platform);
                if (Objects.isNull(audioConfig)) {
                    return singleSVIPInfoVO;
                }
                AudioConfigDTO audioConfigDTO = new AudioConfigDTO();
                BeanUtils.copyProperties(audioConfig, audioConfigDTO);
                singleSVIPInfoVO.setAudioConfig(audioConfigDTO);

                return singleSVIPInfoVO;
            }

            String vipType = VipTypeEnum.SUPER_VIP.getType();

            Gear gear = cashService.getLuxLowGear(payDesk.getId(), platform, singleContext.getAutoPay() == 1, vipType);
            log.info("getSingleSVIPInfo vipType={} lowPriceVipType={} gear={}", vipType, priceGearVO.getLowPriceType(), JSONUtil.toJsonStr(gear));
            List<Filter> filters = invokeValidFilter(priceGearVO, Collections.singletonList(gear), userInfoBO);
            if (CollectionUtils.isEmpty(filters)) {
                log.error("paydesk: getSingleSVIPInfo filters is empty,priceGearVO={}", JSONUtil.toJsonStr(priceGearVO));
                throw new ServiceException(SystemCodeErrorConstant.FILTER_EMPTY);
            }
            Collections.sort(filters);
            PriceGearBO priceGearBO = parseH5SingleGearPrice(filters, payDesk, gear, priceGearVO, singleContext.getAutoPay());
            if (Objects.isNull(priceGearBO) || Objects.isNull(priceGearBO.getData()) || CollectionUtils.isEmpty((List<GearBO>) priceGearBO.getData())) {
                return singleSVIPInfoVO;
            }
            GearBO gearBO = ((List<GearBO>) priceGearBO.getData()).get(0);
            removeCloudPayWay(gearBO);
            singleSVIPInfoVO.setGearInfo(gearBO);
            if(Objects.isNull(gearBO) || Objects.isNull(priceGearVO.getId())){
                return singleSVIPInfoVO;
            }
            audioConfig = invokingRemoteUtil.getAudioConfig(String.valueOf(priceGearVO.getId()),priceGearVO.getDeviceName(), platform);
            if (Objects.isNull(audioConfig)) {
                return singleSVIPInfoVO;
            }
            AudioConfigDTO audioConfigDTO = new AudioConfigDTO();
            BeanUtils.copyProperties(audioConfig, audioConfigDTO);
            singleSVIPInfoVO.setAudioConfig(audioConfigDTO);

            return singleSVIPInfoVO;
        } catch (Exception e) {
            log.error("getSingleSVIPInfo has error!", e);
        }
        return null;
    }

    @Override
    public GearBO getSingleUpgradeSVIPGear(SinglePriceGearVO priceGearVO) throws ServiceException {
        if(priceGearVO.getPlatform().equals("an")){
            priceGearVO.setPlatform("ar");
        }
        if(priceGearVO.getPlatform().equals("ios")&&(StringUtils.isBlank(priceGearVO.getDeviceId())
                ||StringUtils.equals(priceGearVO.getDeviceId(),"0")||
                StringUtils.equals(priceGearVO.getDeviceId(),"null"))){
            return null;
        }
        priceGearVO.setPayDeskSign("membercenter_cash");
        // 2.收银台信息获取
        try{
            PayDesk payDesk =cacheUtils.getPayDeskCache.get(priceGearVO.getPayDeskSign());
            if (ObjectUtils.isNull(payDesk)) {
                log.error("paydesk: getSingleUpgradeSVPGear payDesk is empty! payDeskId={}", payDesk.getId());
                throw new ServiceException(SystemCodeErrorConstant.PAY_DESK_EMPTY);
            }
            UserInfoBO userInfoBO=new UserInfoBO();
            SingleContext singleContext=new SingleContext();
            invokeInterceptUserInfo(priceGearVO, userInfoBO, singleContext);
            GearBO svipUpgradeGearsPrice = newFilterService.getSvipGearsPrice(userInfoBO, priceGearVO.getPlatform(), priceGearVO.getUserId(), priceGearVO.getPayDeskSign(), priceGearVO.getPayDeskSign(),null);
            listeningExecutor.submit(()-> newFilterService.upgradeGearSaveToRedis(svipUpgradeGearsPrice,priceGearVO.getUserId()));
            return svipUpgradeGearsPrice;
        }catch (Exception e){
            log.error("getSingleUpgradeSVPGear has error!",e);
        }
        return null;
    }

    /**
     * 移除云闪付
     * @param gearBO
     */
    private void removeCloudPayWay(GearBO gearBO){
        if(Objects.isNull(gearBO) || Objects.isNull(gearBO.getAbData())){
            return ;
        }
        List<ABDataBO> abData = (List<ABDataBO>) gearBO.getAbData();
        if(CollectionUtils.isEmpty(abData) || Objects.isNull(abData.get(0)) || StringUtils.isBlank(abData.get(0).getPayWay())){
            return ;
        }
        String payWay = abData.get(0).getPayWay();

        PayWayDTO payWayDTO = JSON.parseObject(payWay, PayWayDTO.class);

        if(Objects.isNull(payWayDTO) || CollectionUtils.isEmpty(payWayDTO.getList())) {
            return ;
        }

        List<PayWayDTO.PayWay> payWayList = payWayDTO.getList();

        List<PayWayDTO.PayWay> payWays = payWayList.stream().filter(item -> {
            if (Objects.equals(item.getBtnType(), "cloud")) {
                return false;
            }
            return true;
        }).collect(Collectors.toList());

        PayWayDTO newPayWayDTO=new PayWayDTO();
        newPayWayDTO.setStyle(payWayDTO.getStyle());
        newPayWayDTO.setList(payWays);
        abData.get(0).setPayWay(JSON.toJSONString(newPayWayDTO));
        gearBO.setAbData(abData);
    }

    /**
     * 豪华VIP 低价
     *
     * @param filters
     * @param autoPay
     * @param priceGearVO
     * @return
     */
    public String lowLuxParse(List<Filter> filters,int autoPay,PriceGearVO priceGearVO){
        for(Filter filter: filters ){
            if(filter.getType()!=1){
                continue;
            }
            boolean filterCheckForIos = filterNewLuxCheckForIos(filter,autoPay, priceGearVO);
            if (filterCheckForIos) {
                continue;
            }
            DecimalFormat decimalFormat = new DecimalFormat("###################.###########");
            String formatPrice=decimalFormat.format(filter.getPrice());
            if(filter.getPrice().compareTo(new BigDecimal(10))<0){
                log.info("low price out show price,userId={},price={},plat={} ", priceGearVO.getUserId(), filter.getPrice(),priceGearVO.getPlatform());
            }
            return formatPrice;
        }
        return "";
    }

    /**
     * svip低价
     *
     * @param filters
     * @param autoPay
     * @param priceGearVO
     * @return
     */
    public String lowSvipParse(List<Filter> filters,int autoPay,PriceGearVO priceGearVO) {
        for(Filter filter: filters ){
            if(filter.getType()!=1){
                continue;
            }
            boolean filterCheckForIos = filterNewSVIPCheckForIos(filter,autoPay, priceGearVO);
            if (filterCheckForIos) {
                continue;
            }
            DecimalFormat decimalFormat = new DecimalFormat("###################.###########");
            String formatPrice=decimalFormat.format(filter.getPrice());
            if(filter.getPrice().compareTo(new BigDecimal(10))<0){
                log.info("low svip price out show price,userId={},price={},plat={}", priceGearVO.getUserId(), filter.getPrice(),priceGearVO.getPlatform());
            }
            return formatPrice;
        }
        return "";
    }

    /**
     * H5单档位
     *
     * @param filters
     * @param payDesk
     * @param gear
     * @param priceGearVO
     * @return
     */
    public PriceGearBO parseH5SingleGearPrice(List<Filter> filters, PayDesk payDesk, Gear gear, SinglePriceGearVO priceGearVO,int autoPay) {
        PriceGearBO priceGearBO = new PriceGearBO();
        try {
            if(autoPay==0&&!StringUtils.equals(VipTypeEnum.AD_VIP.getType(),priceGearVO.getLowPriceType())){
                if(StringUtils.equals("vip_34",priceGearVO.getLowPriceType())){
                    newFilterService.initLowPriceGearBO(filters,priceGearVO,payDesk,0,2);
                }else{
                    newFilterService.initLowPriceGearBO(filters,priceGearVO,payDesk,0,1);
                }
            }
            Iterator<Filter> filterIterator = filters.iterator();
            while (filterIterator.hasNext()) {
                Filter filter = filterIterator.next();
                if (filter.getType()!=1) {
                    filterIterator.remove();
                }
            }
            List<GearBO> gearMixResult = new ArrayList<>();
            priceGearBO.setShowType(1);
            priceGearBO.setDoc(payDesk.getDoc());
            List<Gear> gears = new ArrayList<>();
            gears.add(gear);
            Map<Integer, List<Filter>> filterMap = filters.stream().collect((Collectors.groupingBy(Filter::getGearId)));
            newFilterService.checkRegion(priceGearVO,httpServletRequest);
           // log.info("parseH5SingleGearPrice gears={} filterMap={}", JSONUtil.toJsonStr(gears), JSONUtil.toJsonStr(filterMap));
            newFilterService.groupByMixTypes(gears, filterMap, gearMixResult, priceGearVO,null);
            priceGearBO.setData(gearMixResult);
            GearBO gearBO = gearMixResult.get(0);
            List<ABDataBO> adDataList = (List<ABDataBO>) gearBO.getAbData();
            ABDataBO abDataBO = adDataList.get(0);
            DecimalFormat decimalFormat = new DecimalFormat("###################.###########");
            String formatPrice = decimalFormat.format(abDataBO.getPrice());
            priceGearBO.setTitle(formatPrice + "元开通会员");
            listeningExecutor.submit(() -> newFilterService.saveGearToRedis(priceGearBO, priceGearVO,"getSinglePriceInfo"));
        } catch (Exception e) {
            log.error("parseH5SingleGearPrice has error!priceGearVO={}", JSONUtil.toJsonStr(priceGearVO), e);
        }
        return priceGearBO;
    }

    /**
     * H5 豪华V&超会挡位
     *
     * @param payDesk
     * @param priceGearVO
     * @return
     */
    public PriceGearBO parseActivityH5SingleGearPrice(PayDesk payDesk, SinglePriceGearVO priceGearVO,int autoPay,UserInfoBO userInfoBO) {
        PriceGearBO priceGearBO = new PriceGearBO();
        SingleGearParamBO gearParamBO=new SingleGearParamBO();
        gearParamBO.setPayDeskId(payDesk.getId());
        gearParamBO.setPlatform(priceGearVO.getPlatform());
        gearParamBO.setAutoPay(autoPay);
        try {
            List<Gear> gears = null;
//            if(StringUtils.isBlank(priceGearVO.getPayDeskSign())){
//                priceGearVO.setPayDeskSign("membercenter_cash");
//            }
            if(priceGearVO.getIstsgear()){
                if(autoPay==1){
                    return null;
                }
                gears=cacheUtils.getTSGearListCache.get(gearParamBO);
            }else{
                gears=cacheUtils.getGearListCache.get(gearParamBO);
            }
            List<Filter> filters = invokeValidFilter(priceGearVO, gears,userInfoBO);
            if (CollectionUtils.isEmpty(filters)) {
                log.error("parseActivityH5SingleGearPrice filters is empty,priceGearVO={}", JSONUtil.toJsonStr(priceGearVO));
                throw new ServiceException(SystemCodeErrorConstant.FILTER_EMPTY);
            }
            if(autoPay==0){
                if(vipConfConfigNacos.getVipConfBO().getLowPriceType()==2){
                    newFilterService.initLowPriceGearBO(filters,priceGearVO,payDesk,0,0);
                }else{
                    newFilterService.initOldLowPriceGearBO(filters,priceGearVO,payDesk,0);
                }
            }
            Collections.sort(filters);
            List<VipTypeBO> vipTypeBOS = new ArrayList<>();
            Map<Integer, List<Filter>> filterMap = filters.stream().collect((Collectors.groupingBy(Filter::getGearId)));
            groupByVipTypes(gears, filterMap, vipTypeBOS,  payDesk.getDoc(),priceGearVO);
            priceGearBO.setData(vipTypeBOS);
            if(priceGearVO.isShowRenewal()){
                priceGearBO.setResultType(1);
            }
            listeningExecutor.submit(() -> newFilterService.saveGearToRedis(priceGearBO, priceGearVO,"getSinglePriceInfo"));
        } catch (Exception e) {
            log.error("parseActivityH5SingleGearPrice has error! priceGearVO={}", JSONUtil.toJsonStr(priceGearVO), e);
        }
        return priceGearBO;
    }
    /**
     * IOS初始化数据
     *
     * @return
     */
    public void invokeForIOS(SinglePriceGearVO priceGearVO) {
        if (priceGearVO.getPlatform().equals("ios")) {
            isFirstPayUser(priceGearVO.getUserId(),priceGearVO.getDeviceId(),priceGearVO);
        }
    }

    /**
     * apple购买信息
     *
     * @param priceGearVO
     */
    public void invokeForApplePayRule(SinglePriceGearVO priceGearVO){
        try{
            String url=vipDomain+"/commercia/indicator/ios/subscript/sign?deviceId="+priceGearVO.getDeviceId()+"&userId="+priceGearVO.getUserId()+"&source="+priceGearVO.getSource()+"&fromsrc=vipCenter";
            String result= HttpUtil.get(url,200);
            log.info("single invokeForApplePayRule url={} result={}",url,result);
            com.alibaba.fastjson.JSONObject jsonObject= com.alibaba.fastjson.JSONObject.parseObject(result);
            com.alibaba.fastjson.JSONObject object= jsonObject.getJSONObject("data");
            Integer isNewCustomer=object.getInteger("luxVipNewCustomer");
            Integer isFirstRenew=object.getInteger("luxVipFirstRenewal");
            Integer isSvipNewCustomer=object.getInteger("svipNewCustomer");
            Integer isSvipFirstRenewal=object.getInteger("svipFirstRenewal");
            Integer vehicleNewCustomer=object.getInteger("vehicleNewCustomer");
            Integer vehicleFirstRenewal=object.getInteger("vehicleFirstRenewal");
            priceGearVO.setFirstPay(isNewCustomer==1);
            priceGearVO.setFirstRenew(isFirstRenew==1);
            priceGearVO.setFirstSvipPay(isSvipNewCustomer==1);
            priceGearVO.setFirstRenewalSvipPay(isSvipFirstRenewal==1);
            priceGearVO.setFirstVehiclePay(vehicleNewCustomer==1);
            priceGearVO.setVehicleFirstRenewal(vehicleFirstRenewal==1);
        }catch (Exception e){
            carMonitor.applePayError.increment();
            log.error("single invokeForApplePayRule has error!userId={}",priceGearVO.getUserId(),e);
        }
    }

    /**
     * 解析档位价格
     *
     * @param filters filters
     * @param priceGearVO 参数请求
     * @return
     */
    public String parseFilterPrice(Gear gear,List<Filter> filters, SinglePriceGearVO priceGearVO){
        for(Filter filter: filters ){
            boolean filterCheckForIos = filterCheckForIos(gear,filter, priceGearVO);
            if (filterCheckForIos) {
                continue;
            }
            DecimalFormat decimalFormat = new DecimalFormat("###################.###########");
            String formatPrice=decimalFormat.format(filter.getPrice());
            if(filter.getPrice().compareTo(new BigDecimal(10))<0){
                log.info("low price out show price,userId={},price={},plat={}", priceGearVO.getUserId(), filter.getPrice(),priceGearVO.getPlatform());
            }
            return formatPrice;
        }
        return "";
    }

    /**
     * ios 豪华购买判断
     *
     * @param filter
     * @param autoPay
     * @param priceGearVO
     * @return
     */
    public boolean filterNewLuxCheckForIos(Filter filter, int autoPay,PriceGearVO priceGearVO) {
        if (priceGearVO.getPlatform().equals("ios")&autoPay==0) {
            if (filter.getFilterFirstPay() != null
                    && filter.getFilterFirstPay().equals(1)
                    && !priceGearVO.getFirstPay() && filter.getOrderRank() != 1) {
                return true;
            }
            if ((filter.getFilterFirstPay() == null
                    || !filter.getFilterFirstPay().equals(1))
                    && priceGearVO.getFirstPay() && filter.getOrderRank() != 1) {
                return true;
            }
            if (filter.getFilterFirstRenew() != null
                    && (filter.getFilterFirstRenew().equals(1)
                    || filter.getFilterFirstRenew().equals(4)
                    || filter.getFilterFirstRenew().equals(5))
                    && !priceGearVO.getFirstRenew() && filter.getOrderRank() != 1) {
                return true;
            }
        }
        return false;
    }

    /**
     * ios 超会购买判断
     *
     * @param filter
     * @param autoPay
     * @param priceGearVO
     * @return
     */
    public boolean filterNewSVIPCheckForIos(Filter filter, int autoPay,PriceGearVO priceGearVO) {
        if (priceGearVO.getPlatform().equals("ios")&autoPay==0) {
            if (filter.getFilterFirstPay() != null
                    && filter.getFilterFirstPay().equals(1)
                    && !priceGearVO.getFirstSvipPay()&&filter.getOrderRank()!=1) {
                return true;
            }
            if (filter.getFilterFirstPay() != null
                    && !(filter.getFilterFirstPay().equals(1))
                    && priceGearVO.getFirstSvipPay()&&filter.getOrderRank()!=1) {
                return true;
            }
            if (filter.getFilterFirstRenew() != null
                    && (filter.getFilterFirstRenew().equals(1)
                    || filter.getFilterFirstRenew().equals(4)
                    || filter.getFilterFirstRenew().equals(5))
                    && !priceGearVO.getFirstRenewalSvipPay()&&filter.getOrderRank()!=1) {
                return true;
            }

        }
        return false;
    }

    /**
     * ios 策略
     * @param filter
     * @param gear
     * @param priceGearVO
     * @return
     */
    public boolean filterCheckForIos(Gear gear,Filter filter,PriceGearVO priceGearVO) {
        if (priceGearVO.getPlatform().equals("ios")&&gear.getAutoPay()==1) {
            if (StringUtils.equals(gear.getVipType(), VipTypeEnum.SUPER_VIP.getType())) {
                if (filter.getFilterFirstPay() != null
                        && filter.getFilterFirstPay().equals(1)
                        && !priceGearVO.getFirstSvipPay()&&filter.getOrderRank()!=1) {
                    return true;
                }
                if ((filter.getFilterFirstPay() == null
                        || !filter.getFilterFirstPay().equals(1))
                        && priceGearVO.getFirstSvipPay()&&filter.getOrderRank()!=1) {
                    return true;
                }
                if (filter.getFilterFirstRenew() != null
                        && (filter.getFilterFirstRenew().equals(1)
                        || filter.getFilterFirstRenew().equals(4)
                        || filter.getFilterFirstRenew().equals(5))
                        && !priceGearVO.getFirstRenewalSvipPay()&&filter.getOrderRank()!=1) {
                    return true;
                }

            }
            if (StringUtils.equals(gear.getVipType(), VipTypeEnum.VIP_LUXURY.getType())) {
                if (filter.getFilterFirstPay() != null
                        && filter.getFilterFirstPay().equals(1)
                        && !priceGearVO.getFirstPay()&&filter.getOrderRank()!=1) {
                    return true;
                }
                if ((filter.getFilterFirstPay() == null
                        || !filter.getFilterFirstPay().equals(1))
                        && priceGearVO.getFirstPay()&&filter.getOrderRank()!=1) {
                    return true;
                }
                if (filter.getFilterFirstRenew() != null
                        && (filter.getFilterFirstRenew().equals(1)
                        || filter.getFilterFirstRenew().equals(4)
                        || filter.getFilterFirstRenew().equals(5))
                        && !priceGearVO.getFirstRenew()&&filter.getOrderRank()!=1) {
                    return true;
                }
            }

        }
        return false;
    }

    /**
     * 过滤有效filter
     *
     * @param priceGearVO 请求参数
     * @return
     * @throws ServiceException
     */
    private List<Filter> invokeValidFilter(SinglePriceGearVO priceGearVO, List<Gear> gear,UserInfoBO userInfoBO) throws ServiceException {
        String gearIds = gear.stream().map(x -> String.valueOf(x.getId())).collect(Collectors.joining(","));
        List<Filter> filters = null;
        try {
                // 远程调用接口过滤filter
                filters = invokeFilterGears(priceGearVO, gearIds,gear,userInfoBO);
            } catch (Exception e) {
                log.error("paydesk:invoke filters is error ! priceGearVO={},gearIds={}", JSONUtil.toJsonStr(priceGearVO),JSONUtil.toJsonStr(gear), e);
                throw new ServiceException(SystemCodeErrorConstant.INVOKE_FILTER_FAILED);
            }
        return filters;
    }

    /**
     * 获取过滤后的filter
     *
     * @param priceGearVO
     * @param gearId
     * @return
     */
    public List<Filter> invokeFilterGears(PriceGearVO priceGearVO, String gearId,List<Gear> gears,UserInfoBO userInfoBO) {
        List<Filter> filters=null;
        try {
            log.info("paydesk:invokeFilterGears before RPC  gearId={}", gearId);
            AbRequest abRequest = new AbRequest();
            abRequest.setRuleType("PC_AB_RULE");
            abRequest.setUserId(priceGearVO.getUserId());
            abRequest.setGearId(gearId);
            abRequest.setRSource("");
            abRequest.setSource(priceGearVO.getSource());
            abRequest.setDeviceId(priceGearVO.getDeviceId());
            abRequest.setVirtualUserId(priceGearVO.getVirtualUserId());
            abRequest.setPayDeskSign(priceGearVO.getPayDeskSign());
            abRequest.setPlatform(priceGearVO.getPlatform());
            abRequest.setUserInfoBO(userInfoBO);
            if(priceGearVO.getPayDeskSign()!=null&&priceGearVO.getPayDeskSign().equals("actRecallPopup")){
                abRequest.setFromsrc(priceGearVO.getFilterFromsrc());
            }else{
                abRequest.setFromsrc(priceGearVO.getFromsrc());
            }
            AbResponse eval = abRuleService.eval(abRequest);
            log.info("paydesk:invokeFilterGears after RPC filterIds={}", JSONUtil.toJsonStr(eval));
            List<Object> maps = eval.getHits();
            priceGearVO.setPayType(eval.getPayType());
            List<Integer> filterIds = maps.stream().map(x -> Integer.parseInt(String.valueOf(x))).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filterIds)) {
                return new ArrayList<Filter>();
            }
            // 使用CacheAdapter获取Filter列表，支持灰度控制和缓存降级
            filters = cacheAdapter.getFiltersByIds(filterIds, priceGearVO.getUserId());
            // 创建一个新的ArrayList来避免ConcurrentModificationException
            if (filters != null) {
                filters = new ArrayList<>(filters);
            } else {
                filters = new ArrayList<>();
            }
        }catch (Exception e){
            carMonitor.abError.increment();
            log.error("single isAddStandFilters has error!userId={}",priceGearVO.getUserId(),e);
        }
        return filters;
    }

    /**
     * 分类模式 单挡位
     *
     * @param gears 挡位
     * @param filterMap
     * @param vipTypeBOS
     */
    public void groupByVipTypes(List<Gear> gears, Map<Integer, List<Filter>> filterMap, List<VipTypeBO> vipTypeBOS, String payDeskDoc,  SinglePriceGearVO priceGearVO) {
        // 所有的档位按类型进行分类
        try {
            Map<String,ABDataBO> map=new HashMap<>();
            Map<String, List<Gear>> gearMap = gears.stream().collect(Collectors.groupingBy(Gear::getVipType, LinkedHashMap::new, Collectors.toList()));
            for (String vipType : gearMap.keySet()) {
                try {
                    VipTypeBO vipTypeBO = new VipTypeBO();
                    List<GearBO> gearResult = new ArrayList<>();
                    vipTypeBO.setType(vipType);
                    vipTypeBO.setName(VipTypeEnum.getNameByType(vipType));
                    vipTypeBO.setId(VipTypeEnum.getIdByType(vipType));
                    List<Gear> vipTypeGears = gearMap.get(vipType);
                    vipTypeGears.forEach(itemGear -> {
                        try {
                            GearBO gearBo = new GearBO();
                            // 档位对应的策略
                            List<Filter> filters = filterMap.get(itemGear.getId());
                            if (CollectionUtils.isEmpty(filters)) {
                                return;
                            }
                            newFilterService.parseData(gearBo, itemGear, filters, priceGearVO,map,null);
                            gearResult.add(gearBo);
                        } catch (Exception e) {
                            log.error("SinglePriceService groupByVipTypes itemGear has error ", e);
                        }
                    });
                    newFilterService.parseDocJson(payDeskDoc, vipTypeBO);
                    Collections.sort(gearResult);
                    vipTypeBO.setList(gearResult);
                    vipTypeBOS.add(vipTypeBO);
                } catch (Exception e) {
                    log.error("SinglePriceService SinglePriceService gearMap item has error", e);
                }
            }
            //newFilterService.makeTsAbDataBo(map,null,vipTypeBOS);
        } catch (Exception e) {
            log.error("SinglePriceService groupByVipTypes has error !",e);
        }
    }

    public void invokeInterceptUserInfo(SinglePriceGearVO priceGearVO, UserInfoBO userInfoBO, SingleContext singleContext) {
        try {
            // 3.ios 购买信息
            invokeForPayRule(priceGearVO);
            invokeInterceptUserInfoForRPC(priceGearVO.getUserId(), userInfoBO, singleContext);
        } catch (Exception e) {
            log.error("invokeInterceptUserInfo has error! ", e);
        }
    }

    public void invokeSingleUserInfo(SinglePriceGearVO priceGearVO, UserInfoBO userInfoBO, SingleContext singleContext) {
        try {
            // 3.ios 购买信息
            invokeForPayRule(priceGearVO);
            invokeInterceptUserInfoForRPC(priceGearVO.getUserId(), userInfoBO, singleContext);
        } catch (Exception e) {
            log.error("invokeInterceptUserInfo has error! ", e);
        }
    }

    /**
     * 判断IOS是否能使用微信支付
     *
     * @param priceGearVO
     * @return
     */
    private void invokeForPayRule(SinglePriceGearVO priceGearVO) {
        if (priceGearVO.getPlatform().equals("ios")&&StringUtils.isNotBlank(priceGearVO.getUserId())&&MyNumberUtils.toLONG(priceGearVO.getUserId())>0) {
            invokeForApplePayRule(priceGearVO);
        }
    }
    /**
     * 组装签约、会员信息
     *
     * @param userId
     * @param userInfoBO
     * @param singleContext
     */
    private void invokeInterceptUserInfoForRPC(String userId, UserInfoBO userInfoBO, SingleContext singleContext) {
        try {
            if(StringUtils.isBlank(userId)||MyNumberUtils.toLONG(userId)<=0){
                singleContext.setAutoPay(1);
                singleContext.setCarAutoPay(1);
                singleContext.setAdAutoPay(1);
                return;
            }
            CompletableFuture<VipInfo> vipInfoFuture = uiInfoService.getAndValidateUIAllInfo(userId);
            VipInfo vipInfo= vipInfoFuture.get(500, TimeUnit.MILLISECONDS);;
            //log.info("invokeInterceptUserInfoForRPC vipInfo={}",JSONUtil.toJsonStr(vipInfo));
            BasicUInfo basicUInfo= vipInfo.getBasicUInfo();
            SignStateInfo signStateInfo= vipInfo.getSignStateInfo();
            userInfoBO.setVipluxuryexpire(basicUInfo.getVipLuxuryExpireTime());
            userInfoBO.setVipmexpire(basicUInfo.getVipmExpireTime());
            userInfoBO.setSvipExpire(basicUInfo.getSvipExpireTime());
            userInfoBO.setVipWatch1Expire(basicUInfo.getVipWatch1ExpireTime());
            userInfoBO.setVipVehicleExpire(basicUInfo.getVehicleExpireTime());
            userInfoBO.setVipAdExpireTime(basicUInfo.getVipAdExpireTime());
            userInfoBO.setVipmAutoPayUser(signStateInfo.getVipmAutoPayUser());
            userInfoBO.setSvipAutoPayUser(signStateInfo.getSvipAutoPayUser());
            userInfoBO.setLuxAutoPayUser(signStateInfo.getLuxAutoPayUser());
            userInfoBO.setIsVIPCarAutoPay(signStateInfo.getVehicleAutoPayUser());
            userInfoBO.setVipAdSignAutoPay(signStateInfo.getVipAdSignStatus());
            if (signStateInfo.getVipmAutoPayUser() == 1 || signStateInfo.getLuxAutoPayUser() == 1 || signStateInfo.getSvipAutoPayUser() == 1) {
                singleContext.setAutoPay(1);
            }
            if (signStateInfo.getVehicleAutoPayUser()==1) {
                singleContext.setCarAutoPay(1);
            }
            if (signStateInfo.getVipAdSignStatus()==1) {
                singleContext.setAdAutoPay(1);
            }
        } catch (Exception e) {
            userInfoBO.setVipmexpire(-1L);
            singleContext.setAutoPay(1);
            singleContext.setCarAutoPay(1);
            singleContext.setAdAutoPay(1);
            log.error("invokeInterceptUserInfoForRPC has error!uesrId={}", userId, e);
        }
    }

    /**
     * 灰度userId
     *
     * @param userId
     * @return
     */
    public boolean newSingleUser(String userId){
        try{
            if(StringUtils.isBlank(userId)||userId.equals("0")){
                return false;
            }
            List<String> newSingleUserIds=vipConfConfigNacos.getVipConfBO().getNewSingleUserIds();
            if(CollectionUtils.isEmpty(newSingleUserIds)){
                return false;
            }
            if(!CollectionUtils.isEmpty(newSingleUserIds)){
                for(String keyStr:newSingleUserIds){
                    if(userId.endsWith(keyStr)){
                        return true;
                    }
                }
            }
            return false;
        }catch (Exception e){
            log.error("newSingleUser error",e);
        }
        return false;
    }
    /**
     * 灰度userId
     *
     * @param userId
     * @return
     */
    public boolean newNewSingleUser(String userId){
        try{
            if(StringUtils.isBlank(userId)||userId.equals("0")){
                return false;
            }
            List<String> newSingleUserIds=vipConfConfigNacos.getVipConfBO().getNewUserIds();
            if(CollectionUtils.isEmpty(newSingleUserIds)){
                return false;
            }
            if(!CollectionUtils.isEmpty(newSingleUserIds)){
                for(String keyStr:newSingleUserIds){
                    if(userId.endsWith(keyStr)){
                        return true;
                    }
                }
            }
            return false;
        }catch (Exception e){
            log.error("newSingleUser error",e);
        }
        return false;
    }


    /**
     * 判断是否首开用户
     *
     * @param
     * @return
     */
    public Boolean isFirstPayUser(String userId,String deviceId,PriceGearVO priceGearVO){
        try{
            String result= HttpUtil.get(vipDomain+"/vip/v2/user/vip?op=getNewPayCategoryPrizeForIos&vers=rvQlmmEd1dDluXS&deviceId="+deviceId+"&uid="+userId,1000);
            JSONObject jsonObject= JSONUtil.parseObj(result);
            JSONObject object= jsonObject.getJSONObject("data");
            if(object.containsKey("isFirstRenew")){
                Integer isFirstRenew=object.getInt("isFirstRenew");
                priceGearVO.setFirstRenew(isFirstRenew==1);
            }
            if(object.containsKey("isNewCustomer")){
                Integer isNewCustomer=object.getInt("isNewCustomer");
                priceGearVO.setFirstPay(isNewCustomer==1);
            }
            if(object.containsKey("isSvipNewCustomer")){
                Integer svipNewCustomer=object.getInt("isSvipNewCustomer");
                priceGearVO.setFirstSvipPay(svipNewCustomer==1);
            }
            if(object.containsKey("isSvipFirstRenew")){
                Integer svipFirstRenew=object.getInt("isSvipFirstRenew");
                priceGearVO.setFirstRenewalSvipPay(svipFirstRenew==1);
            }
        }catch (Exception e){
            log.error("isFirstPayUser has error!userId={}",userId,e);
        }
        return false;
    }

    @Override
    public Object getRecallMusicCard(PriceGearVO priceGearVO) {
        if (org.apache.commons.lang3.StringUtils.isBlank(priceGearVO.getIdent())) {
            log.debug("ident is blank");
            return SingleGearBoUtil.getDefaultSeasonLuxCard(priceGearVO.getPlatform());
        }
        String userId = priceGearVO.getUserId();
        String deviceId = priceGearVO.getDeviceId();
        String signPre = String.format("uid=%s&devid=%s&rcg=rcMusic&salt=ZT3Qpu0Nb", userId, deviceId);
        if (!org.apache.commons.lang3.StringUtils.equals(MD5.create().digestHex(signPre, StandardCharsets.UTF_8), priceGearVO.getIdent())) {
            log.debug("ident sign is invalid");
            return SingleGearBoUtil.getDefaultSeasonLuxCard(priceGearVO.getPlatform());
        }
        SingleGearBo luxGear = SingleGearBoUtil.getSingleLuxGearBase(priceGearVO.getPlatform());
        if (SingleGearBoUtil.isIOS(priceGearVO.getPlatform())) {
            luxGear.setPid("unionbuy1get6_88");
            luxGear.setIsIosDiscounts("1");
            luxGear.setIsNewUser("0");
        }
        luxGear.setName("豪华VIP年卡");
        luxGear.setMonth(12);
        luxGear.setPrice("88");
        luxGear.setOPrice("216");
        luxGear.setOrgPrice("216");
        luxGear.setTagTopTxt("");
        luxGear.setTagBottomTxt("豪华VIP限时福利");
        luxGear.setSrc("12MonthForVipm");
        luxGear.setPayBtnClick("1");
        luxGear.setDiscountTip("限时4折");
        return luxGear;
    }

    @Override
    public Object getSVipRecallYearGear(PriceGearVO priceGearVO) {
        try {
            // 酷我X腾讯音乐榜五四投票活动
            if ("youniYouthYearSvip".equals(priceGearVO.getFromsrc()) && vipConfConfigNacos.getVipConfBO().getVoteActivitySwitch() == 1) {
                PayDesk payDesk = cacheUtils.getPayDeskCache.get("membercenter_cash");
                if (payDesk != null) {
                    int autoPay = singlePriceServiceImpl.getAutoPayVipInfo(priceGearVO.getUserId());
                    GearParamBO gearParamBO = new GearParamBO();
                    gearParamBO.setPayDeskId(payDesk.getId());
                    gearParamBO.setPlatform(priceGearVO.getPlatform());
                    gearParamBO.setAutoPay("ar".equals(priceGearVO.getPlatform()) && autoPay != 1);
                    gearParamBO.setSource(priceGearVO.getSource());
                    gearParamBO.setVipType(VipTypeEnum.SUPER_VIP.getType());
                    gearParamBO.setGearType(GearTypeEnum.YEAR.getMonths());
                    Gear singleGearBo = cacheUtils.getVipTypeByGearType.get(gearParamBO);
                    // 档位对应的策略
                    if (singleGearBo != null) {
                        String gearIds = org.apache.commons.lang3.StringUtils.join(singleGearBo.getId(), ",");
                        List<Filter> filters = cacheUtils.gearCache.get(gearIds);
                        Collections.sort(filters);
                        Filter filter = filters.stream().findFirst().orElse(null);
                        if (filter != null) {
                            return SingleGearBoUtil.getSingleSvipGearBase(filter, singleGearBo);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("getLuxSvipLowPriceInfo error", e);
        }
        return null;
    }

}
