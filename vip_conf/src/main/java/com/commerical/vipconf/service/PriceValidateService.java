package com.commerical.vipconf.service;

import java.util.Collection;

/**
 * 价格校验服务
 */
public interface PriceValidateService {

    /**
     * 加入到src校验列表
     * @param userId
     * @param src
     * @return
     */
    boolean addValidateUidSrc(String userId, String ... src);


    /**
     * 提供新的添加方法
     * @param userId
     * @param srcList
     * @return
     */
    boolean addValidateUidSrc(String userId, Collection<? extends String> srcList);
    /**
     * 判断src是否在当前的
     * @param userId
     * @param src
     * @return
     */
    boolean isInValidateUidSrcSet(String userId, String src);

    /**
     * 清除uid 校验
     * @param userId
     * @return
     */
    boolean clearValidateUid(String userId);


    /**
     *   加入到src校验列表 虚拟id
     * @param virtualUid
     * @param src
     * @return
     */
    boolean addValidateVirtualUidSrc(String virtualUid, String ... src);

    boolean addValidateVirtualUidSrc(String virtualUid, Collection<? extends String> srcList);

    /**
     *  判断src是否在当前的 虚拟id
     * @param virtualUid
     * @param src
     * @return
     */
    boolean isInValidateSrcSet(String virtualUid, String src);

    /**
     * 清除VirtualUi 校验
     * @param virtualUid
     * @return
     */
    boolean clearValidateVirtualUid(String virtualUid);






}
