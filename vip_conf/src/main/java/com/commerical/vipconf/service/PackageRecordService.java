package com.commerical.vipconf.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.commerical.vipconf.config.exception.ServiceException;
import com.commerical.vipconf.domain.PackageRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import com.commerical.vipconf.domain.vo.PackageRecordQueryVO;
import com.commerical.vipconf.domain.vo.PackageRecordVO;


/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-09
 */
public interface PackageRecordService extends IService<PackageRecord> {

    PackageRecord addPackageRecord(PackageRecordVO packageRecordVO) throws ServiceException;

    PackageRecord getPackageRecordByKey(String key);

    IPage<PackageRecord> getPackageRecord(PackageRecordQueryVO packageRecordQueryVO);

     void deletePackageRecordById(Integer id);

    PackageRecord addCarChannelPackageRecord(PackageRecord packageRecord);

    PackageRecord getCarChannelPackageRecordByKey(String key);

}
