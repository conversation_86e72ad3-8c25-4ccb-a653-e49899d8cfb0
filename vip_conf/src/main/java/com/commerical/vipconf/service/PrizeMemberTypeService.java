package com.commerical.vipconf.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.commerical.vipconf.domain.DressCenterPic;
import com.commerical.vipconf.domain.PrizeMemberType;
import com.commerical.vipconf.domain.vo.DressCenterPicQueryVO;
import com.commerical.vipconf.domain.vo.DressCenterPicVO;
import com.commerical.vipconf.domain.vo.PrizeMemberTypeQueryVo;

/**
 * <p>
 * 支付结果页优化抽奖分类配置
 * </p>
 */
public interface PrizeMemberTypeService extends IService<PrizeMemberType> {

    PrizeMemberType addOrUpdate(PrizeMemberType prizeMemberType);

    void deleteById(Integer id);

    IPage<PrizeMemberType> getList(PrizeMemberTypeQueryVo prizeMemberTypeQueryVo);

    PrizeMemberType getOneById(Integer id);

}
