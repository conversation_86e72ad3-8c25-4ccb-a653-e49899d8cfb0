package com.commerical.vipconf.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.commerical.vipconf.domain.PrizeDrawConfig;
import com.commerical.vipconf.domain.vo.PrizeDrawConfigQueryVo;

import java.util.List;

/**
 * <p>
 * 支付结果页优化抽奖奖品配置
 * </p>
 */
public interface PrizeDrawConfigService extends IService<PrizeDrawConfig> {

    PrizeDrawConfig addOrUpdate(PrizeDrawConfig prizeDrawConfig);

    void deleteById(Integer id);

    IPage<PrizeDrawConfig> getList(PrizeDrawConfigQueryVo prizeDrawConfigQueryVo);

    PrizeDrawConfig getOneById(Integer id);

    List<PrizeDrawConfig> getListByPid(Integer pid,Long userId,String platform);

    JSONObject getPrizeDrawConfigByPidAndProbability(Integer pid,Long userId,Long orderId,String platform);
}
