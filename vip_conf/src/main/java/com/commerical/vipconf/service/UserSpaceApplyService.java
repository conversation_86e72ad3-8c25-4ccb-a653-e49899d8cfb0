package com.commerical.vipconf.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.commerical.vipconf.domain.UserSpaceApply;
import com.baomidou.mybatisplus.extension.service.IService;
import com.commerical.vipconf.domain.vo.UserSpaceApplyQueryVO;
import com.commerical.vipconf.domain.vo.UserSpaceApplyVO;

import java.util.List;

/**
 * <p>
 * 用户空间权限申请 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-13
 */
public interface UserSpaceApplyService extends IService<UserSpaceApply> {

    UserSpaceApply addOrUpdateUserSpaceApply(UserSpaceApplyVO userSpaceApplyVO);

    boolean updateUserApplyStatus(UserSpaceApplyVO userSpaceApplyVO);

    IPage<UserSpaceApply> getUserSpaceApplyList(UserSpaceApplyQueryVO userSpaceApplyVO);

    UserSpaceApply getUserSpaceApplyInfoById(Integer id);

    boolean authUserSpace(String projectSpaceCode, String applyNames,String approveName, Integer type);

    List<String> getUserSpaceCode(String userName);

    List<Integer> getUserApplyPass(String userName,String code);
}
