package com.commerical.vipconf.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.commerical.vipconf.domain.DressCenterBanner;
import com.baomidou.mybatisplus.extension.service.IService;
import com.commerical.vipconf.domain.vo.DressCenterBannerQueryVO;
import com.commerical.vipconf.domain.vo.DressCenterBannerVO;

/**
 * <p>
 * 装扮中心 banner配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-23
 */
public interface DressCenterBannerService extends IService<DressCenterBanner> {

    DressCenterBanner addOrUpdateDressCenterBanner(DressCenterBannerVO DressCenterBannerVO);

    void deleteDressCenterBannerById(Integer id,Boolean isTrue,Integer isDelete);

    IPage<DressCenterBanner> getDressCenterBannerList(DressCenterBannerQueryVO DressCenterBannerQueryVO);

    DressCenterBanner getDressCenterBannerInfoById(Integer id);

    DressCenterBanner getDressCenterBannerInfoByCode(String code);
}
