package com.commerical.vipconf.service.Factory;

import com.commerical.vipconf.enums.DressCenterTabEnum;
import com.commerical.vipconf.service.DressSkinInvokeService;
import com.commerical.vipconf.service.impl.dresscenter.DressCenterPlayerInvokeServiceImpl;
import com.commerical.vipconf.service.impl.dresscenter.DressHeadInvokeServiceImpl;
import com.commerical.vipconf.service.impl.dresscenter.DressSkinInvokeServiceImpl;
import com.commerical.vipconf.service.impl.dresscenter.DressbackgroundInvokeServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
public class DressCenterFactory {
    private final Map<Integer, DressSkinInvokeService> strategyMaps = new ConcurrentHashMap();

    @Autowired
    private DressSkinInvokeServiceImpl dressSkinInvokeService;
    @Autowired
    private DressCenterPlayerInvokeServiceImpl dressCenterPlayerInvokeService;
    @Autowired
    private DressHeadInvokeServiceImpl dressHeadInvokeService;
    @Autowired
    private DressbackgroundInvokeServiceImpl dressbackgroundInvokeService;

    public DressSkinInvokeService getDressCenterStrategy(Integer tabType) {
      if(!strategyMaps.containsKey(tabType)){
          strategyMaps.putIfAbsent(DressCenterTabEnum.SKIN.getType(), dressSkinInvokeService);
          strategyMaps.putIfAbsent(DressCenterTabEnum.PLAYER.getType(), dressCenterPlayerInvokeService);
          strategyMaps.putIfAbsent(DressCenterTabEnum.HEAD_DRESS.getType(), dressHeadInvokeService);
          strategyMaps.putIfAbsent(DressCenterTabEnum.BACKGROUND.getType(), dressbackgroundInvokeService);
      }
        return strategyMaps.get(tabType);
    }


}
