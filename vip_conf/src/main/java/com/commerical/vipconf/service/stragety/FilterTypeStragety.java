package com.commerical.vipconf.service.stragety;

import com.commerical.abserviceapi.domain.UserInfoBO;
import com.commerical.vipconf.domain.Filter;
import com.commerical.vipconf.domain.Gear;
import com.commerical.vipconf.domain.bo.*;
import com.commerical.vipconf.domain.vo.PriceGearVO;
import java.util.List;

public  interface  FilterTypeStragety {

    /**
     * 普通filter过滤
     */
     void parseFilter(Filter filter, Gear gear, GearBO gearBO, PriceGearVO priceGearVO, Boolean isWeChat, List<ABDataBO> adDataList, List<PayDataBO> payDataList, List<FreeDataBO> freeDataList, String vipType, UserInfoBO userInfoBO);

}
