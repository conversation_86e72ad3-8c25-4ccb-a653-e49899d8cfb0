package com.commerical.vipconf.service.stragety.newStragety;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.commerical.abserviceapi.domain.UserInfoBO;
import com.commerical.vipconf.domain.Filter;
import com.commerical.vipconf.domain.Gear;
import com.commerical.vipconf.domain.PackageRecord;
import com.commerical.vipconf.domain.bo.ABDataBO;
import com.commerical.vipconf.domain.bo.FreeDataBO;
import com.commerical.vipconf.domain.bo.GearBO;
import com.commerical.vipconf.domain.bo.PayDataBO;
import com.commerical.vipconf.domain.vo.PriceGearVO;
import com.commerical.vipconf.service.PackageRecordService;
import com.commerical.vipconf.util.CacheUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class NewGiveFilterTypeStragety implements NewFilterTypeStragety {

    @Autowired
    private PackageRecordService packageRecordService;

    @Autowired
    private CacheUtils cacheUtils;
    /**
     * 普通filter过滤
     */
    @Override
    public void parseFilter(Filter filter, Gear gear, GearBO gearBO, PriceGearVO priceGearVO, List<ABDataBO> adDataList, List<PayDataBO> payDataList, List<FreeDataBO> freeDataList, String vipType, UserInfoBO userInfoBO){
        if(CollectionUtils.isNotEmpty(freeDataList)){
            return;
        }
        // 赠送
        FreeDataBO freeDataBO=new FreeDataBO();
        freeDataBO.setPrice(filter.getPrice());
        freeDataBO.setDoc(filter.getDoc());
        if(ObjectUtils.isNotNull(filter.getThreeMemberId())){
            try{
                PackageRecord packageRecord=cacheUtils.getPackageRecordByKeyCache.get(filter.getThreeMemberId());
                if(packageRecord!=null&& StringUtils.isNotBlank(packageRecord.getPackageValue())){
                    freeDataBO.setBaseData(packageRecord.getPackageValue());
                }
            }catch (Exception e){
                log.error("parseFilter getPackageRecordByKeyCache error",e);
            }
        }
        freeDataList.add(freeDataBO);
    }

}
