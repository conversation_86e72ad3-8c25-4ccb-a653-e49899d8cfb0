package com.commerical.vipconf.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.commerical.vipconf.domain.SongSheetSkin;
import com.kuwo.commercialization.common.message.MessageModel;
import java.util.List;
public interface SongSheetSkinService extends IService<SongSheetSkin> {

    SongSheetSkin getById(Long id);

    MessageModel saveOrUpdateBack(SongSheetSkin songSheetSkin);

    MessageModel deleteById(Long id);

    List<SongSheetSkin> getByCondition(SongSheetSkin songSheetSkin);

    List<SongSheetSkin> getAll();
}
