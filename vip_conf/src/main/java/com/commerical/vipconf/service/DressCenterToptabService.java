package com.commerical.vipconf.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.commerical.vipconf.domain.DressCenterToptab;
import com.baomidou.mybatisplus.extension.service.IService;
import com.commerical.vipconf.domain.vo.DressCenterToptabQueryVO;
import com.commerical.vipconf.domain.vo.DressCenterToptabVO;

/**
 * <p>
 * 装扮中心顶部导航栏配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-23
 */
public interface DressCenterToptabService extends IService<DressCenterToptab> {
    
    DressCenterToptab addOrUpdateDressCenterToptab(DressCenterToptabVO DressCenterToptabVO);

    void deleteDressCenterToptabById(Integer id,Boolean isTrue,Integer isDelete);

    IPage<DressCenterToptab> getDressCenterToptabList(DressCenterToptabQueryVO DressCenterToptabQueryVO);

    DressCenterToptab getDressCenterToptabInfoById(Integer id);

    DressCenterToptab getDressCenterToptabInfoByCode(String code);
}
