package com.commerical.vipconf.service;

import com.commerical.vipconf.domain.DressCenterBanner;
import com.commerical.vipconf.domain.PubDressCenterBanner;
import com.baomidou.mybatisplus.extension.service.IService;
import com.commerical.vipconf.domain.vo.DressCenterBannerVO;
import com.kuwo.commercialization.common.message.MessageModel;
import com.kuwo.commercialization.common.message.SystemCodeErrorConstant;

/**
 * <p>
 * 装扮中心 banner配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-28
 */
public interface PubDressCenterBannerService extends IService<PubDressCenterBanner> {

    SystemCodeErrorConstant pubDressCenterBanner(String code);

    int deleteDressCenterBannerByCode(String code);
}
