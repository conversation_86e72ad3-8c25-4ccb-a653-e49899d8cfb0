package com.commerical.vipconf.service;

import com.commerical.vipconf.config.exception.ServiceException;
import com.commerical.vipconf.domain.bo.GearBO;
import com.commerical.vipconf.domain.vo.PriceGearVO;
import com.commerical.vipconf.domain.vo.SinglePriceGearVO;
import com.commerical.vipconf.domain.vo.SingleSVIPInfoVO;

import java.util.Map;

public interface NewSinglePriceService {


    Object getSinglePriceInfo(SinglePriceGearVO priceGearVO) throws ServiceException;


    Map<String, String> getSingleLuxSVIPPrice(SinglePriceGearVO priceGearVO);

    Object getLuxSvipLowPriceInfo(SinglePriceGearVO priceGearVO) throws ServiceException;

    GearBO getSingleUpgradeSVIPGear(SinglePriceGearVO priceGearVO) throws ServiceException;

    SingleSVIPInfoVO getSingleSVIPInfo(SinglePriceGearVO priceGearVO) throws ServiceException;

    Object getRecallMusicCard(PriceGearVO priceGearVO);

    Object getSVipRecallYearGear(PriceGearVO priceGearVO);

}
