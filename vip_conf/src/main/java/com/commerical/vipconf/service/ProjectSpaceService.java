package com.commerical.vipconf.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.commerical.vipconf.domain.ProjectSpace;
import com.baomidou.mybatisplus.extension.service.IService;
import com.commerical.vipconf.domain.vo.ProjectSpaceQueryVO;
import com.commerical.vipconf.domain.vo.ProjectSpaceVO;

/**
 * <p>
 * 项目空间 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-01
 */
public interface ProjectSpaceService extends IService<ProjectSpace> {

    ProjectSpace addOrUpdateProjectSpace(ProjectSpaceVO ProjectSpaceVO);

    void deleteProjectSpaceById(Integer id,Boolean isTrue,Integer isDelete);

    IPage<ProjectSpace> getProjectSpaceList(ProjectSpaceQueryVO ProjectSpaceQueryVO);

    ProjectSpace getProjectSpaceInfoById(Integer id);

    ProjectSpace getProjectSpaceInfoByCode(String code);

}
