package com.commerical.vipconf.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.commerical.vipconf.domain.PublishPageModule;
import com.commerical.vipconf.domain.PublishProjectPage;
import com.commerical.vipconf.domain.bo.PublishProjectPageBO;
import com.commerical.vipconf.mapper.PublishPageModuleMapper;
import com.commerical.vipconf.mapper.PublishProjectPageMapper;
import com.commerical.vipconf.service.PublishPageModuleService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 发布后的页面模块 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-20
 */
@Service
public class PublishPageModuleServiceImpl extends ServiceImpl<PublishPageModuleMapper, PublishPageModule> implements PublishPageModuleService {


    @Autowired
    private PublishPageModuleMapper publishPageModuleMapper;

    @Autowired
    private PublishProjectPageMapper publishProjectPageMapper;

    /**
     * 项目模块 列表查询
     *
     * @param projectPageCode
     * @return
     */
    @Override
    public PublishProjectPageBO getPublishPageContent(String projectPageCode){
        if(StringUtils.isBlank(projectPageCode)){
            return null;
        }
        LambdaQueryWrapper<PublishProjectPage> wrapper= Wrappers.lambdaQuery();
        wrapper.eq(ObjectUtils.isNotEmpty(projectPageCode),PublishProjectPage::getCode,projectPageCode)
                .eq(PublishProjectPage::getIsDelete,0);
        PublishProjectPage publishProjectPage= publishProjectPageMapper.selectOne(wrapper);
        PublishPageModule pageModule= this.getPublicPageModuleList(projectPageCode);
        return PublishProjectPageBO.builder()
                .code(projectPageCode)
                .name(publishProjectPage.getName())
                .content(publishProjectPage.getContent())
                .pageModule(pageModule).build();
    }

    @Override
    public boolean updatePublishPageModuleStatus(String projectPageCode, Integer status) {
        LambdaUpdateWrapper< PublishPageModule> wrapper= Wrappers.lambdaUpdate();
        wrapper.set(PublishPageModule::getIsDelete,status)
                .eq(ObjectUtils.isNotEmpty(projectPageCode), PublishPageModule::getProjectPageCode,projectPageCode);
        return this.update(wrapper);
    }

    /**
     * 项目模块 列表查询
     *
     * @param projectPageCode
     * @return
     */
    @Override
    public PublishPageModule getPublicPageModuleList(String projectPageCode){
        if(StringUtils.isBlank(projectPageCode)){
            return null;
        }
        LambdaQueryWrapper<PublishPageModule> wrapper= Wrappers.lambdaQuery();
        wrapper.eq(ObjectUtils.isNotEmpty(projectPageCode),PublishPageModule::getProjectPageCode,projectPageCode)
                .eq(PublishPageModule::getIsDelete,0);
        return publishPageModuleMapper.selectOne(wrapper);
    }

}
