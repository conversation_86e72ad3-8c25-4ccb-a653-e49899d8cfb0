package com.commerical.vipconf.service;

import com.commerical.vipconf.domain.PageModule;
import com.commerical.vipconf.domain.PublishPageModule;
import com.baomidou.mybatisplus.extension.service.IService;
import com.commerical.vipconf.domain.bo.ProjectPageBO;
import com.commerical.vipconf.domain.bo.PublishProjectPageBO;

/**
 * <p>
 * 发布后的页面模块 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-20
 */
public interface PublishPageModuleService extends IService<PublishPageModule> {

    PublishProjectPageBO getPublishPageContent(String projectPageCode);

    boolean updatePublishPageModuleStatus(String projectPageCode,Integer status);

    PublishPageModule getPublicPageModuleList(String projectPageCode);
}
