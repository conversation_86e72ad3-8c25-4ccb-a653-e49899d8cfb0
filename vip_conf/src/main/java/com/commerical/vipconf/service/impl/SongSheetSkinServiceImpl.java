package com.commerical.vipconf.service.impl;
import java.util.Date;
import java.util.List;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.commerical.vipconf.domain.SongSheetSkin;
import com.commerical.vipconf.enums.DeleteStatusEnum;
import com.commerical.vipconf.mapper.SongSheetSkinMapper;
import com.commerical.vipconf.service.SongSheetSkinService;
import com.kuwo.commercialization.common.message.MessageModel;
import com.kuwo.commercialization.common.message.SystemCodeErrorConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@DS("vip")
@Service
public class SongSheetSkinServiceImpl extends ServiceImpl<SongSheetSkinMapper, SongSheetSkin> implements SongSheetSkinService {
	private Logger logger = LoggerFactory.getLogger(SongSheetSkinServiceImpl.class);
    @Autowired
    private SongSheetSkinMapper songSheetSkinMapper;

    public SongSheetSkin getById(Long id) {
    	SongSheetSkin liveBookingAward = songSheetSkinMapper.selectByPrimaryKey(id);  	
    	return liveBookingAward;
    } 

    public MessageModel saveOrUpdateBack(SongSheetSkin songSheetSkin) {
    	MessageModel messageModel = new MessageModel();
    	if(songSheetSkin==null) {
    		return new MessageModel(SystemCodeErrorConstant.FAIL);
    	}else {
    		if (null == songSheetSkin.getId() || 0L == songSheetSkin.getId()) {
        		songSheetSkin.setCreateTime(new Date());
        		songSheetSkin.setUpdateTime(new Date());
				if(songSheetSkin.getShowOrder()==null){
					songSheetSkin.setShowOrder(10000);
				}
        		songSheetSkin.setDeleteStatus(DeleteStatusEnum.deleteno.getType());
        		songSheetSkinMapper.insertSelective(songSheetSkin);
            } else {
            	SongSheetSkin songSheetSkinOri = songSheetSkinMapper.selectByPrimaryKey(songSheetSkin.getId());
            	if(songSheetSkinOri==null) {
            		messageModel.setCode(SystemCodeErrorConstant.NULL_OBJECT.getCode());
                    messageModel.setDesc(SystemCodeErrorConstant.NULL_OBJECT.getMessage());
            		return messageModel;
            	}
            	songSheetSkin.setUpdateTime(new Date());
            	songSheetSkinMapper.updateByPrimaryKeySelective(songSheetSkin);
            }
    	}
    	
    	return messageModel;
    }

	public MessageModel deleteById(Long id) {
		MessageModel messageModel = new MessageModel();
		SongSheetSkin songSheetSkin = songSheetSkinMapper.selectByPrimaryKey(id); 
		if(songSheetSkin==null) {
			return new MessageModel();
		}else {
			//做逻辑删除
			songSheetSkin.setDeleteStatus(DeleteStatusEnum.deleteyes.getType());
			songSheetSkinMapper.updateByPrimaryKeySelective(songSheetSkin);
			return messageModel;
		}
    	
	}


	public List<SongSheetSkin> getByCondition(SongSheetSkin songSheetSkin) {
		 List <SongSheetSkin> liveBookingAwards = songSheetSkinMapper.selectByCondition(songSheetSkin);	
		return liveBookingAwards;
	}

	public List<SongSheetSkin> getAll() {
		List <SongSheetSkin> liveBookingAwards = songSheetSkinMapper.selectAll();
		return liveBookingAwards;
	}

}
