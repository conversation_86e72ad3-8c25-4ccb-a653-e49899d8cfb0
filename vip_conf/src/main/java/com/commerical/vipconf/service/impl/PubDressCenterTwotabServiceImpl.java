package com.commerical.vipconf.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.commerical.vipconf.domain.DressCenterTwotab;
import com.commerical.vipconf.domain.PubDressCenterTwotab;
import com.commerical.vipconf.domain.PubDressCenterTwotab;
import com.commerical.vipconf.mapper.PubDressCenterTwotabMapper;
import com.commerical.vipconf.mapper.PubDressCenterTwotabMapper;
import com.commerical.vipconf.service.DressCenterTwotabService;
import com.commerical.vipconf.service.PubDressCenterTwotabService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <p>
 * 装扮中心二级tab 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-29
 */
@Service
public class PubDressCenterTwotabServiceImpl extends ServiceImpl<PubDressCenterTwotabMapper, PubDressCenterTwotab> implements PubDressCenterTwotabService {

    @Autowired
    private PubDressCenterTwotabMapper pubDressCentePicMapper;

    @Autowired
    private DressCenterTwotabService dressCenterTwotabService;

    /**
     * 添加/修改
     *
     * @param
     * @return
     */
    @Override
    public boolean pubDressCenterTwotab(String code) {
        DressCenterTwotab dressCenterTwotab=dressCenterTwotabService.getDressCenterTwotabInfoByCode(code);
        PubDressCenterTwotab pubDressCenterTwotab= this.getDressCenterPicInfoByCode(code);
        if(pubDressCenterTwotab==null){
            PubDressCenterTwotab pubDressCenter=PubDressCenterTwotab.builder().build();
            BeanUtils.copyProperties(dressCenterTwotab, pubDressCenter);
            pubDressCenter.setCreateTime(new Date());
            pubDressCenter.setUpdateTime(new Date());
            this.save(pubDressCenter);
        }else{
            // 修改
            LambdaUpdateWrapper<PubDressCenterTwotab> wrapper= Wrappers.lambdaUpdate();
            wrapper.set(PubDressCenterTwotab::getResourceId,dressCenterTwotab.getResourceId())
                    .set(PubDressCenterTwotab::getToptabCode,dressCenterTwotab.getToptabCode())
                    .set(PubDressCenterTwotab::getShowSkinAutoColor,dressCenterTwotab.getShowSkinAutoColor())
                    .set(PubDressCenterTwotab::getShowSkinAutoPic,dressCenterTwotab.getShowSkinAutoPic())
                    .set(PubDressCenterTwotab::getShowBgAutoPic,dressCenterTwotab.getShowBgAutoPic())
                    .set(PubDressCenterTwotab::getSort,dressCenterTwotab.getSort())
                    .set(PubDressCenterTwotab::getEditorName,dressCenterTwotab.getEditorName())
                    .set(PubDressCenterTwotab::getUpdateTime,new Date())
                    .eq(PubDressCenterTwotab::getCode,dressCenterTwotab.getCode());
            this.update(wrapper);
        }
        return true;
    }

    /**
     * 装扮中心Pic详情获取
     *
     * @param code code
     * @return PubDressCenterTwotab 详情
     */
    public PubDressCenterTwotab getDressCenterPicInfoByCode(String code) {
        LambdaQueryWrapper<PubDressCenterTwotab> wrapper= Wrappers.lambdaQuery();
        wrapper.eq(PubDressCenterTwotab::getCode,code);
        return pubDressCentePicMapper.selectOne(wrapper);
    }
}
