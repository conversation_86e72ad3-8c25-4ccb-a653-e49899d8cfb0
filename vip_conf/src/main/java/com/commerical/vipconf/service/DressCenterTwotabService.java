package com.commerical.vipconf.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.commerical.vipconf.config.exception.ServiceException;
import com.commerical.vipconf.domain.DressCenterTwotab;
import com.baomidou.mybatisplus.extension.service.IService;
import com.commerical.vipconf.domain.vo.DressCenterTwotabQueryVO;
import com.commerical.vipconf.domain.vo.DressCenterTwotabVO;

import java.util.List;

/**
 * <p>
 * 装扮中心二级tab 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-23
 */
public interface DressCenterTwotabService extends IService<DressCenterTwotab> {

    DressCenterTwotab addOrUpdateDressCenterTwotab(DressCenterTwotabVO DressCenterTwotabVO);

    void deleteDressCenterTwotabById(Integer id,Boolean isTrue,Integer isDelete);

    IPage<DressCenterTwotab> getDressCenterTwotabList(DressCenterTwotabQueryVO DressCenterTwotabQueryVO) throws ServiceException;

    DressCenterTwotab getDressCenterTwotabInfoById(Integer id);

    DressCenterTwotab getDressCenterTwotabInfoByCode(String code);

    List<DressCenterTwotab> getDressCenterTwotabByTopCode(String code);


}
