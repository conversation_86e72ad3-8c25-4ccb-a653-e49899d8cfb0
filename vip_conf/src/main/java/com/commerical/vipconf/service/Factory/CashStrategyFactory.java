package com.commerical.vipconf.service.Factory;

import com.commerical.vipconf.enums.FilterTypeEnum;
import com.commerical.vipconf.service.cashstragety.CashAddBuyFilterTypeStragety;
import com.commerical.vipconf.service.cashstragety.CashCommonFilterTypeStragety;
import com.commerical.vipconf.service.cashstragety.CashGiveFilterTypeStragety;
import com.commerical.vipconf.service.cashstragety.FilterTypeStragety;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
public class CashStrategyFactory {
    private final Map<Integer, FilterTypeStragety> strategyMaps = new ConcurrentHashMap();

    @Autowired
    private CashAddBuyFilterTypeStragety cashAddBuyFilterTypeStragety;
    @Autowired
    public CashCommonFilterTypeStragety cashCommonFilterTypeStragety;
    @Autowired
    private CashGiveFilterTypeStragety cashGiveFilterTypeStragety;

    public FilterTypeStragety getFilterStrategy(Integer filterType) {
      if(!strategyMaps.containsKey(filterType)){
          strategyMaps.putIfAbsent(FilterTypeEnum.COMMON.getType(), cashCommonFilterTypeStragety);
          strategyMaps.putIfAbsent(FilterTypeEnum.ADD_BUY.getType(), cashAddBuyFilterTypeStragety);
          strategyMaps.putIfAbsent(FilterTypeEnum.GIVE.getType(), cashGiveFilterTypeStragety);
      }
        return strategyMaps.get(filterType);
    }


}
