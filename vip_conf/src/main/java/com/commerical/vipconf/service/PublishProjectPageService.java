package com.commerical.vipconf.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.commerical.vipconf.domain.PublishProjectPage;
import com.commerical.vipconf.domain.bo.PublishProjectPageBO;

/**
 * <p>
 * 发布后的项目页面 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-20
 */
public interface PublishProjectPageService extends IService<PublishProjectPage> {

    boolean updatePublishProjectPageStatus(String projectPageCode,Integer status);

    PublishProjectPageBO getPageContent(String projectPageCode);

    PublishProjectPage getProjectPageByChannel(String channel);

}
