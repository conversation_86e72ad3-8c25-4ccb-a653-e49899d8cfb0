package com.commerical.vipconf.service.stragety.newStragety;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.commerical.abserviceapi.domain.UserInfoBO;
import com.commerical.vipconf.config.constant.VipConstant;
import com.commerical.vipconf.domain.Filter;
import com.commerical.vipconf.domain.Gear;
import com.commerical.vipconf.domain.PackageRecord;
import com.commerical.vipconf.domain.bo.ABDataBO;
import com.commerical.vipconf.domain.bo.FreeDataBO;
import com.commerical.vipconf.domain.bo.GearBO;
import com.commerical.vipconf.domain.bo.PayDataBO;
import com.commerical.vipconf.domain.vo.PriceGearVO;
import com.commerical.vipconf.enums.GearTypeEnum;
import com.commerical.vipconf.enums.VipTypeEnum;
import com.commerical.vipconf.nacos.VipConfConfigNacos;
import com.commerical.vipconf.service.PackageRecordService;
import com.commerical.vipconf.util.CacheUtils;
import com.commerical.vipconf.util.FreeModeConst;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

@Slf4j
@Component
public class NewCommonFilterTypeStragety implements NewFilterTypeStragety {

    @Autowired
    private PackageRecordService packageRecordService;

    @Value("${vip.domain}")
    private String vipDomain;

    @Autowired
    private CacheUtils cacheUtils;

    @Autowired
    private VipConfConfigNacos vipConfConfigNacos;

    private List<Integer> wxPayType= Arrays.asList(31,54,64,78,122,123);
    private List<Integer> aliPayType= Arrays.asList(39,44,49,82,101,102);

    /**
     * 普通filter过滤
     */
    @Override
    public void parseFilter(Filter filter, Gear gear, GearBO gearBO, PriceGearVO priceGearVO, List<ABDataBO> adDataList, List<PayDataBO> payDataList, List<FreeDataBO> freeDataList,String vipType, UserInfoBO userInfoBO){
        if(CollectionUtils.isNotEmpty(adDataList)){
            return;
        }
        try{
            // ios规则过滤
            boolean filterCheckForIos = filterCheckForIos(filter, gear, priceGearVO);
            if (filterCheckForIos) {
                log.info("parseFilter filterCheckForIos filterId={} pass!",filter.getId());
                return;
            }
            ABDataBO abDataBO=new ABDataBO();
            abDataBO.setPrice(filter.getPrice());
            abDataBO.setRenewalPrice(filter.getRenewalPrice());
            abDataBO.setDoc(filter.getDoc());
            boolean iosDiscount=IsIOSDiscount(gear,priceGearVO,filter);
            abDataBO.setIsIosDiscounts(iosDiscount);
            abDataBO.setIsFirstRenewal(iosDiscount);
            boolean newUser =IsNewUser(gear,priceGearVO,filter);
            abDataBO.setIsNewUser(newUser);
            if(StringUtils.isNotBlank(filter.getExtend())){
                String extend = filter.getExtend();
                if(JSONUtil.isTypeJSON(extend)){
                    JSONObject object= JSONObject.parseObject(extend);
                    if(object.containsKey("singleProd")&&object.getString("singleProd").equals("1")){
                        abDataBO.setSingleProdImg(object.getString("singleProdImg"));
                        priceGearVO.setSingleGearId(filter.getGearId());
                    }
                }
            }
            if(StringUtils.isNotBlank(filter.getPayWay())&&priceGearVO.getPlatform().equals("ios")){
                String payWay=filter.getPayWay();
                JSONObject jsonObject= JSONObject.parseObject(payWay);
                if(jsonObject.containsKey("list")){
                    JSONArray array= jsonObject.getJSONArray("list");
                    for(int i=0;i<array.size();i++){
                        JSONObject itemJSon= array.getJSONObject(i);
                        if(itemJSon.getString("id").equals("wxout")){
                            array.remove(itemJSon);
                            log.info("parseFilter filter id ={} has wechat",filter.getId());
                            filter.setPayWay(jsonObject.toJSONString());
                        }
                    }
                }
            }
            abDataBO.setPayWay(filter.getPayWay());
            abDataBO.setFilterId(filter.getId());
            abDataBO.setOrderRank(filter.getOrderRank());
            abDataBO.setExtend(filter.getExtend());
            abDataBO.setFilterFromsrc(filter.getFilterFromsrc());
            if(ObjectUtils.isNotNull(filter.getEndTime())){
                long lastTime=  DateUtil.between(filter.getEndTime(),new Date(), DateUnit.MS);
                // 剩余时间
                abDataBO.setLastTime(lastTime);
            }
            if(gear.getGearType().equals(GearTypeEnum.UNION_VIP.getMonths())&&ObjectUtils.isNotNull(filter.getUnionId())){
                PackageRecord packageRecord=cacheUtils.getPackageRecordByKeyCache.get(filter.getThreeMemberId());
                abDataBO.setBaseData(packageRecord.getPackageValue());
            }
            if(!priceGearVO.getPayDeskSign().equals("noAdPop")&&StringUtils.isNotBlank(priceGearVO.getSource())&&(priceGearVO.getSource().indexOf("hw")>0||priceGearVO.getSource().indexOf("138")>0)&&gear.getAutoPay()==1&&JSONUtil.isTypeJSON(filter.getDoc())){
                if(FreeModeConst.versionLargeThan(priceGearVO.getSource(),11830)){
                    String doc= filter.getDoc();
                    JSONObject docJSON= JSONObject.parseObject(doc);
                    docJSON.put("autoPayBtnShow","1");
                    docJSON.put("autoPayChecked","0");
                    filter.setDoc(docJSON.toJSONString());
                }
            }
            if(!priceGearVO.getPayDeskSign().equals("noAdPop")&&StringUtils.isNotBlank(priceGearVO.getSource())&&priceGearVO.getSource().indexOf("_18")>0&&gear.getAutoPay()==1&&JSONUtil.isTypeJSON(filter.getDoc())){
                if(FreeModeConst.versionLargeThan(priceGearVO.getSource(),11830)){
                    String doc= filter.getDoc();
                    JSONObject docJSON= JSONObject.parseObject(doc);
                    docJSON.put("autoPayBtnShow","1");
                    docJSON.put("autoPayChecked","0");
                    filter.setDoc(docJSON.toJSONString());
                }
            }

            if(priceGearVO.getPayDeskSign().equals("doudi_watchpay")||priceGearVO.getPayDeskSign().equals("watchPay")){
                if(userInfoBO!=null&&userInfoBO.getVipWatch1Expire()!=null&&userInfoBO.getVipWatch1Expire()>new Date().getTime()&&gear.getVipType().equals(VipTypeEnum.WATCH_VIP.getType())){
                    String doc= filter.getDoc();
                    JSONObject docJSON= JSONObject.parseObject(doc);
                    docJSON.put("btnText","立即续费");
                    filter.setDoc(docJSON.toJSONString());
                }
                if(userInfoBO!=null&&userInfoBO.getSvipExpire()!=null&&userInfoBO.getSvipExpire()>new Date().getTime()&&gear.getVipType().equals(VipTypeEnum.SUPER_VIP.getType())){
                    String doc= filter.getDoc();
                    JSONObject docJSON= JSONObject.parseObject(doc);
                    docJSON.put("btnText","立即续费");
                    filter.setDoc(docJSON.toJSONString());
                }
            }
            if(FreeModeConst.payDeskList.contains(priceGearVO.getPayDeskSign())||
                    (Objects.equals(priceGearVO.getEarphonePopSign(),1))){
                String doc= filter.getDoc();
                JSONObject docJSON= JSONObject.parseObject(doc);
                if(priceGearVO.getIsOFFAutoPay()==1&&gear.getAutoPay()==1){
                    docJSON.put("autoPayBtnShow","1");
                    docJSON.put("autoPayChecked","0");
                }else{
                    docJSON.put("autoPayBtnShow","0");
                    docJSON.put("autoPayChecked","0");
                }
                filter.setDoc(docJSON.toJSONString());
            }else if(gear.getAutoPay()==1){
                    String doc= filter.getDoc();
                    if(org.apache.commons.lang3.StringUtils.isNotBlank(doc)){
                        JSONObject docJSON= JSONObject.parseObject(doc);
                        docJSON.put("autoPayBtnShow","1");
                        docJSON.put("autoPayChecked","0");
                        filter.setDoc(docJSON.toJSONString());
                    }
            }else{
                String doc= filter.getDoc();
                if(org.apache.commons.lang3.StringUtils.isNotBlank(doc)){
                    JSONObject docJSON= JSONObject.parseObject(doc);
                    docJSON.put("autoPayBtnShow","0");
                    docJSON.put("autoPayChecked","0");
                    filter.setDoc(docJSON.toJSONString());
                }
            }
            if(VipConstant.appPayDeskSigns.contains(priceGearVO.getPayDeskSign())&&priceGearVO.getPlatform().equals("ar")&&StringUtils.isNotBlank(priceGearVO.getSource())&&gear.getAutoPay()==1&&JSONUtil.isTypeJSON(filter.getDoc())){
                try {
                    if(CacheUtils.cashMap.getIfPresent("checkVersions")!=null){
                        Set<String> srcSets= (Set<String>) CacheUtils.cashMap.getIfPresent("checkVersions");
                        if(CollectionUtils.isNotEmpty(srcSets)&&srcSets.contains(priceGearVO.getSource())&&gear.getAutoPay()==1){
                            String doc= filter.getDoc();
                            JSONObject docJSON= JSONObject.parseObject(doc);
                            docJSON.put("autoPayBtnShow","1");
                            docJSON.put("autoPayChecked","0");
                            filter.setDoc(docJSON.toJSONString());
                            log.info("checkVersions version is check, source ={}",priceGearVO.getSource());
                        }
                    }
                }catch (Exception e){
                    log.error("source parse error!");
                }
            }
            checkFilterRenewalValid(filter, priceGearVO);
            abDataBO.setPayWay(filter.getPayWay());
            abDataBO.setDoc(filter.getDoc());
            adDataList.add(abDataBO);
        }catch (Exception e){
            log.error("parseFilter has error ！",e);
        }
    }


    /**
     * ios策略
     * @param filter
     * @param gear
     * @param priceGearVO
     * @return
     */
    public boolean filterCheckForIos(Filter filter, Gear gear, PriceGearVO priceGearVO){
        if(priceGearVO.getPlatform().equals("ios")&gear.getAutoPay()==1){
            if (StringUtils.equals(gear.getVipType(), VipTypeEnum.VIP_LUXURY.getType())) {
                if (filter.getFilterFirstPay() != null
                        && filter.getFilterFirstPay().equals(1)
                        && !priceGearVO.getFirstPay()&&filter.getOrderRank()!=1) {
                    return true;
                }
                if ((filter.getFilterFirstPay() == null
                        || !filter.getFilterFirstPay().equals(1))
                        && priceGearVO.getFirstPay()&&filter.getOrderRank()!=1) {
                    return true;
                }
                if (filter.getFilterFirstRenew() != null
                        && (filter.getFilterFirstRenew().equals(1)
                        || filter.getFilterFirstRenew().equals(4)
                        || filter.getFilterFirstRenew().equals(5))
                        && !priceGearVO.getFirstRenew()&&filter.getOrderRank()!=1) {
                    return true;
                }

            }
            if (StringUtils.equals(gear.getVipType(), VipTypeEnum.SUPER_VIP.getType())) {
                if (filter.getFilterFirstPay() != null
                        && filter.getFilterFirstPay().equals(1)
                        && !priceGearVO.getFirstSvipPay()&&filter.getOrderRank()!=1) {
                    return true;
                }
                if ((filter.getFilterFirstPay() == null
                        || !filter.getFilterFirstPay().equals(1))
                        && priceGearVO.getFirstSvipPay()&&filter.getOrderRank()!=1) {
                    return true;
                }
                if (filter.getFilterFirstRenew() != null
                        && (filter.getFilterFirstRenew().equals(1)
                        || filter.getFilterFirstRenew().equals(4)
                        || filter.getFilterFirstRenew().equals(5))
                        && !priceGearVO.getFirstRenewalSvipPay()&&filter.getOrderRank()!=1) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * check 单挡位是否合法时间内
     *
     * @return
     */
    public void checkFilterRenewalValid(Filter filter,PriceGearVO priceGearVO){
        if(priceGearVO.getPlatform().equals("ar")&& com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(filter.getExtend())){
            String extend = filter.getExtend();
            if(JSONUtil.isTypeJSON(extend)) {
                JSONObject object = JSONObject.parseObject(extend);
                if (object.containsKey("IsDetainUser") && object.getString("IsDetainUser").equals("1")) {
                    //log.info("checkFilterRenewalValid priceGearVO={}",JSONUtil.toJsonStr(priceGearVO));
                    String payWay=filter.getPayWay();
                    JSONObject jsonObject= JSONObject.parseObject(payWay);
                    int payType=priceGearVO.getPayType();
                    if(payType!=0){
                        priceGearVO.setShowRenewal(true);
                        if(jsonObject.containsKey("list")){
                            JSONArray array= jsonObject.getJSONArray("list");
                            for(int i=0;i<array.size();i++){
                                JSONObject itemJSon= array.getJSONObject(i);
                                if(aliPayType.contains(payType)){
                                    if(itemJSon.getString("id").equals("wx")){
                                        array.remove(itemJSon);
                                    }

                                }
                                if(wxPayType.contains(payType)){
                                    if(itemJSon.getString("id").equals("ali")){
                                        array.remove(itemJSon);
                                    }
                                }
                            }
                            filter.setPayWay(jsonObject.toJSONString());
                        }
                    }else{
                        priceGearVO.setShowRenewal(true);
                        if(jsonObject.containsKey("list")){
                            JSONArray array= jsonObject.getJSONArray("list");
                            for(int i=0;i<array.size();i++){
                                JSONObject itemJSon= array.getJSONObject(i);
                                if(itemJSon.getString("id").equals("ali")){
                                    array.remove(itemJSon);
                                }
                            }
                            filter.setPayWay(jsonObject.toJSONString());
                        }

                    }

                }
            }
        }
    }

    /**
     * 优惠档位判断
     *
     * @param gear
     * @param priceGearVO
     * @return
     */
    public Boolean IsIOSDiscount(Gear gear,PriceGearVO priceGearVO,Filter filter){
        if (priceGearVO.getPlatform().equals("ar")) {
            return false;
        }
        if(priceGearVO.getPlatform().equals("ios")) {
            String doc = filter.getDoc();
            JSONObject docJSON = JSONObject.parseObject(doc);
            if (docJSON.containsKey("isIosDiscounts") && StringUtils.isNotBlank(docJSON.getString("isIosDiscounts"))) {
                String docStr = docJSON.getString("isIosDiscounts");
                if (docStr.equals("1")) {
                    if (gear.getVipType().equals(VipTypeEnum.VIP_LUXURY.getType())) {
                        log.info("enter ios discount gear vip!");
                        return priceGearVO.getFirstRenew();
                    }
                    if (gear.getVipType().equals(VipTypeEnum.SUPER_VIP.getType())) {
                        log.info("enter ios discount gear svip!");
                        return priceGearVO.getFirstRenewalSvipPay();
                    }
                }
            }
        }
        return false;
    }

    /**
     * 是否新客
     *
     * @param gear
     * @param priceGearVO
     * @param filter
     * @return
     */
    public Boolean IsNewUser(Gear gear,PriceGearVO priceGearVO,Filter filter){
        try{
            if (priceGearVO.getPlatform().equals("ar")) {
                return false;
            }
            if(priceGearVO.getPlatform().equals("ios")) {
                if (filter.getFilterFirstPay()!=null&&filter.getFilterFirstPay().equals(1)) {
                    if (gear.getVipType().equals(VipTypeEnum.VIP_LUXURY.getType())) {
                        return priceGearVO.getFirstPay();
                    }
                    if (gear.getVipType().equals(VipTypeEnum.SUPER_VIP.getType())) {
                        return priceGearVO.getFirstSvipPay();
                    }
                }
            }
        }catch (Exception e){
            log.error("IsNewUser has error!",e);
        }
        return false;
    }

}
