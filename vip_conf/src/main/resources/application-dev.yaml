spring:
  cloud:
    nacos:
      discovery:
        server-addr: **********:8848
      config:
        server-addr: **********:8848
        group: DEFAULT_GROUP
    sentinel:
      transport:
        dashboard: *********:8081
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          url: ***********************************************************************************************************************
          username: pay_vip_write
          password: vip^_^_dr521
          driverClassName: com.mysql.cj.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
        vip:
          url: ***********************************************************************************************************************
          username: pay_vip_write
          password: vip^_^_dr521
          driverClassName: com.mysql.cj.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
      druid:
        initial-size: 5
        minIdle: 5
        maxActive: 20
        maxWait: 60000
        timeBetweenEvictionRunsMillis: 60000
        minEvictableIdleTimeMillis: 60000
        validationQuery: SELECT 1
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        filters: stat,wall
        connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
        logSlowSql: true

dubbo:
  application:
    name: abService-consoumer
  protocol:
    name: dubbo
    port: -1
  registries:
    p_1:
      address: nacos://**********:8848?namespace=67f2182d-0b31-444c-8746-054c171d7a11
      group: dev
  scan:
    base-packages: com.commerical.vipconf.service
  consumer:
    filter: tracing
server:
  port: 8442
mybatis-plus:
  # 自定义xml文件路径
  mapper-locations: classpath:com/commerical/vipconf/mapper/xml/*.xml
vip:
   domain: http://testvip.kuwo.cn
   wapidomain: https://test-wapi.kuwo.cn
dress:
  domain: http://testvip.kuwo.cn
nacos:
  config:
    namespace: 6ed67133-66f5-472a-85e8-7cb5f90def6e

redis:
  names: d1,d2,d3,priceValidator,d4
  config:
    d1:
      address: redis://*************:6380
      password: 2019yyFF$%
      selectDb: 2
    d2:
      address: redis://*************:6380
      password: 2019yyFF$%
      selectDb: 2
      userStringCodec: true
    d3:
      address: redis://*********:6382
      password: 2019yyFF$%
      selectDb: 0
      userStringCodec: true
    priceValidator:
      address: redis://*********:6382
      password: 2019yyFF$%
      selectDb: 0
      userStringCodec: true
    d4:
      address: redis://*************:6380
      password: 2019yyFF$%
      selectDb: 0
    userPlay:
      address: redis://************:6379
      password: "!oiBR&wK5Tuy124"
      select-db: 0
      user-string-codec: true
    cashback:
      address: redis://*************:6380
      password: 2019yyFF$%
      selectDb: 0
      userStringCodec: true
    logReport:
      address: redis://*************:6380
      password: 2019yyFF$%
      select-db: 9
      user-string-codec: true

validate:
  login:
    url : http://loginserver.kuwo.cn/u.s?type=validate&uid=%s&sid=%s&req_enc=utf8&res_enc=utf8
check:
  virtual:
    user:
      login:
        url: http://loginserver.kuwo.cn/u.s?type=virtual_valid&uid=%s&sid=%s&req_enc=utf8&res_enc=utf8
kuwo:
  monitor:
    enabled: true
    sentinel:
      address: **********:8848
      namespace: 6f61394b-0c40-42af-bb2c-72944d8439c3
      groupId: dev
      dataId: vipconf-flow-rules

musicpay:
  domain: http://musicpaytest3.kuwo.cn/music.pay

dc:
  domain: https://testdc2.kuwo.cn
logging:
  level:
    com.commerical.vipconf.service.impl.CommonConfServiceImpl: debug
