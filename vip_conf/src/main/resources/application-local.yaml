spring:
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
    sentinel:
      transport:
        dashboard: 127.0.0.1:8080
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          url: **********************************************************************************************************************
          username: root
          password: root
          driverClassName: com.mysql.cj.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
        vip:
          url: **********************************************************************************************************************
          username: root
          password: root
          driverClassName: com.mysql.cj.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
      druid:
        initial-size: 5
        minIdle: 5
        maxActive: 20
        maxWait: 60000
        timeBetweenEvictionRunsMillis: 60000
        minEvictableIdleTimeMillis: 60000
        validationQuery: SELECT 1
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        filters: stat,wall
        connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
        logSlowSql: true
redis:
  names: d1, d2, d3,priceValidator,d4
  config:
    d1:
      address: redis://127.0.0.1:6379
      selectDb: 2
    d2:
      address: redis://127.0.0.1:6379
      userStringCodec: true
    d3:
      address: redis://127.0.0.1:6379
      userJsonCodec: true
    priceValidator:
      address: redis://127.0.0.1:6379
      userJsonCodec: true
    d4:
      address: redis://127.0.0.1:6379
      userJsonCodec: true
    userPlay:
      address: redis://127.0.0.1:6379
      user-string-codec: true
    logReport:
      address: redis://127.0.0.1:6379
      select-db: 9
      user-string-codec: true
    cashback:
      address: redis://127.0.0.1:6379
      userStringCodec: true
dubbo:
  application:
    name: abService-consoumer
  protocol:
    name: dubbo
    port: -1
  registries:
    local_1:
      address: nacos://127.0.0.1:8848?namespace=a62ae846-2e12-424a-9d71-bf58f12accf1
      group: local
  scan:
    base-packages: com.commerical.vipconf.service
  consumer:
    filter: tracing
  cloud:
    subscribed-services: abService-provider

server:
  port: 8442
mybatis-plus:
  # 自定义xml文件路径
  mapper-locations: classpath:com/commerical/vipconf/mapper/xml/*.xml
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
vip:
  domain: http://testvip.kuwo.cn
  wapidomain: https://test-wapi.kuwo.cn
dress:
  domain: http://vip1.kuwo.cn

validate:
  login:
    url : http://loginserver.kuwo.cn/u.s?type=validate&uid=%s&sid=%s&req_enc=utf8&res_enc=utf8
check:
  virtual:
    user:
      login:
        url: http://loginserver.kuwo.cn/u.s?type=virtual_valid&uid=%s&sid=%s&req_enc=utf8&res_enc=utf8


kuwo:
  monitor:
    enabled: true
    sentinel:
      address: 127.0.0.1:8848
      namespace: 8b40d906-fa9d-4fc9-b164-f43ad92e3835
      groupId: local
      dataId: vipconf-flow-rules
      
nacos:
  config:
    namespace: 8c294c72-e12f-44c2-a05a-db75b6749bc1

sentinel:
  nacos:
    namespace: df1616de-b65e-44b6-ab0e-debdc7cca419
    groupId: prod
    vip-rule-dataId: vipConf-flow-rules
    vip-Degrade-dataId: vipConf-degrade-rules

musicpay:
  domain: http://musicpaytest3.kuwo.cn/music.pay

dc:
  domain: https://testdc.kuwo.cn
