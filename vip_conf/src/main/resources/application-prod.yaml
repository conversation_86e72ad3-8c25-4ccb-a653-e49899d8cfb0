spring:
  cloud:
    nacos:
      discovery:
        server-addr: ************:8848
      config:
        server-addr: ************:8848
        group: DEFAULT_GROUP
    sentinel:
      transport:
        dashboard: *************:8080
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          url: *************************************************************************************************************************
          username: vip_conf_write
          password: vip^_^_dr521
          driverClassName: com.mysql.cj.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
        vip:
          url: *******************************************************************************************************************************************************
          username: pay_vip_read
          password: vip0_0_5201314
          driverClassName: com.mysql.cj.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
      druid:
        initial-size: 5
        minIdle: 5
        maxActive: 20
        maxWait: 60000
        timeBetweenEvictionRunsMillis: 60000
        minEvictableIdleTimeMillis: 60000
        validationQuery: SELECT 1
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        filters: stat,wall
        connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
        logSlowSql: true

redis:
  names: d1, d2, d3,priceValidator,d4,indicator
  config:
    d1:
      address: redis://************:6380
      password: 2019yyFF$%
      selectDb: 2
    d2:
      address: redis://************:6380
      password: 2019yyFF$%
      userStringCodec: true
    d3:
      address: redis://************:6380
      password: 2019yyFF$%
      userStringCodec: true
    priceValidator:
      address: redis://************:6380
      password: 2019yyFF$%
      userStringCodec: true
    d4:
      address: redis://***********:6380
      password: 2019yyFF$%
      selectDb: 0
    userPlay:
      address: redis://************:6379
      password: "!oiBR&wK5Tuy124"
      select-db: 0
      user-string-codec: true
    indicator:
      address: redis://************:6379
      password: SJtO$3jJlxurlnS@
      selectDb: 0
      user-string-codec: true
    cashback:
      address: redis://***********:6380
      password: 2019yyFF$%
      selectDb: 0
      userStringCodec: true
    # 日志统计
    logReport:
      address: redis://*************:6380
      password: 2019yyFF$%
      select-db: 0
      user-string-codec: true

login:
  check:
    url: http://loginserver.kuwo.cn/u.s?type=validate&uid=%s&sid=%s&req_enc=utf8&res_enc=utf8

dubbo:
  application:
    name: abService-consoumer
  protocol:
    name: dubbo
    port: -1
  registries:
    p_1:
      address: nacos://************:8848?namespace=4566ca54-8e14-4749-a4c5-4ffc028d6377
      group: prod

  scan:
    base-packages: com.commerical.vipconf.service
  consumer:
    filter: tracing
server:
  port: 8442
mybatis-plus:
  # 自定义xml文件路径
  mapper-locations: classpath:com/commerical/vipconf/mapper/xml/*.xml
vip:
  domain: http://vip1.kuwo.cn
  wapidomain: https://wapi.kuwo.cn
validate:
  login:
    url : http://loginserver.kuwo.cn/u.s?type=validate&uid=%s&sid=%s&req_enc=utf8&res_enc=utf8
check:
  virtual:
    user:
      login:
        url: http://loginserver.kuwo.cn/u.s?type=virtual_valid&uid=%s&sid=%s&req_enc=utf8&res_enc=utf8

kuwo:
  monitor:
    enabled: true
    sentinel:
      address: ************:8848
      namespace: df1616de-b65e-44b6-ab0e-debdc7cca419
      groupId: prod
      dataId: vipconf-flow-rules
dress:
  domain: http://vip1.kuwo.cn
nacos:
  config:
    namespace: 8c294c72-e12f-44c2-a05a-db75b6749bc1
sentinel:
  nacos:
    namespace: df1616de-b65e-44b6-ab0e-debdc7cca419
    groupId: prod
    vip-rule-dataId: vipConf-flow-rules
    vip-Degrade-dataId: vipConf-degrade-rules

musicpay:
  domain: http://musicpay.kuwo.cn/music.pay

dc:
  domain: http://bd-datacenter.kuwo.cn