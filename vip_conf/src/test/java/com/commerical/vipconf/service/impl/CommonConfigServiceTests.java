package com.commerical.vipconf.service.impl;

import com.commerical.vipconf.domain.CommonConf;
import com.commerical.vipconf.domain.vo.CommonConfQueryVO;
import com.commerical.vipconf.service.CommonConfService;
import com.kuwo.commercialization.common.message.MessageModel;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.Arrays;
import java.util.Date;

@SpringBootTest
public class CommonConfigServiceTests {
    @Autowired
    private CommonConfService commonConfService;

    @Test
    public void testGetById() {
        CommonConf configById = commonConfService.getConfigById(21L);
        System.out.println(configById);
    }

    @Test
    public void testAdd() {
        CommonConf commonConf = new CommonConf();
        commonConf.setId(25L);
        commonConf.setStarId("11112222");
        commonConf.setConfType(1);
        commonConf.setPlatform("ar");
        commonConf.setOperator("aa");
        commonConf.setDeleteStatus(0);
        commonConf.setConfText("configText");
        commonConf.setCreateTime(new Date());
        commonConf.setUpdateTime(new Date());
        commonConf.setStartTime(Date.from(LocalDate.now().atStartOfDay().toInstant(ZoneOffset.ofHours(8))));
        commonConf.setEndTime(Date.from(LocalDate.now().plusDays(9).atStartOfDay().toInstant(ZoneOffset.ofHours(8))));
        commonConf.setVipTypes("1, 2, 5, 9");
        commonConf.setImgUrl("imgUrl");
        commonConf.setJumpType(1);
        commonConf.setJumpUrl("jumpUrl");
        commonConf.setModelType(1);
        MessageModel messageModel = commonConfService.saveOrUpdateBack(commonConf);
        System.out.println(messageModel);
    }

    @Test
    public void testDelete() {
        commonConfService.deleteById(25L, "AAA2");
    }

    @Test
    public void testGetPage() {
        CommonConfQueryVO confQueryVO = new CommonConfQueryVO();
        confQueryVO.setConfTypeStr("1,2");
        confQueryVO.setConfType(1);
        confQueryVO.setPage(1);
        confQueryVO.setPageSize(3);
        System.out.println(commonConfService.getListBack(confQueryVO));
    }

    @Test
    public void testGetPayInfo() {
        System.out.println(commonConfService.getPayConf("ip", 1));
    }

}
