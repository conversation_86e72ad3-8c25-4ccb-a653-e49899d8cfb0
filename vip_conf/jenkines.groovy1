// 灰度某个节点的方法
def gray_node_weight(nacos_ip,service_name,node_ip, node_port, weight_list){
    echo "start grays [${node_ip}], weight list:  ${weight_list}"
    if (weight_list != null && weight_list.size() > 0){
        weight_list.each{ weight ->
            try {
                def inputResp = input id: "gray_input_${BUILD_NUMBER}", message: "确认灰度当前节点: ${node_ip} 流量:${weight} 请输入：approve", parameters: [choice(choices: ['yes','no'], description: '''确认是否灰度流量到当前百分比，确认点击yes，终止请点击no''', name: 'action')]
                if (inputResp == "yes" ){
                    echo "start grays node [${node_ip}], weight: ${weight}"
                    sh "python '${SWITCH_WEIGHT_PY}'  '${node_ip}' '${service_name}' '${node_port}' '${nacos_ip}' '${weight}'"
                }
            } catch (Exception e) {
                echo "skip exception when grays node [${node_ip}], weight: ${weight}"
            }
        }
    }
}

pipeline {
    agent any

    tools {
        // Install the Maven version configured as "M3" and add it to the path.
        maven "default_mavn"
        jdk "jdk8"
    }
     parameters {
            choice(name: 'ACTION', choices: ['Deploy', 'Rollback'], description: '选择要执行的操作')
            choice(name: 'GRAY_DEPLOY_ACTION', choices: ['YES', 'NO'], description: '是否启用灰度发布')
            string(name: 'ROLLBACK_BUILD_NUMBER', defaultValue: '', description: '回滚的构建号,jenkines上的（仅在选择 Rollback 时有效）')
        }

    environment {
        def json = readJSON file: 'vip_conf/env.json'
        UPLOAD_PY = "/home/<USER>/.script/sendAndExecWithSwitchNetNoOnline.sh"
        SWITCH_WEIGHT_PY = "/home/<USER>/.script/GaryTraffic.py"
        deployFile = "'${env.WORKSPACE}'/vip_conf/target/${json.WAR_NAME}"
        targetMenu = "${json.TARGET_MENU}"
        warName = "${json.WAR_NAME}"
        SERVICE_NAME = "${json.NACOS_SERVICE_NAME}"
        NACOS_IP =  "************:8848"
        NODE_PORT =  "${json.SERVER_PORT}"
        hard_password = credentials('75180b69-fd51-4456-9e66-16d59a3c07b1')
        DEPLOY_IPS_JSON = writeJSON returnText: true, json: json.DEPLOY_IPS
        NODE_GRAY_WEIGHT_JSON = writeJSON returnText: true, json: json.NODE_GRAY_WEIGHT
        BUILD_DIR = "${json.ROLLBACK_PKG_PATH}"
    }
    stages{
        stage('checkout') {
            when {
                   expression { params.ACTION == 'Deploy' }
            }
            steps{
                checkout([$class: 'GitSCM', branches: [[name: '*/main']], extensions: [], userRemoteConfigs: [[credentialsId: '1e7a6c16-05e0-405b-b62c-e5c9e94a9364', url: 'https://cnb.tmeoa.com/kuwo/commercial/member-intergral']]])
            }
        }
        stage('build') {
            when {
                   expression { params.ACTION == 'Deploy' }
            }
            steps{
                echo "mvn build ....."
                sh "mvn clean package -pl vip_conf -am -Dmaven.test.skip=true"
            }
        }
        stage('Save Build Artifact') {
            when {
                expression { params.ACTION == 'Deploy' }
            }
            steps {
                script {
                    def buildNumber = env.BUILD_NUMBER
                    def artifactPath = "${BUILD_DIR}/${buildNumber}/${warName}"
                    sh "mkdir -p ${BUILD_DIR}/${buildNumber}"
                    sh "cp ${env.WORKSPACE}/vip_conf/target/${warName} ${artifactPath}"
                }
            }
        }

        stage('deploy') {
            when {
                expression { params.ACTION == 'Deploy' }
            }
            steps{
                echo "deploy ....."
                echo pwd()
                script{
                    def hostsArray = readJSON text: env.DEPLOY_IPS_JSON  // 将JSON字符串解析为数组
                    echo "hosts: ${hostsArray}"
                    def node_weight_list = readJSON text: env.NODE_GRAY_WEIGHT_JSON
                    hostsArray.each{ host  ->
                        def inputResp = input id: "input_${BUILD_NUMBER}", message: "确认部署到生产${host.ip}节点请输入：approve", parameters: [choice(choices: ['yes','no'], description: '''确认是否部署到该节点，发布点击yes，终止请点击no''', name: 'action')]
                        echo "======================== delopy [${host.ip}] start ======================"
                        if (inputResp == "yes" ){
                            def pass = (host.pass == "hard")? "${hard_password}" : ""
                            sh "sh '${UPLOAD_PY}' '${deployFile}' '${host.ip}' '${pass}' '${targetMenu}' '${warName}' '${env.JOB_NAME}' '56000'  '${SERVICE_NAME}' '${NODE_PORT}' '${NACOS_IP}'"
                            if (params.GRAY_DEPLOY_ACTION == "YES"){
                                echo "node_weight_list: ${node_weight_list}"
                                gray_node_weight(NACOS_IP, SERVICE_NAME,host.ip,NODE_PORT,node_weight_list)
                            }
                            echo "======================== delopy [${host.ip}] finish ======================"
                        }else {
                            echo"skip delopy [${host.ip}] ..."
                        }
                    }
                }
            }
        }
        stage('Rollback') {
            when {
                expression { params.ACTION == 'Rollback' }
            }
            steps {
                script {
                    def rollbackBuildNumber = params.ROLLBACK_BUILD_NUMBER
                    if (rollbackBuildNumber) {
                        def rollbackArtifactPath = "${BUILD_DIR}/${rollbackBuildNumber}/${warName}"
                        def hostsArray = readJSON text: env.DEPLOY_IPS_JSON  // 将JSON字符串解析为数组
                        def node_weight_list = readJSON text: env.NODE_GRAY_WEIGHT_JSON
                        echo "hosts: ${hostsArray}"
                        hostsArray.each{ host  ->
                            def inputResp = input id: "input_${BUILD_NUMBER}", message: "确认回滚部署到生产${host.ip}节点请输入：approve", parameters: [choice(choices: ['yes','no'], description: '''确认是否部署到该节点，发布点击yes，终止请点击no''', name: 'action')]
                            echo "======================== rollback [${host.ip}] start ======================"
                            if (inputResp == "yes" ){
                                def pass = (host.pass == "hard")? "${env.hard_password}" : ""
                                sh "sh '${UPLOAD_PY}' '${rollbackArtifactPath}' '${host.ip}' '${pass}' '${targetMenu}' '${warName}' '${env.JOB_NAME}' '56000'  '${SERVICE_NAME}' '${NODE_PORT}' '${NACOS_IP}'"
                                if (params.GRAY_DEPLOY_ACTION == "YES"){
                                    echo "node_weight_list: ${node_weight_list}"
                                    gray_node_weight(NACOS_IP, SERVICE_NAME,host.ip,NODE_PORT,node_weight_list)
                                }
                                echo "======================== rollback [${host.ip}] finish ======================"
                            }else {
                                echo"skip delopy rollback [${host.ip}] ..."
                            }
                        }
                    } else {
                        error '回滚的构建号未指定'
                    }
                }
            }
        }

    }

}