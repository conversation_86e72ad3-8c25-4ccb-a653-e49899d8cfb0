package com.commercal.indicatorService.metrics.config;

import com.commercal.indicatorService.cenum.RedisKey;
import com.commercal.indicatorService.metrics.MetricsConfig;
import com.commercal.indicatorService.metrics.MetricsRequestContext;
import com.commercal.indicatorService.metrics.Request;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Desc: kweeidb 主要有 数据结构
 *       数据类别： HASH
 *                大key:
 *                子key:
 * @date 2025-03-03 14:32:18
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class KweeidbDefaultMetricsConfig implements MetricsConfig {

    private String redisName;

    // redis类型
    private String type;

    // 主要key
    private String key;


    @Override
    public DynamicRunningConfig merge(MetricsRequestContext context) {
        RedisHashDynamicConfig redisHashDynamicConfig = null;
        if ("HASH".equals(type)){
            redisHashDynamicConfig = new RedisHashDynamicConfig();
        }
        Request request = context.getRequest();
        if (RedisKey.MEM_INFO.getKey().equals(key) && redisHashDynamicConfig!=null){
            redisHashDynamicConfig.setFormatKey(String.format(key,request.getUserId())) ;
        }
        return redisHashDynamicConfig;
    }
}
