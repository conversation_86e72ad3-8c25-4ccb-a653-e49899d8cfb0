package com.commercal.indicatorService.service;

import com.commercal.indicatorService.cenum.RedisKey;
import com.commercal.indicatorService.config.redis.DynamicChoiceRedissonClient;
import com.commercal.indicatorService.enity.ThirdBuyInfo;
import com.commercal.indicatorService.enity.ThirdVipDetail;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RedissonClient;
import org.redisson.client.protocol.ScoredEntry;
import org.redisson.codec.TypedJsonJacksonCodec;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Desc:
 * @date 2025-01-08 11:28:52
 */
@Service
public class DefaultThirdBuyVipService implements ThirdBuyVipService {

    private DynamicChoiceRedissonClient dynamicChoiceRedissonClient;

    public DefaultThirdBuyVipService(DynamicChoiceRedissonClient dynamicChoiceRedissonClient) {
        this.dynamicChoiceRedissonClient = dynamicChoiceRedissonClient;
    }

    @Override
    public Optional<List<ThirdBuyInfo>> getUserBuyThirdOrderInfo(Long userId, Long productTypeId, Date filterEndDate, int offset, int size) {
        size = size<=0?1:size;
        size = Math.min(size, 100);
        RedissonClient redissonClient = dynamicChoiceRedissonClient.getClient("main");
        String zSetKey = String.format(RedisKey.THIRD_SOURCE_ORDER_INFO.getKey(), productTypeId, userId);
        // 插入操作，直接创建即可
        RScoredSortedSet<ThirdVipDetail> scoredSortedSet = redissonClient.getScoredSortedSet(zSetKey, TypedJsonJacksonCodec.INSTANCE);
        Collection<ScoredEntry<ThirdVipDetail>> scoredEntries = scoredSortedSet.entryRange(filterEndDate.getTime(), true, Double.MAX_VALUE, false, offset, size);
        if (scoredEntries.isEmpty()) {
            return Optional.empty();
        }
        List<ThirdBuyInfo> results = scoredEntries.stream().map(ScoredEntry::getValue).map(ThirdVipDetail::toThirdBuyInfo).collect(Collectors.toList());
        return Optional.of(results);
    }
}
