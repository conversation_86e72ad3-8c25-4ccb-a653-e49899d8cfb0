package com.kuwo.commerical.loginNewService.config.exception;


import com.kuwo.commercialization.common.cenum.Result;
import com.kuwo.commercialization.common.resp.BasicResponse;
import com.kuwo.commercialization.common.utill.SpringAwareUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletResponse;

/**
 * 全局异常统一处理
 */
@RestControllerAdvice
class GlobalExceptionHandler {

    private Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    @ExceptionHandler(value = Exception.class)
    public BasicResponse exception(Exception e, HttpServletResponse httpServletResponse) {
        if ("local".equals(SpringAwareUtil.getActiveProfile())){
            logger.error("global exception --> ", e);
        }
        httpServletResponse.setCharacterEncoding("UTF-8");
        httpServletResponse.setContentType("application/json; charset=utf-8");
        Result result = Result.SERVER_EXCEPTION;
        if (e instanceof RuntimeException){
            result = Result.PARAM_CHECK_ERROR;
        }
        BasicResponse response = new BasicResponse(result, e.getMessage());
        return  response;
    }

}
