package com.memberintergral.carservice.upgrade;

import com.memberintergral.carservice.domain.BO.UserInfoBO;
import com.memberintergral.carservice.domain.BO.VehiclePriceBO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;


public abstract class PriceInterceptor {

    protected Logger logger = LoggerFactory.getLogger(PriceInterceptor.class);

    protected UserInfoBO userInfoBO;
    private PriceInterceptor next;

    public PriceInterceptor(UserInfoBO userInfoBO) {
        this.userInfoBO = userInfoBO;
    }

    public PriceInterceptor next() {
        return next;
    }

    public PriceInterceptor appendNext(PriceInterceptor next) {
        this.next = next;
        return this;
    }

    public abstract void doAuth(Long uid, List<VehiclePriceBO> vehiclePriceVIPBOS);

}
