package com.memberintergral.carservice.upgrade;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.memberintergral.carservice.config.enums.VipUpdatePriceEnum;
import com.memberintergral.carservice.domain.BO.UserInfoBO;
import com.memberintergral.carservice.domain.BO.VehiclePriceBO;
import com.memberintergral.carservice.util.DateUtils;

import java.util.Date;
import java.util.List;

/**
 * 豪华VIP 升级超级会员档位
 */
public class LuxVIPUpdateInterceptor extends PriceInterceptor {

    public LuxVIPUpdateInterceptor(UserInfoBO userInfoBO) {
        super(userInfoBO);
    }

    @Override
    public void doAuth(Long uid, List<VehiclePriceBO> vehiclePriceSVIPBOS) {
        int vipVehicleAutoPayUser= userInfoBO.getVipVehicleAutoPayUser();
        int luxAutoPayUser= userInfoBO.getLuxAutoPayUser();
        long vipLuxVExpire= userInfoBO.getVipluxuryexpire();
        long vipVehicleExpire= userInfoBO.getVipVehicleExpire();
        Date luxDate =new Date(vipLuxVExpire);
        Date vehData =new Date(vipVehicleExpire);
        Date nowDate=new Date();
        Date svipExpire =new Date(userInfoBO.getSvipExpire());
        PriceInterceptor interceptor= next();
        if(luxDate.before(nowDate)||svipExpire.after(nowDate)){
            if (interceptor==null) {
                return;
            }
            interceptor.doAuth(uid,vehiclePriceSVIPBOS);
            return;
        }
        long betweenDay = DateUtil.between(luxDate,nowDate, DateUnit.DAY);
        if(luxAutoPayUser==0&& DateUtils.checkOther(vipLuxVExpire,nowDate)){
            // 豪华VIP 非连续续费 升级为连续续费超级会员
            long vehbetweenDay = DateUtil.between(vehData,nowDate, DateUnit.DAY);
            if(vipVehicleAutoPayUser==1||(vipVehicleAutoPayUser==0&&vehbetweenDay>31&&vehData.after(nowDate))){
               logger.info("LuxVIPUpdateInterceptor Vehicle update first vipVehicleAutoPayUser={} betweenDay={}",vipVehicleAutoPayUser,vehbetweenDay);
            } else{
                VipUpdatePriceEnum.getShowPrice(VipUpdatePriceEnum.LUXURY_SVIP,vehiclePriceSVIPBOS);
            }
        }
        if (interceptor==null) {
            return;
        }
        interceptor.doAuth(uid,vehiclePriceSVIPBOS);
    }

}
