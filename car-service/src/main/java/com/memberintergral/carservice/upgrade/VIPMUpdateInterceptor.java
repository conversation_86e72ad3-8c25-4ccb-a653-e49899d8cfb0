package com.memberintergral.carservice.upgrade;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.memberintergral.carservice.config.enums.VipUpdatePriceEnum;
import com.memberintergral.carservice.domain.BO.UserInfoBO;
import com.memberintergral.carservice.domain.BO.VehiclePriceBO;
import com.memberintergral.carservice.util.DateUtils;

import java.util.Date;
import java.util.List;

/**
 * 音乐包升级超级会员后者车载VIP
 */
public class VIPMUpdateInterceptor extends  PriceInterceptor{

    public VIPMUpdateInterceptor(UserInfoBO userInfoBO) {
        super(userInfoBO);
    }

    @Override
    public void doAuth(Long uid, List<VehiclePriceBO> vehiclePriceSVIPBOS) {
        int vipmAutoPayUser= userInfoBO.getVipmAutoPayUser();
        long vipmexpire= userInfoBO.getVipmexpire();
        Date vipmexpireDate =new Date(vipmexpire);
        Date nowDate=new Date();
        PriceInterceptor interceptor= next();
        // 音乐包已经过期跳过
        if(vipmexpireDate.before(nowDate)){
            if (interceptor==null) {
                return;
            }
            return;
        }
        // 如果车载VIP会员或者豪华VIP也满足升级超会条件则不返回音乐包升级超会挡位
        if(vipmAutoPayUser==0&& DateUtils.checkOther(userInfoBO.getVipmexpire(),nowDate)&&userInfoBO.getVipVehicleAutoPayUser()==0&&userInfoBO.getLuxAutoPayUser()==0){
            if((DateUtils.checkOther(userInfoBO.getVipluxuryexpire(),nowDate)&&userInfoBO.getLuxAutoPayUser()==0)||DateUtils.checkOther(userInfoBO.getVipVehicleExpire(),nowDate)) {
                logger.info("VIPMUpdateInterceptor 满足车载VIP会员或者豪华VIP也满足升级超会条件则不返回音乐包升级超会挡位 跳过音乐包升级超会档位，uid={}",uid);
            }else {
                Date svipExpire= new Date(userInfoBO.getSvipExpire());
                // 如果超会已过期 则显示超会档位
                if(svipExpire.before(nowDate)){
                    // 音乐包非连续续费挡位升级为超级会员
                    VipUpdatePriceEnum.getShowPrice(VipUpdatePriceEnum.VIPM_SVIP,vehiclePriceSVIPBOS);
                }
            }
            Date vipVehicleExpire= new Date(userInfoBO.getVipVehicleExpire());
            Date vipLux= new Date(userInfoBO.getVipluxuryexpire());
            if(vipVehicleExpire.before(nowDate)&&vipLux.before(nowDate)){
                // 音乐包非连续续费挡位升级为车载VIP
                VipUpdatePriceEnum.getShowPrice(VipUpdatePriceEnum.VIPM_VVIP,vehiclePriceSVIPBOS);
            }
        }
        if (interceptor==null) {
            return;
        }
        interceptor.doAuth(uid,vehiclePriceSVIPBOS);
    }

    /**
     * 检查剩余天数
     *
     * @param expire
     * @param nowDate
     * @return
     */
    boolean checkOther(long expire, Date nowDate){
        Date expireDate= new Date(expire);
        long betweenDay = DateUtil.between(expireDate,nowDate, DateUnit.DAY);
        if(betweenDay>31&&expireDate.after(nowDate)){
            return true;
        }
        return false;
    }

}
