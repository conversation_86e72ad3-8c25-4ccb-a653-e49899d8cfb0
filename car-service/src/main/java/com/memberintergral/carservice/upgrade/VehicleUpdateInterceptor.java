package com.memberintergral.carservice.upgrade;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.memberintergral.carservice.config.enums.VipUpdatePriceEnum;
import com.memberintergral.carservice.domain.BO.UserInfoBO;
import com.memberintergral.carservice.domain.BO.VehiclePriceBO;
import com.memberintergral.carservice.util.TimeUtils;

import java.util.Date;
import java.util.List;

/**
 * 车载VIP升级超级会员
 */
public class VehicleUpdateInterceptor extends PriceInterceptor {

    public VehicleUpdateInterceptor(UserInfoBO userInfoBO) {
        super(userInfoBO);
    }

    @Override
    public void doAuth(Long uid, List<VehiclePriceBO> vehiclePriceSVIPBOS) {
        int vipVehicleAutoPayUser= userInfoBO.getVipVehicleAutoPayUser();
        long vipVehicleExpire= userInfoBO.getVipVehicleExpire();
        Date vehicleDate =new Date(vipVehicleExpire);
        Date svipExpire =new Date(userInfoBO.getSvipExpire());
        Date nowDate=new Date();
        PriceInterceptor interceptor= next();
        // 车载VIP到期或者SVIP没有到期则不限时升级档位
        if(vehicleDate.before(nowDate)||svipExpire.after(nowDate)){
            if (interceptor==null) {
                return;
            }
            interceptor.doAuth(uid,vehiclePriceSVIPBOS);
            return;
        }
        long betweenDay = DateUtil.between(vehicleDate,nowDate, DateUnit.DAY);
        // 车载非连续续费挡位升级为超级会员 小于31天不升级
        if(vipVehicleAutoPayUser==0&&betweenDay>31){
            VipUpdatePriceEnum.getShowPrice(VipUpdatePriceEnum.VEHICLE_SVIP,vehiclePriceSVIPBOS);
        }
        if(vipVehicleAutoPayUser==1&&betweenDay>31){
            // 连续续费挡位升级为超级会员
            VipUpdatePriceEnum.getShowPrice(VipUpdatePriceEnum.VEHICLE_AUTO_SVIP,vehiclePriceSVIPBOS);
        }
        if(vipVehicleAutoPayUser==1&&betweenDay<=31&&betweenDay>0){
            String endTime= TimeUtils.getDateFormatTimeString(vehicleDate);
            // 连续续费挡位升级为超级会员 补差价0.3n
           VipUpdatePriceEnum.getOneMonthPrice(betweenDay,vehiclePriceSVIPBOS,endTime);
        }
        if (interceptor==null) {
            return;
        }
        interceptor.doAuth(uid,vehiclePriceSVIPBOS);
    }
}
