package com.memberintergral.carservice.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.kuwo.commercialization.common.message.SystemCodeErrorConstant;
import com.memberintergral.carservice.config.constant.MessageModel;

import java.util.HashMap;
import java.util.Map;

/**
 * 基础数据返回层
 */
public class BaseController {

    public MessageModel success() {
        MessageModel result = new MessageModel();
        result.setCode(SystemCodeErrorConstant.SUCCESS.getCode()); // 默认返回成功
        result.setDesc(SystemCodeErrorConstant.SUCCESS.getMessage());
        return result;
    }

    public MessageModel success(Object data) {
        MessageModel result = new MessageModel();
        result.setCode(SystemCodeErrorConstant.SUCCESS.getCode());
        result.setDesc(SystemCodeErrorConstant.SUCCESS.getMessage());
        result.setData(data);
        return result;
    }

    public MessageModel success(IPage data) {
        MessageModel result = new MessageModel();
        result.setCode(SystemCodeErrorConstant.SUCCESS.getCode());
        result.setDesc(SystemCodeErrorConstant.SUCCESS.getMessage());
        Map<String,Object> map=new HashMap<>();
        map.put("record",data.getRecords());
        map.put("total",data.getTotal());
        result.setData(map);
        return result;
    }

    public MessageModel success(SystemCodeErrorConstant systemCodeErrorConstant) {
        MessageModel result = new MessageModel();

        result.setCode(systemCodeErrorConstant.getCode());
        result.setDesc(systemCodeErrorConstant.getMessage());
        return result;
    }

    public MessageModel failed() {
        MessageModel result = new MessageModel();
        result.setCode(SystemCodeErrorConstant.PARAM_CHECK_ERROR.getCode());
        result.setData(null);
        result.setDesc(SystemCodeErrorConstant.PARAM_CHECK_ERROR.getMessage());
        return result;
    }

    public MessageModel failed(SystemCodeErrorConstant systemCodeErrorConstant) {
        MessageModel result = new MessageModel();
        result.setCode(systemCodeErrorConstant.getCode());
        result.setDesc(systemCodeErrorConstant.getMessage());
        return result;
    }
}
