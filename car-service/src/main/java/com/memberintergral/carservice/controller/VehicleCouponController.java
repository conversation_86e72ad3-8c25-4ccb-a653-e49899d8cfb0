package com.memberintergral.carservice.controller;


import com.memberintergral.carservice.config.constant.MessageModel;
import com.memberintergral.carservice.service.VehicleCouponService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-24
 */
@Controller
@RequestMapping("/vehicleCoupon")
public class VehicleCouponController {


    @Autowired
    private VehicleCouponService vehicleCouponService;


    /**
     * 首次发送优惠券
     *
     * @param uid 用户iD
     * @param channel 渠道
     * @return
     */
    @RequestMapping("/coupon/getFirstCoupon")
    public MessageModel getFirstCoupon(Long uid, String channel, Long virtualUid, String  deviceId){
        return vehicleCouponService.getFirstCoupon(uid,channel,virtualUid,deviceId);
    }

    /**
     * 获取优惠券列表
     *
     * @param uid 用户
     * @param channel 渠道
     * @param virtualUid 虚拟iD
     * @param deviceId 设备iD
     * @return
     */
    @RequestMapping("/coupon/getCouponList")
    public MessageModel getCouponList(Long uid,String channel,Long virtualUid,String  deviceId){
        return vehicleCouponService.getCouponList(uid,channel,virtualUid,deviceId,null,null);
    }
}

