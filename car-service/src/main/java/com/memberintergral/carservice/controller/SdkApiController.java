package com.memberintergral.carservice.controller;

import com.memberintergral.carservice.config.constant.MessageModel;
import com.memberintergral.carservice.domain.VO.CarSdkApiVO;
import com.memberintergral.carservice.service.SdkApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/sdkapi")
@ResponseBody
@Slf4j
public class SdkApiController {

    @Autowired
    private SdkApiService sdkApiService;

    /**
     * 获取活动报价信息
     *
     * @param carSdkApiVO 车载活动信息对象，包含 src（价格标识）、channel（渠道）、uid（用户ID）
     * @return 返回活动报价信息的消息模型，如果出错返回错误兜底数据
     */
    @RequestMapping("/getSdkApiPriceInfo")
    public MessageModel getSdkApiPriceInfo(CarSdkApiVO carSdkApiVO){
        return sdkApiService.getSdkApiPriceInfo(carSdkApiVO);
    }
}
