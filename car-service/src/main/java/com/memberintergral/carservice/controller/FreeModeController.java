package com.memberintergral.carservice.controller;

import cn.hutool.json.JSONUtil;
import com.memberintergral.carservice.config.constant.MessageModel;
import com.memberintergral.carservice.service.FreeModeService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/freeMode")
public class FreeModeController extends  BaseController{

    @Autowired
    private FreeModeService freeModeService;

    /**
     * 是否开通免费模式
     * @param uid 用户id
     * @param virtualUid 虚拟设备ID
     *
     */
    @GetMapping("/openFreeMode")
    public MessageModel openFreeMode(String uid, String virtualUid)
    {
        boolean hasBuyVip=freeModeService.openFreeMode(uid,virtualUid);
        return success(JSONUtil.createObj().set("hasBuyVip",hasBuyVip));
    }
}
