package com.memberintergral.carservice.controller;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.memberintergral.carservice.config.constant.MessageModel;
import com.memberintergral.carservice.config.sentinel.ExceptionHandler;
import com.memberintergral.carservice.domain.VO.CarActivityVO;
import com.memberintergral.carservice.domain.VO.PriceGearVO;
import com.memberintergral.carservice.domain.VO.SaveCarActivityInfoVO;
import com.memberintergral.carservice.domain.VO.SaveCarActivityVO;
import com.memberintergral.carservice.service.ActivityService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/activity")
@ResponseBody
@Slf4j
public class ActivityController {

    @Autowired
    private ActivityService activityService;

    /**
     * 获取活动报价信息
     *
     * @param carActivityVO 车载活动信息对象，包含 src（价格标识）、channel（渠道）、uid（用户ID）
     * @return 返回活动报价信息的消息模型，如果出错返回错误兜底数据
     */
    @SentinelResource(value = "getActivityPriceInfo", fallbackClass ={ExceptionHandler.class}, fallback = "getActivityPriceInfoHandler")
    @RequestMapping("/getActivityPriceInfo")
    public MessageModel getActivityPriceInfo(CarActivityVO carActivityVO){
        return activityService.getActivityPriceInfo(carActivityVO);
    }

    /**
     * 渠道是否有活动
     *
     * @param activityName 档位标识
     * @param paySrc 活动渠道
     * @return
     */
    @SentinelResource(value = "existactivity", fallbackClass ={ExceptionHandler.class}, fallback = "existactivityHandler")
    @RequestMapping("/existactivity")
    public MessageModel existActivity(String activityName, String paySrc) {
        return activityService.existActivity(activityName, paySrc);
    }

    /**
     * 渠道活动时间
     *
     * @return
     */
    @SentinelResource(value = "getChannelInfo", fallbackClass ={ExceptionHandler.class}, fallback = "getChannelInfoHandler")
    @RequestMapping("/getChannelInfo")
    public MessageModel getChannelInfo(String src,String channel){
        return activityService.getChannelInfo(src,channel);
    }

    @RequestMapping("/saveAndUpdateCarActivity")
    public MessageModel saveAndUpdateCarActivity(@RequestBody SaveCarActivityVO saveCarActivityVO){
        if(!StringUtils.equals(saveCarActivityVO.getSign(),"asuhjbdiqwiethgbjertreoo9345234!@#")){
            return new MessageModel();
        }
        return activityService.saveAndUpdateCarActivity(saveCarActivityVO);
    }

    @RequestMapping("/saveAndUpdateCarActivityInfo")
    public MessageModel saveAndUpdateCarActivityInfo(@RequestBody SaveCarActivityInfoVO saveCarActivityInfoVO){
        if(!StringUtils.equals(saveCarActivityInfoVO.getSign(),"*********************************!@#")){
            return new MessageModel();
        }
        return activityService.saveAndUpdateCarActivityInfo(saveCarActivityInfoVO);
    }

    /**
     * 获取活动报价信息
     *
     * @param priceGearVO 车载活动信息对象，包含 src（价格标识）、channel（渠道）、uid（用户ID）
     * @return 返回活动报价信息的消息模型，如果出错返回错误兜底数据
     */
    @RequestMapping("/getCarMobilePriceInfo")
    public MessageModel getCarMobilePriceInfo(PriceGearVO priceGearVO){
        return activityService.getCarMobilePriceInfo(priceGearVO);
    }

    @RequestMapping("/getSimpleCarInfo")
    public MessageModel getSimpleCarInfo(PriceGearVO priceGearVO){
        return activityService.getSimpleCarInfo(priceGearVO);
    }


}
