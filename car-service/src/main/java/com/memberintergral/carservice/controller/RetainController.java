package com.memberintergral.carservice.controller;

import com.memberintergral.carservice.config.constant.MessageModel;
import com.memberintergral.carservice.domain.VO.PriceGearVO;
import com.memberintergral.carservice.service.RetainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/retain")
@ResponseBody
@Slf4j
public class RetainController {

    @Autowired
    private RetainService retainService;

    @RequestMapping("/getPriceGearInfo")
    public MessageModel getPriceGearInfo(PriceGearVO priceGearVO)  {
        return retainService.getPriceGearInfo(priceGearVO);
    }
}
