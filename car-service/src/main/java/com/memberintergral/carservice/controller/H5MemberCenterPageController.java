package com.memberintergral.carservice.controller;

import com.memberintergral.carservice.config.constant.MessageModel;
import com.memberintergral.carservice.domain.VO.GuideBarResult;
import com.memberintergral.carservice.service.H5MemberCenterPageService;
import com.memberintergral.carservice.util.MyNumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Desc: h5会员中心页的相关配置
 * @date 2024-06-18 16:40:35
 */
@RestController
@RequestMapping("/memberPage")
public class H5MemberCenterPageController extends BaseController{

    @Autowired
    private H5MemberCenterPageService h5MemberCenterPageService;

    // 车载VIP>超级会员>豪华VIP>听书VIP>非会员/未登录
    // 需求文档见： https://www.tapd.cn/58761277/prong/stories/view/1158761277001212672
    @RequestMapping("/downloadGuideBar/config")
    public MessageModel guideBarConfig(String uid, String sid){
        long uidL = MyNumberUtils.toLONG(uid);
        long sidL = MyNumberUtils.toLONG(sid);
        GuideBarResult guideBarResult = h5MemberCenterPageService.getGuideBarText(uidL, sidL);
        return success(guideBarResult);
    }

}
