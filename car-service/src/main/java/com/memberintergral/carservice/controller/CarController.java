package com.memberintergral.carservice.controller;

import com.memberintergral.carservice.config.aspect.LoggerManage;
import com.memberintergral.carservice.config.constant.MessageModel;
import com.memberintergral.carservice.domain.entity.OrderReqEntity;
import com.memberintergral.carservice.service.impl.VehicleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/car")
@ResponseBody
@Slf4j
public class CarController extends BaseController{


    @Autowired
    private VehicleService vehicleService;


    @RequestMapping("/carOrder")
    @LoggerManage(description = "h5下单接口")
    public MessageModel carOrder(@RequestBody OrderReqEntity orderReq) {
        return vehicleService.order(orderReq);
    }

}
