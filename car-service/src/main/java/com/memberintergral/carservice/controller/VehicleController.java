package com.memberintergral.carservice.controller;

import cn.hutool.http.HttpUtil;
import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.com.google.protobuf.ServiceException;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.memberintergral.carservice.config.aspect.LoggerManage;
import com.memberintergral.carservice.config.aspect.ValidateUser;
import com.memberintergral.carservice.config.constant.MessageModel;
import com.memberintergral.carservice.config.constant.SystemCodeErrorConstant;
import com.memberintergral.carservice.config.constant.VehicleConstant;
import com.memberintergral.carservice.config.nacos.CarConfigNacos;
import com.memberintergral.carservice.config.redis.added.RedisLowPriceDAO;
import com.memberintergral.carservice.config.sentinel.ExceptionHandler;
import com.memberintergral.carservice.domain.VO.PreOrderVO;
import com.memberintergral.carservice.domain.VO.PriceGearVO;
import com.memberintergral.carservice.domain.entity.OrderReqEntity;
import com.memberintergral.carservice.domain.entity.PayDesk;
import com.memberintergral.carservice.mapper.VehicleProductMapper;
import com.memberintergral.carservice.service.FilterService;
import com.memberintergral.carservice.service.NewFilterService;
import com.memberintergral.carservice.service.PayDeskService;
import com.memberintergral.carservice.service.RetainService;
import com.memberintergral.carservice.service.impl.VehicleActivityService;
import com.memberintergral.carservice.service.impl.VehicleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/vehicle")
@ResponseBody
@Slf4j
public class VehicleController extends BaseController {

    @Autowired
    private VehicleService vehicleService;

    @Autowired
    private FilterService filterService;

    @Autowired
    private VehicleActivityService vehicleActivityService;

    @Autowired
    private PayDeskService payDeskService;

    @Autowired
    private VehicleProductMapper vehicleProductMapper;

    @Autowired
    private RetainService retainService;

    @Autowired
    private CarConfigNacos carConfigNacos;

    @Autowired
    private NewFilterService newFilterService;



    /**
     * 下单接口
     *
     * @param orderReq
     * @return cn.kuwo.vip1.util.common.constants.MessageModel
     * <AUTHOR>
     * @date 2019/5/9 15:44
     */
    @RequestMapping("/order")
    @LoggerManage(description = "下单接口")
    @ValidateUser
    public MessageModel createOrder(OrderReqEntity orderReq) {
        return vehicleService.order(orderReq);
    }

    /**
     * 预下单
     *
     * @return
     */
    @LoggerManage(description = "中间接口")
    @RequestMapping("/createOrder")
    @ValidateUser
    public MessageModel createOrder(PreOrderVO preOrderVO){
        return vehicleActivityService.createOrder(preOrderVO);
    }

    /**
     * 订单二维码创建
     *
     * @param preOrderVO
     * @return
     */
    @LoggerManage(description = "生成二维码接口")
    @RequestMapping("/createQrCode")
    public MessageModel createQrCode(PreOrderVO preOrderVO){
        return vehicleActivityService.createQrCode(preOrderVO);
    }

    @LoggerManage(description = "三方-生成二维码接口")
    @RequestMapping("/thirdQrCode")
    public MessageModel thirdQrCode(PreOrderVO preOrderVO){
        return vehicleActivityService.createQrCode(preOrderVO);
    }


    /**
     * 是否支付成功
     *
     * @return
     */
    @RequestMapping("/isSuccess")
    public MessageModel isSuccess(String requestId){
        return vehicleActivityService.isSuccess(requestId);
    }


    /**
     * 车载会员信息
     *
     * @return
     */
    @RequestMapping("/getVipVehicleExpire")
    public MessageModel getVipVehicleExpire(String uid,String vers){
        return vehicleActivityService.getVipVehicleExpire(uid,vers);
    }

    /**
     * 价格档位获取
     *
     * @param priceGearVO 价格档位参数
     * @return
     * @throws ServiceException
     */
    @RequestMapping("/getPriceGearInfo")
    @SentinelResource(value = "carprice", fallbackClass ={ExceptionHandler.class}, fallback = "blockExceptionHandler")
    public MessageModel getPriceGearInfo(PriceGearVO priceGearVO)  {
        if(StringUtils.equals(priceGearVO.getPayDeskSign(),VehicleConstant.PAY_DESK_SIGN_KEY)){
            return retainService.getPriceGearInfo(priceGearVO);
        }
        return newFilterService.getNewPriceGearInfo(priceGearVO);
    }

    @RequestMapping("/getCarIcon")
    public MessageModel getCarIcon(String token){
        if(!StringUtils.equals(token,"eyJhbGciOiJIUzI1NiJ9")){
            return new MessageModel("错误");
        }
        PayDesk payDesk= payDeskService.getPayDeskInfoBySign("carengine");
        String doc=payDesk.getDoc();
        JSONObject obj= JSONObject.parseObject(doc);
        JSONArray array= obj.getJSONArray("vipData");
        Map<String,String> result=new HashMap<>();
        array.forEach(json->{
            JSONObject js=(JSONObject) json;
            if(js.getString("type").equals("vip_17")){
                result.put("car",js.getString("icon"));
                result.put("car-gray",js.getString("desc"));
            }
            if(js.getString("type").equals("vip_34")){
                result.put("svip",js.getString("icon"));
                result.put("svip-gray",js.getString("desc"));
            }
        });
        return new MessageModel(result);
    }

    /**
     * 购买状态
     *
     * @return
     */
    @RequestMapping("/isBuySuccess")
    public MessageModel isBuySuccess(String requestId,String uid ,String virtualUid){
        return vehicleActivityService.isBuySuccess(requestId,uid,virtualUid);
    }

    /**
     * 购买状态
     *
     * @return
     */
    @RequestMapping("/isBuySuccessNew")
    public MessageModel isBuySuccessNew(String requestId,String uid ,String virtualUid){
        return vehicleActivityService.isBuySuccessNew(requestId,uid,virtualUid);
    }

    /**
     * 是否签约
     *
     * @param uid
     * @return
     */
    @SentinelResource(value = "isSign", fallbackClass ={ExceptionHandler.class}, fallback = "isSign")
    @RequestMapping("/isSign")
    public MessageModel isSign(String uid) throws InterruptedException {
        if(StringUtils.isBlank(uid)||StringUtils.equals(uid,"0")){
            return new MessageModel(SystemCodeErrorConstant.PARAM_CHECK_ERROR);
        }
        Thread.sleep(500);
        String key= VehicleConstant.USER_SIGN_KEY +uid;
        String value= RedisLowPriceDAO.getInstance().getString(key);
        if(StringUtils.isNotBlank(value)){
            return new MessageModel(Integer.parseInt(value));
        }
        return new MessageModel(0);
    }
    /**
     * 灰度userId
     *
     * @param userId
     * @return
     */
    public boolean newUser(String userId){
        try{
            if(StringUtils.isBlank(userId)||userId.equals("0")){
                return false;
            }
            List<String> newUids=carConfigNacos.getCarConfigBO().getNewUids();
            if(CollectionUtils.isEmpty(newUids)){
                return false;
            }
            if(!CollectionUtils.isEmpty(newUids)){
                for(String keyStr:newUids){
                    if(userId.endsWith(keyStr)){
                        return true;
                    }
                }
            }
            return false;
        }catch (Exception e){
            log.error("newUser error",e);
        }
        return false;
    }

    /**
     * sdk测试平添
     *
     * @param paySrc
     * @return
     */
    @RequestMapping("/getSDKAPIPriceDev")
    public MessageModel getSDKAPIPriceDev(String paySrc){
        List<String>  apicars= carConfigNacos.getCarConfigBO().getApiChannels();
        if(apicars.contains(paySrc)){
            String url="http://car-vip.kuwo.cn/products/list?appId="+paySrc;
            String res= HttpUtil.get(url,200);
            JSONObject resJson=JSONObject.parseObject(res);
            return new MessageModel(resJson.getJSONArray("data"));
        }else{
            String url="http://car-vip.kuwo.cn/products/tongyong/list?paySrc="+paySrc;
            String res=HttpUtil.get(url,200);
            JSONObject resJson=JSONObject.parseObject(res);
            JSONArray array=  resJson.getJSONArray("data");
            array.forEach(Json->{
                JSONObject js=(JSONObject)Json;
                BigDecimal price= js.getBigDecimal("price");
                Integer cnt= js.getInteger("cnt");
                BigDecimal newPrice = price.multiply(new BigDecimal(cnt));
                js.put("price",newPrice);
            });
            log.info("getSDKAPIPrice array={}",array);
            return new MessageModel(array);
        }
    }

    /**
     * 端外收银台 - 支付挽留
     *
     * @param priceGearVO
     * @param filterId
     * @return
     */
    @RequestMapping("/getPaymentRetention")
    public MessageModel getHandlePaymentRetention(PriceGearVO priceGearVO, String filterId){
        return newFilterService.handlePaymentRetention(priceGearVO, filterId);
    }
}
