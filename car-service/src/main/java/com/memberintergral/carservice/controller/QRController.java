package com.memberintergral.carservice.controller;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.memberintergral.carservice.config.constant.MessageModel;
import com.memberintergral.carservice.config.sentinel.ExceptionHandler;
import com.memberintergral.carservice.domain.VO.PreOrderVO;
import com.memberintergral.carservice.service.QRCodeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/qr")
@ResponseBody
@Slf4j
public class QRController extends BaseController {

    @Autowired
    private QRCodeService qrCodeService;

    @RequestMapping("/getQRCode")
    @SentinelResource(value = "qrcode", fallbackClass ={ExceptionHandler.class}, fallback = "QRCodeHandler")
    public MessageModel getRedeemCode(String url)  {
        log.info("getQRCode url={}",url);
        String base64Url= qrCodeService.makeBase64QRCode(url,true);
        return success(base64Url);
    }

    /**
     * 活动二维码生成
     *
     * @param preOrderVO
     * @return
     */
    @RequestMapping("/getActivityQRCode")
    @SentinelResource(value = "getActivityQRCode", fallbackClass ={ExceptionHandler.class}, fallback = "activityQRCodeHandler")
    public MessageModel getActivityQRCode(PreOrderVO preOrderVO)  {
        return qrCodeService.getActivityQRCode(preOrderVO);
    }


    /**
     * 车载二维码
     *
     * @param uid
     * @param sid
     * @param channel
     * @return
     */
    @RequestMapping("/payCode")
    public MessageModel payCode(String uid,String sid,String channel)  {
        return qrCodeService.payCode(uid,sid,channel);
    }
}
