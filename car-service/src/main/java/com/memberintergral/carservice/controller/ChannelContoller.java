package com.memberintergral.carservice.controller;


import com.memberintergral.carservice.config.constant.MessageModel;
import com.memberintergral.carservice.service.ChannelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/channel")
@ResponseBody
@Slf4j
public class ChannelContoller {

    @Autowired
    private ChannelService channelService;

    @RequestMapping("/checkSvipSupport")
    public MessageModel checkSvipSupport(String channel, String uid)  {
        return channelService.checkSvipSupport(channel, uid);
    }

}
