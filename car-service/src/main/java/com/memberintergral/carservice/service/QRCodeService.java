package com.memberintergral.carservice.service;

import com.memberintergral.carservice.config.constant.MessageModel;
import com.memberintergral.carservice.domain.VO.PreOrderVO;


public interface QRCodeService {
     String makeBase64QRCode(String content ,boolean userShortURL);
     MessageModel getActivityQRCode(PreOrderVO preOrderVO );
     MessageModel getVIPCenterQRCode(PreOrderVO preOrderVO);
     MessageModel payCode(String uid ,String sid,String channel);
}
