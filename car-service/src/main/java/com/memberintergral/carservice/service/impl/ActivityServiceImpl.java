package com.memberintergral.carservice.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.memberintergral.carservice.config.constant.MessageModel;
import com.memberintergral.carservice.config.constant.SystemCodeErrorConstant;
import com.memberintergral.carservice.config.constant.VehicleConstant;
import com.memberintergral.carservice.config.enums.*;
import com.memberintergral.carservice.config.monitor.CarMonitor;
import com.memberintergral.carservice.config.nacos.CarConfigNacos;
import com.memberintergral.carservice.config.redis.added.RedisLowPriceDAO;
import com.memberintergral.carservice.domain.BO.DocBO;
import com.memberintergral.carservice.domain.BO.VehicleActivityItemBO;
import com.memberintergral.carservice.domain.BO.VehiclePriceBO;
import com.memberintergral.carservice.domain.VO.CarActivityVO;
import com.memberintergral.carservice.domain.VO.PriceGearVO;
import com.memberintergral.carservice.domain.VO.SaveCarActivityInfoVO;
import com.memberintergral.carservice.domain.VO.SaveCarActivityVO;
import com.memberintergral.carservice.domain.entity.*;
import com.memberintergral.carservice.enums.VipTypeEnum;
import com.memberintergral.carservice.mapper.DataItemMapper;
import com.memberintergral.carservice.service.*;
import com.memberintergral.carservice.util.CacheUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.memberintergral.carservice.config.constant.VehicleConstant.*;


@Service
@DS("master")
@Slf4j
public class ActivityServiceImpl implements ActivityService {
    @Autowired
    private DataModelService dataModelService;
    @Autowired
    private DataItemMapper dataItemMapper;
    @Autowired
    private CarActivityType carActivityType;
    @Autowired
    private CarMonitor carMonitor;

    @Autowired
    private CarActivityService carActivityService;
    @Autowired
    private VehicleActivityService vehicleActivityService;

    @Autowired
    private CacheUtils cacheUtils;

    @Autowired
    private PayDeskService payDeskService;

    @Autowired
    private GearService gearService;

    @Autowired
    private RetainServiceImpl retainService;

    @Autowired
    private SchemeConfigService schemeConfigService;

    @Autowired
    private CarConfigNacos carConfigNacos;

    @Autowired
    private NewFilterServiceImpl newFilterService;

    /**
     * 获取活动报价信息
     *
     * @param carActivityVO 车载活动信息对象，包含 src（价格标识）、channel（渠道）、uid（用户ID）
     * @return 返回活动报价信息的消息模型，如果出错返回错误兜底数据
     */
    public MessageModel getActivityPriceInfo(CarActivityVO carActivityVO){
        // 记录信息输入日志
        log.info("getActivityPriceInfo input param={}", JSONUtil.toJsonStr(carActivityVO));
        // 增加信息处理计数
        carMonitor.getACTIVITY_PRICE_INFO_COUNT().increment();
        String channel=carActivityVO.getChannel().toLowerCase();
        if(VehicleConstant.lianyouChannels.contains(channel)){
            channel=VehicleConstant.lianyouChannel;
        }else if(VehicleConstant.jiaChannels.contains(channel)){
            channel=VehicleConstant.jiaChannel;
        }else if(VehicleConstant.wuFenChannels.contains(channel)){
            channel=VehicleConstant.wuFenChannel;
        }else if(VehicleConstant.bydChannels.contains(channel)){
            channel=VehicleConstant.bydChannel;
        }else if(VehicleConstant.caocaoChannels.contains(channel)){
            channel=VehicleConstant.caocaoChannel;
        }else if(VehicleConstant.kedaxunfeis.contains(channel)){
            channel=VehicleConstant.kedaxunfeichannel;
        }else if(VehicleConstant.shangqidazhongChannels.contains(channel)){
            channel=VehicleConstant.shangqidazhongChannel;
        }
        carActivityVO.setChannel(channel);
        int backCount=3;
        String backCountStr= RedisLowPriceDAO.getInstance().getString(CAR_ACTIVITY_COUNT+channel);
        if(StringUtils.isNotBlank(backCountStr)){
            try{
                backCount=Integer.parseInt(backCountStr);
            }catch (Exception e){
                log.error("getActivityPriceInfo NumberFormatException",e);
            }
        }
        // 参数验证
        if(StringUtils.isBlank(carActivityVO.getSrc())||StringUtils.isBlank(carActivityVO.getChannel())) {
            // 记录参数错误日志
            log.error("getActivityPriceInfo param has error!!");
            // 增加错误处理计数
            carMonitor.getACTIVITY_PRICE_INFO_ERROR().increment();
            // 返回参数错误消息
            return new MessageModel(SystemCodeErrorConstant.AVTIVITY_PRICE_CHANNEL_SRC);
        }
        Map<String,List<VehicleActivityItemBO>> map=new HashMap<>();
        try{
            List<VehicleActivityItemBO> resultVipList=new ArrayList<>();
            List<VehicleActivityItemBO> resultSVipList=new ArrayList<>();
            // 判断是否签约
            int autoPay= vehicleActivityService.getCarAutoPay(carActivityVO.getUid(),null);
            if(StringUtils.isBlank(carActivityVO.getUid())){
                autoPay=0;
            }
            String signKey=VehicleConstant.USER_SIGN_KEY+carActivityVO.getUid();
            String signValue=RedisLowPriceDAO.getInstance().getString(signKey);
            if(StringUtils.equals(signValue,"1")){
                autoPay=1;
            }
            carActivityVO.setAutoPay(autoPay);
            long count=0;
            if(autoPay==0){
                // 对未签约用户增加访问次数统计
                if(carActivityVO.getFromType()==5){
                    count= RedisLowPriceDAO.getInstance().setIncr(CAR_ACTIVITY_UV+carActivityVO.getUid(),60*60*24*60);
                    carActivityVO.setShowAlter(1);
                }else if(carActivityVO.getFromType()==6){
                    count= Long.parseLong(RedisLowPriceDAO.getInstance().getString(CAR_ACTIVITY_UV+carActivityVO.getUid()));
                    carActivityVO.setFromType(5);
                }
                boolean isNotBuyNormal=isNotBuyNormal(carActivityVO);
               // log.info("getActivityPriceInfo enter retain! count={} isNotBuyNormal={}",count,isNotBuyNormal);
                if(count>=backCount&&isNotBuyNormal&&(carActivityVO.getFromType()==5)){
                    // 获取车载活动信息
                   // log.info("getActivityPriceInfo enter retain! isNotBuyNormal={}",isNotBuyNormal);
                    carActivityVO.setGearType(1);
                    List<CarActivity> carActivityList=cacheUtils.getCarActivityCache.get(carActivityVO);
                    log.info("getActivityPriceInfo enter retain! carActivityList={}",JSONUtil.toJsonStr(carActivityList));
                    if(CollectionUtils.isEmpty(carActivityList)){
                        cacheUtils.getCarActivityCache.refresh(carActivityVO);
                        carActivityList=cacheUtils.getCarActivityCache.get(carActivityVO);
                    }
                    parseCarActivityList(carActivityList, resultVipList, resultSVipList, map,count, carActivityVO,backCount);
                    return new MessageModel(map);
                }
            }
            if(carActivityVO.getFromType()==6){
                carActivityVO.setFromType(5);
            }
            carActivityVO.setGearType(0);
            List<CarActivity> carActivityList=cacheUtils.getCarActivityCache.get(carActivityVO);
            if(CollectionUtils.isEmpty(carActivityList)){
                cacheUtils.getCarActivityCache.refresh(carActivityVO);
                carActivityList=cacheUtils.getCarActivityCache.get(carActivityVO);
            }
            log.info("getActivityPriceInfo enter carActivityList={}",JSONUtil.toJsonStr(carActivityList));
            // 处理正常活动挡位信息
            parseCarActivityList(carActivityList, resultVipList, resultSVipList, map,count,carActivityVO,backCount);
        }catch (Exception e){
            JSONObject json=JSONObject.parseObject(VehicleConstant.carActivityPriceInfo);
            log.error("getActivityPriceInfo has error!",e);
            return new MessageModel(json);
        }
        return new MessageModel(map);
    }

    /**
     * 是否购买常规活动挡位
     *
     * @param carActivityVO
     * @return
     */
    boolean isNotBuyNormal(CarActivityVO carActivityVO){
        if(StringUtils.isBlank(carActivityVO.getUid())||StringUtils.equals(carActivityVO.getUid(),"0")){
            return true;
        }
        String key=VehicleConstant.CAR_ACTIVITY_BUY_TYPE+carActivityVO.getSrc()+carActivityVO.getUid();
        String stockId= RedisLowPriceDAO.getInstance().getString(key);
        log.info("isNotBuyNormal key ={} stockid={}",key,stockId);
        if(StringUtils.isNotBlank(stockId)){
            CarActivity carActivity= carActivityService.getById(stockId);
            if(carActivity!=null&&carActivity.getGearType()!=null&&carActivity.getGearType()==0){
                return false;
            }
        }
        return true;
    }

    /**
     * 解析车载活动列表
     *
     * @param carActivities 车载活动列表，包含各个车载的活动信息
     * @param resultVipList 解析后存放车载VIP活动项的列表
     * @param resultSVipList 解析后存放SVIP车载活动项的列表
     * @param map 用于存储最终解析结果的映射表，其中包含车载VIP和SVIP的价格信息
     * @param count 用于判断特定条件的计数器
     * @return 返回包含VIP和SVIP车载活动信息的映射表
     */
    public Map<String,List<VehicleActivityItemBO>> parseCarActivityList(List<CarActivity> carActivities,List<VehicleActivityItemBO> resultVipList, List<VehicleActivityItemBO> resultSVipList,  Map<String,List<VehicleActivityItemBO>> map,long count,CarActivityVO carActivityVO,int backCount){
        // 遍历车载活动列表，对每项活动进行处理
        carActivities.forEach(carActivity->{
            try{
                // 初始化车载活动项BO对象
                VehicleActivityItemBO vehicleItemBO=new VehicleActivityItemBO();
                // 设置当前价格和续费价格
                vehicleItemBO.setCurrentPrice(carActivity.getPrice().doubleValue());
                vehicleItemBO.setRenewPrice(carActivity.getRenewalPrice()!=null?carActivity.getRenewalPrice().doubleValue():null);
                // 计算折扣率和原价
                BigDecimal linePrice=carActivity.getLinePrice();
                BigDecimal price=carActivity.getPrice();
                BigDecimal discountRate = price.divide(linePrice, 2, RoundingMode.HALF_UP).multiply(BigDecimal.TEN);
                vehicleItemBO.setDiscountTag(discountRate.doubleValue());
                vehicleItemBO.setUnderPrice(linePrice.doubleValue());
                // 计算节省的金额并格式化
                BigDecimal disPrice=linePrice.subtract(price);
                DecimalFormat decimalFormat = new DecimalFormat("###################.###########");
                String formatPrice=decimalFormat.format(disPrice);
                vehicleItemBO.setTagOne("限时");
                vehicleItemBO.setTagTwo(String.format("立省%s元",formatPrice));
                vehicleItemBO.setRank(carActivity.getRank());
                // 设置是否连续和月份信息
                vehicleItemBO.setIsContinute(carActivity.getAutoPay());
                vehicleItemBO.setMonths(carActivity.getMonth());
                vehicleItemBO.setStockId(carActivity.getId().intValue());
                if(StringUtils.isNotBlank(carActivity.getExtend())){
                    JSONObject json=JSONObject.parseObject(carActivity.getExtend());
                    vehicleItemBO.setSendDay(json.getInteger("addDay"));
                }
                // 根据特定条件设置提醒标志
                if((carActivity.getMonth()==1||carActivity.getMonth()==6)&&count==backCount&&carActivity.getAlert()==1&&carActivityVO.getShowAlter()==1){
                    vehicleItemBO.setIsAlert(1);
                }else{
                    vehicleItemBO.setIsAlert(0);
                }
                // 根据车载类型分别加入到VIP或大VIP列表中
                if(StringUtils.equals(carActivity.getVipType(), ProductEnum.VIP_VEHICLE.getName())){
                    if(carActivity.getAutoPay()==1){
                        vehicleItemBO.setWxTflag("vehicle_"+carActivity.getMonth());
                    }
                    resultVipList.add(vehicleItemBO);
                }else if(StringUtils.equals(carActivity.getVipType(),ProductEnum.SUPER_VIP.getName())){
                    if(carActivity.getAutoPay()==1){
                        vehicleItemBO.setWxTflag("svip_"+carActivity.getMonth());
                    }
                    resultSVipList.add(vehicleItemBO);
                }
            }catch (Exception e){
                // 捕获异常，记录日志，并增加错误计数
                log.error("parseActivityConfig carActivities.forEach is error !",e);
                carMonitor.getACTIVITY_PRICE_INFO_ERROR().increment();
            }
        });
        // 对VIP和大VIP列表进行排序
        Collections.sort(resultVipList);
        Collections.sort(resultSVipList);
        // 将VIP和大VIP列表存入映射表中，返回该映射表
        map.put("vipPrice",resultVipList);
        map.put("svipPrice",resultSVipList);
        return map;
    }


    /**
     * 查看档位活动是否有效
     *
     * @param activityName 活动src
     * @param paySrc 渠道
     * @return
     */
    public MessageModel existActivity(String activityName, String paySrc) {
        MessageModel messageModel = new MessageModel();
        if (StringUtils.isEmpty(activityName)) {
            return new MessageModel(SystemCodeErrorConstant.PARAM_CHECK_ERROR);
        }
        ActivityParams activityParams = new ActivityParams();
        activityParams.setType(activityName);
        activityParams.setPaySrc(paySrc);
        activityParams.setCurrentDate(new Date());
        messageModel.setData(carActivityType.getActivityType(activityParams));
        return messageModel;
    }

    /**
     * 活动渠道时间信息
     *
     * @param src 档位标识
     * @param channel 渠道
     * @return
     */
    public MessageModel getChannelInfo(String src,String channel) {
        try {
            log.info("getChannelInfo param src ={} channel={}",src,channel);
            carMonitor.getACTIVITY_CHANNEL_COUNT().increment();
            if(StringUtils.isBlank(channel)||StringUtils.isBlank(src)){
                log.error("getChannelInfo error src or channel is empty value !");
                carMonitor.getACTIVITY_CHANNEL_ERROR().increment();
                return new MessageModel(SystemCodeErrorConstant.AVTIVITY_CHANNEL_TIME);
            }
            Map<String,Object> returnMap=new HashMap<>();
            channel=channel.toLowerCase();
            if(VehicleConstant.lianyouChannels.contains(channel)){
                channel=VehicleConstant.lianyouChannel;
            }else if(VehicleConstant.jiaChannels.contains(channel)){
                channel=VehicleConstant.jiaChannel;
            }else if(VehicleConstant.wuFenChannels.contains(channel)){
                channel=VehicleConstant.wuFenChannel;
            }else if(VehicleConstant.bydChannels.contains(channel)){
                channel=VehicleConstant.bydChannel;
            }else if(VehicleConstant.caocaoChannels.contains(channel)){
                channel=VehicleConstant.caocaoChannel;
            }else if(VehicleConstant.kedaxunfeis.contains(channel)){
                channel=VehicleConstant.kedaxunfeichannel;
            }
            else if(VehicleConstant.fangyiChannels.contains(channel)){
                channel=VehicleConstant.fangyiChannel;
            }
            Map<String,Map<String,String>> stringStringMap= carConfigNacos.getCarConfigBO().getActivityTimeMap();
            if(stringStringMap!=null){
                Map<String,String> timeMap=stringStringMap.get(channel);
                returnMap.put("startTime",timeMap.getOrDefault("startTime",""));
                returnMap.put("endTime",timeMap.getOrDefault("endTime",""));
            }
            return new MessageModel(returnMap);
        }catch (Exception e){
            log.error("getChannelInfo has error!",e);
        }
        return new MessageModel();
    }

    /**
     * 解析item数据
     *
     * @param dataItemList
     * @param returnMap
     */
    public void parseDataItem(List<DataItem> dataItemList,Map<String,Object> returnMap){
        DataItem dataItem= dataItemList.get(0);
        JSONObject jsonObject = JSONObject.parseObject(dataItem.getContent());
        String startTime = jsonObject.getString("start_time");
        String endtTime = jsonObject.getString("end_time");
        String fromFormat="yyyy/MM/dd HH:mm:ss";
        String toFormat="yyyy-MM-dd";
        startTime=convertDateFormat(startTime,fromFormat,toFormat);
        endtTime=convertDateFormat(endtTime,fromFormat,toFormat);
        returnMap.put("startTime",startTime);
        returnMap.put("endTime",endtTime);
    }

    /**
     * 时间格式转换
     *
     * @param time
     * @param fromFormat
     * @param toFormat
     * @return
     */
    private String convertDateFormat(String time,String fromFormat,String toFormat){
        String formatTime="";
        try{
            SimpleDateFormat sdf = new SimpleDateFormat(fromFormat);
            Date dateTime = sdf.parse(time);
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(toFormat);
            formatTime = simpleDateFormat.format(dateTime);
        }catch (Exception e){
            return "";
        }
        return formatTime;
    }
    public MessageModel saveAndUpdateCarActivity(SaveCarActivityVO saveCarActivityVO){
        boolean res=false;
        if(CollectionUtils.isEmpty(saveCarActivityVO.getCarActivities())){
            return new MessageModel(res);
        }
        List<CarActivity> carActivityList=saveCarActivityVO.getCarActivities();
        List<String> codes=carActivityList.stream().map(CarActivity::getCode).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(saveCarActivityVO.getCarActivities())){
            return new MessageModel(codes);
        }
        List<CarActivity> removeCarActivity=  carActivityService.getRemoveByCodes(codes);
        if(CollectionUtils.isNotEmpty(removeCarActivity)){
            List<String> removeCodes=removeCarActivity.stream().map(CarActivity::getCode).collect(Collectors.toList());
            for(String codeStr:removeCodes){
                carActivityService.updateCarStatusByCode(codeStr);
            }
        }

        log.info("saveAndUpdateCarActivity has remove removeCarActivity={}",JSONUtil.toJsonStr(removeCarActivity));
        for(CarActivity carActivity:carActivityList){
            CarActivity car=carActivityService.getCarActivityByCode(carActivity.getCode());
            if(car!=null){
                res= carActivityService.updateCarActivityByCode(carActivity);
            }else{
                res= carActivityService.save(carActivity);
            }
        }
        return new MessageModel(res);
    }

    public MessageModel saveAndUpdateCarActivityInfo(SaveCarActivityInfoVO saveCarActivityInfoVO){
        log.info("saveAndUpdateCarActivityInfo param saveCarActivityInfoVO={}",JSONUtil.toJsonStr(saveCarActivityInfoVO));
        boolean res=false;
        if(CollectionUtils.isEmpty(saveCarActivityInfoVO.getCarActivityInfoList())){
            return new MessageModel(res);
        }
        List<CarActivityInfo> carActivityList=saveCarActivityInfoVO.getCarActivityInfoList();
        carActivityList.forEach(carActivityInfo -> {
            log.info("channel={} count={}",CAR_ACTIVITY_COUNT+carActivityInfo.getBigChannel(),carActivityInfo.getCount());
            RedisLowPriceDAO.getInstance().addString(CAR_ACTIVITY_COUNT+carActivityInfo.getBigChannel(), String.valueOf(carActivityInfo.getCount()),60*60*24*30);
        });
        return new MessageModel();
    }

    /**
     * 获取活动报价信息
     *
     * @param priceGearVO 车载活动信息对象，包含 src（价格标识）、channel（渠道）、uid（用户ID）
     * @return 返回活动报价信息的消息模型，如果出错返回错误兜底数据
     */
    public MessageModel getCarMobilePriceInfo(PriceGearVO priceGearVO){
        log.info("Retain getCarMobilePriceInfo param={}",JSONUtil.toJsonStr(priceGearVO));
        List<VehicleActivityItemBO> vehiclePriceVIPBOS=new ArrayList<>();
        List<VehicleActivityItemBO> vehiclePriceSVIPBOS=new ArrayList<>();
        Map<String,List<VehicleActivityItemBO>> result=new LinkedHashMap<>();
        result.put("vipPrice",vehiclePriceVIPBOS);
        result.put("svipPrice",vehiclePriceSVIPBOS);
        result.put("vehicleToSvipPrice",new ArrayList<>());
        try{
            int autoPay= vehicleActivityService.getCarAutoPay(priceGearVO.getUid(),priceGearVO.getVirtualUid());
            if (StringUtils.isBlank(priceGearVO.getUid())||StringUtils.equals(priceGearVO.getUid(),"0")) {
                autoPay=0;
            }
            PayDesk payDesk = payDeskService.getPayDeskInfoBySign(CAR_PAY_DESK);
            if(payDesk==null){
                log.error("getCarMobilePriceInfo payDesk is null!");
                return new MessageModel(result);
            }
            List<Gear> gears= gearService.getCarMobileGearList(payDesk.getId(),"ar",Arrays.asList(VipTypeEnum.VEHICLE_VIP.getType(), VipTypeEnum.SUPER_VIP.getType()),priceGearVO,autoPay);
            if(CollectionUtils.isEmpty(gears)){
                log.error("getCarMobilePriceInfo gears is null!");
                return new MessageModel(result);
            }
            priceGearVO.setIsActivity(1);
            List<Filter> filters= retainService.invokeValidFilter(priceGearVO, gears);
            log.info("getCarMobilePriceInfo filters ={}",JSONUtil.toJsonStr(filters));
            if(CollectionUtils.isEmpty(filters)){
                log.error("getCarMobilePriceInfo filters is null!");
                return new MessageModel(result);
            }
            Collections.sort(filters);
            Map<Integer, List<Filter>> filterMap = filters.stream().collect((Collectors.groupingBy(Filter::getGearId)));
            groupByVipTypes(gears,filterMap, priceGearVO, result);
        }catch (Exception e){
            log.error("getCarMobilePriceInfo  has error",e);
        }
        return new MessageModel(result);
    }

    /**
     * 分类模式 单挡位
     *
     * @param gears 挡位
     * @param filterMap
     */
    public void groupByVipTypes(List<Gear> gears, Map<Integer, List<Filter>> filterMap, PriceGearVO priceGearVO, Map<String,List<VehicleActivityItemBO>> result) {
        // 所有的档位按类型进行分类
        try {
            Map<String, List<Gear>> gearMap = gears.stream().collect(Collectors.groupingBy(Gear::getVipType, LinkedHashMap::new, Collectors.toList()));
            for (String vipType : gearMap.keySet()) {
                List<VehicleActivityItemBO> vipList=null;
                if(vipType.equals(VipTypeEnum.VEHICLE_VIP.getType())){
                    vipList= result.get("vipPrice");
                }
                if(vipType.equals(VipTypeEnum.SUPER_VIP.getType())){
                    vipList= result.get("svipPrice");
                }
                try {
                    List<Gear> vipTypeGears = gearMap.get(vipType);
                    List<VehicleActivityItemBO> finalVipList = vipList;
                    vipTypeGears.forEach(itemGear -> {
                        try {
                            if(itemGear.getVipType().equals(VipTypeEnum.VEHICLE_VIP.getType())
                                    &&itemGear.getGearType()==3
                                    &&StringUtils.equals(priceGearVO.getChannel(),"Activity_Banma_h5")){
                                        return;
                            }
                            VehicleActivityItemBO vehicleItemBO=new VehicleActivityItemBO();
                            // 档位对应的策略
                            List<Filter> filters = filterMap.get(itemGear.getId());
                            if (CollectionUtils.isEmpty(filters)) {
                                return;
                            }
                            parseData(vehicleItemBO, itemGear, filters, priceGearVO);
                            boolean isSvipUpgrade=checkCarToSip(itemGear,priceGearVO, result, vehicleItemBO);
                            if(!isSvipUpgrade){
                                finalVipList.add(vehicleItemBO);
                            }
                        } catch (Exception e) {
                            log.error("SinglePriceService groupByVipTypes itemGear has error ", e);
                        }
                    });
                } catch (Exception e) {
                    log.error("SinglePriceService SinglePriceService gearMap item has error", e);
                }
            }

        } catch (Exception e) {
            log.error("SinglePriceService groupByVipTypes has error !",e);
        }
    }

    /**
     * 车载升级超会
     *
     * @param itemGear
     * @param priceGearVO
     */
    public boolean checkCarToSip(Gear itemGear,PriceGearVO priceGearVO, Map<String,List<VehicleActivityItemBO>> result,VehicleActivityItemBO vehicleItemBO){
        try{
            List<VehiclePriceBO> upgradeList=new ArrayList<>();
            if(StringUtils.isNotBlank(itemGear.getExtend())&&JSONUtil.isTypeJSON(itemGear.getExtend())){
                JSONObject gearJson= JSONObject.parseObject(itemGear.getExtend());
                if(gearJson.containsKey("carToSvip")&& StringUtils.isNotBlank(gearJson.getString("carToSvip"))){
                    newFilterService.checkCarToSVIP(priceGearVO.getUid(),upgradeList,vehicleItemBO,null);
                    if(CollectionUtils.isNotEmpty(upgradeList)){
                        VehiclePriceBO vehiclePriceBO= upgradeList.get(0);
                        VehicleActivityItemBO vehicleActivityItemBO=new VehicleActivityItemBO();
                        vehicleActivityItemBO.setCurrentPrice(vehiclePriceBO.getCurrentPrice());
                        vehicleActivityItemBO.setRenewPrice(30d);
                        vehicleActivityItemBO.setIsContinute(vehiclePriceBO.getAutoPay()?1:0);
                        vehicleActivityItemBO.setMonths(vehicleItemBO.getMonths());
                        vehicleActivityItemBO.setSrc(vehiclePriceBO.getPaySrc());
                        vehicleActivityItemBO.setTopLeftTag(vehicleItemBO.getTopLeftTag());
                        vehicleActivityItemBO.setAutoPayDesc("次月到期后将30元/月自动续费，可随时取消。");
                        vehicleActivityItemBO.setUpGradeId(vehiclePriceBO.getUpGradeId());
                        vehicleActivityItemBO.setTagOne("限时");
                        vehicleActivityItemBO.setWxTflag("svip_1");
                        vehicleActivityItemBO.setDiscount(vehicleItemBO.getDiscount());
                        vehicleActivityItemBO.setUnderPrice(vehicleItemBO.getUnderPrice());
                        vehicleActivityItemBO.setFilterId(vehicleItemBO.getFilterId());
                        vehicleActivityItemBO.setTitle(vehicleItemBO.getTitle());
                        result.put("vehicleToSvipPrice",Arrays.asList(vehicleActivityItemBO));
                    }
                    return true;
                }
            }
        }catch (Exception e){
            log.error("checkCarToSip has error!",e);
        }
        return false;
    }

    /**
     * 解析parse
     *
     * @param vehicleItemBO
     * @param gear
     * @param filterLists
     * @param priceGearVO
     */
    public void parseData(VehicleActivityItemBO vehicleItemBO, Gear gear, List<Filter> filterLists, PriceGearVO priceGearVO) {
        try {
            filterLists.forEach(filter -> {
                try {
                    if(filter.getIsActivity()!=1){
                        return;
                    }
                    parseFilter(filter, gear, vehicleItemBO, priceGearVO);
                } catch (Exception e) {
                    log.error("paydesk：filterType has error!filterType", e);
                }
            });
        } catch (Exception e) {
            log.error("paydesk：parseData has error!gear={}", JSONObject.toJSON(gear), e);
        }
    }

    /**
     * 价格解析
     * @param filter
     * @param gear
     * @param vehicleItemBO
     * @param priceGearVO
     */
    public void parseFilter(Filter filter, Gear gear, VehicleActivityItemBO vehicleItemBO, PriceGearVO priceGearVO){
        // abData 普通
        if(vehicleItemBO.getStockId()!=null){
            return;
        }
        try{
            JSONObject gearDoc=JSONObject.parseObject(gear.getDoc());
            String oPrice=gearDoc.getString("oPrice");
            JSONObject filterDoc=JSONObject.parseObject( filter.getDoc());
            String src=filterDoc.getString("src");
            String title=filterDoc.getString("title");
            String topLeftTag=filterDoc.getString("topLeftTag");
            String discount=filterDoc.getString("discount");
            String autoPayDesc=filterDoc.getString("autoPayDesc");
            vehicleItemBO.setCurrentPrice(filter.getPrice());
            vehicleItemBO.setRenewPrice(filter.getRenewalPrice());
            BigDecimal linePrice=new BigDecimal(oPrice);
            BigDecimal price=new BigDecimal(filter.getPrice());
            BigDecimal discountRate = price.divide(linePrice, 2, RoundingMode.HALF_UP).multiply(BigDecimal.TEN);
            vehicleItemBO.setDiscountTag(discountRate.doubleValue());
            vehicleItemBO.setUnderPrice(linePrice.doubleValue());
            BigDecimal disPrice=linePrice.subtract(price);
            DecimalFormat decimalFormat = new DecimalFormat("###################.###########");
            String formatPrice=decimalFormat.format(disPrice);
            vehicleItemBO.setTagOne("限时");
            vehicleItemBO.setTagTwo(String.format("立省%s元",formatPrice));
            vehicleItemBO.setIsContinute(gear.getAutoPay());
            vehicleItemBO.setMonths(gear.getGearType());
            vehicleItemBO.setStockId(filter.getId());
            vehicleItemBO.setSrc(src);
            vehicleItemBO.setFilterId(String.valueOf(filter.getId()));
            vehicleItemBO.setTopLeftTag(topLeftTag);
            vehicleItemBO.setDiscount(discount);
            vehicleItemBO.setAutoPayDesc(autoPayDesc);
            vehicleItemBO.setTitle(title);
            SchemeConfig schemeConfig= schemeConfigService.getSrcSchemeConfig(src);
            String resource=schemeConfig.getResource();
            resource=resource.replace("\\","");
            JSONObject object=JSONObject.parseObject(resource);
            if(object.containsKey("addDay")&& StringUtils.isNotBlank(object.getString("addDay"))) {
                int addDay=Integer.parseInt(object.getString("addDay"));
                vehicleItemBO.setSendDay(addDay);
            }
            if(StringUtils.equals(gear.getVipType(), ProductEnum.VIP_VEHICLE.getName())){
                if(gear.getAutoPay()==1){
                    vehicleItemBO.setWxTflag("vehicle_"+gear.getGearType());
                }
            }else if(StringUtils.equals(gear.getVipType(),ProductEnum.SUPER_VIP.getName())){
                if(gear.getAutoPay()==1){
                    vehicleItemBO.setWxTflag("svip_"+gear.getGearType());
                }
            }
            if (StringUtils.isNotBlank(filter.getExtend())&&JSONUtil.isTypeJSON(filter.getExtend())) {
                String extend= filter.getExtend().replace("\\","");
                JSONObject js= JSONObject.parseObject(extend);
                if(js.containsKey("carToSvipDayPrice")||js.containsKey("carToSvipMonthPrice")){
                    String carToSvipDayPrice=js.getString("carToSvipDayPrice");
                    vehicleItemBO.setCarToSvipDayPrice(carToSvipDayPrice);
                    String carToSvipMonthPrice=js.getString("carToSvipMonthPrice");
                    vehicleItemBO.setCarToSvipMonthPrice(carToSvipMonthPrice);
                }
            }
        }catch (Exception e){
            log.error("parseFilter has  error !,filter={}",JSONUtil.toJsonStr(filter),e);
        }
    }

    @Override
    public MessageModel getSimpleCarInfo(PriceGearVO priceGearVO){
        log.info("getSimpleCarInfo  param={}",JSONUtil.toJsonStr(priceGearVO));
        List<VehiclePriceBO> result=new LinkedList<>();
        try{
            int autoPay= vehicleActivityService.getCarAutoPay(priceGearVO.getUid(),priceGearVO.getVirtualUid());
            if (StringUtils.isBlank(priceGearVO.getUid())||StringUtils.equals(priceGearVO.getUid(),"0")) {
                autoPay=0;
            }
            PayDesk payDesk = payDeskService.getPayDeskInfoBySign("car_mini_limitpop");
            if(payDesk==null){
                log.error("getSimpleCarInfo payDesk is null!");
                return new MessageModel(result);
            }
            List<String> vipTypes=  Arrays.asList(com.memberintergral.carservice.enums.VipTypeEnum.VEHICLE_VIP.getType(), com.memberintergral.carservice.enums.VipTypeEnum.SUPER_VIP.getType());
            if(StringUtils.equals(priceGearVO.getFromsrc(),"play_super_vip")|| StringUtils.equals(priceGearVO.getFromsrc(),"play_ts_vip")){
                vipTypes=  Arrays.asList(com.memberintergral.carservice.enums.VipTypeEnum.SUPER_VIP.getType());
            }
            List<Gear> gears= gearService.getCarMobileGearList(payDesk.getId(),"ar",vipTypes,priceGearVO,autoPay);
            log.info("getSimpleCarInfo gears ={}",JSONUtil.toJsonStr(gears));
            if(CollectionUtils.isEmpty(gears)){
                log.error("getSimpleCarInfo gears is null!");
                return new MessageModel(result);
            }
            List<Filter> filters= retainService.invokeValidFilter(priceGearVO, gears);
            log.info("getSimpleCarInfo filters ={}",JSONUtil.toJsonStr(filters));
            if(CollectionUtils.isEmpty(filters)){
                log.error("getSimpleCarInfo filters is null!");
                return new MessageModel(result);
            }
            Collections.sort(filters);
            Map<Integer, List<Filter>> filterMap = filters.stream().collect((Collectors.groupingBy(Filter::getGearId)));
            groupMixByVipTypes(gears,filterMap, priceGearVO, result);
        }catch (Exception e){
            log.error("getSimpleCarInfo  has error",e);
        }
        return new MessageModel(result);
    }

    /**
     * 分类模式混合挡位
     *
     * @param gears 挡位
     * @param filterMap
     */
    public void groupMixByVipTypes(List<Gear> gears, Map<Integer, List<Filter>> filterMap, PriceGearVO priceGearVO, List<VehiclePriceBO> result) {
        // 所有的档位按类型进行分类
        for (Gear gear : gears) {
            try {
                VehiclePriceBO vehiclePriceBO=new VehiclePriceBO();
                // 档位对应的策略
                List<Filter> filters = filterMap.get(gear.getId());
                if (CollectionUtils.isEmpty(filters)) {
                    continue;
                }
                parseSimpleData(vehiclePriceBO, gear, filters, priceGearVO);
                result.add(vehiclePriceBO);
            } catch (Exception e) {
                log.error("groupMixByVipTypes gear has error ", e);
            }
        }
    }

    /**
     * 解析parseSimpleData
     *
     * @param vehiclePriceBO
     * @param gear
     * @param filterLists
     * @param priceGearVO
     */
    public void parseSimpleData(VehiclePriceBO vehiclePriceBO, Gear gear, List<Filter> filterLists, PriceGearVO priceGearVO) {
        try {
            DocBO docBO=new DocBO();
            parseGearDoc(gear.getDoc(),docBO);
            filterLists.forEach(filter -> {
                try {
                    parseFilter(filter, gear, vehiclePriceBO, priceGearVO, docBO);
                } catch (Exception e) {
                    log.error("parseSimpleData forEach has error!", e);
                }
            });
        } catch (Exception e) {
            log.error("parseSimpleData has error! gear={}", JSONObject.toJSON(gear), e);
        }
    }

    /**
     * 解析扩展字段
     *
     * @param doc
     * @param docBO
     */
    public void parseGearDoc(String doc,DocBO docBO){
        try{
            if(StringUtils.isNotBlank(doc)){
                JSONObject docJS=JSONObject.parseObject(doc);
                //竖屏
                String descImgTall= docJS.getString("verticalScreenImg");
                //宽屏
                String descImgWide= docJS.getString("wideScreenImg");
                //超宽屏
                String superWideScreenImg= docJS.getString("superWideScreenImg");
                docBO.setDescImgTall(descImgTall);
                docBO.setDescImgWide(descImgWide);
                docBO.setSuperWideScreenImg(superWideScreenImg);
            }
        }catch (Exception e){
            log.error("parseGearDoc has error!",e);
        }
    }

    /**
     * 价格解析
     * @param filter
     * @param gear
     * @param vehiclePriceBO
     * @param priceGearVO
     */
    public void parseFilter(Filter filter, Gear gear, VehiclePriceBO vehiclePriceBO, PriceGearVO priceGearVO,DocBO docBO){
        // abData 普通
        if(vehiclePriceBO.getFilterId()!=null){
            return;
        }
        try{
            JSONObject filterDoc=JSONObject.parseObject(filter.getDoc());
            String topTag=filterDoc.getString("topTag");
            String lotteryQrCode=filterDoc.getString("lotteryQrCode");
            String src=filterDoc.getString("src");
            parseDoc(filterDoc,vehiclePriceBO,gear,docBO,priceGearVO,filter, src);
            String subTitle=filterDoc.getString("subTitle");
            String bottomTag=filterDoc.getString("bottomTag");
            JSONObject gearDoc=JSONObject.parseObject(gear.getDoc());
            String oPrice=gearDoc.getString("oPrice");
            String autoPayBtnShow=gearDoc.getString("autoPayBtnShow");
            DecimalFormat decimalFormat = new DecimalFormat("###################.###########");
            String formatPrice=decimalFormat.format(filter.getPrice());
            String topLeftTag=filterDoc.getString("topLeftTag");
            String discount=filterDoc.getString("discount");
            String autoPayDesc=filterDoc.getString("autoPayDesc");
            vehiclePriceBO.setCurrentPrice(Double.parseDouble(formatPrice));
            vehiclePriceBO.setRenewPrice(filter.getRenewalPrice());
            vehiclePriceBO.setFilterId(filter.getId());
            vehiclePriceBO.setDoc(filter.getDoc());
            vehiclePriceBO.setMonth(gear.getGearType());
            vehiclePriceBO.setUnderPrice(org.apache.commons.lang.StringUtils.isBlank(oPrice)?0:Double.parseDouble(oPrice));
            if(gear.getGearName().contains("听书")){
                String title=gearDoc.getString("title");
                vehiclePriceBO.setTitle(title);
            }else{
                vehiclePriceBO.setTitle(gear.getGearName());
            }
            vehiclePriceBO.setSubTitle(subTitle);
            vehiclePriceBO.setSelected(org.apache.commons.lang.StringUtils.isNotBlank(autoPayBtnShow) && autoPayBtnShow.equals("1"));
            vehiclePriceBO.setGearId(gear.getId());
            vehiclePriceBO.setPaySrc(src);
            vehiclePriceBO.setAutoPay(gear.getAutoPay().equals(1));
            vehiclePriceBO.setUpGradeId(null);
            vehiclePriceBO.setLabel(null);
            vehiclePriceBO.setBottomTag(bottomTag);
            if(gear.getVipType().equals(com.memberintergral.carservice.enums.VipTypeEnum.SUPER_VIP.getType())){
                vehiclePriceBO.setWxTflg("svip_"+gear.getGearType());
            }
            if(gear.getVipType().equals(com.memberintergral.carservice.enums.VipTypeEnum.VEHICLE_VIP.getType())){
                vehiclePriceBO.setWxTflg("vehicle_"+gear.getGearType());
            }
            if(gear.getVipType().equals(com.memberintergral.carservice.enums.VipTypeEnum.SUPER_VIP.getType())&&gear.getGearType()==1
                    && org.apache.commons.lang.StringUtils.isNotBlank(gear.getExtend())
                    &&JSONUtil.isTypeJSON(gear.getExtend())){
                JSONObject gearJson= JSONObject.parseObject(gear.getExtend());
                if(gearJson.containsKey("wxTflag")&& org.apache.commons.lang.StringUtils.isNotBlank(gearJson.getString("wxTflag"))){
                    String wxTflag=gearJson.getString("wxTflag");
                    if(org.apache.commons.lang.StringUtils.isNotBlank(wxTflag)){
                        vehiclePriceBO.setWxTflg(wxTflag);
                    }
                }
            }
            vehiclePriceBO.setTag(topTag);
            vehiclePriceBO.setLabel(autoPayBtnShow);
            vehiclePriceBO.setFilterId(filter.getId());
            vehiclePriceBO.setShowQrcode(lotteryQrCode);
            vehiclePriceBO.setDiscount(discount);
            vehiclePriceBO.setTopLeftTag(topLeftTag);
            vehiclePriceBO.setAutoPayDesc(autoPayDesc);
            vehiclePriceBO.setVipType(gear.getVipType());
        }catch (Exception e){
            log.error("parseFilter has  error ! filter={}",JSONUtil.toJsonStr(filter),e);
            carMonitor.PRICEINFO_PARAM_ERROR.increment();
        }
    }

    /**
     * 解析策略doc
     *
     * @param filterDoc
     * @param vehiclePriceBO
     * @param docBO
     */
    public void parseDoc(JSONObject filterDoc , VehiclePriceBO vehiclePriceBO, Gear gear, DocBO docBO, PriceGearVO priceGearVO, Filter filter, String src){
        try {
            //竖屏
            String descImgTall = filterDoc.getString("verticalScreenImg");
            //宽屏
            String descImgWide = filterDoc.getString("wideScreenImg");
            //超宽屏
            String superWideScreenImg = filterDoc.getString("superWideScreenImg");
            //是否活动抽奖弹窗
            String showActivityPopup = filterDoc.getString("openLotteryPopup");
            //是否活动挡位
            String isActivity = gear.getIsActivity();
            //二维码链接
            String qrcodeLink=filterDoc.getString("qrcodeLink");
            //支付成功弹窗图片
            String payFinishedPopupImg=filterDoc.getString("payFinishedPopupImg");
            //二维码未登录遮罩文案
            String qrcodeUnloginText=filterDoc.getString("qrcodeUnloginText");
            //支付成功弹窗标题
            String payFinishedPopupTitle=filterDoc.getString("payFinishedPopupTitle");
            //支付成功弹窗描述
            String payFinishedPopupDesc=filterDoc.getString("payFinishedPopupDesc");
            //支付成功弹窗提示
            String payFinishedPopupTips=filterDoc.getString("payFinishedPopupTips");
            vehiclePriceBO.setDescImgTall(org.apache.commons.lang.StringUtils.isBlank(descImgTall) ? docBO.getDescImgTall() : descImgTall);
            vehiclePriceBO.setDescImgWide(org.apache.commons.lang.StringUtils.isBlank(descImgWide) ? docBO.getDescImgWide() : descImgWide);
            vehiclePriceBO.setDescImgLong(org.apache.commons.lang.StringUtils.isBlank(superWideScreenImg) ? docBO.getSuperWideScreenImg() : superWideScreenImg);
            vehiclePriceBO.setShowActivityPopup(showActivityPopup);
            vehiclePriceBO.setIsActivity(isActivity);
            vehiclePriceBO.setQrcodeLink(qrcodeLink);
            vehiclePriceBO.setPayFinishedPopupImg(payFinishedPopupImg);
            vehiclePriceBO.setQrcodeUnloginText(qrcodeUnloginText);
            vehiclePriceBO.setPayFinishedPopupTitle(payFinishedPopupTitle);
            vehiclePriceBO.setPayFinishedPopupDesc(payFinishedPopupDesc);
            vehiclePriceBO.setPayFinishedPopupTips(payFinishedPopupTips);
        }catch (Exception e){
            log.error("parseDoc doc has error!",e);
        }
    }

}
