package com.memberintergral.carservice.service.impl;

import com.memberintergral.carservice.config.constant.MessageModel;
import com.memberintergral.carservice.config.constant.SystemCodeErrorConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class UserService {

    @Autowired
    private VehicleService vehicleService;

    /**
     * 获取用户vipm过期时间
     * @param uid 用户uid
     * @return -1:服务异常 0:用户未购买vip  >0:vipm过期时间
     */
    public long getVipmExpire(String uid,String virtualUid){
        long vipmExpire = -1;
        MessageModel messageModel=vehicleService.queryVipInfoFromVIP(uid,virtualUid);
        if (!messageModel.getCode().equals(SystemCodeErrorConstant.SUCCESS.getCode())) {
            return vipmExpire;
        }
        Map<String, Object> map= (Map<String, Object>) messageModel.getData();
        if (map.containsKey("vipmExpire")){
            vipmExpire = Long.parseLong(String.valueOf(map.get("vipmExpire")));
        }else{
            vipmExpire=0;
        }
        return vipmExpire;

    }

}
