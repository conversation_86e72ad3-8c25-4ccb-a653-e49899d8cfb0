package com.memberintergral.carservice.service.impl;

import com.memberintergral.carservice.config.enums.CarActivityMonthPriceEnum;
import com.memberintergral.carservice.config.enums.CarActivitySuperEnum;
import com.memberintergral.carservice.config.enums.ProductEnum;
import com.memberintergral.carservice.config.exception.SVWException;
import com.memberintergral.carservice.config.exception.VehicleCouponException;
import com.memberintergral.carservice.domain.DTO.PayInfoDTO;
import com.memberintergral.carservice.domain.entity.*;
import com.memberintergral.carservice.domain.entity.Product;
import com.memberintergral.carservice.domain.entity.VehicleOrder;
import com.memberintergral.carservice.domain.entity.VehicleOrderExtend;
import com.memberintergral.carservice.domain.entity.VehicleProduct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * @program: VipNew
 * @description: 车载活动价格抽象类
 * @author: <EMAIL>
 * @create: 2018-09-19 15:29
 **/
public abstract class CarActivityAbs implements InitializingBean {
    private static final Logger logger = LoggerFactory.getLogger(CarActivityAbs.class);

    public abstract double getfinalPrice(ActivityParams params);

    /**
     * 判断当前时间是否在活动有效期内
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2020/10/26 16:33
     */
    public static boolean isInPeroid(Date date, String startTime, String endTime) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm");
        Date start = null;
        Date end = null;
        try {
            start = sdf.parse(startTime);
            end = sdf.parse(endTime);
        } catch (Exception e) {
            logger.error("日期转换错误", e);
        }
        if (start == null || end == null)
            return false;
        if (date.getTime() - start.getTime() > 0 && date.getTime() - end.getTime() < 0)
            return true;
        else
            return false;
    }

    /**
     * 活动开始时间
     * @param params
     * @return
     */
    public String getBeginTime(ActivityParams params){
        return "";
    }

    /**
     * 活动结束时间
     * @param params
     * @return
     */
    public String getEndTime(ActivityParams params){
        return "";
    }


    /**
     * 校验活动是否在期
     * @param params
     * @return
     */
    public boolean validateInPeriod(ActivityParams params){
        return false;
    }

    /**
     *  未来考虑根据这段信息进行代码重构
     * 获取活动标识
     * @return
     */
    public CarActivitySuperEnum getActivityCode(){
        return null;
    }

    /**
     * 避免每次添加活动src都需要进行修改主流程代码
     * @throws Exception
     */
    public void afterPropertiesSet() throws Exception{
        ActivityManager.registerActivity(getActivityCode().getOpStr(), this);
    }

    /**
     * 获取次月续费价格信息
     * 默认价格策略, 按照 caclNormalMonth方式进行扣费
     *  1： 19.9
     *  3： 57
     *  6：  108
     *  12： 204
     */
    public RenewalPrice getRenewalPriceInfo(RenewalParams renewalParams){
        RenewalPrice renewalPrice = new RenewalPrice();
        short cnt = renewalParams.getCnt();
        CarActivityMonthPriceEnum instance = CarActivityMonthPriceEnum.getInstance(Integer.valueOf(cnt));
        renewalPrice.setCnt(cnt);
        renewalPrice.setDuration((short) (cnt * 31));
        renewalPrice.setPrice(instance.getFinalAmount());
        return renewalPrice;
    }

    /**
     * 在创建插入 product到数据库之前可以进行更改 product信息
     * 比如 买2增1活动 ,可以将 cnt为2个月改为3个月,一般情况下不做修改操作
     * 但如果三方系统不能修改的话,就在相关活动实现类这里进行修改
     * 一般改 cnt. duration
     * @param product
     * @return
     */
    public Product updateProductInfoBeforeCreateProduct (Product product, ActivityParams activityParams){ return product;}


    /**
     * 物理时间是否过期,只用来注册时进行判断
     * @return
     */
    public abstract boolean isPhysicTimeExpire();

    /**
     * 校验src, 默认购买的都是车载vip
     * @return
     */
    public boolean validateSrc(ActivityParams activityParams){
        Product product = activityParams.getProduct();
        Long productTypeId = product.getProductTypeId();
        if (productTypeId!= ProductEnum.VIP_VEHICLE.getId()){
            return false;
        }
        return true;
    }

    /**
     * 检查库存
     *
     * @param params
     * @return
     */
    public boolean checkStock(ActivityParams params) throws SVWException {
        return true;
    };

    /**
     * 减去库存
     * @param order
     * @param products
     */
    public void subStock(VehicleOrder order, List<VehicleProduct> products){ }

    /**
     * 优惠券是否有效
     *
     * @param order
     * @return
     */
    public boolean isCouponValid(VehicleOrder order){
       return true;
    }

    /**
     * 订单优惠券信息
     *
     * @param vehicleOrderExtend
     * @return
     */
    public boolean makeOrderExtendCoupon(VehicleOrderExtend vehicleOrderExtend, String  couponUniqueId, Long orderId, PayInfoDTO payInfo) throws VehicleCouponException {
        return true;
    }

    /**
     * 获取升级id
     *
     * @param order
     * @return
     */
    public int getUpgradeId(VehicleOrder order){
        return -1;
    }

}
