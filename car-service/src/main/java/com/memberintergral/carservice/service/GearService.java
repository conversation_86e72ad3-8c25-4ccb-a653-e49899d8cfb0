package com.memberintergral.carservice.service;



import com.baomidou.mybatisplus.extension.service.IService;
import com.memberintergral.carservice.domain.VO.PriceGearVO;
import com.memberintergral.carservice.domain.entity.Gear;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-31
 */
public interface GearService extends IService<Gear> {


    List<Gear> getGearList(Integer payDeskId, String platform, String vipType , PriceGearVO priceGearVO);

    List<Gear> getCarGearList(Integer payDeskId, String platform, String vipType , PriceGearVO priceGearVO,int cnt);

    List<Gear> getCarMobileGearList(Integer payDeskId, String platform,List<String> vipType, PriceGearVO priceGearVO,int autoPay);

    List<Gear> getNewCarGearList(Integer payDeskId, String platform, String vipType,Integer autoPay,String payDeskSign);

    Gear getCarGearById(Integer id);

}
