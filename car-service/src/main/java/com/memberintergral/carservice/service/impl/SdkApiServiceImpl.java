package com.memberintergral.carservice.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.memberintergral.carservice.config.constant.MessageModel;
import com.memberintergral.carservice.config.constant.VehicleConstant;
import com.memberintergral.carservice.config.monitor.CarMonitor;
import com.memberintergral.carservice.config.nacos.CarConfigNacos;
import com.memberintergral.carservice.config.nacos.VipAdvConfigNacos;
import com.memberintergral.carservice.config.redis.added.RedisLowPriceDAO;
import com.memberintergral.carservice.domain.BO.SDKBO;
import com.memberintergral.carservice.domain.VO.CarSdkApiVO;
import com.memberintergral.carservice.domain.entity.Filter;
import com.memberintergral.carservice.domain.entity.Gear;
import com.memberintergral.carservice.domain.entity.PayDesk;
import com.memberintergral.carservice.enums.ChannelTypeEnum;
import com.memberintergral.carservice.enums.VipTypeEnum;
import com.memberintergral.carservice.mapper.DataItemMapper;
import com.memberintergral.carservice.service.*;
import com.memberintergral.carservice.util.CacheUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.memberintergral.carservice.config.constant.VehicleConstant.SDK_API_CAR;


@Service
@DS("master")
@Slf4j
public class SdkApiServiceImpl implements SdkApiService {

    @Autowired
    private DataModelService dataModelService;

    @Autowired
    private DataItemMapper dataItemMapper;

    @Autowired
    private CarActivityType carActivityType;

    @Autowired
    private CarMonitor carMonitor;

    @Autowired
    private CarActivityService carActivityService;

    @Autowired
    private VehicleActivityService vehicleActivityService;

    @Autowired
    private CacheUtils cacheUtils;

    @Autowired
    private PayDeskService payDeskService;

    @Autowired
    private GearService gearService;

    @Autowired
    private RetainServiceImpl retainService;

    @Autowired
    private SchemeConfigService schemeConfigService;

    @Autowired
    private CarConfigNacos carConfigNacos;

    @Autowired
    private FilterService filterService;

    @Autowired
    private VipAdvConfigNacos vipAdvConfigNacos;


    /**
     * 获取活动报价信息
     *
     * @param carSdkApiVO 车载活动信息对象，包含 src（价格标识）、channel（渠道）、uid（用户ID）
     * @return 返回活动报价信息的消息模型，如果出错返回错误兜底数据
     */
    @Override
    public MessageModel getSdkApiPriceInfo(CarSdkApiVO carSdkApiVO){
        log.info("getSdkApiPriceInfo param={}",JSONUtil.toJsonStr(carSdkApiVO));
        List<SDKBO> vipList=new ArrayList<>();
        try{
            int autoPay= 1;
            PayDesk payDesk = payDeskService.getPayDeskInfoBySign(SDK_API_CAR);
            if(payDesk==null){
                log.error("getSdkApiPriceInfo payDesk is null!");
                return new MessageModel();
            }
            List<Gear> gears= gearService.getCarMobileGearList(payDesk.getId(),"ar", Arrays.asList(VipTypeEnum.VEHICLE_VIP.getType()),null,autoPay);
            log.info("getSdkApiPriceInfo gears ={}",JSONUtil.toJsonStr(gears));
            if(CollectionUtils.isEmpty(gears)){
                log.error("getSdkApiPriceInfo gears is null!");
                return new MessageModel();
            }
            List<Integer> ids= gears.stream().map(x->x.getId()).collect(Collectors.toList());
            List<Filter> filters= filterService.getFiltersByGIds(ids);
            log.info("getSdkApiPriceInfo filters ={}",JSONUtil.toJsonStr(filters));
            if(CollectionUtils.isEmpty(filters)){
                log.error("getSdkApiPriceInfo filters is null!");
                return new MessageModel();
            }
            Collections.sort(filters);
            Map<Integer, List<Filter>> filterMap = filters.stream().collect((Collectors.groupingBy(Filter::getGearId)));
     //       List<String> apiChannels= carConfigNacos.getCarConfigBO().getApiChannels();
//            Integer channelType=ChannelTypeEnum.SDK.getType();
//            if(apiChannels.contains(carSdkApiVO.getChannel())){
//                channelType=ChannelTypeEnum.API.getType();
//            }
            log.info("getSdkApiPriceInfo channelType ={}",carSdkApiVO.getChannelType());
            groupByVipTypes(gears,filterMap, carSdkApiVO,vipList,carSdkApiVO.getChannelType());
            if(CollectionUtils.isEmpty(vipList)){
                JSONArray res= checkDoudi(carSdkApiVO);
                log.info("getSdkApiPriceInfo doudi ={}",res);
                return new MessageModel(res);
            }
            log.info("getSdkApiPriceInfo common ={}",vipList);
            Collections.sort(vipList);
            savePriceToRedis(carSdkApiVO.getChannel(),vipList,carSdkApiVO.getChannelType());
        }catch (Exception e){
            log.error("getCarMobilePriceInfo  has error",e);
            JSONArray res= checkDoudi(carSdkApiVO);
            log.info("getSdkApiPriceInfo doudi ={}",res);
            return new MessageModel(res);
        }
        return new MessageModel(vipList);
    }

    /**
     * 存储redis
     *
     * @param channel
     * @param vipList
     */
    public void savePriceToRedis(String channel,  List<SDKBO> vipList,Integer channelType){
        try {
            log.info("savePriceToRedis  start channel={}",channel);
            if(channelType.equals(ChannelTypeEnum.API.getType())&& vipAdvConfigNacos.getVipAdvNacosBO()!=null){
                Map<String,String> appIds= vipAdvConfigNacos.getVipAdvNacosBO().getAppIdToChannel();
                log.info("savePriceToRedis  appIds={}",appIds);
                 channel= appIds.get(channel);
                log.info("savePriceToRedis  end channel={}",channel);
            }

            String redisKey="car:sdkapi:price:%s:%s";
            if(CollectionUtils.isNotEmpty(vipList)){
                for(SDKBO sdkbo:vipList){
                    String channelKey=String.format(redisKey,channel,sdkbo.getCnt());
                    RedisLowPriceDAO.sadd(channelKey,String.valueOf(sdkbo.getTotal()),60*60*24*30);
                }
            }
        }catch (Exception e){
            log.error("savePriceToRedis has error!",e);
        }
    }

    /**
     * 分类模式 单挡位
     *
     * @param gears 挡位
     * @param filterMap
     */
    public void groupByVipTypes(List<Gear> gears, Map<Integer, List<Filter>> filterMap, CarSdkApiVO carSdkApiVO,  List<SDKBO> vipList,Integer channelType) {
        // 所有的档位按类型进行分类
        try {
            Map<String, List<Gear>> gearMap = gears.stream().collect(Collectors.groupingBy(Gear::getVipType, LinkedHashMap::new, Collectors.toList()));
            for (String vipType : gearMap.keySet()) {
                try {
                    List<Gear> vipTypeGears = gearMap.get(vipType);
                    List<SDKBO> finalVipList = vipList;
                    vipTypeGears.forEach(itemGear -> {
                        try {
                            SDKBO sdkbo=new SDKBO();
                            // 档位对应的策略
                            List<Filter> filters = filterMap.get(itemGear.getId());
                            if (CollectionUtils.isEmpty(filters)) {
                                return;
                            }
                            parseData(sdkbo, itemGear, filters, carSdkApiVO,channelType);
                            if(StringUtils.isBlank(sdkbo.getId())){
                                return;
                            }
                            finalVipList.add(sdkbo);
                        } catch (Exception e) {
                            log.error("SinglePriceService groupByVipTypes itemGear has error ", e);
                        }
                    });
                } catch (Exception e) {
                    log.error("SinglePriceService SinglePriceService gearMap item has error", e);
                }
            }
        } catch (Exception e) {
            log.error("SinglePriceService groupByVipTypes has error !",e);
        }
    }

    public void parseData(SDKBO sdkbo, Gear gear, List<Filter> filterLists, CarSdkApiVO carSdkApiVO,Integer channelType) {
        try {
            filterLists.forEach(filter -> {
                try {
                    parseFilter(filter, gear, sdkbo, carSdkApiVO, channelType);
                } catch (Exception e) {
                    log.error("paydesk：filterType has error!filterType", e);
                }
            });
        } catch (Exception e) {
            log.error("paydesk：parseData has error!gear={}", JSONObject.toJSON(gear), e);
        }
    }

    /**
     * 价格解析
     * @param filter
     * @param gear
     */
    public void parseFilter(Filter filter, Gear gear, SDKBO sdkbo, CarSdkApiVO carSdkApiVO,Integer channelType){
        // abData 普通
        if(StringUtils.isNotBlank(sdkbo.getId())){
            return;
        }
        try{
            boolean blockedChannel = checkBlockedChannel(filter.getBlockedChannel(), carSdkApiVO.getChannel());
            if (!blockedChannel) {
                return;
            }

            boolean checkChannel=checkChannel(filter.getFilterChannel(),carSdkApiVO.getChannel());
            if(!checkChannel){
                return;
            }
            Date startTime= filter.getActivityStartTime();
            if(startTime!=null){
                boolean startRes= isStartInPeroid(startTime);
                log.info("parseFilter startRes ={}",startRes);
                if(!startRes){
                    return;
                }
            }
            Date endTime= filter.getActivityEndTime();
            if(endTime!=null){
                boolean endRes= isEndInPeroid(endTime);
                log.info("parseFilter endRes ={}",endRes);
                if(!endRes){
                    return;
                }
            }
            sdkbo.setId("17");
            sdkbo.setCnt(gear.getGearType());
            sdkbo.setFilterId(filter.getId()!=null?String.valueOf(filter.getId()):"");
            BigDecimal price = new BigDecimal(Double.toString(filter.getPrice()));
            sdkbo.setTotal(price);
            log.info("sdk parseFilter cnt={} price={} biPrice={}",gear.getGearType(), filter.getPrice(),price);
            //sdk渠道 总价=月数*月单价
            if(channelType.equals(ChannelTypeEnum.SDK.getType())){
                price = price.divide(BigDecimal.valueOf(gear.getGearType()), 2, RoundingMode.DOWN).stripTrailingZeros();

            }
            DecimalFormat decimalFormat = new DecimalFormat("###################.###########");
            String formatPrice=decimalFormat.format(price);
            BigDecimal newPr=new BigDecimal(formatPrice);
            sdkbo.setPrice(newPr);

            sdkbo.setType("vip_17");
        }catch (Exception e){
            log.error("parseFilter has  error !,filter={}",JSONUtil.toJsonStr(filter),e);
        }
    }


    /**
     * 判断当前时间是否在活动有效期内
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2020/10/26 16:33
     */
    public static boolean isStartInPeroid( Date startTime) {
        if (startTime == null)
            return false;
        if (new Date().getTime()- startTime.getTime() >= 0)
            return true;
        else
            return false;
    }

    /**
     * 判断当前时间是否在活动有效期内
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2020/10/26 16:33
     */
    public static boolean isEndInPeroid( Date endTime) {
        if (endTime == null)
            return false;
        if (new Date().getTime()- endTime.getTime() < 0)
            return true;
        else
            return false;
    }



    /**
     * 渠道check
     *
     * @param filterChannel
     * @param channel
     * @return
     */
    private  boolean checkChannel(String filterChannel,String channel){
        if(StringUtils.isBlank(filterChannel)){
            return true;
        }
        List<String> filterChannels= Arrays.asList(filterChannel.split(","));
        if(CollectionUtil.isNotEmpty(filterChannels)){
            return filterChannels.contains(channel);
        }
        return false;
    }

    private boolean checkBlockedChannel(String blockedChannel, String channel) {
        if (StringUtils.isBlank(blockedChannel)) {
            return true;
        }
        List<String> blockedChannels = Arrays.asList(blockedChannel.split(","));
        if (CollectionUtil.isNotEmpty(blockedChannels)) {
            return !blockedChannels.contains(channel);
        }
        return true;
    }

    public JSONArray checkDoudi(CarSdkApiVO carSdkApiVO){
       Integer channelType= carSdkApiVO.getChannelType();
       String doudiPrice="";
        if(channelType.equals(ChannelTypeEnum.SDK.getType())){
            doudiPrice=VehicleConstant.SDK_PRICE;
        }else{
            doudiPrice=VehicleConstant.API_PRICE;
        }
       return JSONArray.parseArray(doudiPrice);
    }
}
