package com.memberintergral.carservice.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.memberintergral.carservice.domain.entity.DataItem;
import com.memberintergral.carservice.mapper.DataItemMapper;
import com.memberintergral.carservice.service.DataItemService;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-24
 */
@DS("abRead")
@Service
public class DataItemServiceImpl extends ServiceImpl<DataItemMapper, DataItem> implements DataItemService {

}
