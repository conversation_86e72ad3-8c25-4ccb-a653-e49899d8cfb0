package com.memberintergral.carservice.service.impl.activity;


import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.memberintergral.carservice.config.enums.CarActivityMonthActivityPriceEnum;
import com.memberintergral.carservice.config.enums.CarActivityMonthPriceEnum;
import com.memberintergral.carservice.config.enums.CarActivitySuperEnum;
import com.memberintergral.carservice.config.exception.VehicleCouponException;
import com.memberintergral.carservice.config.redis.added.RedisDAO;
import com.memberintergral.carservice.domain.DTO.PayInfoDTO;
import com.memberintergral.carservice.domain.entity.Product;
import com.memberintergral.carservice.domain.entity.VehicleCoupon;
import com.memberintergral.carservice.domain.entity.VehicleOrder;
import com.memberintergral.carservice.domain.entity.VehicleOrderExtend;
import com.memberintergral.carservice.mapper.VehicleCouponMapper;
import com.memberintergral.carservice.mapper.VehicleOrderExtendMapper;
import com.memberintergral.carservice.service.impl.ActivityParams;
import com.memberintergral.carservice.service.impl.CarActivityAbs;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @program: VipNew
 * @description: 车载业务购买多个月的活动
 * @author: <EMAIL>
 * @create: 2018-09-19 15:38
 **/
@Component
public class CarMonthActivity extends CarActivityAbs {
    private static final Logger logger = LoggerFactory.getLogger(CarMonthActivity.class);
    @Autowired
    private VehicleCouponMapper vehicleCouponMapper;

    @Autowired
    private VehicleOrderExtendMapper vehicleOrderExtendMapper;

    @Override
    public double getfinalPrice(ActivityParams params) {
        logger.info("下单 验证参数：CarMonthActivity params={}", JSONUtil.toJsonStr(params));
        CarActivityMonthPriceEnum cpe = CarActivityMonthPriceEnum.getInstance(params.getCnt());
        // 使用优惠券支付 主要验证 uid cnt
        if (StringUtils.isNotBlank(params.getCouponUniqueId())) {
            VehicleCoupon vehicleCoupon = vehicleCouponMapper.getVehicleCouponsBySerialKey(params.getCouponUniqueId(), params.getUid(),params.getVirtualUid());
            if (vehicleCoupon != null && vehicleCoupon.getUsed() == 0) {
                return cpe == null ? 0 : cpe.getFinalAmount()-vehicleCoupon.getDiscountAmount();
            }
        }
        if(StringUtils.equals(params.getPaySrc(),"kedaxunfei")&& cpe!=null&&BigDecimal.valueOf(cpe.getFinalAmount()).compareTo(BigDecimal.valueOf(params.getCredit()))!=0){
            CarActivityMonthActivityPriceEnum car= CarActivityMonthActivityPriceEnum.getInstance(params.getCnt(),params.getPaySrc());
            logger.info("下单 验证参数：params={},price={}", JSONUtil.toJsonStr(params),car.getPrice());
            return car == null ? 0 : car.getFinalAmount();
        }
        if(StringUtils.equals(params.getPaySrc(),"kwApp")
                &&cpe!=null&&params.getCnt()==12
                &&StringUtils.equals(params.getPlatform(),"ios")
                &&BigDecimal.valueOf(cpe.getFinalAmount()).compareTo(BigDecimal.valueOf(params.getCredit()))!=0){
            CarActivityMonthActivityPriceEnum car= CarActivityMonthActivityPriceEnum.IOS_KWAPP;
            logger.info("IOS下单 验证参数：params={},price={}", JSONUtil.toJsonStr(params),car.getPrice());
            return car == null ? 0 : car.getFinalAmount();
        }
        return cpe == null ? 0 : cpe.getFinalAmount();
    }

    @Override
    public boolean validateInPeriod(ActivityParams params) {
        return true;
    }

    @Override
    public CarActivitySuperEnum getActivityCode() {
        return CarActivitySuperEnum.CALCULATE_MONTH;
    }

    @Override
    public boolean isPhysicTimeExpire() {
        return false;
    }

    /**
     * 优惠券是否有效
     *
     * @param order
     * @return
     */
    public boolean isCouponValid(VehicleOrder order){
        String redisKey="vehicle_coupon_"+order.getId();
        try{
            // 使用优惠券支付
            if (RedisDAO.exists(redisKey)) {
                String couponUniqueId = RedisDAO.getInstance().getString(redisKey);
                VehicleCoupon vehicleCoupon = vehicleCouponMapper.getVehicleCouponsBySerialKey(couponUniqueId, null,null);
                if (vehicleCoupon != null && vehicleCoupon.getUsed() == 0&&order.getStatus()==1) {
                    vehicleCouponMapper.updateVehicleCouponsBySerialKey(couponUniqueId, 1, order.getId());
                    return true;
                } else {
                    logger.info("vehicle_coupon_error:coupon has used! order_id={}",order.getId());
                    // throw new VehicleException("优惠券使用失败");
                    return false;
                }
                // 不用优惠券
            } else {
                VehicleOrderExtend vehicleOrderExtend = vehicleOrderExtendMapper.getVehicleOrderExtendByOid(order.getId());
                if (vehicleOrderExtend == null || StringUtils.isBlank(vehicleOrderExtend.getExt1())) {
                    return true;
                } else {
                    // 缓存失效情况 查询数据库是否有优惠券
                    String centent = vehicleOrderExtend.getExt1();
                    JSONObject json = JSONObject.parseObject(centent);
                    if(json.containsKey("couponUniqueId")){
                        String couponUniqueId = json.getString("couponUniqueId");
                        VehicleCoupon vehicleCoupon = vehicleCouponMapper.getVehicleCouponsBySerialKey(couponUniqueId,null,null);
                        if (vehicleCoupon != null && vehicleCoupon.getUsed() == 0&&order.getStatus()==1) {
                            vehicleCouponMapper.updateVehicleCouponsBySerialKey(couponUniqueId, 1, order.getId());
                            return true;
                        } else {
                            logger.info("vehicle_coupon_error:coupon has used! order_id={}",order.getId());
                            // throw new VehicleException("优惠券使用失败");
                            return false;
                        }
                    }else{
                        return true;
                    }
                }
            }

        }catch (Exception e){
            logger.error("vehicle_coupon_error:isCouponValid is error,order_id={}",order.getId(),e);
        }
        return true;
    }

    /**
     * 订单优惠券信息
     * 验证优惠券 会员类型 有效期
     *
     * @param vehicleOrderExtend
     * @return
     */
    public boolean makeOrderExtendCoupon(VehicleOrderExtend vehicleOrderExtend, String couponUniqueId, Long orderId, PayInfoDTO payInfo) throws VehicleCouponException {
        if(StringUtils.isNotBlank(couponUniqueId)) {
            Product product = payInfo.getProducts().get(0);
            logger.info("vehicle_first_coupon：payInfo={} ,couponUniqueId={}",JSONObject.toJSON(payInfo),couponUniqueId);
            Long productTypeId = product.getProductTypeId();
            Long uid = payInfo.getUid() > 0 ? payInfo.getUid() : payInfo.getPid();
            if(uid==null||uid==0){
                logger.info("vehicle_first_coupon：vehicleCoupon uid is ZERO ,orderId={}",orderId);
                throw new VehicleCouponException("优惠券用户id为0");
            }
            VehicleCoupon vehicleCoupon = vehicleCouponMapper.getVehicleCouponsBySerialKey(couponUniqueId,  payInfo.getUid(), payInfo.getPid());
            logger.info("vehicle_first_coupon：vehicleCoupon={} ,couponUniqueId={}",JSONObject.toJSON(payInfo),couponUniqueId);
            if (vehicleCoupon == null ||vehicleCoupon.getUsed() != 0) {
                logger.error("vehicle_first_coupon：vehicleCoupon is empty or used!=0,couponUniqueId={}",couponUniqueId);
                throw new VehicleCouponException("所选优惠券已使用");
            }
            if (productTypeId.intValue() != vehicleCoupon.getType()){
                logger.error("vehicle_first_coupon：vehicleCoupon productTyep is not equal,couponUniqueId={},product={}",couponUniqueId,JSONObject.toJSON(product));
                throw new VehicleCouponException("所选优惠券不支持当前会员类型");
            }

            if(product.getCnt()!=vehicleCoupon.getMonths().shortValue()){
                logger.error("vehicle_first_coupon：vehicleCoupon month is not equal,couponUniqueId={},a={}",couponUniqueId,vehicleCoupon.getMonths());
                throw new VehicleCouponException("所选优惠券不支持当前挡位");
            }
            if (!validTime(new Date(),vehicleCoupon.getStartTime(),vehicleCoupon.getEndTime())) {
                logger.error("vehicle_first_coupon：vehicleCoupon validTime is not valid,couponUniqueId={} ,nowDate={}",couponUniqueId,new Date().getTime());
                throw new VehicleCouponException("所选优惠券没到有效期");
            }
            if(vehicleCoupon.getContinuteType()==1&&!payInfo.getAutoPay().equals("yes")){
                logger.error("vehicle_first_coupon：makeOrderExtendCoupon getContinuteType is not valid,couponUniqueId={},payInfo.getAutoPay()={}",couponUniqueId,payInfo.getAutoPay());
                throw new VehicleCouponException("所选优惠券仅支持连续续费");
            }
            if(vehicleCoupon.getContinuteType()==2&&!payInfo.getAutoPay().equals("no")){
                logger.error("vehicle_first_coupon：makeOrderExtendCoupon getContinuteType is not valid,couponUniqueId={},payInfo.getAutoPay()={}",couponUniqueId,payInfo.getAutoPay());
                throw new VehicleCouponException("所选优惠券仅支持非连续续费");
            }
            if(!vehicleCoupon.getChannel().equalsIgnoreCase(payInfo.getPaySrc())){
                logger.error("vehicle_first_coupon：makeOrderExtendCoupon channel paysrc is not valid,couponUniqueId={},payInfo={}",couponUniqueId,JSONObject.toJSON(payInfo));
                throw new VehicleCouponException("所选优惠券不支持该渠道");
            }
            Map<String,String> map=new HashMap<>();
            map.put("couponUniqueId",couponUniqueId);
            vehicleOrderExtend.setExt1(JSONObject.toJSONString(map));
            RedisDAO.getInstance().addString("vehicle_coupon_"+orderId, couponUniqueId, 60*60);
        }
        return true;
    }

    /**
     * 验证有效期
     * @param date
     * @param start
     * @param end
     * @return
     */
    boolean validTime(Date date,Date start,Date end){
        if (date.getTime() - start.getTime() > 0 && date.getTime() - end.getTime() < 0){
            return true;
        }
        return false;
    }


}
