package com.memberintergral.carservice.service.impl;

import cn.hutool.extra.validation.ValidationUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 星画类
 */
@Service
public class TagStarService {

    private  static Logger log= LoggerFactory.getLogger(TagStarService.class);


    public boolean checkTagStar(String ruleId, String uid){

        if (!StringUtils.isNumeric(uid) || StringUtils.isBlank(ruleId)){
            return false;
        }
        try {
            Map<String,Object> paramMap=new HashMap<>();
            paramMap.put("sUID",uid);
            paramMap.put("vctRuleIds", Lists.newArrayList(ruleId));
            String url = "http://newabapi.kuwo.cn:8081/abtest/kuwo/tagstar/rulecheck";
            //链式构建请求
            String res = HttpRequest.post(url)
                    .body(JSONUtil.toJsonStr(paramMap))
                    .timeout(400)//超时，毫秒
                    .execute().body();
            JSONObject jsonObject = JSONObject.parseObject(res);
            log.info("checkTagStar invoke userId={} paramMap={} ruleId={} res={}", uid,JSONUtil.toJsonStr(paramMap),ruleId,res);
            if (jsonObject.getInteger("code") != 0) {
                log.error("checkTagStar code error");
                return false;
            }
            JSONObject object = jsonObject.getJSONObject("data");
            JSONObject mapResultObject = object.getJSONObject("mapResult");
            if(mapResultObject.containsKey(ruleId)){
                return mapResultObject.getBoolean(ruleId);
            }
            return false;
        }
        catch (Exception e){
            log.error("checkTagStar invoke  has error",e );
        }
        return false;
    }

}
