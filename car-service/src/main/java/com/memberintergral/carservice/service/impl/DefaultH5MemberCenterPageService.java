package com.memberintergral.carservice.service.impl;

import com.memberintergral.carservice.config.nacos.GuideBarTextByNacos;
import com.memberintergral.carservice.domain.BO.UserInfoBO;
import com.memberintergral.carservice.domain.VO.GuideBarResult;
import com.memberintergral.carservice.service.H5MemberCenterPageService;
import com.memberintergral.carservice.util.TingShuUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Desc:
 * @date 2024-06-18 17:11:46
 */
@Service
public class DefaultH5MemberCenterPageService implements H5MemberCenterPageService {

    @Autowired
    private GuideBarTextByNacos guideBarTextByNacos;

    @Autowired
    private VehicleService vehicleService;

    @Override
    public GuideBarResult getGuideBarText(long userId, long sid) {
        GuideBarResult guideBarResult = new GuideBarResult();
        String text = "";
        if (userId == 0){
            text=  guideBarTextByNacos.getText("noLogin");
        }
        // 获取会员时长
        UserInfoBO userVIPInfo = vehicleService.getUserVIPInfo(userId);
        Long vipmexpire = 0L;
        long now = System.currentTimeMillis();
        boolean isDisPlay = false;
        if (userVIPInfo != null){
            Long svipExpire = userVIPInfo.getSvipExpire();
            Long vipVehicleExpire = userVIPInfo.getVipVehicleExpire();
            Long vipluxuryexpire = userVIPInfo.getVipluxuryexpire();
            guideBarResult.setSvipExpireTime(svipExpire);
            guideBarResult.setVehicleExpireTime(vipVehicleExpire);
            guideBarResult.setVipLuxExpireTime(vipVehicleExpire);
            vipmexpire = userVIPInfo.getVipmexpire();
            if (vipVehicleExpire > now){
                text = guideBarTextByNacos.getText("vehicleVip");
                isDisPlay = true;
            }else if (svipExpire > now){
                text = guideBarTextByNacos.getText("svip");
                isDisPlay = true;
            }else if (vipluxuryexpire > now){
                text = guideBarTextByNacos.getText("luxvip");
                isDisPlay = true;
            }
        }
        // 查询是否为听书会员
        Long tingShuExpireTime = TingShuUtil.getTingShuExpireTime(String.valueOf(userId));
        if (tingShuExpireTime!=null){
            guideBarResult.setTsExpireTime(tingShuExpireTime);
        }
        if (tingShuExpireTime!=null && tingShuExpireTime > now &&  !isDisPlay){
            text = guideBarTextByNacos.getText("tingshuVip");
        }
        if (StringUtils.isBlank(text) && vipmexpire <now){
            text = guideBarTextByNacos.getText("noMemeber");
        }
        guideBarResult.setText(text);
        return guideBarResult;
    }
}
