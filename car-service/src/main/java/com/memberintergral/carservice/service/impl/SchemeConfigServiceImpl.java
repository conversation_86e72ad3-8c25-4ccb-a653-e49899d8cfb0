package com.memberintergral.carservice.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.memberintergral.carservice.domain.entity.SchemeConfig;
import com.memberintergral.carservice.mapper.SchemeConfigMapper;
import com.memberintergral.carservice.service.SchemeConfigService;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-24
 */
@DS("abRead")
@Service
public class SchemeConfigServiceImpl extends ServiceImpl<SchemeConfigMapper, SchemeConfig> implements SchemeConfigService {
    @Override
    public SchemeConfig getSrcSchemeConfig(String src) {
        if(StringUtils.isBlank(src)){
            log.error("SchemeConfigServiceImpl:src is empty!");
            return null;
        }
        LambdaQueryWrapper<SchemeConfig> wrapper= Wrappers.lambdaQuery();
        wrapper.eq(SchemeConfig::getCategory, "src");
        wrapper.eq(SchemeConfig::getCode, src);
        return this.getOne(wrapper);
    }
}
