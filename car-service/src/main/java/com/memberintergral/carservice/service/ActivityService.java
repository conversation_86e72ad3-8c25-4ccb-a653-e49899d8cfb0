package com.memberintergral.carservice.service;

import com.memberintergral.carservice.config.constant.MessageModel;
import com.memberintergral.carservice.domain.VO.CarActivityVO;
import com.memberintergral.carservice.domain.VO.PriceGearVO;
import com.memberintergral.carservice.domain.VO.SaveCarActivityInfoVO;
import com.memberintergral.carservice.domain.VO.SaveCarActivityVO;

public interface ActivityService {
  MessageModel getActivityPriceInfo(CarActivityVO carActivityVO);
  MessageModel existActivity(String activityName, String paySrc);
  MessageModel getChannelInfo(String src,String channel) ;
  MessageModel saveAndUpdateCarActivity(SaveCarActivityVO saveCarActivityVO);
  MessageModel saveAndUpdateCarActivityInfo(SaveCarActivityInfoVO saveCarActivityInfoVO);
  MessageModel getCarMobilePriceInfo(PriceGearVO priceGearVO);
  MessageModel getSimpleCarInfo(PriceGearVO priceGearVO);
}
