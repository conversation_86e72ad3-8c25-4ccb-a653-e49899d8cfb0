package com.memberintergral.carservice.service;



import com.baomidou.mybatisplus.extension.service.IService;
import com.memberintergral.carservice.domain.VO.PayDeskVO;
import com.memberintergral.carservice.domain.entity.PayDesk;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-27
 */
public interface PayDeskService extends IService<PayDesk> {

    PayDesk addOrUpdatePayDesk(PayDeskVO payDeskVO);

    void deletePayDeskById(Integer id,Boolean isTrue,Integer isDelete);

    PayDesk getPayDeskInfoById(Integer id);

    PayDesk getPayDeskInfoBySign(String payDeskSign);

    List<PayDesk> getAllPayDesk();


}
