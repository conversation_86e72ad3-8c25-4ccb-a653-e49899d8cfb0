package com.memberintergral.carservice.service.impl;

import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;

/**
 * @program: VipNew
 * @description: 车载价格方案反射类
 * @author: <EMAIL>
 * @create: 2018-09-19 16:12
 **/
@Deprecated
public class CarActivityContext {

    Class<?> clazz = null;
    Object obj = null;

    public CarActivityContext(String className, Class[] paramsType, Object[] params) {
        try {
            clazz = Class.forName(className);
            Constructor con = clazz.getConstructor(paramsType);
            obj = con.newInstance(params);
        } catch (InstantiationException | IllegalAccessException e) {
            e.printStackTrace();
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (IllegalArgumentException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
        } catch (SecurityException e) {
            e.printStackTrace();
        }
    }

    public double getResult(ActivityParams params) {
        return ((CarActivityAbs) obj).getfinalPrice(params);
    }
}
