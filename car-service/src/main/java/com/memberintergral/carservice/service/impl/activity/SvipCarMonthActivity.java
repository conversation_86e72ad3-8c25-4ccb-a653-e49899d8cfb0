package com.memberintergral.carservice.service.impl.activity;


import com.memberintergral.carservice.config.enums.CarActivitySuperEnum;
import com.memberintergral.carservice.config.enums.CarActivitySuperVipAutoPayMonthPriceEnum;
import com.memberintergral.carservice.config.enums.CarActivitySuperVipMonthPriceEnum;
import com.memberintergral.carservice.config.enums.ProductEnum;
import com.memberintergral.carservice.domain.entity.Product;
import com.memberintergral.carservice.service.impl.ActivityParams;
import com.memberintergral.carservice.service.impl.CarActivityAbs;
import com.memberintergral.carservice.service.impl.RenewalParams;
import com.memberintergral.carservice.service.impl.RenewalPrice;
import org.springframework.stereotype.Component;

/**
 * @program: VipNew
 * @description: 超级会员价格体系
 *               超级会员必须登录uid,不能使用virtual id
 * @author: <EMAIL>
 * @create: 2018-09-19 15:38
 **/
@Component
public class SvipCarMonthActivity extends CarActivityAbs {
    @Override
    public double getfinalPrice(ActivityParams params) {
        double requestCredit = params.getRequestCredit();
        String autoPay = params.getAutoPay();
        if ("yes".equals(autoPay)){
            CarActivitySuperVipAutoPayMonthPriceEnum cpe = CarActivitySuperVipAutoPayMonthPriceEnum.getInstance(params.getCnt());
            double finalAmount = cpe.getFinalAmount();
            return cpe == null ? 0 : finalAmount;
        }else {
            CarActivitySuperVipMonthPriceEnum cpe = CarActivitySuperVipMonthPriceEnum.getInstance(params.getCnt());
            return cpe == null ? 0 : cpe.getFinalAmount();
        }
    }

    @Override
    public boolean validateInPeriod(ActivityParams params) {
        Long uid = params.getUid();
        if (uid == null || uid ==0){
            return false;
        }
        return true;
    }

    @Override
    public CarActivitySuperEnum getActivityCode() {
        return CarActivitySuperEnum.CALCULATE_SVIP_MONTH;
    }

    @Override
    public boolean isPhysicTimeExpire() {
        return false;
    }

    @Override
    public boolean validateSrc(ActivityParams activityParams) {
        Product product = activityParams.getProduct();
        Long productTypeId = product.getProductTypeId();
        if (productTypeId!= ProductEnum.SUPER_VIP.getId()){
            return false;
        }
        return true;
    }

    @Override
    public RenewalPrice getRenewalPriceInfo(RenewalParams renewalParams) {
        RenewalPrice renewalPrice = new RenewalPrice();
        short cnt = renewalParams.getCnt();
        CarActivitySuperVipAutoPayMonthPriceEnum instance = CarActivitySuperVipAutoPayMonthPriceEnum.getInstance(Integer.valueOf(cnt));
        renewalPrice.setCnt(cnt);
        renewalPrice.setDuration((short) (cnt * 31));
        renewalPrice.setPrice(instance.getFinalAmount());
        return renewalPrice;
    }
}
