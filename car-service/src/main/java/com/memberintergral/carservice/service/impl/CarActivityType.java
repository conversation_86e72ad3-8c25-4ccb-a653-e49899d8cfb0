package com.memberintergral.carservice.service.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

import static com.memberintergral.carservice.config.enums.CarActivitySuperEnum.VEHICLE_ACTIVITY;

/**
 * @program: VipNew
 * @description: 车载活动类型列表
 * @author: <EMAIL>
 * @create: 2018-09-19 18:46
 **/
@Component
public class CarActivityType {
    public static final Logger logger = LoggerFactory.getLogger(CarActivityType.class);
    @Autowired
    private VehicleService vehicleService;

    public boolean getActivityType(ActivityParams activityParams) {
        logger.info("payInfoMap---------------" + activityParams.toString());
        String type = activityParams.getType();
        activityParams.setCurrentDate(new Date());
        activityParams.setActivityCheck(true);

        CarActivityAbs activity = ActivityManager.getActivityInfo(VEHICLE_ACTIVITY.getOpStr());
        if (activity != null && activity.validateInPeriod(activityParams)){
            return true;
        }
        return false;
    }
}
