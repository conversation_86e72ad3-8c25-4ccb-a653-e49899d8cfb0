package com.memberintergral.carservice.service;


import com.alibaba.nacos.shaded.com.google.protobuf.ServiceException;
import com.baomidou.mybatisplus.extension.service.IService;
import com.memberintergral.carservice.config.constant.MessageModel;
import com.memberintergral.carservice.config.exception.VehicleException;
import com.memberintergral.carservice.domain.VO.PriceGearVO;
import com.memberintergral.carservice.domain.entity.Filter;

import java.util.List;


/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-04
 */
public interface FilterService extends IService<Filter> {

    /**
     * 获取车载价格档位
     *
     * @param priceGearVO
     * @return
     * @throws ServiceException
     * @throws VehicleException
     */
    MessageModel getPriceGearInfo(PriceGearVO priceGearVO);

    /**
     * 获取车载价格档位
     *
     * @param priceGearVO
     * @return
     * @throws ServiceException
     * @throws VehicleException
     */
    MessageModel getSDKAPIPriceInfo(PriceGearVO priceGearVO);

    /**
     * 获取档位
     *
     * @param gearIDs
     * @return
     */
    List<Filter> getFilterByGearIDs(List<Integer> gearIDs,String channel);

    String getWxTflag(String filterId);

    /**
     * 策略列表
     *
     * @param gearIds
     * @return
     */
    List<Filter> getFiltersByGIds(List<Integer> gearIds);
}
