package com.memberintergral.carservice.service.impl;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @date:2023/5/29
 */

@Service
public class PrivateAuthService {
	private Logger logger = LoggerFactory.getLogger(PrivateAuthService.class);

    @Value("${usergrade.server.domain}")
    private String userGradeDomain;

    private static final String URL = "/openapi/v1/usersystem/userRank?loginUid=%s&loginSid=%s&appUid=0";

    public Boolean isPass(Integer rRank, String uid, String sid){
    	String url=String.format(userGradeDomain + URL, uid, sid);
    	String response=HttpUtil.get(url);
    	logger.trace(String.format("PrivateAuthService-isPass\turl:%s\tresponse:%s",url,response));
        if (StringUtils.isBlank(response)) {
			logger.trace(String.format("PrivateAuthService-isPass-response-blank\tuserId:%s\tsid:%s",uid,sid));
            return false;
        }
        JSONObject jsonObject = null;
        try {
            jsonObject = JSONObject.parseObject(response);
        }catch (Exception e) {
        	logger.trace(String.format("PrivateAuthService-isPass-response-jsonError\tuserId:%s\tsid:%s",uid,sid));
            return false;
        }
        if (jsonObject == null) {
            return false;
        }
        JSONObject data = jsonObject.getJSONObject("data");
        if (data == null) {
            return false;
        }
        int rank = data.getIntValue("rank");
        if (rRank >= rank) {
            return true;
        }
        return false;
    }
    
    public Integer getGradeNum(String uid, String sid){
    	String url=String.format(userGradeDomain + URL, uid, sid);
    	String response=HttpUtil.get(url);
    	logger.trace(String.format("PrivateAuthService-getGradeNum\turl:%s\tresponse:%s",url,response));
        if (StringUtils.isBlank(response)) {
			logger.trace(String.format("PrivateAuthService-getGradeNum-response-blank\tuserId:%s\tsid:%s",uid,sid));
            return 1;
        }
        JSONObject jsonObject = null;
        try {
            jsonObject = JSONObject.parseObject(response);
        }catch (Exception e) {
        	logger.trace(String.format("PrivateAuthService-getGradeNum-response-jsonError\tuserId:%s\tsid:%s",uid,sid));
            return 1;
        }
        if (jsonObject == null) {
            return 1;
        }
        JSONObject data = jsonObject.getJSONObject("data");
        if (data == null) {
            return 1;
        }
        int rank = data.getIntValue("rank");
        return rank;
    }
}
