package com.memberintergral.carservice.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.memberintergral.carservice.domain.entity.PackageRecord;
import com.memberintergral.carservice.mapper.PackageRecordMapper;
import com.memberintergral.carservice.service.PackageRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;


/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-09
 */
@DS("vipconf")
@Service
public class PackageRecordServiceImpl extends ServiceImpl<PackageRecordMapper, PackageRecord> implements PackageRecordService {

    @Autowired
    private PackageRecordMapper packageRecordMapper;

    @Override
    public PackageRecord addPackageRecord(PackageRecord packageRecord)  {
        PackageRecord packageRecordDO= getPackageRecordByKey(packageRecord.getPackageKey());
        if(packageRecordDO==null){
            packageRecord.setType("5");
            packageRecord.setCreateTime(new Date());
            packageRecord.setUpdateTime(new Date());
            this.saveOrUpdate(packageRecord);
        }
        return packageRecord;
    }

    @Override
    public PackageRecord getPackageRecordByKey(String key) {
        LambdaUpdateWrapper<PackageRecord> wrapper= Wrappers.lambdaUpdate();
        wrapper.eq(PackageRecord::getPackageKey,key);
        wrapper.eq(PackageRecord::getType,"5");
        return packageRecordMapper.selectOne(wrapper);
    }

}
