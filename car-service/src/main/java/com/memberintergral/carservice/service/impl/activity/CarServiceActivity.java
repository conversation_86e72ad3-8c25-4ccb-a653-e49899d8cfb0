package com.memberintergral.carservice.service.impl.activity;


import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.memberintergral.carservice.config.enums.CarActivityMonthPriceEnum;
import com.memberintergral.carservice.config.enums.CarActivitySuperEnum;
import com.memberintergral.carservice.config.enums.ProductEnum;
import com.memberintergral.carservice.config.enums.VipUpdatePriceEnum;
import com.memberintergral.carservice.config.exception.VehicleCouponException;
import com.memberintergral.carservice.config.nacos.CarConfigNacos;
import com.memberintergral.carservice.config.redis.added.RedisDAO;
import com.memberintergral.carservice.config.redis.added.RedisLowPriceDAO;
import com.memberintergral.carservice.domain.BO.SchemeConfigBO;
import com.memberintergral.carservice.domain.DTO.PayInfoDTO;
import com.memberintergral.carservice.domain.entity.*;
import com.memberintergral.carservice.mapper.VehicleCouponMapper;
import com.memberintergral.carservice.mapper.VehicleOrderExtendMapper;
import com.memberintergral.carservice.service.SchemeConfigService;
import com.memberintergral.carservice.service.impl.*;
import com.memberintergral.carservice.util.LogTraceContextHolder;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.memberintergral.carservice.config.constant.VehicleConstant.CAR_ONCE_BUY;


/**
 * @program: VipNew
 * @description: 车载业务购买多个月的活动
 * @author: <EMAIL>
 * @create: 2018-09-19 15:38
 **/
@Component
public class CarServiceActivity extends CarActivityAbs {
    private static final Logger logger = LoggerFactory.getLogger(CarServiceActivity.class);
    @Autowired
    private VehicleCouponMapper vehicleCouponMapper;

    @Autowired
    private SchemeConfigService schemeConfigService;

    @Autowired
    private VehicleOrderExtendMapper vehicleOrderExtendMapper;

    @Autowired
    private FilterServiceImpl filterService;

    @Autowired
    private CarConfigNacos carConfigNacos;

    @Override
    public double getfinalPrice(ActivityParams params) {
        logger.info("Car-Service 下单 验证参数：CarMonthActivity params={}", JSONUtil.toJsonStr(params));
        // 优惠券
        if (StringUtils.isNotBlank(params.getCouponUniqueId())) {
            CarActivityMonthPriceEnum cpe = CarActivityMonthPriceEnum.getInstance(params.getCnt());
            VehicleCoupon vehicleCoupon = vehicleCouponMapper.getVehicleCouponsBySerialKey(params.getCouponUniqueId(), params.getUid(),params.getVirtualUid());
            if (vehicleCoupon != null && vehicleCoupon.getUsed() == 0) {
                return cpe == null ? 0 : cpe.getFinalAmount()-vehicleCoupon.getDiscountAmount();
            }
        }
        // 加赠天数
        List<String> addDaysSrcs=carConfigNacos.getCarConfigBO().getAddDaysSrc();
        if(!CollectionUtils.isEmpty(addDaysSrcs)&&addDaysSrcs.contains(params.getSrc())||params.getSrc().startsWith("car_ar_retain_")||params.getSrc().startsWith("car_activity_")||params.getSrc().startsWith("car_guide_")){
            SchemeConfig schemeConfig= schemeConfigService.getSrcSchemeConfig(params.getSrc());
            String resource=schemeConfig.getResource();
            resource=resource.replace("\\","");
            JSONObject object=JSONObject.parseObject(resource);
            if(object.containsKey("addDay")&&StringUtils.isNotBlank(object.getString("addDay"))) {
                int addDay=Integer.parseInt(object.getString("addDay"));
                if(addDay>0){
                    SchemeConfigBO schemeConfigBO=new SchemeConfigBO();
                    schemeConfigBO.setAddDay(addDay);
                    LogTraceContextHolder.setSrcConfig(schemeConfigBO);
                }
            }
            if(params.getSrc().startsWith("car_guide_")){
                double price=Double.parseDouble(object.getString("price"));
                return price;
            }
        }
        if(params.getSrc().startsWith("car_activity_")){
            SchemeConfig schemeConfig= schemeConfigService.getSrcSchemeConfig(params.getSrc());
            String resource=schemeConfig.getResource();
            resource=resource.replace("\\","");
            JSONObject object=JSONObject.parseObject(resource);
            logger.info("Car-Service car_activity_ object={}", object);
            double price=Double.parseDouble(object.getString("price"));
            return price;
        }
        if(params.getSrc().startsWith("car_ar_retain_")){
            SchemeConfig schemeConfig= schemeConfigService.getSrcSchemeConfig(params.getSrc());
            String resource=schemeConfig.getResource();
            resource=resource.replace("\\","");
            JSONObject object=JSONObject.parseObject(resource);
            logger.info("Car-Service car_retain object={}", object);
            double price=Double.parseDouble(object.getString("price"));
            int cnt=params.getCnt();
            int month=Integer.parseInt(object.getString("month"));
            if(cnt!=month){
                return -1;
            }
            String key=CAR_ONCE_BUY+params.getSrc()+params.getUid();
            String cacheUser= RedisLowPriceDAO.getInstance().getString(key);
            logger.info("Car-Service car_retain cacheUser={}", cacheUser);
            if(StringUtils.isNotBlank(cacheUser)){
                return -1;
            }
            return price;
        }

        if(params.getPaySrc().equals("kwApp")||(params.getAutoPay().equals("no")&&!StringUtils.equals(params.getPaySrc(),"C_APK_CaoCao_h5"))||(params.getCnt()==1&&BigDecimal.valueOf(params.getCredit()).compareTo(new BigDecimal("19.9"))==0)){
            SchemeConfig schemeConfig= schemeConfigService.getSrcSchemeConfig(params.getSrc());
            String resource=schemeConfig.getResource();
            resource=resource.replace("\\","");
            JSONObject object=JSONObject.parseObject(resource);
            logger.info("Car-Service 下单 验证参数：CarMonthActivity object={}", object);
            double price=Double.parseDouble(object.getString("price"));
            int cnt=params.getCnt();
            int month=Integer.parseInt(object.getString("month"));
            if(cnt!=month){
                return -1;
            }
            return price;
        }
        if(StringUtils.equals(params.getPaySrc(),"C_APK_CaoCao_h5")){
            SchemeConfig schemeConfig= schemeConfigService.getSrcSchemeConfig(params.getSrc());
            String resource=schemeConfig.getResource();
            resource=resource.replace("\\","");
            JSONObject object=JSONObject.parseObject(resource);
            if(object.containsKey("hasFirstMonth")&&object.containsKey("addDay")){
                int hasFirstMonth=Integer.parseInt(object.getString("hasFirstMonth"));
                int addDay=Integer.parseInt(object.getString("addDay"));
                if(hasFirstMonth==0&&addDay>0){
                    SchemeConfigBO schemeConfigBO=new SchemeConfigBO();
                    schemeConfigBO.setAddDay(addDay);
                    schemeConfigBO.setHasFirstMonth(hasFirstMonth);
                    LogTraceContextHolder.setSrcConfig(schemeConfigBO);
                }
            }
        }
        if(StringUtils.equals(params.getSrc(),"c_ar_calculateMonth0_7_19.9")){
            SchemeConfig schemeConfig= schemeConfigService.getSrcSchemeConfig(params.getSrc());
            String resource=schemeConfig.getResource();
            resource=resource.replace("\\","");
            JSONObject object=JSONObject.parseObject(resource);
            if(object.containsKey("hasFirstMonth")&&object.containsKey("addDay")){
                int hasFirstMonth=Integer.parseInt(object.getString("hasFirstMonth"));
                int addDay=Integer.parseInt(object.getString("addDay"));
                if(hasFirstMonth==0&&addDay>0){
                    SchemeConfigBO schemeConfigBO=new SchemeConfigBO();
                    schemeConfigBO.setAddDay(addDay);
                    schemeConfigBO.setHasFirstMonth(hasFirstMonth);
                    LogTraceContextHolder.setSrcConfig(schemeConfigBO);
                }
            }
        }

        double filterPrice=filterService.getValidPrice( params.getPaySrc(),params.getUid(),params.getVirtualUid(),params.getCnt(),params.getProductTypeId(),params.getAutoPay().equals("yes"),params.getCouponUniqueId(),params.getCredit(),params.getFromType());
        logger.info("Car-Service 下单 验证参数：CarMonthActivity filterPrice={}", filterPrice);
        return  filterPrice;
    }

    @Override
    public boolean validateInPeriod(ActivityParams params) {
        return true;
    }

    @Override
    public CarActivitySuperEnum getActivityCode() {
        return CarActivitySuperEnum.CAR_SERVICE;
    }

    @Override
    public boolean isPhysicTimeExpire() {
        return false;
    }

    /**
     * 优惠券是否有效
     *
     * @param order
     * @return
     */
    public boolean isCouponValid(VehicleOrder order){
        String redisKey="vehicle_coupon_"+order.getId();
        try{
            // 使用优惠券支付
            if (RedisDAO.exists(redisKey)) {
                String couponUniqueId = RedisDAO.getInstance().getString(redisKey);
                VehicleCoupon vehicleCoupon = vehicleCouponMapper.getVehicleCouponsBySerialKey(couponUniqueId, null,null);
                if (vehicleCoupon != null && vehicleCoupon.getUsed() == 0&&order.getStatus()==1) {
                    vehicleCouponMapper.updateVehicleCouponsBySerialKey(couponUniqueId, 1, order.getId());
                    return true;
                } else {
                    logger.info("vehicle_coupon_error:coupon has used! order_id={}",order.getId());
                    // throw new VehicleException("优惠券使用失败");
                    return false;
                }
                // 不用优惠券
            } else {
                VehicleOrderExtend vehicleOrderExtend = vehicleOrderExtendMapper.getVehicleOrderExtendByOid(order.getId());
                if (vehicleOrderExtend == null || StringUtils.isBlank(vehicleOrderExtend.getExt1())) {
                    return true;
                } else {
                    // 缓存失效情况 查询数据库是否有优惠券
                    String centent = vehicleOrderExtend.getExt1();
                    JSONObject json = JSONObject.parseObject(centent);
                    if(json.containsKey("couponUniqueId")){
                        String couponUniqueId = json.getString("couponUniqueId");
                        VehicleCoupon vehicleCoupon = vehicleCouponMapper.getVehicleCouponsBySerialKey(couponUniqueId,null,null);
                        if (vehicleCoupon != null && vehicleCoupon.getUsed() == 0&&order.getStatus()==1) {
                            vehicleCouponMapper.updateVehicleCouponsBySerialKey(couponUniqueId, 1, order.getId());
                            return true;
                        } else {
                            logger.info("vehicle_coupon_error:coupon has used! order_id={}",order.getId());
                            // throw new VehicleException("优惠券使用失败");
                            return false;
                        }
                    }else{
                        return true;
                    }
                }
            }

        }catch (Exception e){
            logger.error("vehicle_coupon_error:isCouponValid is error,order_id={}",order.getId(),e);
        }
        return true;
    }

    /**
     * 订单优惠券信息
     * 验证优惠券 会员类型 有效期
     *
     * @param vehicleOrderExtend
     * @return
     */
    public boolean makeOrderExtendCoupon(VehicleOrderExtend vehicleOrderExtend, String couponUniqueId, Long orderId, PayInfoDTO payInfo) throws VehicleCouponException {
        if(StringUtils.isNotBlank(couponUniqueId)) {
            Product product = payInfo.getProducts().get(0);
            logger.info("vehicle_first_coupon：payInfo={} ,couponUniqueId={}",JSONObject.toJSON(payInfo),couponUniqueId);
            Long productTypeId = product.getProductTypeId();
            Long uid = payInfo.getUid() > 0 ? payInfo.getUid() : payInfo.getPid();
            if(uid==null||uid==0){
                logger.info("vehicle_first_coupon：vehicleCoupon uid is ZERO ,orderId={}",orderId);
                throw new VehicleCouponException("优惠券用户id为0");
            }
            VehicleCoupon vehicleCoupon = vehicleCouponMapper.getVehicleCouponsBySerialKey(couponUniqueId,  payInfo.getUid(), payInfo.getPid());
            logger.info("vehicle_first_coupon：vehicleCoupon={} ,couponUniqueId={}",JSONObject.toJSON(payInfo),couponUniqueId);
            if (vehicleCoupon == null ||vehicleCoupon.getUsed() != 0) {
                logger.error("vehicle_first_coupon：vehicleCoupon is empty or used!=0,couponUniqueId={}",couponUniqueId);
                throw new VehicleCouponException("所选优惠券已使用");
            }
            if (productTypeId.intValue() != vehicleCoupon.getType()){
                logger.error("vehicle_first_coupon：vehicleCoupon productTyep is not equal,couponUniqueId={},product={}",couponUniqueId,JSONObject.toJSON(product));
                throw new VehicleCouponException("所选优惠券不支持当前会员类型");
            }

            if(product.getCnt()!=vehicleCoupon.getMonths().shortValue()){
                logger.error("vehicle_first_coupon：vehicleCoupon month is not equal,couponUniqueId={},a={}",couponUniqueId,vehicleCoupon.getMonths());
                throw new VehicleCouponException("所选优惠券不支持当前挡位");
            }
            if (!validTime(new Date(),vehicleCoupon.getStartTime(),vehicleCoupon.getEndTime())) {
                logger.error("vehicle_first_coupon：vehicleCoupon validTime is not valid,couponUniqueId={} ,nowDate={}",couponUniqueId,new Date().getTime());
                throw new VehicleCouponException("所选优惠券没到有效期");
            }
            if(vehicleCoupon.getContinuteType()==1&&!payInfo.getAutoPay().equals("yes")){
                logger.error("vehicle_first_coupon：makeOrderExtendCoupon getContinuteType is not valid,couponUniqueId={},payInfo.getAutoPay()={}",couponUniqueId,payInfo.getAutoPay());
                throw new VehicleCouponException("所选优惠券仅支持连续续费");
            }
            if(vehicleCoupon.getContinuteType()==2&&!payInfo.getAutoPay().equals("no")){
                logger.error("vehicle_first_coupon：makeOrderExtendCoupon getContinuteType is not valid,couponUniqueId={},payInfo.getAutoPay()={}",couponUniqueId,payInfo.getAutoPay());
                throw new VehicleCouponException("所选优惠券仅支持非连续续费");
            }
            if(!vehicleCoupon.getChannel().equalsIgnoreCase(payInfo.getPaySrc())){
                logger.error("vehicle_first_coupon：makeOrderExtendCoupon channel paysrc is not valid,couponUniqueId={},payInfo={}",couponUniqueId,JSONObject.toJSON(payInfo));
                throw new VehicleCouponException("所选优惠券不支持该渠道");
            }
            Map<String,String> map=new HashMap<>();
            map.put("couponUniqueId",couponUniqueId);
            vehicleOrderExtend.setExt1(JSONObject.toJSONString(map));
            RedisDAO.getInstance().addString("vehicle_coupon_"+orderId, couponUniqueId, 60*60);
        }
        List<Product> products=payInfo.getProducts();
        if(!CollectionUtils.isEmpty(products)&&products.get(0).getUpGradeId()!=null) {
            Integer upGradeId= products.get(0).getUpGradeId();
            Map<String,Integer> map=new HashMap<>();
            map.put("upGradeId",upGradeId);
            vehicleOrderExtend.setExt1(JSONObject.toJSONString(map));
        }
        return true;
    }

    /**
     * 验证有效期
     * @param date
     * @param start
     * @param end
     * @return
     */
    boolean validTime(Date date,Date start,Date end){
        if (date.getTime() - start.getTime() > 0 && date.getTime() - end.getTime() < 0){
            return true;
        }
        return false;
    }

    @Override
    public RenewalPrice getRenewalPriceInfo(RenewalParams renewalParams) {
        RenewalPrice renewalPrice = new RenewalPrice();
        short cnt = renewalParams.getCnt();
        SchemeConfig schemeConfig= schemeConfigService.getSrcSchemeConfig(renewalParams.getSrc());
        String resource=schemeConfig.getResource();
        JSONObject object=JSONObject.parseObject(resource);
        double price=Double.parseDouble(object.getString("renewalPrice"));
        renewalPrice.setCnt(cnt);
        renewalPrice.setDuration((short) (cnt * 31));
        renewalPrice.setPrice(price);
        return renewalPrice;
    }

    /**
     * 获取升级id
     *
     * @param order
     * @return
     */
    public int getUpgradeId(VehicleOrder order) {
        VehicleOrderExtend vehicleOrderExtend = vehicleOrderExtendMapper.getVehicleOrderExtendByOid(order.getId());
        if (vehicleOrderExtend == null || StringUtils.isBlank(vehicleOrderExtend.getExt1())) {
            return -1;
        } else {
            // 缓存失效情况 查询数据库是否有优惠券
            String centent = vehicleOrderExtend.getExt1();
            JSONObject json = JSONObject.parseObject(centent);
            if (json.containsKey("upGradeId")) {
                return json.getInteger("upGradeId");
            }
        }
        return -1;
    }

    public void afterPropertiesSet() throws Exception{
        ActivityManager.registerActivity(getActivityCode().getOpStr(), this);
    }

    /**
     * 校验src
     * @return
     */
    public boolean validateSrc(ActivityParams activityParams){
        Product product = activityParams.getProduct();
        Integer upId=product.getUpGradeId();
        if(upId!=null&&(upId.equals(VipUpdatePriceEnum.VEHICLE_AUTO_SVIP.getId()) || upId.equals(VipUpdatePriceEnum.VEHICLE_AUTO_ONE_MONTH_SVIP.getId()))){
            return true;
        }
        Long productTypeId = product.getProductTypeId();
        if (productTypeId!= ProductEnum.VIP_VEHICLE.getId()&&productTypeId!= ProductEnum.SUPER_VIP.getId()){
            return false;
        }
        return true;
    }
}
