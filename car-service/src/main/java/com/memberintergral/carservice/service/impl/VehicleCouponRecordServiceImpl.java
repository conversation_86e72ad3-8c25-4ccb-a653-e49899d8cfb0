package com.memberintergral.carservice.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.memberintergral.carservice.domain.entity.VehicleCouponRecord;
import com.memberintergral.carservice.mapper.VehicleCouponRecordMapper;
import com.memberintergral.carservice.service.VehicleCouponRecordService;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-24
 */
@DS("master")
@Service
public class VehicleCouponRecordServiceImpl extends ServiceImpl<VehicleCouponRecordMapper, VehicleCouponRecord> implements VehicleCouponRecordService {

}
