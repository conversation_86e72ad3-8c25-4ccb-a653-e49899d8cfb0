package com.memberintergral.carservice.service.impl;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import com.memberintergral.carservice.config.constant.MessageModel;
import com.memberintergral.carservice.config.constant.SystemCodeErrorConstant;
import com.memberintergral.carservice.config.constant.VehicleConstant;
import com.memberintergral.carservice.config.enums.ProductEnum;
import com.memberintergral.carservice.config.exception.VehicleException;
import com.memberintergral.carservice.config.monitor.CarMonitor;
import com.memberintergral.carservice.config.monitor.MonitorUtil;
import com.memberintergral.carservice.config.nacos.CarConfigNacos;
import com.memberintergral.carservice.config.redis.added.RedisDAO;
import com.memberintergral.carservice.config.redis.added.RedisLowPriceDAO;
import com.memberintergral.carservice.domain.BO.CarConfigBO;
import com.memberintergral.carservice.domain.VO.PreOrderVO;
import com.memberintergral.carservice.domain.entity.QueryExpireRes;
import com.memberintergral.carservice.domain.entity.VehicleBusinessOrder;
import com.memberintergral.carservice.domain.entity.VehicleOrder;
import com.memberintergral.carservice.domain.entity.VehicleProduct;
import com.memberintergral.carservice.enums.CarPayWayEnum;
import com.memberintergral.carservice.mapper.VehicleBusinessOrderMapper;
import com.memberintergral.carservice.mapper.VehicleOrderMapper;
import com.memberintergral.carservice.mapper.VehicleProductMapper;
import com.memberintergral.carservice.service.FilterService;
import com.memberintergral.carservice.service.QRCodeService;
import com.memberintergral.carservice.util.LogTraceContextHolder;
import com.memberintergral.carservice.util.MyNumberUtils;
import com.memberintergral.carservice.util.VirtualUserLoginUtil;
import com.memberintergral.carservice.util.cos.CosConfig;
import com.memberintergral.carservice.util.cos.CosUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.memberintergral.carservice.config.constant.VehicleConstant.VIR_VEHICLE_KEY;
import static com.memberintergral.carservice.enums.CarPayWayEnum.BROWSER;

/**
 * @program: vip_adv
 * @description: 车载业务逻辑类
 * @author: <EMAIL>
 * @create: 2023-01-30 10:54
 **/
@Service
@DS("master")
public class VehicleActivityService {

    public static final Logger logger = LoggerFactory.getLogger(VehicleActivityService.class);

    @Value("${vip.server.domain}")
    private String vipDomain;

    @Value("${pay.server.domain}")
    private String payDomain;

    @Value("${order.server.domain}")
    private String orderDomain;

    @Value("${qrcode.image.path}")
    private String qrcodeImagePath;


    @Value("${image.access.path}")
    private String imageAccessPath;

    @Autowired
    private CarMonitor carMonitor;

    @Autowired
    private VehicleService vehicleService;


    @Autowired
    private VehicleProductMapper vehicleProductMapper;

    @Autowired
    private VehicleOrderMapper vehicleOrderMapper;

    @Autowired
    private VehicleAssistant vehicleAssistant;

    @Autowired
    private CarConfigNacos carConfigNacos;

    @Autowired
    private VirtualUserLoginUtil virtualUserLoginUtil;

    @Autowired
    private QRCodeService qrCodeService;

    @Autowired
    private FilterService filterService;

    @Autowired
    private VehicleBusinessOrderMapper vehicleBusinessOrderMapper;
    /**
     * 长链接转换为短链接
     *
     * @param fullURL 长链接
     * @param expireTime 过期时间
     * @return
     */
    public String invokeCreateShortURL(String fullURL,long expireTime){
        if (StringUtils.isBlank(fullURL)){
            logger.error("isUserBuySuccess invokeShortURL：function  fullURL is empty value !fullURL={}",fullURL);
            return "";
        }
        Map<String,Object> param=new HashMap<>();
        param.put("fullUrl",fullURL);
        param.put("validtime",expireTime);
        String result= HttpUtil.post("http://"+vipDomain+"/commercia/s/getShortUrl",param,1500);
        if(StringUtils.isBlank(result)){
            logger.error("isUserBuySuccess invokeShortURL：function http result is empty value !result={}",result);
            return "";
        }
        cn.hutool.json.JSONObject object= JSONUtil.parseObj(result);
        if(!object.getStr("code").equals("200")){
            logger.error("isUserBuySuccess invokeShortURL：function http result is error value !result={}",result);
            return "";
        }
        cn.hutool.json.JSONObject dataJsonObject= object.getJSONObject("data");
        return dataJsonObject.getStr("shortUrl");
    }

    /**
     * 长链接转换为短链接
     *
     * @param fullURL 长链接
     * @param expireTime 过期时间
     * @return
     */
    public String invokeSignShortURL(String fullURL,long expireTime) {
        if (StringUtils.isBlank(fullURL)){
            logger.error("isUserBuySuccess invokeSignShortURL：function  fullURL is empty value !fullURL={}",fullURL);
            return "";
        }
        try {
            String data = String.format("fullUrl=%s&validtime=%s", URLEncoder.encode(fullURL, "utf-8"), expireTime);
            String result = com.memberintergral.carservice.util.HttpUtil.httpPostWithRetry("http://" + vipDomain + "/commercia/s/getShortUrl", "application/x-www-form-urlencoded", data, 5 * 1000, 5 * 1000, null);
            logger.info("invokeShortURL fullURL={} expireTime={} result={}", fullURL, expireTime, result);
            if (StringUtils.isBlank(result)) {
                logger.error("isUserBuySuccess invokeSignShortURL：function http result is empty value !result={}", result);
                return "";
            }
            cn.hutool.json.JSONObject object = JSONUtil.parseObj(result);
            if (!object.getStr("code").equals("200")) {
                logger.error("isUserBuySuccess invokeSignShortURL：function http result is error value !result={}", result);
                return "";
            }
            cn.hutool.json.JSONObject dataJsonObject = object.getJSONObject("data");
            return dataJsonObject.getStr("shortUrl");
        } catch (Exception e) {
            logger.error("isUserBuySuccess invokeSignShortURL：function http error!", e);
            return "";
        }
    }

    /**
     * 虚拟的登录购买验证
     *
     * @param userId
     * @param src
     * @return
     */
    public boolean checkVehicleLogin(String userId,String src){
        try{
            if(StringUtils.isBlank(userId)||userId.equals("0")){
                return true;
            }
            String key= VIR_VEHICLE_KEY+src+userId;
            if(RedisLowPriceDAO.exists(key)){
                return true;
            }
        }catch (Exception e){
            logger.error("checkVehicleLogin has error!",e);
        }
        return false;
    }


    /**
     * 车载预下单接口
     *
     * <p>
     * 根据userAgent自动调起对应支付组件
     * <p/>
     * @param preOrderVO 支付/下单参数
     * @return
     */
    public MessageModel createOrder(PreOrderVO preOrderVO){
            // 监控业务指标
            carMonitor.addNewCreateOrder();
            logger.info("newCreateOrder input param={}", JSONUtil.toJsonStr(preOrderVO));

            Map<String ,Object> resultMap=new HashMap<>();
            // 2.验证参数
            if(StringUtils.isNotBlank(preOrderVO.getJsonStr())&&!JSONUtil.isTypeJSON(preOrderVO.getJsonStr())){
                logger.error("newCreateOrderError userAgent or jsonStr is empty ! jsonStr={}",preOrderVO.getJsonStr());
                CarMonitor.checkAndSendMsg(carMonitor.getNEW_CREATE_ORDER_ERROR(), "NEW_CREATE_ORDER_ERROR",100, "car-service NewCreateOrder 最近一天下单参数错误数：", TimeUnit.DAYS,"刘杰");
                return new MessageModel(SystemCodeErrorConstant.PRE_ORDER_ERROE);
            }
            JSONObject jsonObject= JSONObject.parseObject(preOrderVO.getJsonStr());
            String autoPay=jsonObject.getString("autoPay");
            String src=jsonObject.getString("src");
            String paySrc=jsonObject.getString("paySrc");
            if(StringUtils.equals(src,"carenginevip17ar3mth57-93+31")&&checkVehicleLogin(preOrderVO.getUid(),"carenginevip17ar3mth57-93+31")){
                logger.error("virLogin buy repeat!");
                return new MessageModel(SystemCodeErrorConstant.CAR_PRICE_ERROR);
            }
            String userAgent= LogTraceContextHolder.getUserAgent();
            // 3.支付方式
            CarPayWayEnum carPayWayEnum= CarPayWayEnum.getCarPayInstance(autoPay,userAgent,preOrderVO.getUid());
            if(carPayWayEnum==CarPayWayEnum.WEICHTT_AUTO){
                boolean isPayUser= virtualUserLoginUtil.isPayTypeUser(preOrderVO.getUid(),src);
                boolean isVirPayUser= virtualUserLoginUtil.isVuidPayTypeUser(preOrderVO.getVirtualUid(),src);
                logger.info("isPayUser--------------{} src={}",isPayUser,src);
                if(isPayUser||isVirPayUser){
                    carPayWayEnum=CarPayWayEnum.NEW_WEICHTT_AUTO;
                }
            }
            if(carPayWayEnum==BROWSER){
                resultMap.put("qrcode","https://h5app.kuwo.cn/m/carmembercash/index.html?channel=scanQRcodesfail");
                logger.info("newCreateOrder car pay way browser ! carPayWayEnumid ={} ,jsonStr={}",carPayWayEnum.getId(),preOrderVO.getJsonStr());
              if(!userAgent.contains("Uptime-Kuma")){
                //  CarMonitor.checkAndSendMsg(carMonitor.getNEW_CREATE_ORCODE_BROWSER(), "NEW_CREATE_ORCODE_BROWSER",40, "car-service 最近一小时浏览器扫码数：", TimeUnit.HOURS,"刘杰");
              }
                return new MessageModel(resultMap);
            }
            SystemCodeErrorConstant sc=parseWeChatSign(carPayWayEnum, userAgent, preOrderVO);
            if(sc!=null){
                logger.info("newCreateOrderError order repeat ! carPayWayEnumid ={} ,jsonStr={}",carPayWayEnum.getId(),preOrderVO.getJsonStr());
                resultMap.put("qrcode","https://h5app.kuwo.cn/m/carmembercash/index.html?channel=scanQRcodesfail");
                return new MessageModel(resultMap);
            }
            logger.info("newCreateOrder carPayWayEnumID={},autoPay={},userAgent={}",carPayWayEnum.getId(),autoPay,userAgent);
            jsonObject.put("payType",carPayWayEnum.getPayType());
            if(new BigDecimal(preOrderVO.getCash()).compareTo(BigDecimal.ZERO)==0){
               if(carPayWayEnum.getPayType()==39){
                  jsonObject.put("payType",49);
               }
            }
            preOrderVO.setJsonStr(jsonObject.toJSONString());
            handleH5CallBackUrl(preOrderVO, jsonObject);
            // 4.创建订单
            MessageModel messageModel= vehicleService.order(preOrderVO);
            // 5.支付信息
            MessageModel returnMessage= makePayInfo( messageModel, carPayWayEnum, preOrderVO, resultMap,src,paySrc);
            if(returnMessage!=null){
                return returnMessage;
            }
            logger.info("newCreateOrder resultMap={}",JSONUtil.toJsonStr(resultMap));
        return new MessageModel(resultMap);
    }

    /**
     * h5支付结果页
     *
     * @param preOrderVO
     * @param jsonObject
     */
    public void handleH5CallBackUrl(PreOrderVO preOrderVO,JSONObject jsonObject){
        try{
            if(StringUtils.equals("1",preOrderVO.getCallbackUrlExt())&&StringUtils.isNotBlank(preOrderVO.getCallbackUrl())){
                String callbackUrl=preOrderVO.getCallbackUrl();
                callbackUrl=URLUtil.decode(callbackUrl);
                JSONObject products= jsonObject.getJSONObject("products");
                JSONArray array=products.getJSONArray("vip");
                String paySrc= jsonObject.getString("paySrc");
                JSONObject object= array.getJSONObject(0);
                String vipType=object.getString("type");
                String newCallBack="";
                if(StringUtils.isNotBlank(preOrderVO.getUid())&&MyNumberUtils.toLONG(preOrderVO.getUid())>0){
                    newCallBack=callbackUrl+"?vipType="+vipType+"&uid="+preOrderVO.getUid()+"&sid="+preOrderVO.getSid()+"&fromsrc="+preOrderVO.getFromsrc()+"&channel="+paySrc+"&kwflag="+preOrderVO.getKwflag()+"&pay_traceid="+preOrderVO.getPay_traceid();
                }else{
                    newCallBack=callbackUrl+"?vipType="+vipType+"&vuid="+preOrderVO.getVirtualUid()+"&vsid="+preOrderVO.getSid()+"&fromsrc="+preOrderVO.getFromsrc()+"&channel="+paySrc+"&kwflag="+preOrderVO.getKwflag()+"&pay_traceid="+preOrderVO.getPay_traceid();
                }
                preOrderVO.setCallbackUrl(newCallBack);
            }
        }catch (Exception e){
            logger.error("handleH5CallBackUrl");
        }
    }

    /**
     * 支付信息
     *
     * @param messageModel
     * @param carPayWayEnum
     * @param preOrderVO
     * @param resultMap
     * @return
     */
    public MessageModel makePayInfo(MessageModel messageModel,CarPayWayEnum carPayWayEnum,PreOrderVO preOrderVO, Map<String ,Object> resultMap,String src,String paySrc){
        try {
            logger.info("newCreateOrder orderResult={}",JSONUtil.toJsonStr(messageModel));
            Map<String,Object> result= (Map<String, Object>) messageModel.getData();
            if(!messageModel.getCode().equals(200)){
                logger.error("newCreateOrderError ErrorOrderResult={}",JSONUtil.toJsonStr(messageModel));
                if(!messageModel.getCode().equals(21002)){
                    CarMonitor.checkAndSendMsg(carMonitor.getNEW_CREATE_ORDER_ERROR(), "NEW_CREATE_ORDER_ERROR",100, "car-service NewCreateOrder 最近一天下单参数错误数：", TimeUnit.DAYS,"刘杰");
                }
                return messageModel;
            }
            long orderId= (long) result.get("orderId");
            if(preOrderVO.getFromType()!=null&&preOrderVO.getFromType().equals(VehicleConstant.BAIDU_THIRD)){
                insertThirdBussinessOrder(orderId,preOrderVO,paySrc);
            }
            carMonitor.addNewCreateOrderSuccess();
            RedisDAO.getInstance().addString(VehicleConstant.VEHICLE_ACTIVITY_NEW_ORDER+":order:"+orderId,preOrderVO.getRequestId(),60*60*10);
            RedisDAO.getInstance().addString(VehicleConstant.VEHICLE_ACTIVITY_NEW_ORDER+":BuyReqId:"+preOrderVO.getRequestId(),"2",60*60*10);
            // 支付信息
            if(carPayWayEnum.getId() == 2||carPayWayEnum.getId() == 6){
                logger.info("WECHAT:CALLBACK:CLOSE: -------------------orderId={}",orderId);
                if (StringUtils.equals(preOrderVO.getCallbackUrlExt(), "1") && StringUtils.isNotBlank(preOrderVO.getCallbackUrl())) {
                    RedisDAO.getInstance().addString("WECHAT:CALLBACK:CLOSE:" + orderId, "1", 60 * 10);
                }else{
                    RedisDAO.getInstance().addString("WECHAT:CALLBACK:CLOSE:" + orderId, "2", 60 * 10);
                }
                if (StringUtils.equals(preOrderVO.getNotClose(), "1")){
                    RedisDAO.getInstance().addString("WECHAT:CALLBACK:CLOSE:" + orderId, "3", 60 * 10);
                }
            }
            if(carPayWayEnum==CarPayWayEnum.WEICHTT||carPayWayEnum==CarPayWayEnum.NEW_WEICHTT_AUTO){
                String url="https://"+payDomain+"/wechat/getcode?state=carmusic|"+orderId;
                String res="";
                try {
                    res= HttpUtil.get(url,2000);
                }catch (Exception e){
                    logger.error("http re dopay url getcode has error!retry");
                    res= HttpUtil.get(url,2000);
                    logger.error("http re dopay url getcode has error!res={}",res,e);
                }
                res= URLUtil.decode(res);
                logger.info("newCreateOrder weichat dopay url={},result={}",url,res);
                resultMap.put("qrcode",res);
                resultMap.put("orderId",orderId);
                resultMap.put("payType",carPayWayEnum.getPayType());
                carMonitor.addWechatZFBOrder();
                return new MessageModel(resultMap);
            }
            String doPayParam=makeDoPayURLParam(carPayWayEnum, preOrderVO, orderId,src);
            String url="https://"+payDomain+"/dopay?"+doPayParam;
            if(new BigDecimal(preOrderVO.getCash()).compareTo(BigDecimal.ZERO)==0){
                url="https://"+payDomain+"/signing?"+doPayParam;
            }
            String doPayResult="";
            try{
                doPayResult= HttpUtil.get(url,2000);
            }catch (Exception e){
                logger.error("http re dopay url common has error! retry");
                doPayResult= HttpUtil.get(url,2000);
                logger.error("http re dopay url common has error! doPayResult={}",doPayResult,e);
            }
            logger.info("newCreateOrder url={} doPayParam={} doPayResult={}",url, doPayParam,doPayResult);
            JSONObject doPayObject=JSONObject.parseObject(doPayResult);
            if(doPayObject.getInteger("code").equals(200)){
                String qrCode=doPayObject.getString("qrcode");
                String shortUrl="";
                if(new BigDecimal(preOrderVO.getCash()).compareTo(BigDecimal.ZERO)==0){
                    logger.info("newCreateOrder price=0 qrcode={}",qrCode);
                    if(carPayWayEnum.getPayType()==122){
                        qrCode=qrCode.replace("\\","");
                        qrCode= URLUtil.decode(qrCode);
                    }
                     shortUrl=invokeSignShortURL(qrCode,System.currentTimeMillis()+60*60*24*10*1000);
                    logger.info("newCreateOrder qrCode={} shorturl={}",qrCode,shortUrl);
                    resultMap.put("qrcode",shortUrl);
                }else{
                    qrCode=qrCode.replace("\\","");
                    qrCode= URLUtil.decode(qrCode);
                    shortUrl=invokeCreateShortURL(qrCode,System.currentTimeMillis()+60*60*24*10*1000);
                }
                logger.info("newCreateOrder qrCode={} shorturl={}",qrCode,shortUrl);
                resultMap.put("qrcode",shortUrl);
                resultMap.put("orderId",orderId);
                resultMap.put("payType",carPayWayEnum.getPayType());
                carMonitor.addWechatZFBOrder();
            }else{
                logger.error("newCreateOrderError dopay has error!url={} doPayParam={} doPayResult={}",url, doPayParam,doPayResult);
                //CarMonitor.checkAndSendMsg(carMonitor.getNEW_CREATE_ORDER_ERROR(), "NEW_CREATE_ORDER_ERROR",20, "car-service NewCreateOrder 最近一天下单参数错误数：", TimeUnit.DAYS,"刘杰");
                return new MessageModel(SystemCodeErrorConstant.QRCODE_PAY_ERROE);
            }
        }catch (Exception e){
            logger.error("newCreateOrderError createOrder has error! param={}", JSONUtil.toJsonStr(preOrderVO),e);
            //CarMonitor.checkAndSendMsg(carMonitor.getNEW_CREATE_ORDER_ERROR(), "NEW_CREATE_ORDER_ERROR",50, "car-service NewCreateOrder 最近一天下单参数错误数：", TimeUnit.DAYS,"刘杰");
            return new MessageModel(SystemCodeErrorConstant.PRE_ORDER_ERROE);
        }

        return null;
    }

    /**
     * 三方订单
     *
     * @param orderId
     * @param preOrderVO
     * @param paySrc
     */
    public void insertThirdBussinessOrder(long orderId,PreOrderVO preOrderVO,String paySrc){
        try{
            VehicleBusinessOrder vehicleBusinessOrder = new VehicleBusinessOrder();
            vehicleBusinessOrder.setOrderId(orderId);
            vehicleBusinessOrder.setChannel(paySrc);
            vehicleBusinessOrder.setBusinessId(preOrderVO.getRequestId());
            vehicleBusinessOrder.setCreateTime(new Date());
            vehicleBusinessOrder.setAmount(Double.parseDouble(preOrderVO.getCash()));
            logger.info("insertThirdBussinessOrder bussiness={}",JSONUtil.toJsonStr(vehicleBusinessOrder));
            vehicleBusinessOrderMapper.insert(vehicleBusinessOrder);
        }catch (Exception e){
            carMonitor.THIRD_ORDER_ERROR.increment();
            logger.error("insertThirdBussinessOrder has  error!",e);
        }
    }

    /**
     * 微信连续包月签约档位有效验证
     *
     * @param carPayWayEnum
     * @param userAgent
     * @param preOrderVO
     */
        public SystemCodeErrorConstant parseWeChatSign(CarPayWayEnum carPayWayEnum,String userAgent,PreOrderVO preOrderVO) {
          try{
                if(carPayWayEnum==CarPayWayEnum.WEICHTT_AUTO){
                    int index=userAgent.indexOf("mmwebsdk");
                    if(index==-1){
                        index=userAgent.indexOf("mobile");
                    }
                    String webId=userAgent.substring(index,userAgent.indexOf("micromessenger"));
                    String pid=(StringUtils.isBlank(preOrderVO.getUid())||preOrderVO.getUid().equals("0"))?preOrderVO.getVirtualUid():preOrderVO.getUid();
                    String userKey=VehicleConstant.USER_WECHAT_WEBID+":pid:"+pid;
                    logger.info("newCreateOrder user scan qrcode pid={},webId={}，userKey={}，userAgent={}",pid,webId,userKey,userAgent);
                    if(RedisDAO.exists(userKey)){
                        String redisWebId= RedisDAO.getInstance().getString(userKey);
                        if(!redisWebId.equals(webId)){
                            logger.error("newCreateOrderError user scan qrcode repeat!pid={},webId={}，userKey={}，userAgent={}",pid,webId,userKey,userAgent);
                            //CarMonitor.checkAndSendMsg(carMonitor.getNEW_CREATE_QRCODE_REPEAT(), "NEW_CREATE_QRCODE_REPEAT",50, "car-service NewCreateOrder 最近一天重复扫码数：", TimeUnit.DAYS,"刘杰");
                           return SystemCodeErrorConstant.QRCODE_REPEAT_ERROE;
                        }
                    }else{
                        RedisDAO.getInstance().addString(userKey,webId,60*2);
                    }
                }
            }catch (Exception e){
              logger.error("newCreateOrderError user scan qrcode error !uid={},vid={}，userAgent={}",preOrderVO.getUid(),preOrderVO.getVirtualUid(),userAgent,e);
          }
          return null;
        }

    /**
     * dopay接口参数组装
     *
     * @param carPayWayEnum
     * @param preOrderVO
     * @param orderId
     * @return
     */
    public String makeDoPayURLParam(CarPayWayEnum carPayWayEnum,PreOrderVO preOrderVO,long orderId,String src){
        String doPayParam="";
        try{
            TreeMap<String,Object> treeMap = new TreeMap<>();
            treeMap.put("service","carmusic");
            treeMap.put("payType",carPayWayEnum.getPayType());
            treeMap.put("platform","car");
            treeMap.put("cash",preOrderVO.getCash());
            treeMap.put("customerid",orderId);
            treeMap.put("userName",preOrderVO.getUserName());
            treeMap.put("isRedirect",preOrderVO.getIsRedirect());
            treeMap.put("userId",StringUtils.isBlank(preOrderVO.getUid())||StringUtils.equals(preOrderVO.getUid(),"0")?0:preOrderVO.getUid());
            treeMap.put("virtualUid",StringUtils.isBlank(preOrderVO.getVirtualUid())?0:preOrderVO.getVirtualUid());
            treeMap.put("commandid",preOrderVO.getCommandid());
            treeMap.put("invokeSrc", "7399");
            treeMap.put("psrc", "qr");
            treeMap.put("originalCode", "true");
            treeMap.put("tradeType", "JSAPI");
            if(StringUtils.equals(preOrderVO.getCallbackUrlExt(),"1")&&StringUtils.isNotBlank(preOrderVO.getCallbackUrl())){
                treeMap.put("returnUrl", preOrderVO.getCallbackUrl());
            }
            if(new BigDecimal(preOrderVO.getCash()).compareTo(BigDecimal.ZERO)==0){
                treeMap.put("type", "nopass");
                if(carPayWayEnum.getPayType()==39){
                    treeMap.put("payType",49);
                }
            }
            logger.info ("newCreateOrder makeDoPayURLParamproduct={}",JSONUtil.toJsonStr(preOrderVO));
            if(preOrderVO.getProduct()!=null){
                VehicleProduct product= preOrderVO.getProduct();
                if(product.getProductTypeId()==17){
                    treeMap.put("wxTflag", "vehicle_"+product.getCnt());
                }
                if(product.getProductTypeId()==17&&new BigDecimal(preOrderVO.getCash()).compareTo(BigDecimal.ZERO)==0){
                    treeMap.put("wxTflag", "vehicle_1");
                }
                if(product.getProductTypeId()==34){
                    treeMap.put("wxTflag", "svip_"+product.getCnt());
                    if(StringUtils.isBlank(preOrderVO.getFilterId())&&product.getCnt()==1){
                        if(StringUtils.equals(src,"car_newsvip_calculateMonth10_22")||StringUtils.equals(src,"car_resvip_calculateMonth20_22")){
                            treeMap.put("wxTflag", "svip_1_22");
                        }
                    }
                    if(StringUtils.isNotBlank(preOrderVO.getFilterId())&&product.getCnt()==1){
                       String wxTflag= filterService.getWxTflag(preOrderVO.getFilterId());
                        logger.info ("newCreateOrder filterId={} wxTflag={}",preOrderVO.getFilterId(),wxTflag);
                       if(StringUtils.isNotBlank(wxTflag)){
                          treeMap.put("wxTflag", wxTflag);
                      }
                   }
                }
            }
            doPayParam= PreOrderVO.packageData(treeMap,false);
        }catch (Exception e){
            logger.error("newCreateOrderError makeDoPayURLParam has error! preOrderVO={}",JSONUtil.toJsonStr(preOrderVO),e);
            CarMonitor.checkAndSendMsg(carMonitor.getNEW_CREATE_ORDER_ERROR(), "NEW_CREATE_ORDER_ERROR",100, "car-service NewCreateOrder 最近一天下单参数错误数：", TimeUnit.DAYS,"刘杰");
        }
        return doPayParam;
    }

    /**
     * 订单二维码
     *
     * @param preOrderVO 参数
     * @return
     */
    public MessageModel createQrCode(PreOrderVO preOrderVO){
        logger.info("createQrCode ----------- + input param={}", JSONUtil.toJsonStr(preOrderVO));
        if(virtualUserLoginUtil.isQRBase64Uid(preOrderVO.getUid(),preOrderVO.getType())||virtualUserLoginUtil.isQRBase64Vir(preOrderVO.getVirtualUid(),preOrderVO.getType())){
            logger.info("createQrCode vipCenter qr code");
            return qrCodeService.getVIPCenterQRCode(preOrderVO);
        }
        StopWatch stopWatch = new StopWatch("createQrCode");
        String uuid=IdUtil.simpleUUID();
        stopWatch.start("NEW_CREATE_ORCODE");
        carMonitor.addNewCreateQrCode();
        stopWatch.stop();
        String qrCode="";
        try {

            stopWatch.start("urlEncode");
            preOrderVO.setRequestId(uuid);
            if(preOrderVO.getFromType()!=null&&preOrderVO.getFromType().equals(VehicleConstant.BAIDU_THIRD)){
                preOrderVO.setRequestId(preOrderVO.getBusinessId());
            }
            String encodeUrl=preOrderVO.urlEncode();
            stopWatch.stop();
            String url=orderDomain+"?"+encodeUrl;
            stopWatch.start("invokeCreateShortUR");
            String shortUrl=invokeCreateShortURL(url,System.currentTimeMillis()+60*60*24*10*1000);
            stopWatch.stop();
            stopWatch.start("getQRCodeImagePath");
            qrCode=mkCosQrCode(preOrderVO.getDeviceId(), shortUrl);
            stopWatch.stop();
            logger.info("createQrCode url={},shortUrl={}，qrCode={}",url,shortUrl,qrCode);
            if(StringUtils.isBlank(qrCode)){
                logger.error("createQrCode is empty ! preOrderVO={}",JSONUtil.toJsonStr(preOrderVO));
                CarMonitor.checkAndSendMsg(carMonitor.getNEW_CREATE_ORCODE_ERROR(), "NEW_CREATE_ORCODE_ERROR",3, "car-service 二维码 最近1分钟错误数：", TimeUnit.MINUTES,"刘杰");
            }
        }catch (Exception e){
            logger.error("createQrCodeError has error! preOrderVO={}",JSONUtil.toJsonStr(preOrderVO),e);
            CarMonitor.checkAndSendMsg(carMonitor.getNEW_CREATE_ORCODE_ERROR(), "NEW_CREATE_ORCODE_ERROR",3, "car-service 二维码 最近1分钟错误数：", TimeUnit.MINUTES,"刘杰");
            return new MessageModel(SystemCodeErrorConstant.QRCODE_ERROE);
        }
        logger.info("createQrCode time ={}",stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
        Map<String,String> parmMap=new HashMap<>();
        parmMap.put("requestId", uuid);
        parmMap.put("qrCode", qrCode);
        parmMap.put("type", "0");
        return new MessageModel(parmMap);
    }

    public String mkCosQrCode(String deviceId, String qrCodeContent){
        String qrCode="";
        try{
            logger.info("mkCosQrCode deviceId={} qrCodeContent={}", deviceId,qrCodeContent);
            if(StringUtils.isNotBlank(deviceId)&&deviceId.contains("\\u0000\\u0000\\u0000")){
                deviceId="deviceid";
            }
            deviceId=StringUtils.isBlank(deviceId)?"":deviceId;
            Date now = new Date();
            SimpleDateFormat ymd = new SimpleDateFormat("yyyyMMdd");
            String curTimeYMDStr = ymd.format(now);
            String finalFolderName = "star/pay/newcarservice/qrcode" + File.separator + curTimeYMDStr ;
            SimpleDateFormat ymdhms = new SimpleDateFormat("yyyyMMddHHmmssSSS");
            String curTimeYMDHMSStr = ymdhms.format(now);
            String imageName = deviceId + curTimeYMDHMSStr + ".png";
            finalFolderName = finalFolderName + File.separator + imageName;
            QrConfig qrConfig = new QrConfig(301, 301);
            qrConfig.setErrorCorrection(ErrorCorrectionLevel.H);
            qrCode = CosUtil.makeAndUploadQrCode(qrConfig, CosConfig.payConfig(),qrCodeContent, finalFolderName, "png");
        }catch (Exception e){
            logger.error("mkCosQrCode error qrcode url {}", qrCode);
        }
        if(StringUtils.isBlank(qrCode)){
            qrCode=getQRCodeImagePath(deviceId, qrCodeContent);
            CarMonitor.checkAndSendMsg(carMonitor.getNEW_CREATE_ORCODE_ERROR(), "NEW_CREATE_ORCODE_ERROR",3, "car-service cos二维码 最近1分钟错误数：", TimeUnit.MINUTES,"刘杰");
        }
        logger.info("mkCosQrCode qrcode url {}", qrCode);
        return qrCode;
    }

    /**
     * 生成二维码
     *
     * @param deviceId
     * @param qrCodeContent
     * @return
     */
    protected String getQRCodeImagePath(String deviceId, String qrCodeContent) {
        if(StringUtils.isNotBlank(deviceId)&&deviceId.contains("\\u0000\\u0000\\u0000")){
            deviceId="deviceid";
        }
        Date now = new Date();
        SimpleDateFormat ymd = new SimpleDateFormat("yyyyMMdd");
        String curTimeYMDStr = ymd.format(now);
        String finalFolderName = qrcodeImagePath  + "newcarservice/qrcode" + File.separator + curTimeYMDStr ;
        String qrCodeImagePath="";
        File finalFolder = new File(finalFolderName);
        if (!finalFolder.exists()) {
            // 如果创建失败
            if (!finalFolder.mkdirs()) {
                return "";
            }
            try {
                Process p = Runtime.getRuntime().exec("chmod 775 -R " + finalFolder);
                p.waitFor();
            } catch (IOException | InterruptedException e) {
                logger.error("getQRCodeImagePath create  path failed !",e);
                return "";
            }
        }
        SimpleDateFormat ymdhms = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        String curTimeYMDHMSStr = ymdhms.format(now);
        String imageName = deviceId + curTimeYMDHMSStr + ".png";
        qrCodeImagePath = finalFolder.getAbsolutePath() + File.separator + imageName;
        logger.info("getQRCodeImagePath  qrCodeContent={} qrCodeImagePath={}",qrCodeContent,qrCodeImagePath);
        QrCodeUtil.generate(qrCodeContent, 301, 301, FileUtil.file(qrCodeImagePath));
//        try {
//            Process p = Runtime.getRuntime().exec("chmod 777 -R " + finalFolder.getAbsolutePath());
//            p.waitFor();
//
//        } catch (IOException | InterruptedException e) {
//            logger.error("PreOrder：getQRCodeImagePath create  path failed !",e);
//            return "";
//        }

        String imageAccessP = imageAccessPath + "newcarservice/qrcode" + File.separator + curTimeYMDStr  + File.separator + imageName;
        logger.info("imageAccessP ={}",imageAccessP);
        return imageAccessP;
    }


    /**
     * 是否支付成功
     *
     * @param requestId
     * @return
     */
    public MessageModel isSuccess(String requestId){
        String key=VehicleConstant.VEHICLE_ACTIVITY_NEW_ORDER+":reqId:"+requestId;
        if(RedisDAO.exists(key)){
            return new MessageModel(true);
        }
        return new MessageModel(false);
    }

    /**
     * 获取车载音乐时间
     *
     * @param uid
     * @param vers
     * @return
     */
    public MessageModel getVipVehicleExpire(String uid, String vers){
        if(StringUtils.isBlank(uid)||StringUtils.equals(uid,"0")||!StringUtils.equals(vers,"2BD9A720A6D04B0880082BE9E8420C34")){
            logger.error("getVipVehicleExpire uid is empty!uid={},vers={}",uid,vers);
            return new MessageModel(SystemCodeErrorConstant.LIVE_BOOKING_NOTIFYSUCCESSORDER_USERID_IS_NULL);
        }
        Map<String,Object> resultMap=new HashMap<>();
        String redisKey="carvehiclevip:vipVehicleExpire:"+uid;
        //查询车载vip信息
        List<QueryExpireRes> resList = vehicleProductMapper.getAllExpire(Long.parseLong(uid), null);
        //车载vip到期时间*
        Long vipVehicleExpire = 0L;
        if (resList.size() > 0) {
            for (QueryExpireRes queryExpireRes : resList) {
                long productType = queryExpireRes.getProductType().longValue();
                long vipExpire = queryExpireRes.getExpire().getTime();
                if (ProductEnum.VIP_VEHICLE.getId() == productType ) {
                    vipVehicleExpire = Math.max(vipExpire,vipVehicleExpire);
                }
            }
        }
        resultMap.put("vipVehicleExpire", vipVehicleExpire);
        RedisLowPriceDAO.getInstance().addString(redisKey,String.valueOf(vipVehicleExpire),60*60*24*31);
        return new MessageModel(resultMap);
    }

    public MessageModel updatePayTypeById(String orderIds){
        List<Long> ids=  Arrays.stream(orderIds.split(",")).map(x->Long.valueOf(x)).collect(Collectors.toList());
        ids.forEach(id->{
            try{
                VehicleOrder vehicleOrder= vehicleOrderMapper.getOrderById(id);
                if(vehicleOrder!=null&&vehicleOrder.getId()!=null&&vehicleOrder.getPayType()==122&&vehicleOrder.getAutoPay().equals("yes")){
                    logger.info("updatePayTypeById id={} before ={}",id,JSONUtil.toJsonStr(vehicleOrder));

                    vehicleOrderMapper.updateOrderPayTypeById(vehicleOrder.getId(),39);
                    VehicleOrder nowvehicle= vehicleOrderMapper.getOrderById(id);
                    logger.info("updatePayTypeById id={} after ={}",id,JSONUtil.toJsonStr(nowvehicle));
                }
                Thread.sleep(100);
            }catch (Exception e){
                logger.error("updatePayTypeById has error! orderId={}",id,e);
            }
        });
        return new MessageModel();
    }

    /**
     * 更新信息
     *
     * @param orderIds
     * @return
     */
    public MessageModel updateRenewalPayTypeById(String orderIds){
        try{
            List<Long> ids=  Arrays.stream(orderIds.split(",")).map(x->Long.valueOf(x)).collect(Collectors.toList());{
                ids.forEach(id->{
                    VehicleOrder vehicleOrder= vehicleOrderMapper.getOrderById(id);
                    if(vehicleOrder!=null&&vehicleOrder.getId()!=null&&vehicleOrder.getPayType()==39){
                        List<VehicleOrder> vehicleOrders= vehicleOrderMapper.queryRenewalByUserAuto(vehicleOrder.getPid());
                        for(int i=0;i<vehicleOrders.size();i++){
                            VehicleOrder x=vehicleOrders.get(i);
                            if(x.getId()>vehicleOrder.getId()&&x.getPayType()==122&&x.getAutoPay().equals("yes")&&x.getClientAct().equals("autoTask")&&x.getSrc().equals(String.valueOf(vehicleOrder.getId()))){
                                logger.info("updateRenewalPayTypeById1 id={} before ={}",x.getId(),JSONUtil.toJsonStr(x));
                                vehicleOrderMapper.updateOrderPayTypeById(x.getId(),39);
                                VehicleOrder nowvehicle= vehicleOrderMapper.getOrderById(x.getId());
                                logger.info("updateRenewalPayTypeById2 id={} after ={}",x.getId(),JSONUtil.toJsonStr(nowvehicle));
                            }
                        }
                    }
                    try {
                        Thread.sleep(200);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                });
            }
        }catch (Exception e){
            logger.error("updateRenewalPayTypeById has error! orderId={}",Long.parseLong(orderIds),e);
        }
        return new MessageModel();
    }

    /**
     * @Description: 查询车载vip自动续费
     * @Param: [uid, virtualUid]
     * @return: cn.kuwo.vip1.util.common.constants.MessageModel
     * @Author: <EMAIL>
     * @Date: 2018/9/28 15:08
     */
    public int getCarAutoPay(String uid, String virtualUid) {
        logger.info("getCarAutoPay 查询用户是否有车载vip自动续费,入参: uid:{}, virtualUid:{}", uid, virtualUid);
        try {
            if ((StringUtils.equals(uid,"0")||StringUtils.isBlank(uid))&&(StringUtils.equals(virtualUid,"0")||StringUtils.isBlank(virtualUid)||StringUtils.equals(virtualUid,"-1"))) {
                logger.info("getCarAutoPay uid or virtualUid not valid ,uid={},virtualUid={}",uid,virtualUid);
               return 1;
            }
            Long pid = StringUtils.isNotEmpty(uid) && !StringUtils.equals(uid, "0") ? Long.parseLong(uid) : Long.parseLong(virtualUid);
            if (pid == 0) {
                logger.info("getCarAutoPay pid=0");
                return 1;
            }
            String pidKey=VehicleConstant.userSignPID+pid;
            if(RedisLowPriceDAO.exists(pidKey)){
                String num=RedisLowPriceDAO.getInstance().getString(pidKey);
                logger.info("getCarAutoPay enter cache: pid:{} num={}", pid,num);
                VehicleOrder vehicleOrder = vehicleOrderMapper.getAutoPay(pid);
                logger.info("getCarAutoPay autopay 1");
                if (null == vehicleOrder) {
                    logger.info("getCarAutoPay autopay 2");
                    vehicleOrder = vehicleOrderMapper.getAutoPayByUserId(pid);
                }
                int numbak = 1;
                if (null == vehicleOrder) {
                    logger.info("getCarAutoPay autopay 3");
                    numbak = 0;
                }
                if(Integer.parseInt(num)!=numbak){
                    logger.error("getCarAutoPay autopay  cache  not valid ! pid={} cacheNum={} numbak={} vehicleOrder={}", pid,num,numbak,JSONUtil.toJsonStr(vehicleOrder));
                    return 1;
                }
                return numbak;
            }
            VehicleOrder vehicleOrder = vehicleOrderMapper.getAutoPay(pid);
            if (null == vehicleOrder) {
                vehicleOrder = vehicleOrderMapper.getAutoPayByUserId(pid);
            }
            if (null == vehicleOrder) {
                return 0;
            }
            return 1;
        } catch (Exception e) {
            logger.error("getCarAutoPay 查询用户是否有车载vip自动续费异常", e);
            return 1;
        }
    }

    /**
     * 是否支付成功
     *<p>
     *     status=0 未支付 未创建订单
     *     status=1 支付成功
     *     status=2 创建订单
     *     status=3 登录失效
     *</p>
     *
     * @param requestId
     * @return
     */
    public MessageModel isBuySuccess(String requestId,String uid,String virtualUid){
        if(StringUtils.isBlank(requestId)){
            logger.error("isSuccess requestId is empty !");
            return new MessageModel(SystemCodeErrorConstant.PARAM_CHECK_ERROR);
        }
        String pid=StringUtils.isNotBlank(uid)&&!uid.equals("0")?uid:virtualUid;
        if(MyNumberUtils.toLONG(pid)>0){
            if(RedisLowPriceDAO.exists(VehicleConstant.VEHICLE_PID_LOGIN+pid)){
                RedisLowPriceDAO.delKey(VehicleConstant.VEHICLE_PID_LOGIN+uid);
                return new MessageModel(3);
            }
        }
        String key=VehicleConstant.VEHICLE_ACTIVITY_NEW_ORDER+":BuyReqId:"+requestId;
        String status= RedisLowPriceDAO.getInstance().getString(key);
        return new MessageModel(StringUtils.isBlank(status)?0:Integer.parseInt(status));
    }

    public MessageModel isBuySuccessNew(String requestId,String uid,String virtualUid){
        if(StringUtils.isBlank(requestId)){
            logger.error("isSuccess requestId is empty !");
            return new MessageModel(SystemCodeErrorConstant.PARAM_CHECK_ERROR);
        }
        String pid=StringUtils.isNotBlank(uid)&&!uid.equals("0")?uid:virtualUid;
        if(MyNumberUtils.toLONG(pid)>0){
            if(RedisLowPriceDAO.exists(VehicleConstant.VEHICLE_PID_LOGIN+pid)){
                RedisLowPriceDAO.delKey(VehicleConstant.VEHICLE_PID_LOGIN+uid);
                return new MessageModel(3);
            }
        }
        String key=VehicleConstant.VEHICLE_ACTIVITY_NEW_ORDER+":BuyReqId:"+requestId;
        String status= RedisLowPriceDAO.getInstance().getString(key);

        JSONObject data = new JSONObject();
        data.put("status", StringUtils.isBlank(status) ? 0 : Integer.parseInt(status));
        try {
            String filterKey = VehicleConstant.VEHICLE_ACTIVITY_NEW_ORDER + ":alertJson:" + requestId;
            String alertJson = RedisLowPriceDAO.getInstance().getString(filterKey);
            if (StringUtils.isNotBlank(alertJson)) {
                JSONObject alertData = JSONObject.parseObject(alertJson);
                data.putAll(alertData);
            }
        } catch (Exception e) {
            logger.error("isBuySuccess error is {}", e.getMessage());
        }

        return new MessageModel(data);
    }


    /**
     * 查询车载svip到期时间
     *
     * @param uid
     * @param vers
     * @return
     */
    public MessageModel getAllVipVehicleExpire(String uid, String vers) {
        if(StringUtils.isBlank(uid)||StringUtils.equals(uid,"0")||!StringUtils.equals(vers,"2BD9A720A6D04B0880082BE9E8420C34")){
            logger.error("getVipVehicleExpire uid is empty!uid={},vers={}",uid,vers);
            return new MessageModel(SystemCodeErrorConstant.LIVE_BOOKING_NOTIFYSUCCESSORDER_USERID_IS_NULL);
        }
        Map<String,Object> resultMap=new HashMap<>();
        // 查询车载vip信息
        List<QueryExpireRes> resList = vehicleProductMapper.getAllExpire(Long.parseLong(uid), null);
        // 车载vip到期时间*
        Long svipVehicleExpire = 0L;
        Long vipVehicleExpire = 0L;
        if (resList.size() > 0) {
            for (QueryExpireRes queryExpireRes : resList) {
                long productType = queryExpireRes.getProductType().longValue();
                long vipExpire = queryExpireRes.getExpire().getTime();
                if (ProductEnum.SUPER_VIP.getId() == productType ) {
                    svipVehicleExpire = Math.max(vipExpire,svipVehicleExpire);
                }
                if (ProductEnum.VIP_VEHICLE.getId() == productType ) {
                    vipVehicleExpire = Math.max(vipExpire,vipVehicleExpire);
                }
            }
        }

        resultMap.put("vipVehicleExpire", vipVehicleExpire);
        resultMap.put("svipVehicleExpire", svipVehicleExpire);
        return new MessageModel(resultMap);
    }

}
