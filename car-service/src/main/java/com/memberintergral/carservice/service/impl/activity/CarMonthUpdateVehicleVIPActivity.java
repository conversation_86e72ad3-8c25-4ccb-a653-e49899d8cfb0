package com.memberintergral.carservice.service.impl.activity;


import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.memberintergral.carservice.config.enums.CarActivitySuperEnum;
import com.memberintergral.carservice.config.enums.ProductEnum;
import com.memberintergral.carservice.config.enums.VipUpdatePriceEnum;
import com.memberintergral.carservice.config.exception.VehicleCouponException;
import com.memberintergral.carservice.domain.BO.UserInfoBO;
import com.memberintergral.carservice.domain.DTO.PayInfoDTO;
import com.memberintergral.carservice.domain.entity.Filter;
import com.memberintergral.carservice.domain.entity.Product;
import com.memberintergral.carservice.domain.entity.VehicleOrder;
import com.memberintergral.carservice.domain.entity.VehicleOrderExtend;
import com.memberintergral.carservice.mapper.VehicleOrderExtendMapper;
import com.memberintergral.carservice.service.FilterService;
import com.memberintergral.carservice.service.impl.ActivityManager;
import com.memberintergral.carservice.service.impl.ActivityParams;
import com.memberintergral.carservice.service.impl.CarActivityAbs;
import com.memberintergral.carservice.service.impl.VehicleService;
import com.memberintergral.carservice.util.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: VipNew
 * @description: 车载业务购买多个月的活动
 * @author: <EMAIL>
 * @create: 2018-09-19 15:38
 **/
@Component
public class CarMonthUpdateVehicleVIPActivity extends CarActivityAbs {
    private static final Logger logger = LoggerFactory.getLogger(CarMonthUpdateVehicleVIPActivity.class);

    @Autowired
    private VehicleService vehicleService;

    @Autowired
    private VehicleOrderExtendMapper vehicleOrderExtendMapper;

    @Autowired
    private FilterService filterService;

    @Override
    public double getfinalPrice(ActivityParams params) {
        Integer id = params.getProduct().getUpGradeId();
        String filterId = params.getFilterId();
        // apk校验
        if (id == null || params.getUid() == null || params.getUid() == 0 ) {
            logger.info("CarMonthUpdateVehicleVIPActivity：getfinalPrice functionparams.getProduct().getStockId() empty value ,params={}", JSONObject.toJSON(params));
            return -1;
        }
        if (params.getProduct().getProductTypeId() != 17 || id != VipUpdatePriceEnum.VIPM_VVIP.getId()) {
            return -1;
        }

        if (StringUtils.isBlank(filterId) || "null" .equals(filterId)) {
            VipUpdatePriceEnum instance = VipUpdatePriceEnum.getVipInstance(id);
            if (params.getProduct().getProductTypeId() != 17 || instance != VipUpdatePriceEnum.VIPM_VVIP) {
                return -1;
            }
            UserInfoBO userInfoBO = vehicleService.getUserVIPInfo(params.getUid());
            Date data = new Date();
            if (!DateUtils.checkOther(userInfoBO.getVipmexpire(), data) || userInfoBO.getVipmAutoPayUser() == 1 || new Date(userInfoBO.getVipVehicleExpire()).after(data)) {
                logger.info("CarMonthUpdateVehicleVIPActivity：getfinalPrice vipmexpire <31 days,params={}", JSONObject.toJSON(params));
                return -1;
            }
            return instance.getPrice();
        }

        Filter filter = filterService.getById(filterId);
        if (filter == null) {
            logger.error("CarMonthUpdateVehicleVIPActivity error filter");
            return -1;
        }
        UserInfoBO userInfoBO= vehicleService.getUserVIPInfo(params.getUid());
        Date data=new Date();
        if (!DateUtils.checkOther(userInfoBO.getVipmexpire(), data) || userInfoBO.getVipmAutoPayUser() == 1 || new Date(userInfoBO.getVipVehicleExpire()).after(data)) {
            logger.info("CarMonthUpdateVehicleVIPActivity：getfinalPrice vipmexpire <31 days,params={}", JSONObject.toJSON(params));
            return -1;
        }

        try {
            if (StringUtils.isNotBlank(filter.getExtend()) && JSONUtil.isTypeJSON(filter.getExtend())) {
                String extend = filter.getExtend().replace("\\", "");
                JSONObject js = JSONObject.parseObject(extend);
                if (js.isEmpty()) {
                    logger.error("checkUpgradePrice js is null");
                    return -1;
                }

                // 音乐包
                String vipmToVehicleVipMonthPrice = js.getString("vipmToVehicleVipMonthPrice");
                String vipmToVehicleVipDayPrice = js.getString("vipmToVehicleVipDayPrice");
                if (StringUtils.isNotBlank(vipmToVehicleVipMonthPrice) || StringUtils.isNotBlank(vipmToVehicleVipDayPrice)) {
                    if (StringUtils.isNotBlank(vipmToVehicleVipMonthPrice)) {
                        BigDecimal upPrice = new BigDecimal(vipmToVehicleVipMonthPrice).setScale(2, RoundingMode.DOWN).stripTrailingZeros();
                        return upPrice.doubleValue();
                    }
                }
            }
        } catch (Exception e) {
            logger.error("checkUpgradePrice has error", e);
        }

        // 兜底
        VipUpdatePriceEnum instance = VipUpdatePriceEnum.getVipInstance(id);
        return instance.getPrice();
    }

    @Override
    public boolean validateInPeriod(ActivityParams params) {
        return true;
    }

    @Override
    public CarActivitySuperEnum getActivityCode() {
        return CarActivitySuperEnum.ACTIVITY_UPDATE_VEHICLE_VIP;
    }

    @Override
    public boolean isPhysicTimeExpire() {
        return false;
    }

    /**
     * 获取升级id
     *
     * @param order
     * @return
     */
    public int getUpgradeId(VehicleOrder order) {
        VehicleOrderExtend vehicleOrderExtend = vehicleOrderExtendMapper.getVehicleOrderExtendByOid(order.getId());
        if (vehicleOrderExtend == null || StringUtils.isBlank(vehicleOrderExtend.getExt1())) {
            return -1;
        } else {
            // 缓存失效情况 查询数据库是否有优惠券
            String centent = vehicleOrderExtend.getExt1();
            JSONObject json = JSONObject.parseObject(centent);
            if (json.containsKey("upGradeId")) {
                return json.getInteger("upGradeId");
            }
        }
        return -1;
    }

    /**
     *会员升级id
     *
     * @param vehicleOrderExtend
     * @return
     */
    public boolean makeOrderExtendCoupon(VehicleOrderExtend vehicleOrderExtend, String couponUniqueId, Long orderId, PayInfoDTO payInfo) throws VehicleCouponException {
        List<Product> products=payInfo.getProducts();
        if(!CollectionUtils.isEmpty(products)&&products.get(0).getUpGradeId()!=null) {
            Integer upGradeId= products.get(0).getUpGradeId();
            Map<String,Integer> map=new HashMap<>();
            map.put("upGradeId",upGradeId);
            vehicleOrderExtend.setExt1(JSONObject.toJSONString(map));
        }
        return true;
    }

    public void afterPropertiesSet() throws Exception{
        ActivityManager.registerActivity(getActivityCode().getOpStr(), this);
    }

    /**
     * 校验src
     *
     * @return
     */
    public boolean validateSrc(ActivityParams activityParams){
        Product product = activityParams.getProduct();
        Long productTypeId = product.getProductTypeId();
        if (productTypeId!= ProductEnum.VIP_VEHICLE.getId()||!activityParams.getAutoPay().equals("yes")){
            return false;
        }
        return true;
    }
}
