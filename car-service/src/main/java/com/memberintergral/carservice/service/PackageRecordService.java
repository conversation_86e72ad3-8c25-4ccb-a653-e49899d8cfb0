package com.memberintergral.carservice.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.memberintergral.carservice.domain.entity.PackageRecord;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-09
 */
public interface PackageRecordService extends IService<PackageRecord> {

    PackageRecord addPackageRecord(PackageRecord packageRecordVO);

    PackageRecord getPackageRecordByKey(String key);


}
