package com.memberintergral.carservice.service.impl;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.memberintergral.carservice.domain.VO.PayDeskVO;
import com.memberintergral.carservice.domain.entity.PayDesk;
import com.memberintergral.carservice.enums.VipConfConstant;
import com.memberintergral.carservice.mapper.PayDeskMapper;
import com.memberintergral.carservice.service.PayDeskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-26
 */
@DS("vipconf")
@Service
public class PayDeskServiceImpl extends ServiceImpl<PayDeskMapper, PayDesk> implements PayDeskService {

    @Autowired
    private PayDeskMapper payDeskMapper;

    @Override
    public PayDesk addOrUpdatePayDesk(PayDeskVO payDeskVO) {
        return null;
    }

    @Override
    public void deletePayDeskById(Integer id, Boolean isTrue, Integer isDelete) {

    }

    @Override
    public PayDesk getPayDeskInfoById(Integer id) {
        return null;
    }

    @Override
    public PayDesk getPayDeskInfoBySign(String payDeskSign) {
        if(StringUtils.isBlank(payDeskSign)){
            log.error("paydesk:getPayDeskInfoBySign payDeskSign is empty!");
            return null;
        }
        LambdaQueryWrapper<PayDesk> wrapper= Wrappers.lambdaQuery();
        wrapper.eq(PayDesk::getPayDeskSign,payDeskSign);
        wrapper.eq(PayDesk::getIsDelete, VipConfConstant.ONLINE);
        PayDesk payDesk=payDeskMapper.selectOne(wrapper);
        return payDesk;
    }

    @Override
    public List<PayDesk> getAllPayDesk() {
        return null;
    }
}
