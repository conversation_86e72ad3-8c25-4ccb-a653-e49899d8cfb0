package com.memberintergral.carservice.service.impl;

import com.memberintergral.carservice.config.enums.OrderStatusEnum;
import com.memberintergral.carservice.config.exception.VehicleException;
import com.memberintergral.carservice.domain.DTO.PayInfoDTO;
import com.memberintergral.carservice.domain.entity.Product;
import com.memberintergral.carservice.domain.entity.VehicleOrder;
import com.memberintergral.carservice.domain.entity.VehicleProduct;
import com.memberintergral.carservice.mapper.VehicleOrderMapper;
import com.memberintergral.carservice.mapper.VehicleProductMapper;
import com.memberintergral.carservice.util.JsonUtil;
import com.memberintergral.carservice.util.TimeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

/**
 * @program: vip_adv
 * @description: 自动续费订单类
 * @author: <EMAIL>
 * @create: 2018-10-23 15:12
 */
@Service
public class AutoPayOrderService {
    private Logger logger = LoggerFactory.getLogger(AutoPayOrderService.class);
    @Autowired
    private VehicleOrderMapper vehicleOrderMapper;
    @Autowired
    private VehicleProductMapper vehicleProductMapper;




    /**
     * 创建自动续费订单
     *
     * @param payinfo
     * <AUTHOR> Wanyu
     */
    public Long createAutoOrder (PayInfoDTO payinfo) throws Exception{
        logger.info("创建自动续费订单，payinfo : {} ", JsonUtil.bean2Json(payinfo));
        final long uid = payinfo.getUid();
        final Timestamp currentTime = TimeUtils.getCurrentTime();

        // 记录“订单”  =~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~

        VehicleOrder orderInfo = new VehicleOrder();
        orderInfo.setUserId(uid);
        orderInfo.setPid(payinfo.getPid());
        orderInfo.setCredit(payinfo.getCredit());
        orderInfo.setTime(currentTime);
        orderInfo.setPayDate(null);
        orderInfo.setPayType(payinfo.getPayType());
        orderInfo.setStatus(OrderStatusEnum.CREATE.getStatus());
        orderInfo.setPlatform(payinfo.getPlatform());
        orderInfo.setSrc(payinfo.getSrc());
        orderInfo.setClientAct(payinfo.getClientAct());
        orderInfo.setAutoPay(payinfo.getAutoPay());
        orderInfo.setType(payinfo.getType());
        orderInfo.setPaySrc(payinfo.getPaySrc());
        orderInfo.setPlatVersion(payinfo.getPlatVersion());
        orderInfo.setProductType(payinfo.getProductType());
        vehicleOrderMapper.insert(orderInfo);


        // 处理购买“产品信息” =~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~

        // 如果这种情况不存在了，则此属性不能再用
        // 记录产品类型，用于日志统计
        String _ptypeIds_Log = "";
        // 记录产品ID，用于日志统计
        String _prodIds_Log = "";
        List<VehicleProduct> savers = new ArrayList<>();

        // FOR---
        for (Product product : payinfo.getProducts()) {

            Long productTypeId = product.getProductTypeId();
            VehicleProduct vehicleProduct = new VehicleProduct();
            vehicleProduct.setUserId(uid);
            vehicleProduct.setPrice(product.getPrice());
            vehicleProduct.setProductId(product.getId());
            vehicleProduct.setProductTypeId(product.getProductTypeId());
            vehicleProduct.setOrderId(orderInfo.getId());
            vehicleProduct.setDuration(product.getDuration());
            vehicleProduct.setCnt(product.getCnt());
            vehicleProduct.setStatus(OrderStatusEnum.CREATE.getStatus());
            vehicleProduct.setVirtualUid(payinfo.getVirtualUid());
            savers.add(vehicleProduct);

            // 记录日志
            _ptypeIds_Log += String.valueOf(productTypeId) + ",";
            _prodIds_Log += product.getId() + ",";

        }

        // 保存保存保存保存保存保存保存保存保存保存
        try {
            vehicleProductMapper.insertBatch(savers);
        } catch (Exception e) {
            logger.error("自动续费插入vehicle_product异常，",e);
            throw new VehicleException(e);
        }
        return orderInfo.getId();
    }
}
