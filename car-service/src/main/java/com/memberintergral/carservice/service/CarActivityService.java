package com.memberintergral.carservice.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.memberintergral.carservice.domain.entity.CarActivity;

import java.util.List;

/**
 * <p>
 * 车载活动表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-17
 */
public interface CarActivityService extends IService<CarActivity> {

    List<CarActivity> getCarActivities(String src, String channel,int gearType,int fromType,int autoPay);

    CarActivity getCarActivity(String src, String channel);

    CarActivity getCarActivityByCode(String code);

    boolean updateCarActivityByCode(CarActivity carActivity);

    List<CarActivity> getRemoveByCodes(List<String> codes);

    boolean updateCarStatusByCode(String code);
}
