package com.memberintergral.carservice.service.impl;

import cn.hutool.core.comparator.VersionComparator;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.com.google.protobuf.ServiceException;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.commerical.abserviceapi.req.AbRequest;
import com.commerical.abserviceapi.resp.AbResponse;
import com.commerical.abserviceapi.service.AbRuleService;
import com.memberintergral.carservice.config.constant.MessageModel;
import com.memberintergral.carservice.config.constant.PayConstant;
import com.memberintergral.carservice.config.constant.SystemCodeErrorConstant;
import com.memberintergral.carservice.config.enums.*;
import com.memberintergral.carservice.config.monitor.CarMonitor;
import com.memberintergral.carservice.config.nacos.CarConfigNacos;
import com.memberintergral.carservice.config.nacos.LowPriceChannelNacos;
import com.memberintergral.carservice.config.redis.RedisKey;
import com.memberintergral.carservice.config.redis.added.RedisLowPriceDAO;
import com.memberintergral.carservice.domain.BO.*;
import com.memberintergral.carservice.domain.VO.PriceGearVO;
import com.memberintergral.carservice.domain.entity.Filter;
import com.memberintergral.carservice.domain.entity.Gear;
import com.memberintergral.carservice.domain.entity.PayDesk;
import com.memberintergral.carservice.domain.entity.VehicleCoupon;
import com.memberintergral.carservice.enums.VipTypeEnum;
import com.memberintergral.carservice.mapper.FilterMapper;
import com.memberintergral.carservice.service.*;
import com.memberintergral.carservice.upgrade.LuxVIPUpdateInterceptor;
import com.memberintergral.carservice.upgrade.PriceInterceptor;
import com.memberintergral.carservice.upgrade.VIPMUpdateInterceptor;
import com.memberintergral.carservice.upgrade.VehicleUpdateInterceptor;
import com.memberintergral.carservice.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.net.HttpCookie;
import java.text.DecimalFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.memberintergral.carservice.config.constant.VehicleConstant.*;

/**
 * <p>
 * 策略服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-04
 */
@Service
@Slf4j
@DS("vipconf")
public class FilterServiceImpl extends ServiceImpl<FilterMapper, Filter> implements FilterService {
    private static final String TRACE_ID = "TRACE_ID";
    @Autowired
    private PayDeskService payDeskService;

    @Autowired
    private GearService gearService;

    @DubboReference
    private AbRuleService abRuleService;

    @Autowired
    private FilterMapper filterMapper;

    @Autowired
    private VehicleCouponService vehicleCouponService;

    @Autowired
    private VehicleService vehicleService;

    @Autowired
    private CarConfigNacos carConfigNacos;

    @Autowired
    private VehicleActivityService vehicleActivityService;

    @Autowired
    private LowPriceChannelNacos lowPriceChannelNacos;

    @Autowired
    private VirtualUserLoginUtil virtualUserLoginUtil;

    @Autowired
    private ChannelServiceImpl channelService;

    @Autowired
    private CacheUtils cacheUtils;

    @Autowired
    private CarMonitor carMonitor;

    /**
     * 无分成渠道
     */
    private final static List<String> channelList=Arrays.asList("apktest","B_yunzhi_h","B_yunzhi_h_h5","B_yunzhicheji","B_nuoweida_vh_test","ad_h5","yingmo","K_jiakong","jiakong","B_cheyunhulian","B_szim","B_szimcj","B_zhuoxingwei_vh","B_baihang_vh","B_feigehz_h5","B_feigehz","B_feige_vh","B_pingwang","xingyingtong","B_xingyingtong_h5","B_xingyingtong","B_tongxingzhe_vh","B_meijia_h","B_siwei","B_lianyou","B_fangxiangzi","B_sirui","B_qichengkeji","B_qichengkejicj","B_zhuoxingwei_vh_h5","B_shouqi_cj","B_yamei_cj","B_yamei","B_zhihuipaiss_h5","B_zhihuipaiss","zhihuipaiss","B_chelizi","B_chelizi_h5","B_sibichi_vh","B_sibichi_vh_h5","B_sibichi_DD","yadi","B_lege_vh","B_wuweizhilian_vh","B_moguchelian_vh_h5","B_moguchelian_vh","B_KTE_v","B_limcet","B_chelianyi","B_dezhong","B_qingcheng","B_carbit","B_qcarlink","B_chenxun2","tengshi","yliang","tongxingzhe","tongxingzhe_test","ruiliangaoke_chejing","yunzhisheng","pinwangss","chelianlian","chexingjian","yidong_fajue","yidong_R631","weishite","fangxiangtong","zhangrui","banya_WM002","banya_WM0001","maigu_199","B_shangli_vh_h5","B_shangli_vh","B_yiweitai","B_fiegehz","niouchejing","szim","B_shanghaixiaozhuochejin","chehangjian","B_zsjhaoche","Yiqibenteng_X40FL_xiaomalixing","xiaomalixing","Yiqibenteng_X60_xiaomalixing","xiaomalixing_HongqiH5","siruntianlang_Yiqijunpai066","Taima_Yiqi","yiqibenteng_T77_xiaomalixing","yiqijiefang_J6L_txzing","yiqi_J6Ppingtaixiangmu_tongxingzhe","yiqijiefang_J6P_zhonghuan_siwei","yiqi_J6pingtaixiangmu_huayangtongyong","dazhong_MQB_desay","xiaomalixing_yiqibenteng_D077","xiaomalixing_YiqibentengX40","tongxingzhe_rongweiRX5","Liantong_ShangqidatongSv61","hezheng_rongweiRX5jiachangban_tongxingzhe","guangqi_A12_maruili","guangqi_A86_maruili","guangqi_A86","guangqi_A12_weishitong","guangqi_A18_weishitong","guangqi_A18_maruili","guangqi_A12","guangqi_A60_maruili","guangqi_A55_maruili","guangqi_A20","guangqi_A20_desay","guangqi_A18Y_A13_maruili","guangqi_AM8","guangqi_A18_M_maruili","guangqi_A18M_maruili","guangqi_A3K","gaungqi_A3K","guangqi_A39","guangqi_A35","guangqi_A26","guangqi_A5_H6","A06","guangqi_A06","guangqi_Gs4","guangqisanling_chelianwan","guangben_lingpai_tongxingzhe","Trumpchi","changanshangyongche_A800","changan_C211_liantong","changan_S301JD_liantong","changanoushang_A002","changanchengyongcheC301","changan_C301ICA_liantong","changanchengyongche_S201","changanchengyongche_S201","changan_S202_MCA","changan_C301ICA_laintong","changanchengyong_S401_MCA","changanchengyongche_S401","changanchengyongche_S301","changanchengyongcheS301_JD","changanchengyongche_C211_EV","changanshangyongcheOuShang_F201","changanshangyongcheOuShang_A800","changanOushang_R111","changanshangyongcheOuShang_V302","changanshangyongcheOuShang_F201","changanshangyongcheOuShang_A800","changanoushang_A002","changankaicheng_P201_haobangshou","changan_S301_18_liantong","changan_C211MCA_shop","changan_C211MCA_rr","changan _cs85_liantong","changan_cs85_liantong_test","changan_C211MCA_r","changan_S111MCA","yuante","changanshangyongcheOuShang_R111","changan_shop_wt","changan_shop_wtS311MCA","dongfengqichen_322EV","dongfengqichen_GEN3","Qiya_KC_xiaomalixing","dongfengxiaokang_F517_liantong","dongfengxiaokang_F537JP_liantong","quliantong_Dongfengxiaokang_S513","quliantong_Dongfengxiaokang_F537","dongfengxiaokang_E70_haobangshou","dongfen_x37_botai","dongfengleinuo_HJE_hangsheng","leinuorichan","leinuorichan_shuping","jidou_dongfengyulong","Boshi_richan","dongfengxiaokang_weixingSUV_hezhijuyun","dongfengxioaknag_S560_liantong","dongfengxiaokang_F513","dongfengxiaokang_F505V","dongfengxiaokang_F527_haobangshou","Dongfengxiaokang_FG580ZG_liantong","yaxunwangluo_dongfengD320_nandouliuxing","huayang_Zhengzhourichan_P15","yika_dongfengxiaokangF507S","Dongfengxiaokang_FG580_liantong","hangsheng_shenlongqiche","xiaoma_dongfengAX4","dongfangqichen","botai_dongfengC35","yika_dongfengxiaokangF516","tianbao_T1A_qirui","tongxingzhe_M1AEV_shuping","qirui_xiaomayi_hongjing","qirui_T1E_tainbao","qirui_S61EV_hongjingdianzi","qirui_T19EVFL_hongjingdianzi","hongjing_qirui_T19","qirui_S51EV_hongjing","qirui_GS11","qirui_M1AEV_T3","qirui_HMA307A_huayang","beidouxingtong_zhzongtaiT700","beidouxingtong_ZhongtaiT600s","luchang_zhongtaiA45","Suoling_ZhongtaiSR7","Suoling_Zhongtai_Z500ev","Suoling_Zhongtai_T500","luchang_hanlongL10","Hezheng_JunmaMeet3","haiwei_zhongtaiMa501","zhongtai_hanteng_huayang","tianyouwei_ZhongtaiJta10","changchengqiche","changcheng_CHK041_desaixiwei","changcheng_B037_huayang","changcheng_B037_huayang","changchengqiche_EC01","changcheng_M6_huayang","changchengqiche_A01_haman","changcheng_pika_desaixiwei","changcheng_EC02_huayang","changcheng_V3_xiandouzhineng","beiqiyinxiang_C30_tongxingzhe","hangsheng_BeiqiB20A","hangsheng_BeiqiB20A","xiaoma_BeiqiB20B","Xiaoaikeji_Beiqiweiwang","beiqi_C53FB_botai","zhijia","beiqi_C40_pateo","Botai_BeiqixinnengyuanC51","Huayang_BeiqiC10","huayang_MK404","beixin","beiqichanghe_B20B_huayang","beiqifutian_P202_tongxingzhe","beiqifutian_P203_huayang","jili_MPC_CN6_huayang","Beiqi_futian","beiqifutian_HMA202A_huayang","beiqifutian_pingtaixm","beiqifutian_HMA6W_huangyang","huayang_M404K","beiqifutianV1_huayang","B_dongjun_v","xiaomalixing_HuachenM82","huachenxinyuan","xiaomalixing_HuachenF70","huachenxinyuan_S401","jiangling_E300_tongxingzhe","jiangling_E315_huayang","jiangling_E180_tongxingzhe","xiaomalixing_beiqiruina","jili_MPC-CN6_huayang","jili_BD2022_bdstar","jili_3C1920_bdstar","jili_BD2022_bdstar","jili_3C1920_bdstar","GKUI_yikatong","botai_jiliboyue","botai_jiliboyue","botai_boyue","woerwo_quanxi_huawei","kaiwoqiche_BE11_kuwozhixing","dongnan_DX7_huayang","dongnan_DX9_huayang","dongnan_DX3","Baowoqiche_BX","yibinkaiyi_kaiyiFX11_tongtuokeji","hengchen_chuanqiyema","mobilecarlink_A102B_yema","dayun_M171_tongxingzhe","dayunS191_tongxingzhe","Buguniao_DayunS171","Huayang_Jiangsusailin_AM139","hangsheng_yijiete_KWID","OBIGO","Jixingkeji_Vt1","Tiananzhilian_YujieLw01","Lianlukeji","sqzk_X6000_txz","huabaoshangqidelongX3000","xinte_DEV1","lingtu_BX100","hezhong_EP11_haobangshou","hualingxingma_hangzhouhongquan_hualingzhongka","shanxizhongxing_hangzhouhongquan_shanxizhongka","xugong_H20_zhonghuan","hongmeng","changma_test","xinte_aevs","Yiqidazhong_EBO_wenzhong","sanyi_328_mingshang","sanyi_8257_mingshang","sanyizhonggong_328","wushiling_siwei","haige_HQG703_hongquan","hzjianghuai_ruifengM6_txz","Adannengliang_MD11","huaqin","ruicheng_D138","faurecia_aptoide","xinjiayuan_P005","dingwei_weichaiU70D","cf_6L32_6V30","gaodesmart","sany_huaxing","jiangxi_isuzu_PT025","hangtian_CZ213A_jactruck","CMZX_qingka_JAC","zhengzhourichan_HMA206A_huayang","hynex_appstore","keluze","zhixingchanglianss","carlt_DamaiX7","changanchengyongche_C301");

    private final static Pattern pattern = Pattern.compile("\\d+(\\.\\d+)+");

    /**
     * 车载强制登录的渠道类型
     */
    private final static List<String> loginTypeList = Arrays.asList("1","4");

    @Override
    public MessageModel getPriceGearInfo(PriceGearVO priceGearVO) {
        try {
            log.info("getPriceGearInfo----------param={}",JSONUtil.toJsonStr(priceGearVO));
            if(StringUtils.isBlank(priceGearVO.getPayDeskSign())){
                priceGearVO.setPayDeskSign("carengine");
                priceGearVO.setPlatform("ar");
            }
            if(priceGearVO.getFromType()!=4&&StringUtils.equals(priceGearVO.getChannel(),"C_APK_CaoCao_h5")){
                priceGearVO.setPayDeskSign("caocaotravel");
                priceGearVO.setPlatform("ar");
            }
            long VirtualUid=0;
            long uid=0;
            if(StringUtils.isNotBlank(priceGearVO.getVirtualUid())){
                VirtualUid= Long.parseLong(priceGearVO.getVirtualUid());
            }
            if(StringUtils.isNotBlank(priceGearVO.getUid())){
                uid= Long.parseLong(priceGearVO.getUid());
            }
            if(StringUtils.isNotBlank(priceGearVO.getChannel())){
                RedisLowPriceDAO.sadd(CAR_APK_REDIS_KEY,priceGearVO.getChannel(),60*30);
                log.info("getPriceGearInfo----------channel={}",priceGearVO.getChannel());
            }
//            boolean isNeedLogin=virtualUserLoginUtil.isNeedLogin(priceGearVO.getVirtualUid(),priceGearVO.getUid(),priceGearVO.getFromType(),priceGearVO.getSource());
//            List<String> lowsChannel=lowPriceChannelNacos.getLowPriceChannels();
//            if(StringUtils.isNotBlank(priceGearVO.getChannel())&&lowsChannel!=null&&!lowsChannel.contains(priceGearVO.getChannel().toLowerCase())&&!isNeedLogin){
//                log.info("getPriceGearInfo lowsChannel size={}!",lowsChannel.size());
//                Map<Integer,Double> conMap=new HashMap<>();
//                Map<Integer,Double> notConMap=new HashMap<>();
//                Map<String,List<VehiclePriceBO>> result=new LinkedHashMap<>();
//                List<VehiclePriceBO> vehiclePriceVIPBOS=new ArrayList<>();
//                List<VehiclePriceBO> vehiclePriceSVIPBOS=new ArrayList<>();
//                if(StringUtils.isNotBlank(priceGearVO.getUid())&&(channelList.contains(priceGearVO.getChannel()))){
//                    checkUpdateSVIP(uid,vehiclePriceVIPBOS);
//                    log.info("music update：getPriceInfo userId={} ,virtualUid={}, vehiclePriceVIPBOS={}!",priceGearVO.getUid(),priceGearVO.getVirtualSid(),JSONUtil.toJsonStr(vehiclePriceVIPBOS));
//                }
//                int autoPay= vehicleActivityService.getCarAutoPay(priceGearVO.getUid(),priceGearVO.getVirtualUid());
//                if(autoPay==1){
//                    CarActivityMonthPriceEnum.getShowPrice(conMap,notConMap,vehiclePriceVIPBOS,true);
//                    CarActivitySuperVipMonthPriceEnum.getShowPrice(conMap,notConMap,vehiclePriceSVIPBOS,false);
//                }else{
//                    CarActivityAutoPayMonthPriceEnum.getShowPrice(conMap,notConMap,vehiclePriceVIPBOS,true);
//                    CarActivitySuperVipAutoPayMonthPriceEnum.getShowPrice(conMap,notConMap,vehiclePriceSVIPBOS,false);
//                    CarActivitySuperVipMonthPriceEnum.getShowPrice(conMap,notConMap,vehiclePriceSVIPBOS,false);
//                }
//                result.put("vipPrice",vehiclePriceVIPBOS);
//                result.put("svipPrice",vehiclePriceSVIPBOS);
//                log.info("getPriceGearInfo doudi channel user price !");
//                return new MessageModel(result);
//            }

            Map<Integer,VehicleCouponBO> conMap=new HashMap<>();
            Map<Integer,VehicleCouponBO> notConMap=new HashMap<>();
            Map<String,List<VehiclePriceBO>> result=new LinkedHashMap<>();
            List<VehiclePriceBO> vehiclePriceVIPBOS=new ArrayList<>();
            List<VehiclePriceBO> vehiclePriceSVIPBOS=new ArrayList<>();
            if(StringUtils.isBlank(priceGearVO.getPayDeskSign())||StringUtils.isBlank(priceGearVO.getPlatform())){
                log.error("getPriceGearInfo param has error ,priceGearVO={}",JSONUtil.toJsonStr(priceGearVO));
                return new MessageModel(SystemCodeErrorConstant.CAR_PARAM_ERROR);
            }
            result.put("vipPrice",vehiclePriceVIPBOS);
            result.put("svipPrice",vehiclePriceSVIPBOS);
            if(carConfigNacos.getCarConfigBO()!=null&&carConfigNacos.getCarConfigBO().isCouponForCar==1){
                log.info("getPriceGearInfo isCouponForCar={}!",carConfigNacos.getCarConfigBO().isCouponForCar);
                if(StringUtils.isNotBlank(priceGearVO.getDeviceId())&& StringUtils.isNotBlank(priceGearVO.getChannel())){
                    vehicleCouponService.getCouponList(uid, priceGearVO.getChannel(), VirtualUid, priceGearVO.getDeviceId(), conMap,notConMap);
                }
            }
            if(StringUtils.isNotBlank(priceGearVO.getUid())&&(channelList.contains(priceGearVO.getChannel()))){
                checkUpdateSVIP(uid,vehiclePriceVIPBOS);
                log.info("music update：getPriceInfo userId={} ,virtualUid={}, vehiclePriceVIPBOS={}!",priceGearVO.getUid(),priceGearVO.getVirtualSid(),JSONUtil.toJsonStr(vehiclePriceVIPBOS));
            }
            PayDesk payDesk = payDeskService.getPayDeskInfoBySign(priceGearVO.getPayDeskSign());
            List<Gear> gears= gearService.getGearList(payDesk.getId(),priceGearVO.getPlatform(), VipTypeEnum.VEHICLE_VIP.getType(),priceGearVO);
            List<Gear> svipgears= gearService.getGearList(payDesk.getId(),priceGearVO.getPlatform(),VipTypeEnum.SUPER_VIP.getType(),priceGearVO);
            gears.addAll(svipgears);

//            List<Filter> filters=null;
//            // 引导登录购买
//            if(isNeedLogin&&(StringUtils.isBlank(priceGearVO.getUid())||priceGearVO.getUid().equals("0"))){
//                return getVirtualUidGear(priceGearVO);
//            }else{
//                ;
//            }
//            // 引导登录 顺序
//            if(isNeedLogin){
//                gears.stream().filter(gear -> needLoginGearId==gear.getId()).forEach(gear -> gear.setOrderRank(100));
//                if(!checkVehicleLogin(priceGearVO.getUid(),"carenginevip17ar3mth57-93+31")){
//                    gears.stream().filter(gear -> needGearId==gear.getId()).forEach(gear -> gear.setOrderRank(99));
//                }
//                Collections.sort(gears);
//            }else{
//                Iterator<Gear> gearIterator = gears.iterator();
//                while (gearIterator.hasNext()) {
//                    Gear gear = gearIterator.next();
//                    if (gear.getId().compareTo(needLoginGearId) == 0) {
//                        gearIterator.remove();
//                    }
//                }
//            }
            CalculatePriceBO calculatePriceBO=new CalculatePriceBO();
            calculatePriceBO(priceGearVO, calculatePriceBO);
            List<Filter> filters = invokeValidFilter(priceGearVO, gears,  calculatePriceBO);
            Collections.sort(filters);
            log.info("----------gears={}",JSONUtil.toJsonStr(gears));
            log.info("----------Filter={}",JSONUtil.toJsonStr(filters));
            Map<Integer, List<Filter>> filterMap = filters.stream().collect((Collectors.groupingBy(Filter::getGearId)));
            groupByVipTypes(gears, filterMap,priceGearVO,result, conMap,notConMap,  calculatePriceBO);
            log.info("----------result={}",JSONUtil.toJsonStr(result));
            return new MessageModel(result);
        }catch (Exception e){
            log.error("getPriceGearInfo has error ! priceGearVO={}",JSONUtil.toJsonStr(priceGearVO),e);
        }
        Map<Integer,Double> conMap=new HashMap<>();
        Map<Integer,Double> notConMap=new HashMap<>();
        Map<String,List<VehiclePriceBO>> result=new LinkedHashMap<>();
        List<VehiclePriceBO> vehiclePriceVIPBOS=new ArrayList<>();
        List<VehiclePriceBO> vehiclePriceSVIPBOS=new ArrayList<>();
        CarActivityMonthPriceEnum.getShowPrice(conMap,notConMap,vehiclePriceVIPBOS,true);
        CarActivityAutoPayMonthPriceEnum.getShowPrice(conMap,notConMap,vehiclePriceVIPBOS,true);
        CarActivitySuperVipMonthPriceEnum.getShowPrice(conMap,notConMap,vehiclePriceSVIPBOS,false);
        CarActivitySuperVipAutoPayMonthPriceEnum.getShowPrice(conMap,notConMap,vehiclePriceSVIPBOS,false);
        result.put("vipPrice",vehiclePriceVIPBOS);
        result.put("svipPrice",vehiclePriceSVIPBOS);
        return new MessageModel(result);
    }

    private void calculatePriceBO(PriceGearVO priceGearVO, CalculatePriceBO calculatePriceBO){
        try {
            // 未登录
            boolean isVirtualUidLogin = LoginUtil.isVirtualUidLogin(priceGearVO.getUid(),priceGearVO.getVirtualUid());
            // 是否符合引导登录条件
            boolean loginPopup = LoginUtil.checkGuideLoginRule(priceGearVO.getFromType(),priceGearVO.getSource(),priceGearVO.getChannel());
            calculatePriceBO.setVirtualUidLogin(isVirtualUidLogin);
            calculatePriceBO.setGuideLoginPopup(loginPopup);
        }catch (Exception e){
            log.error("calculatePriceBO has error!",e);
        }
    }

    /**
     * 获取虚拟ID档位
     *
     * @param priceGearVO
     * @return
     */
    public MessageModel getVirtualUidGear(PriceGearVO priceGearVO){
        Map<Integer,Double> conMap=new HashMap<>();
        Map<Integer,Double> notConMap=new HashMap<>();
        Map<String,List<VehiclePriceBO>> result=new LinkedHashMap<>();
        List<VehiclePriceBO> vehiclePriceVIPBOS=new ArrayList<>();
        List<VehiclePriceBO> vehiclePriceSVIPBOS=new ArrayList<>();
        VehiclePriceBO vehiclePriceBO=new VehiclePriceBO();
        try{
            Gear gear= gearService.getBaseMapper().selectById(needLoginGearId);
            Filter filter= this.baseMapper.selectById(needLoginFilterId);
            if(filter!=null&&StringUtils.isNotBlank(filter.getDoc())){
                JSONObject filterDoc=JSONObject.parseObject(filter.getDoc());
                String topTag=filterDoc.getString("topTag");
                String src=filterDoc.getString("src");
                String subTitle=filterDoc.getString("subTitle");
                String bottomTag=filterDoc.getString("bottomTag");
                DecimalFormat decimalFormat = new DecimalFormat("###################.###########");
                String formatPrice=decimalFormat.format(filter.getPrice());
                vehiclePriceBO.setCurrentPrice(Double.parseDouble(formatPrice));
                vehiclePriceBO.setRenewPrice(filter.getRenewalPrice());
                vehiclePriceBO.setFilterId(filter.getId());
                vehiclePriceBO.setDoc(filter.getDoc());
                vehiclePriceBO.setMonth(gear.getGearType());
                vehiclePriceBO.setTitle(gear.getGearName());
                vehiclePriceBO.setSubTitle(subTitle);
                vehiclePriceBO.setSelected(false);
                vehiclePriceBO.setPaySrc(src);
                vehiclePriceBO.setUpGradeId(null);
                vehiclePriceBO.setLabel(null);
                vehiclePriceBO.setStyle("wide");
                vehiclePriceBO.setBottomTag(bottomTag);
                vehiclePriceBO.setTag(topTag);
                vehiclePriceBO.setFilterId(filter.getId());
                vehiclePriceBO.setForceLogin(1);
                vehiclePriceVIPBOS.add(vehiclePriceBO);
            }
            int autoPay= vehicleActivityService.getCarAutoPay(priceGearVO.getUid(),priceGearVO.getVirtualUid());
            if(autoPay==1){
                VirUidCarActivityMonthPriceEnum.getShowPrice(conMap,notConMap,vehiclePriceVIPBOS,false);
                VirUidCarActivitySuperVipMonthPriceEnum.getShowPrice(conMap,notConMap,vehiclePriceSVIPBOS,false);
            }else{
                VirUidCarActivityAutoPayMonthPriceEnum.getShowPrice(conMap,notConMap,vehiclePriceVIPBOS,false);
                VirUidCarActivitySuperVipAutoPayMonthPriceEnum.getShowPrice(conMap,notConMap,vehiclePriceSVIPBOS,false);
                VirUidCarActivitySuperVipMonthPriceEnum.getShowPrice(conMap,notConMap,vehiclePriceSVIPBOS,false);
            }
        }catch (Exception e){
            List<VehiclePriceBO> vehiclePrices=new ArrayList<>();
            List<VehiclePriceBO> vehiclePriceSVIPs=new ArrayList<>();
            // 异常兜底
            CarActivityMonthPriceEnum.getShowPrice(conMap,notConMap,vehiclePrices,false);
            CarActivitySuperVipMonthPriceEnum.getShowPrice(conMap,notConMap,vehiclePriceSVIPs,false);
            log.error("getVirtualUidGear has error!",e);
        }
        result.put("vipPrice",vehiclePriceVIPBOS);
        result.put("svipPrice",vehiclePriceSVIPBOS);
        log.info("getVirtualUidGear 3");
        return new MessageModel(result);
    }

    /**
     * 验证价格
     *
     * @param parSrc
     * @param userId
     * @param cnt
     * @return
     */
    public double getValidPrice(String parSrc,Long userId,Long virtualUId, int cnt,Long productTypeId,boolean autoPay,String couponUniqueId,Double oldPrice,Integer fromType){
        try{
            log.info("getValidPrice pay_src={},userId={},virtualUId={},autoPay={}，oldPrice={}",parSrc,userId,virtualUId,autoPay,oldPrice);
            PriceGearVO priceGearVO=new PriceGearVO();
            priceGearVO.setUid(String.valueOf(userId));
            priceGearVO.setVirtualUid(String.valueOf(virtualUId));
            priceGearVO.setChannel(parSrc);
            priceGearVO.setPlatform("ar");
            priceGearVO.setFromType(fromType);
            String payDeskSign="carengine";
            if(StringUtils.equals(parSrc,"C_APK_CaoCao_h5")){
                payDeskSign="caocaotravel";
            }
            double apkprice= invokeAPKValidPrice(payDeskSign, priceGearVO, productTypeId, couponUniqueId,  cnt, userId, virtualUId, autoPay,oldPrice);
            log.info("getValidPrice apkprice={}",apkprice);
            return apkprice;
//            if(RedisLowPriceDAO.sismember(CAR_APK_REDIS_KEY,parSrc)){
//
//            }else if(RedisLowPriceDAO.sismember(CAR_SDK_REDIS_KEY,parSrc)){
//                payDeskSign="sdk_car_paydesk";
//                priceGearVO.setPlatform("car");
//                double sdkprice=invokeSDKAPIValidPrice(payDeskSign, priceGearVO,cnt,autoPay);
//                log.info("getValidPrice sdkprice={}",sdkprice);
//                return sdkprice;
//            }else if(RedisLowPriceDAO.sismember(CAR_API_REDIS_KEY,parSrc)){
//                payDeskSign="api_car_paydesk";
//                priceGearVO.setPlatform("car");
//                double apiprice=invokeSDKAPIValidPrice(payDeskSign, priceGearVO,cnt,autoPay);
//                log.info("getValidPrice apiprice={}",apiprice);
//                return apiprice;
//            } else{
//                double apkprice= invokeAPKValidPrice( payDeskSign, priceGearVO, productTypeId, couponUniqueId, cnt, userId, virtualUId, autoPay,oldPrice);
//                log.info("getValidPrice not had valid pay_src,apkprice={}",apkprice);
//                if(BigDecimal.valueOf(apkprice).compareTo(BigDecimal.valueOf(oldPrice))!=0){
//                    payDeskSign="sdk_car_paydesk";
//                    priceGearVO.setPlatform("car");
//                    double sdkprice=invokeSDKAPIValidPrice(payDeskSign, priceGearVO,  cnt, autoPay);
//                    log.info("getValidPrice not had valid pay_src,sdkprice={}",sdkprice);
//                    if(BigDecimal.valueOf(sdkprice).compareTo(BigDecimal.valueOf(oldPrice))!=0){
//                        payDeskSign="api_car_paydesk";
//                        priceGearVO.setPlatform("car");
//                        double apiPrice=invokeSDKAPIValidPrice(payDeskSign, priceGearVO,  cnt, autoPay);
//                        log.info("getValidPrice not had valid pay_src,apiPrice={}",sdkprice);
//                        return apiPrice;
//                    }else{
//                        return sdkprice;
//                    }
//                }else{
//                    return apkprice;
//                }
//            }
            // log.info("getValidPrice SDK priceGearVO={}",JSONUtil.toJsonStr(priceGearVO));
        }catch (Exception e){
            log.error("getValidPrice has error !parSrc={},userId={}",parSrc,userId,e);
        }
        return -1;
    }

    /**
     * 验证apk价格
     *
     * @param payDeskSign
     * @param priceGearVO
     * @param productTypeId
     * @param couponUniqueId
     * @param cnt
     * @param userId
     * @param virtualUId
     * @param autoPay
     * @return
     */
    public Double invokeAPKValidPrice(String payDeskSign,PriceGearVO priceGearVO,Long productTypeId,String couponUniqueId, int cnt,Long userId,Long virtualUId,boolean autoPay,Double oldPrice){
        priceGearVO.setPayDeskSign(payDeskSign);
        MessageModel message=getPriceGearInfo(priceGearVO);
        log.info("getValidPrice priceGearVO={},message={},productTypeId={},userId={}，cnt={}，autoPay={}",JSONUtil.toJsonStr(priceGearVO),JSONUtil.toJsonStr(message),productTypeId,userId,cnt,autoPay);
        Map<String,Object> map = ( Map<String,Object>) message.getData();
        List<VehiclePriceBO> vehiclePriceVIPBOS=null;
        if(productTypeId==17){
            vehiclePriceVIPBOS= (List<VehiclePriceBO>) map.get("vipPrice");
        }
        if(productTypeId==34){
            vehiclePriceVIPBOS= (List<VehiclePriceBO>) map.get("svipPrice");
        }
        VehicleCoupon vehicleCoupon=null;
        // 使用优惠券支付 主要验证 uid cnt
        if (StringUtils.isNotBlank(couponUniqueId)) {
            vehicleCoupon = vehicleCouponService.getVehicleCouponsBySerialKey(couponUniqueId, userId,virtualUId);

        }
        if(oldPrice.compareTo(0.0)==0){
            log.info("invokeAPKValidPrice  ------------------------ before-oldPrice={}",oldPrice);
            for (VehiclePriceBO vehiclePriceBO : vehiclePriceVIPBOS) {
                if (vehiclePriceBO.getCurrentPrice()==0) {
                    log.info("invokeAPKValidPrice  ------------------------ mind getCurrentPrice={}",vehiclePriceBO.getCurrentPrice());
                    return vehiclePriceBO.getCurrentPrice();
                }
            }
        }
        for(int i=0;i<vehiclePriceVIPBOS.size();i++){
            VehiclePriceBO vehiclePriceBO= vehiclePriceVIPBOS.get(i);
            if(vehiclePriceBO.getMonth()==cnt&&vehiclePriceBO.getAutoPay().equals(autoPay)&&vehiclePriceBO.getUpGradeId()==null&&vehiclePriceBO.getCurrentPrice()!=0){
                if (vehicleCoupon != null && vehicleCoupon.getUsed() == 0) {
                    return vehiclePriceBO.getCurrentPrice()-vehicleCoupon.getDiscountAmount();
                }
                if(oldPrice.compareTo(vehiclePriceBO.getCurrentPrice())>=0){
                    log.info("invokeAPKValidPrice price update!!");
                    return oldPrice;
                }
            }
        }
        for(int i=0;i<vehiclePriceVIPBOS.size();i++){
            VehiclePriceBO vehiclePriceBO= vehiclePriceVIPBOS.get(i);
            if(vehiclePriceBO.getMonth()==cnt&&vehiclePriceBO.getAutoPay().equals(autoPay)&&vehiclePriceBO.getUpGradeId()==null&&vehiclePriceBO.getCurrentPrice()!=0){
                if (vehicleCoupon != null && vehicleCoupon.getUsed() == 0) {
                    return vehiclePriceBO.getCurrentPrice()-vehicleCoupon.getDiscountAmount();
                }
                if(oldPrice.compareTo(vehiclePriceBO.getCurrentPrice())>0){
                    log.info("invokeAPKValidPrice has more than one hear!");
                    return oldPrice;
                }
                return vehiclePriceBO.getCurrentPrice();
            }
        }
        return -1.0;
    }

    /**
     * 验证skd价格
     *
     * @param payDeskSign
     * @param priceGearVO
     * @param cnt
     * @param autoPay
     * @return
     */
    public Double invokeSDKAPIValidPrice(String payDeskSign,PriceGearVO priceGearVO, int cnt,boolean autoPay){
        priceGearVO.setPayDeskSign(payDeskSign);
        MessageModel mes=getSDKAPIPriceInfo(priceGearVO);
        List<SDKAPIBO> list= (List<SDKAPIBO>) mes.getData();
        for(int i=0;i<list.size();i++){
            SDKAPIBO sdkapibo= list.get(i);
            if(sdkapibo.getCnt()==cnt&&sdkapibo.getAutoPay().equals(autoPay)){
                return sdkapibo.getPrice();
            }
        }
        return -1.0;
    }



    /**
     * 过滤有效filter
     *
     * @param priceGearVO 请求参数
     * @return
     * @throws ServiceException
     */
    private List<Filter> invokeValidFilter(PriceGearVO priceGearVO, List<Gear> gear, CalculatePriceBO calculatePriceBO) throws ServiceException {
        String gearIds = gear.stream().map(x -> String.valueOf(x.getId())).collect(Collectors.joining(","));
        List<Filter> filters = null;
        try {
            // 远程调用接口过滤filter
            filters = invokeFilterGears(priceGearVO, gearIds, calculatePriceBO,gear);
        } catch (Exception e) {
            log.error("paydesk:invoke filters is error ! priceGearVO={}", JSONUtil.toJsonStr(priceGearVO), e);
        }
        return filters;
    }

    /**
     * 获取过滤后的filter
     *
     * @param priceGearVO
     * @param gearId
     * @return
     */
    public List<Filter> invokeFilterGears(PriceGearVO priceGearVO, String gearId,CalculatePriceBO calculatePriceBO,List<Gear> gears) {
        AbRequest abRequest = new AbRequest();
        abRequest.setRuleType("CAR_RULE_TYPE");
        abRequest.setUserId(priceGearVO.getUid());
        abRequest.setGearId(gearId);
        abRequest.setRSource("");
        abRequest.setSource(priceGearVO.getSource());
        abRequest.setDeviceId(priceGearVO.getDeviceId());
        abRequest.setVirtualUserId(priceGearVO.getVirtualUid());
        abRequest.setFromsrc(priceGearVO.getFromsrc());
        abRequest.setChannel(priceGearVO.getChannel());
        abRequest.setFromType(priceGearVO.getFromType());
        abRequest.setPayDeskSign(priceGearVO.getPayDeskSign());
        abRequest.setPlatform(priceGearVO.getPlatform());
        String channel=priceGearVO.getChannel();
        boolean isSvipChannle= channelService.isSvipChannel(channel);
        if(isSvipChannle){
            abRequest.setVipType(VipTypeEnum.SUPER_VIP.getType());
        }
        if(calculatePriceBO!=null){
            abRequest.setGuideLoginPopup(calculatePriceBO.guideLoginPopup);
        }
//        abRequest.setUserInfoBO(new com.commerical.abserviceapi.domain.UserInfoBO());
        AbResponse eval = abRuleService.evalCar(abRequest);
        log.info("paydesk:invokeFilterGears after RPC filterIds={}", JSONUtil.toJsonStr(eval));
        List<Object> maps = eval.getHits();
        String guideLogin= eval.getGuideLogin();
        Map<String,String> guideLoginRankMap= eval.getGuideLoginRankMap();
        List<Integer> filterIds = maps.stream().map(x -> Integer.parseInt(String.valueOf(x))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterIds)) {
            return new ArrayList<Filter>();
        }
        List<Filter> filters=this.getFilters(filterIds,0);
        if((StringUtils.equals(guideLogin,"1")||StringUtils.equals(guideLogin,"2"))&&guideLoginRankMap!=null){
            guideLoginRank( gears,guideLoginRankMap,calculatePriceBO);
            calculatePriceBO.setGuideLoginRankMap(guideLoginRankMap);
        }
        return filters;
    }

    public void guideLoginRank(List<Gear> gears,Map<String,String> guideLoginRankMap ,CalculatePriceBO calculatePriceBO){
        try{
            for(Gear gear:gears){
                String rank= guideLoginRankMap.get(String.valueOf(gear.getId()));
                if(StringUtils.isNotBlank(rank)){
                    gear.setOrderRank(Integer.parseInt(rank));
                }
            }
            Collections.sort(gears);
        }catch (Exception e){
            log.error("guideLoginRank has error!",e);
        }
    }

    /**
     * 分类模式 单挡位
     *
     * @param gears 挡位
     * @param filterMap
     */
    public void groupByVipTypes(List<Gear> gears, Map<Integer, List<Filter>> filterMap,PriceGearVO priceGearVO, Map<String,List<VehiclePriceBO>> result, Map<Integer,VehicleCouponBO> conMap,Map<Integer,VehicleCouponBO> notConMap, CalculatePriceBO calculatePriceBO) {
        // 所有的档位按类型进行分类
        try {
            Map<String, List<Gear>> gearMap = gears.stream().collect(Collectors.groupingBy(Gear::getVipType, LinkedHashMap::new, Collectors.toList()));
            for (String vipType : gearMap.keySet()) {
                List<VehiclePriceBO> vipList=null;
                if(vipType.equals(VipTypeEnum.VEHICLE_VIP.getType())){
                    vipList= result.get("vipPrice");
                }
                if(vipType.equals(VipTypeEnum.SUPER_VIP.getType())){
                    vipList= result.get("svipPrice");
                }
                try {
                    List<Gear> vipTypeGears = gearMap.get(vipType);
                    List<VehiclePriceBO> finalVipList = vipList;
                    vipTypeGears.forEach(itemGear -> {
                        try {
                            VehiclePriceBO vehiclePriceBO=new VehiclePriceBO();
                            // 档位对应的策略
                            List<Filter> filters = filterMap.get(itemGear.getId());
                            if (CollectionUtils.isEmpty(filters)) {
                                return;
                            }
                            parseData(vehiclePriceBO, itemGear, filters, priceGearVO, conMap, notConMap, calculatePriceBO);
                            if(vehiclePriceBO.getCurrentPrice()==null){
                                log.info("groupByVipTypes price is not valid!");
                                return;
                            }
                            if(priceGearVO.getFromType()==3&&vehiclePriceBO.getCurrentPrice()!=null&&BigDecimal.valueOf(vehiclePriceBO.getCurrentPrice()).compareTo(BigDecimal.ZERO)==0){
                                return;
                            }
                            //听书
                            if(priceGearVO.getFromType()!=4&&priceGearVO.getFromType()!=3&&StringUtils.isNotBlank(vehiclePriceBO.getPaySrc())
                                    &&carConfigNacos.getCarConfigBO()!=null&&carConfigNacos.getCarConfigBO().getTsSrcList().contains(vehiclePriceBO.getPaySrc())){
                                return;
                            }
                            if(((priceGearVO.getFromType()==4||priceGearVO.getFromType()==3)&&carConfigNacos.getCarConfigBO()!=null&&carConfigNacos.getCarConfigBO().getTsSrcList().contains(vehiclePriceBO.getPaySrc()))||BigDecimal.valueOf(vehiclePriceBO.getCurrentPrice()).compareTo(BigDecimal.ZERO)==0){
                                if(priceGearVO.getIsVirtualUidBuy()==0&&(priceGearVO.getUid().equals("0")||StringUtils.isBlank(priceGearVO.getUid()))){
                                    log.info("groupByVipTypes price continute ! ");
                                    return;
                                }
                                vehiclePriceBO.setStyle("wide");
                            }
                            if(sendDaysSrc.contains(vehiclePriceBO.getPaySrc())||(vehiclePriceBO.getFilterId()!=null&&vehiclePriceBO.getFilterId()== needLoginFilterId)){
                                vehiclePriceBO.setStyle("wide");
                            }
                            finalVipList.add(vehiclePriceBO);
                        } catch (Exception e) {
                            log.error("SinglePriceService groupByVipTypes itemGear has error ", e);
                        }
                    });
                } catch (Exception e) {
                    log.error("SinglePriceService SinglePriceService gearMap item has error", e);
                }
            }

        } catch (Exception e) {
            log.error("SinglePriceService groupByVipTypes has error !",e);
        }
    }

    /**
     * 分类模式 单挡位
     *
     * @param gears 挡位
     * @param filterMap
     */
    public void groupBySDKAPIVipTypes(List<Gear> gears, Map<Integer, List<Filter>> filterMap,PriceGearVO priceGearVO, List<SDKAPIBO> sdkapibos) {
        // 所有的档位按类型进行分类
        try {
            Map<String, List<Gear>> gearMap = gears.stream().collect(Collectors.groupingBy(Gear::getVipType, LinkedHashMap::new, Collectors.toList()));
            for (String vipType : gearMap.keySet()) {
                try {
                    List<Gear> vipTypeGears = gearMap.get(vipType);
                    vipTypeGears.forEach(itemGear -> {
                        try {
                            SDKAPIBO sdkapibo=new SDKAPIBO();
                            // 档位对应的策略
                            List<Filter> filters = filterMap.get(itemGear.getId());
                            if (CollectionUtils.isEmpty(filters)) {
                                return;
                            }
                            parseDataSDKAPI(sdkapibo, itemGear, filters, priceGearVO);
                            sdkapibos.add(sdkapibo);
                        } catch (Exception e) {
                            log.error("SinglePriceService groupByVipTypes itemGear has error ", e);
                        }
                    });
                } catch (Exception e) {
                    log.error("SinglePriceService SinglePriceService gearMap item has error", e);
                }
            }

        } catch (Exception e) {
            log.error("SinglePriceService groupByVipTypes has error !",e);
        }
    }

    /**
     * 解析parse
     *
     * @param vehiclePriceBO
     * @param gear
     * @param filterLists
     * @param priceGearVO
     */
    public void parseData(VehiclePriceBO vehiclePriceBO, Gear gear, List<Filter> filterLists, PriceGearVO priceGearVO,Map<Integer,VehicleCouponBO> conMap,Map<Integer,VehicleCouponBO> notConMap, CalculatePriceBO calculatePriceBO) {
        try {
            DocBO docBO=new DocBO();
            parseGearDoc(gear.getDoc(),docBO);
            filterLists.forEach(filter -> {
                try {
                    if(filter.getIsActivity()!=null&&filter.getIsActivity()==1&&priceGearVO.getFromType()!=4){
                        log.info("filter 1 id ={} fromType={}",filter.getId(),priceGearVO.getFromType());
                        return;
                    }
                    parseFilter(filter, gear, vehiclePriceBO,priceGearVO,conMap, notConMap,docBO,  calculatePriceBO);
                } catch (Exception e) {
                    log.error("paydesk：filterType has error!filterType", e);
                }
            });
        } catch (Exception e) {
            log.error("paydesk：parseData has error!gear={}", JSONObject.toJSON(gear), e);
        }
    }

    public void parseGearDoc(String doc,DocBO docBO){
        try{
            if(StringUtils.isNotBlank(doc)){
                JSONObject docJS=JSONObject.parseObject(doc);
                //竖屏
                String descImgTall= docJS.getString("verticalScreenImg");
                //宽屏
                String descImgWide= docJS.getString("wideScreenImg");
                //超宽屏
                String superWideScreenImg= docJS.getString("superWideScreenImg");
                docBO.setDescImgTall(descImgTall);
                docBO.setDescImgWide(descImgWide);
                docBO.setSuperWideScreenImg(superWideScreenImg);
            }
        }catch (Exception e){
            log.error("parseDoc has error!",e);
        }
    }

    /**
     * 解析parse
     *
     * @param sdkapibo
     * @param gear
     * @param filterLists
     * @param priceGearVO
     */
    public void parseDataSDKAPI(SDKAPIBO sdkapibo, Gear gear, List<Filter> filterLists, PriceGearVO priceGearVO) {
        try {
            filterLists.forEach(filter -> {
                try {
                    parseFilterSDKAPI(filter, gear, sdkapibo,priceGearVO);
                } catch (Exception e) {
                    log.error("paydesk：filterType has error!filterType", e);
                }
            });
        } catch (Exception e) {
            log.error("paydesk：parseData has error!gear={}", JSONObject.toJSON(gear), e);
        }
    }

    /**
     * 价格解析
     * @param filter
     * @param gear
     * @param vehiclePriceBO
     * @param priceGearVO
     */
    public void parseFilter(Filter filter, Gear gear, VehiclePriceBO vehiclePriceBO, PriceGearVO priceGearVO,Map<Integer,VehicleCouponBO> conMap,Map<Integer, VehicleCouponBO> notConMap,DocBO docBO,CalculatePriceBO calculatePriceBO){
        // abData 普通
        if(vehiclePriceBO.getFilterId()!=null){
            return;
        }
        try{
            JSONObject filterDoc=JSONObject.parseObject(filter.getDoc());
            String topTag=filterDoc.getString("topTag");
            String lotteryQrCode=filterDoc.getString("lotteryQrCode");
            String src=filterDoc.getString("src");
//            if(filter.getId()==needLoginFilterId&&checkVehicleLogin(priceGearVO.getUid(),src)){
//                return;
//            }
            parseDoc(filterDoc,vehiclePriceBO,gear,docBO,calculatePriceBO,priceGearVO,filter, src);
            forceLoginByChannel(vehiclePriceBO, gear, priceGearVO);
            filterForceLogin(vehiclePriceBO, gear, priceGearVO);
            String subTitle=filterDoc.getString("subTitle");
            String bottomTag=filterDoc.getString("bottomTag");
            JSONObject gearDoc=JSONObject.parseObject(gear.getDoc());
            String oPrice=gearDoc.getString("oPrice");
            String autoPayBtnShow=gearDoc.getString("autoPayBtnShow");
            DecimalFormat decimalFormat = new DecimalFormat("###################.###########");
            String formatPrice=decimalFormat.format(filter.getPrice());
            String topLeftTag=filterDoc.getString("topLeftTag");
            String discount=filterDoc.getString("discount");
            String autoPayDesc=filterDoc.getString("autoPayDesc");
            vehiclePriceBO.setCurrentPrice(Double.parseDouble(formatPrice));
            vehiclePriceBO.setRenewPrice(filter.getRenewalPrice());
            vehiclePriceBO.setFilterId(filter.getId());
            vehiclePriceBO.setDoc(filter.getDoc());
            vehiclePriceBO.setMonth(gear.getGearType());
            vehiclePriceBO.setUnderPrice(StringUtils.isBlank(oPrice)?0:Double.parseDouble(oPrice));
            if(gear.getGearName().contains("听书")){
                String title=gearDoc.getString("title");
                vehiclePriceBO.setTitle(title);
            }else{
                vehiclePriceBO.setTitle(gear.getGearName());
            }
            vehiclePriceBO.setSubTitle(subTitle);
            vehiclePriceBO.setSelected(StringUtils.isNotBlank(autoPayBtnShow) && autoPayBtnShow.equals("1"));
            Map<String,String> map= calculatePriceBO.getGuideLoginRankMap();
            if(map!=null&&calculatePriceBO.isVirtualUidLogin&&map.containsKey(String.valueOf(gear.getId()))){
                vehiclePriceBO.setSelected(true);
            }
            vehiclePriceBO.setGearId(gear.getId());
            vehiclePriceBO.setPaySrc(src);
            vehiclePriceBO.setAutoPay(gear.getAutoPay().equals(1));
            vehiclePriceBO.setUpGradeId(null);
            vehiclePriceBO.setLabel(null);
            vehiclePriceBO.setBottomTag(bottomTag);
            if(gear.getVipType().equals(VipTypeEnum.SUPER_VIP.getType())){
                vehiclePriceBO.setWxTflg("svip_"+gear.getGearType());
            }
            if(gear.getVipType().equals(VipTypeEnum.VEHICLE_VIP.getType())){
                vehiclePriceBO.setWxTflg("vehicle_"+gear.getGearType());
            }
            if(gear.getVipType().equals(VipTypeEnum.SUPER_VIP.getType())&&gear.getGearType()==1
                    &&StringUtils.isNotBlank(gear.getExtend())
                    &&JSONUtil.isTypeJSON(gear.getExtend())){
                JSONObject gearJson= JSONObject.parseObject(gear.getExtend());
                if(gearJson.containsKey("wxTflag")&&StringUtils.isNotBlank(gearJson.getString("wxTflag"))){
                    String wxTflag=gearJson.getString("wxTflag");
                    if(StringUtils.isNotBlank(wxTflag)){
                        vehiclePriceBO.setWxTflg(wxTflag);
                    }
                }
            }
            if(notConMap.get(gear.getGearType())!=null&&gear.getAutoPay()==0&&(isUserCoupon(formatPrice))) {
                vehiclePriceBO.setCouponUniqueId(notConMap.get(gear.getGearType()).getCouponUniqueId());
                vehiclePriceBO.setDiscountAmount(notConMap.get(gear.getGearType()).getDiscountAmount()!=null?new BigDecimal(filter.getPrice()-notConMap.get(gear.getGearType()).getDiscountAmount()).setScale(3,BigDecimal.ROUND_HALF_UP) .doubleValue():null);
            }
            if(conMap.get(gear.getGearType())!=null&&gear.getAutoPay()==1&&(isUserCoupon(formatPrice))){
                vehiclePriceBO.setCouponUniqueId(conMap.get(gear.getGearType()).getCouponUniqueId());
                vehiclePriceBO.setDiscountAmount(conMap.get(gear.getGearType()).getDiscountAmount()!=null?new BigDecimal(filter.getPrice()-conMap.get(gear.getGearType()).getDiscountAmount()).setScale(3,BigDecimal.ROUND_HALF_UP) .doubleValue():null);
            }
            if(ObjectUtils.isNotNull(filter.getEndTime())){
                long nowTime= System.currentTimeMillis();
                long endTime=filter.getEndTime().getTime();
                if(endTime-nowTime>0){
                    // 剩余时间
                    vehiclePriceBO.setLastTime(endTime-nowTime);
                }
            }
            vehiclePriceBO.setTag(topTag);
            vehiclePriceBO.setLabel(autoPayBtnShow);
            vehiclePriceBO.setFilterId(filter.getId());
            vehiclePriceBO.setShowQrcode(lotteryQrCode);
            vehiclePriceBO.setDiscount(discount);
            vehiclePriceBO.setTopLeftTag(topLeftTag);
            vehiclePriceBO.setAutoPayDesc(autoPayDesc);
        }catch (Exception e){
            log.error("parseFilter has  error !,filter={}",JSONUtil.toJsonStr(filter),e);
        }
    }

    /**
     * 强制购买超会登录
     * @param vehiclePriceBO 返回的bo
     * @param gear 档位信息
     * @param priceGearVO  参数
     */
    private void filterForceLogin(VehiclePriceBO vehiclePriceBO, Gear gear, PriceGearVO priceGearVO) {
        try{
            String uid = priceGearVO.getUid();
            String source = priceGearVO.getSource();
            if (StringUtils.isBlank(source)) {
                return;
            }
            if (Convert.toInt(uid, 0) > 0) {
                return;
            }
            if (!Objects.equals(VipTypeEnum.SUPER_VIP.getType(), gear.getVipType())) {
                return;
            }
            if (StringUtils.equals(VipTypeEnum.SUPER_VIP.getType(),gear.getVipType())&&
                    CollectionUtils.isNotEmpty(carConfigNacos.getCarConfigBO().getForceLoginChannel())
                    &&!carConfigNacos.getCarConfigBO().getForceLoginChannel().contains(priceGearVO.getChannel())) {
                vehiclePriceBO.setForceLogin(1);
                return;
            }
            Matcher matcher = pattern.matcher(source);
            if (!matcher.find()) {
                return;
            }
            String verStr = matcher.group();
            Integer version = Convert.toInt(Objects.isNull(verStr) ? null : verStr.replaceAll("\\.", ""), 0);
            if (version > PayConstant.SHIELD_VERSION_THRESHOLD||verStr.startsWith("6")) {
                vehiclePriceBO.setForceLogin(1);
                return;
            }
            int indexOf = source.indexOf(verStr);
            int lastIndexOf = source.lastIndexOf(".");
            if (PayConstant.SHIELD_SUPER_VIP_CHANNELS_2_FORCE_LOGIN.contains(source.substring(indexOf + verStr.length() + 1, lastIndexOf))) {
                vehiclePriceBO.setForceLogin(1);
            }
        }catch (Exception e){
            carMonitor.SVIP_FORCE_LOGIN_ERROR.increment();
            log.error("filterForceLogin has error!",e);
        }
    }


    private void forceLoginByChannel(VehiclePriceBO vehiclePriceBO, Gear gear, PriceGearVO priceGearVO) {

        try {
            String uid = priceGearVO.getUid();
            String source = priceGearVO.getSource();
            //log.info("forceLoginByChannel=> uid:{} source:{}, vipType:{} judge super vip no login shield", uid, source, gear.getVipType());
            if (StringUtils.isBlank(source)) {
                //log.info("forceLoginByChannel=> source is blank");
                return;
            }
            if (Convert.toInt(uid, 0) > 0) {
                //log.info("forceLoginByChannel=> user is login");
                return;
            }
            Matcher matcher = pattern.matcher(source);
            if (!matcher.find()) {
                //log.info("forceLoginByChannel=> not regex ");
                return;
            }
            String verStr = matcher.group();
            //log.info("forceLoginByChannel=> version===>{}",verStr);
            String cacheData=RedisLowPriceDAO.getInstance().getString(RedisKey.CAR_STRONG_LOGIN_CHANNEL_LIST);
            JSONArray channelList=null;
            if (JSONUtil.isTypeJSON(cacheData)){
                channelList=JSON.parseArray(cacheData);
            }
            //log.info("forceLoginByChannel => channelList list:{}",JSON.toJSON(channelList));
            if (channelList!=null && StringUtils.isNotBlank(verStr) ){
                for (int i=0,size=channelList.size();i<size;i++){

                    JSONObject item=channelList.getJSONObject(i);
                    if ( StringUtils.equals("apk",item.getString("appType"))
                            &&(StringUtils.isBlank(item.getString("minVersion")) || (VersionComparator.INSTANCE.compare(verStr, item.getString("minVersion")) >= 0))
                            &&(StringUtils.isBlank(item.getString("maxVersion")) || (VersionComparator.INSTANCE.compare(verStr, item.getString("maxVersion")) <= 0))
                            && loginTypeList.contains(item.getString("loginType"))
                            && str2List(item.getString("channel")).contains(priceGearVO.getChannel())){
                            //log.info("forceLoginByChannel=> need login channel");
                            vehiclePriceBO.setForceLogin(1);
                        break;
                    }

                }
            }
        }catch (Exception e){
            log.error("获取登录渠道失败",e);
        }

    }




    public static List<String> str2List(String channel){
        if (StringUtils.isBlank(channel)){
            return Collections.emptyList();
        }
        return Arrays.stream(StringUtils.split(channel, ",")).collect(Collectors.toList());

    }


    /**
     * 解析策略doc
     *
     * @param filterDoc
     * @param vehiclePriceBO
     * @param docBO
     */
    public void parseDoc(JSONObject filterDoc , VehiclePriceBO vehiclePriceBO, Gear gear,DocBO docBO,CalculatePriceBO calculatePriceBO, PriceGearVO priceGearVO,Filter filter,String src){
        try {
            //竖屏
            String descImgTall = filterDoc.getString("verticalScreenImg");
            //宽屏
            String descImgWide = filterDoc.getString("wideScreenImg");
            //超宽屏
            String superWideScreenImg = filterDoc.getString("superWideScreenImg");
            //是否活动抽奖弹窗
            String showActivityPopup = filterDoc.getString("openLotteryPopup");
            //是否活动挡位
            String isActivity = gear.getIsActivity();
            //是否未登录购买
            String forceLogin = filterDoc.getString("forceLogin");
            //登录弹窗
            String loginPopup = filterDoc.getString("loginPopup");
            //二维码链接
            String qrcodeLink=filterDoc.getString("qrcodeLink");
            //支付成功弹窗图片
            String payFinishedPopupImg=filterDoc.getString("payFinishedPopupImg");
            //二维码未登录遮罩文案
            String qrcodeUnloginText=filterDoc.getString("qrcodeUnloginText");
            //支付成功弹窗标题
            String payFinishedPopupTitle=filterDoc.getString("payFinishedPopupTitle");
            //支付成功弹窗描述
            String payFinishedPopupDesc=filterDoc.getString("payFinishedPopupDesc");
            //支付成功弹窗提示
            String payFinishedPopupTips=filterDoc.getString("payFinishedPopupTips");
            vehiclePriceBO.setDescImgTall(StringUtils.isBlank(descImgTall) ? docBO.getDescImgTall() : descImgTall);
            vehiclePriceBO.setDescImgWide(StringUtils.isBlank(descImgWide) ? docBO.getDescImgWide() : descImgWide);
            vehiclePriceBO.setDescImgLong(StringUtils.isBlank(superWideScreenImg) ? docBO.getSuperWideScreenImg() : superWideScreenImg);
            vehiclePriceBO.setShowActivityPopup(showActivityPopup);
            vehiclePriceBO.setIsActivity(isActivity);
            vehiclePriceBO.setQrcodeLink(qrcodeLink);
            vehiclePriceBO.setPayFinishedPopupImg(payFinishedPopupImg);
            vehiclePriceBO.setQrcodeUnloginText(qrcodeUnloginText);
            vehiclePriceBO.setPayFinishedPopupTitle(payFinishedPopupTitle);
            vehiclePriceBO.setPayFinishedPopupDesc(payFinishedPopupDesc);
            vehiclePriceBO.setPayFinishedPopupTips(payFinishedPopupTips);
            virtualUidLogin(calculatePriceBO, forceLogin , vehiclePriceBO, priceGearVO, filter,src,loginPopup);

        }catch (Exception e){
            log.error("parseDoc doc has error!",e);
        }
    }

    /**
     * 未登录引导登录
     *
     * @param calculatePriceBO
     * @param forceLogin
     * @param vehiclePriceBO
     * @param priceGearVO
     * @param filter
     */
    public void virtualUidLogin(CalculatePriceBO calculatePriceBO,String forceLogin ,VehiclePriceBO vehiclePriceBO,PriceGearVO priceGearVO,Filter filter,String src,String loginPopup){
        try{
            // 未登录forceLogin=1/0 加蒙层1 未登录可购买0
            if (calculatePriceBO.isVirtualUidLogin) {
                if(StringUtils.equals(forceLogin, "1") || StringUtils.equals(forceLogin, "0")){
                    vehiclePriceBO.setForceLogin(Integer.parseInt(forceLogin));
                }
                if(calculatePriceBO.isGuideLoginPopup()){
                    // 支付完是否弹窗
                    if(StringUtils.equals(loginPopup,"1")){
                        vehiclePriceBO.setShowLoginPopup(loginPopup);
                    }
                    // 曝光引导挡位存储缓存
                    if (StringUtils.equals(forceLogin, "1")&&StringUtils.isNotBlank(filter.getExtend())) {
                        String extend= filter.getExtend().replace("\\","");
                        JSONObject js= JSONObject.parseObject(extend);
                        String guideLogin=js.getString("guideLogin");
                        if(StringUtils.equals(guideLogin,"1")){
                            //曝光引导挡位存储缓存
                            if(filter.getId()!=null&&filter.getId()>0&&StringUtils.isNotBlank(src)){
                                String key= String.format(CarConstant.CAR_GUVID_KEY ,filter.getId(),src,priceGearVO.getVirtualUid());
                                RedisLowPriceDAO.getInstance().addString(key,"1",60*60*24*7);
                            }
                        }
                    }
                }
            }
        }catch (Exception e){
            log.error("virtualUidLogin has error!",e);
        }

    }

    public boolean checkVehicleLogin(String userId,String src){
        try{
            if(StringUtils.isBlank(userId)||userId.equals("0")){
                return true;
            }
            String key= VIR_VEHICLE_KEY+src+userId;
            if(RedisLowPriceDAO.exists(key)){
                return true;
            }
        }catch (Exception e){
            log.error("checkVehicleLogin has error!",e);
        }
        return false;
    }


    /**
     * 判断给定的价格是否为用户优惠价
     *
     * @param formatPrice 价格字符串（已格式化）
     * @return 如果给定的价格为用户优惠价，则返回true；否则返回false
     */
    public boolean isUserCoupon(String formatPrice){
        return new BigDecimal(formatPrice).compareTo(new BigDecimal("19.9"))==0
                ||new BigDecimal(formatPrice).compareTo(new BigDecimal("57"))==0
                ||new BigDecimal(formatPrice).compareTo(new BigDecimal("108"))==0
                ||new BigDecimal(formatPrice).compareTo(new BigDecimal("204"))==0;
    }


    /**
     * 价格解析
     * @param filter
     * @param gear
     * @param priceGearVO
     */
    public void parseFilterSDKAPI(Filter filter, Gear gear, SDKAPIBO sdkapibo, PriceGearVO priceGearVO){
        // abData 普通
        if(sdkapibo.getFilterId()!=null){
            return;
        }
        try{
            sdkapibo.setFilterId(filter.getId());
            sdkapibo.setCnt(gear.getGearType());
            sdkapibo.setType(VipTypeEnum.VEHICLE_VIP.getType());
            sdkapibo.setAutoPay(gear.getAutoPay()==1);
            DecimalFormat decimalFormat = new DecimalFormat("###################.###########");
            String formatPrice=decimalFormat.format(filter.getPrice());
            log.info("parseFilterSDKAPI price={}",formatPrice);
            sdkapibo.setPrice(Double.parseDouble(formatPrice));
            sdkapibo.setId(String.valueOf(VipTypeEnum.VEHICLE_VIP.getId()));
        }catch (Exception e){
            log.error("parseFilter has  error !,filter={}",JSONUtil.toJsonStr(filter),e);
        }
    }

    /**
     * 根据id查询策略
     *
     * @param ids 策略主键ids
     * @return
     */
    public List<Filter> getFilters(List<Integer> ids,int isActivity) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<Filter> wrapper = Wrappers.lambdaQuery();
        wrapper.in(CollectionUtils.isNotEmpty(ids), Filter::getId, ids);
        wrapper.eq(Filter::getIsDelete, 0);
        wrapper.eq(Filter::getType, 1);
        if(isActivity==1){
            wrapper.eq(Filter::getIsActivity, 1);
        }
        return filterMapper.selectList(wrapper);
    }

    /**
     * 车载/音乐包升级档位
     *
     * @param userId  用户id
     * @param vehiclePriceVIPBOS 用户会员信息
     */
    private void checkUpdateSVIP(Long userId,List<VehiclePriceBO> vehiclePriceVIPBOS){
        UserInfoBO userInfoBO= vehicleService.getUserVIPInfo(userId);
        if(userInfoBO==null){
            return;
        }
        PriceInterceptor interceptor= new VehicleUpdateInterceptor(userInfoBO)
                .appendNext(new LuxVIPUpdateInterceptor(userInfoBO).appendNext(new VIPMUpdateInterceptor(userInfoBO)));
        interceptor.doAuth(userId,vehiclePriceVIPBOS);
    }

    @Override
    public MessageModel getSDKAPIPriceInfo(PriceGearVO priceGearVO) {
        try {
            log.info("getSDKAPIPriceInfo input param= {}", JSONUtil.toJsonStr(priceGearVO));
            List<SDKAPIBO> sdkapibos=new ArrayList<>();
            PayDesk payDesk = payDeskService.getPayDeskInfoBySign(priceGearVO.getPayDeskSign());
            log.info("getSDKAPIPriceInfo payDesk= {}", JSONUtil.toJsonStr(payDesk));
            List<Gear> gears= gearService.getGearList(payDesk.getId(),priceGearVO.getPlatform(),VipTypeEnum.VEHICLE_VIP.getType(),priceGearVO);
            log.info("--sdkapi--------gears={}",JSONUtil.toJsonStr(gears));
            List<Filter> filters = invokeValidFilter(priceGearVO, gears,null );
            Collections.sort(filters);
            // log.info("---sdkapi-------Filter={}",JSONUtil.toJsonStr(filters));
            Map<Integer, List<Filter>> filterMap = filters.stream().collect((Collectors.groupingBy(Filter::getGearId)));
            groupBySDKAPIVipTypes(gears, filterMap,priceGearVO,sdkapibos);
            log.info("---sdkapi-------result={}",JSONUtil.toJsonStr(sdkapibos));
            log.info("getSDKAPIPriceInfo add CAR_SDK_REDIS_KEY boolean={}",StringUtils.equals(priceGearVO.getPayDeskSign(),"sdk_car_paydesk"));
            if(StringUtils.equals(priceGearVO.getPayDeskSign(),"sdk_car_paydesk")&&StringUtils.isNotBlank(priceGearVO.getChannel())){
                log.info("getSDKAPIPriceInfo add CAR_SDK_REDIS_KEY redis,channel={}",priceGearVO.getChannel());
                RedisLowPriceDAO.sadd(CAR_SDK_REDIS_KEY,priceGearVO.getChannel(),60*30);
                log.info("getSDKAPIPriceInfo add CAR_SDK_REDIS_KEY redis,channel={}",priceGearVO.getChannel());
            }else if (StringUtils.equals(priceGearVO.getPayDeskSign(),"api_car_paydesk")&&StringUtils.isNotBlank(priceGearVO.getChannel())){
                RedisLowPriceDAO.sadd(CAR_API_REDIS_KEY,priceGearVO.getChannel(),60*30);
                log.info("getSDKAPIPriceInfo add CAR_API_REDIS_KEY redis,channel={}",priceGearVO.getChannel());
            }
            return new MessageModel(sdkapibos);
        }catch (Exception e){
            log.error("getSDKAPIPriceInfo has error ! priceGearVO={}",JSONUtil.toJsonStr(priceGearVO),e);
        }
        log.info("getSDKAPIPriceInfo add CAR_SDK_REDIS_KEY boolean={}",StringUtils.equals(priceGearVO.getPayDeskSign(),"sdk_car_paydesk"));
        if(StringUtils.equals(priceGearVO.getPayDeskSign(),"sdk_car_paydesk")&&StringUtils.isNotBlank(priceGearVO.getChannel())){
            log.info("getSDKAPIPriceInfo add CAR_SDK_REDIS_KEY redis,channel={}",priceGearVO.getChannel());
            RedisLowPriceDAO.sadd(CAR_SDK_REDIS_KEY,priceGearVO.getChannel(),-1);
            log.info("getSDKAPIPriceInfo add CAR_SDK_REDIS_KEY redis,channel={}",priceGearVO.getChannel());
        }else if (StringUtils.equals(priceGearVO.getPayDeskSign(),"api_car_paydesk")&&StringUtils.isNotBlank(priceGearVO.getChannel())){
            RedisLowPriceDAO.sadd(CAR_API_REDIS_KEY,priceGearVO.getChannel(),-1);
            log.info("getSDKAPIPriceInfo add CAR_API_REDIS_KEY redis,channel={}",priceGearVO.getChannel());
        }
        String doudi="[{\"id\":\"17\",\"price\":19.9,\"cnt\":1,\"type\":\"vip_17\"},{\"id\":\"17\",\"price\":19,\"cnt\":3,\"type\":\"vip_17\"},{\"id\":\"17\",\"price\":18,\"cnt\":6,\"type\":\"vip_17\"},{\"id\":\"17\",\"price\":17,\"cnt\":12,\"type\":\"vip_17\"}]";
        if(priceGearVO.getPayDeskSign().equals("api_car_paydesk")){
            doudi="[{\"id\":\"17\",\"price\":19.9,\"cnt\":1,\"type\":\"vip_17\"},{\"id\":\"17\",\"price\":57,\"cnt\":3,\"type\":\"vip_17\"},{\"id\":\"17\",\"price\":108,\"cnt\":6,\"type\":\"vip_17\"},{\"id\":\"17\",\"price\":204,\"cnt\":12,\"type\":\"vip_17\"}]";
        }
        log.info("---sdkapi- doudi------result={}",JSONUtil.toJsonStr(doudi));
        doudi=doudi.replace("\\","");
        return new MessageModel(JSONArray.parse(doudi));
    }

    @Override
    public List<Filter> getFilterByGearIDs(List<Integer> gearIDs,String channel) {
        LambdaQueryWrapper<Filter> wrapper = Wrappers.lambdaQuery();
        wrapper.in(CollectionUtils.isNotEmpty(gearIDs), Filter::getGearId, gearIDs);
        wrapper.eq(Filter::getIsDelete, 0);
        wrapper.notIn(Filter::getId, Arrays.asList(1245,1242));
        List<Filter> filters= filterMapper.selectList(wrapper);
        Iterator<Filter> iterator= filters.iterator();
        while(iterator.hasNext()){
            try {
                Filter filter = iterator.next();
                String blockedChannel= filter.getBlockedChannel();
                if (StringUtils.isNotBlank(blockedChannel)) {
                    List<String> blockedKeys = Arrays.stream(blockedChannel.split(",")).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(blockedKeys) && blockedKeys.contains(channel)) {
                        iterator.remove();
                    }
                }
                String channels= filter.getFilterChannel();
                if(StringUtils.isBlank(channels)){
                    continue;
                }
                channels=channels.toLowerCase();
                channel=channel.toLowerCase();
                List<String> keys = Arrays.stream(channels.split(",")).collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(keys)&&!keys.contains(channel)){
                    iterator.remove();
                }
            }catch (Exception e){
                log.error("getFilterByGearIDs has error",e);
            }
        }
        return filters;
    }

    /**
     * 车载虚拟ID引导登录逻辑 7 8 9
     *
     */
    public boolean isNeedVirLogin(PriceGearVO priceGearVO){
        try{
            if(StringUtils.isBlank(priceGearVO.getVirtualUid())||priceGearVO.getVirtualUid().equals("0")){
                return false;
            }
            List<String> virUids=carConfigNacos.getCarConfigBO().getVirUids();
            if(!CollectionUtils.isEmpty(virUids)){
                for(String keyStr:virUids){
                    if(priceGearVO.getVirtualUid().endsWith(keyStr)){
                        return true;
                    }
                }
            }
            return false;
        }catch (Exception e){
            log.error("isNeedVirLogin has error",e);
        }
        return false;
    }

    /**
     * 获取wxtflag标识
     *
     * @param filterId
     * @return
     */
    public String getWxTflag(String filterId) {
        String wxTflag="";
        try {
            wxTflag = cacheUtils.wxTflagCache.get(filterId);
            log.info("getWxTflag cache wxTflag={}",wxTflag);
        }catch (Exception e){
          log.error("getWxTflag has error!",e);
        }
        return wxTflag;
    }

    @Override
    public List<Filter> getFiltersByGIds(List<Integer> gearIds) {
        if(CollectionUtils.isEmpty(gearIds)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<Filter> wrapper = Wrappers.lambdaQuery();
        wrapper.in(CollectionUtils.isNotEmpty(gearIds), Filter::getGearId, gearIds);
        wrapper.eq( Filter::getIsDelete, 0);
        return filterMapper.selectList(wrapper);
    }
}
