package com.memberintergral.carservice.service.impl;

import com.memberintergral.carservice.config.constant.VehicleConstant;
import com.memberintergral.carservice.config.enums.CarActivitySuperEnum;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static com.memberintergral.carservice.config.constant.VehicleConstant.CAR_ACT;
import static com.memberintergral.carservice.config.constant.VehicleConstant.CAR_ACTIVITY;
import static com.memberintergral.carservice.config.enums.CarActivitySuperEnum.VEHICLE_ACTIVITY;

/**
 * 活动管理器，用来关联活动
 */
public class ActivityManager {


    private static Map<String, CarActivityAbs> activitys = new ConcurrentHashMap<>();
    private static Map<String, CarActivityAbs> autoRenewalactivitys = new ConcurrentHashMap<>();
    private static List<String> caculateSrcs= Arrays.asList("calculateMonth19.9","calculateMonth57","calculateMonth108","calculateMonth208","calculateMonth19.9_19.9","calculateSeason57_57","calculateHalfYear108_108","calculateMonth208_208","ar_calculateMonth19.9","ar_calculateMonth57","ar_calculateMonth108","ar_calculateMonth204","ar_calculateMonth19.9_19.9","ar_calculateMonth57_57","ar_calculateMonth108_108","ar_calculateMonth204_204");

    public static void registerActivity(String activityCode, CarActivityAbs activity){
        // 只注册物理时间不过期的活动,方便之后查询可用的src列表
        if (!activity.isPhysicTimeExpire()){
            activitys.put(activityCode, activity);
        }
        autoRenewalactivitys.put(activityCode, activity);
    }

    public static CarActivityAbs getActivity(String activityCode){
        if(caculateSrcs.contains(activityCode)){
            activityCode="calculateMonth";
        }
//        if(activityCode.equalsIgnoreCase(CAR_ACTIVITY)){
//            activityCode= VEHICLE_ACTIVITY.getOpStr();
//        }
        //车载配置平台
        if(!CarActivitySuperEnum.getAllSrcList().contains(activityCode)){
            return activitys.get(CarActivitySuperEnum.CAR_SERVICE.getOpStr());
        }
        return activitys.get(activityCode);
    }
    public static CarActivityAbs getActivityInfo(String activityCode){
        return activitys.get(activityCode);
    }

    /**
     * 自动续费专门获取的src
     * @param activityCode
     * @return
     */
    public static CarActivityAbs getRenewalActivity(String activityCode){
        if(caculateSrcs.contains(activityCode)){
            activityCode="calculateMonth";
        }
        if(activityCode.equalsIgnoreCase(CAR_ACTIVITY)){
            activityCode= VEHICLE_ACTIVITY.getOpStr();
        }
        //车载配置平台
        if(!CarActivitySuperEnum.getAllSrcList().contains(activityCode)){
            return activitys.get(CarActivitySuperEnum.CAR_SERVICE.getOpStr());
        }
        return autoRenewalactivitys.get(activityCode);
    }


}
