package com.memberintergral.carservice.service.impl.activity;

import com.alibaba.fastjson.JSONObject;
import com.memberintergral.carservice.config.constant.VehicleConstant;
import com.memberintergral.carservice.config.enums.CarActivityMonthPriceEnum;
import com.memberintergral.carservice.config.enums.CarActivitySuperEnum;
import com.memberintergral.carservice.config.enums.CarActivitySuperVipAutoPayMonthPriceEnum;
import com.memberintergral.carservice.config.enums.ProductEnum;
import com.memberintergral.carservice.domain.entity.CarActivity;
import com.memberintergral.carservice.domain.entity.DataItem;
import com.memberintergral.carservice.domain.entity.Product;
import com.memberintergral.carservice.service.CarActivityService;
import com.memberintergral.carservice.service.impl.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * @program: vip_adv
 * @description: 车载项目20231111活动
 * @author: <EMAIL>
 * @create: 2022-10-25 13:37
 **/
@Component
public class CarActivity2024Activity extends CarActivityAbs {

    private static final Logger logger = LoggerFactory.getLogger(CarActivity2024Activity.class);

    /**
     * 活动key标识
     */
    private final static String ACTIVITY_KEY = "car_activity_vip_2024";

    /**
     * 数据集servce
     */
    @Autowired
    private DataModelService dataModelService;


    @Autowired
    private CarActivityService carActivityService;

    /**
     * 获取产品价格
     *
     * @param params
     * @return
     */
    @Override
    public double getfinalPrice(ActivityParams params) {
        if(params==null || StringUtils.isBlank(params.getPaySrc()) || params.getProduct()==null){
            logger.error("car_activity_vip_2024：getfinalPrice function params has empty value ,params={}", JSONObject.toJSON(params));
            return -1;
        }
        Product product=params.getProduct();
        String channel=params.getPaySrc().toLowerCase();
        List<String> channelsList=new ArrayList<>();

        channelsList.add("shangqidazhong-pro");
        channelsList.add("shangqidazhong-uat");
        channelsList.add("c_api_svw_banma_act");
        // sdk 校验
        if(channelsList.contains(channel)){
            String discountPrice = getActivityDiscountPrice(product.getProductTypeId(),product.getCnt(),product.getPrice(),params.getAutoPay().equals("yes")?1:0);
            if(StringUtils.isBlank(discountPrice)){
                logger.error("car_activity_vip_2024：getfinalPrice function getActivityDiscountPrice is empty,params={}",JSONObject.toJSON(params));
                return -1;
            }
            return new BigDecimal(discountPrice).doubleValue();
        }
        // apk校验
        else{
            if(params.getProduct().getStockId()==null){
                logger.error("car_activity_vip_2024：getfinalPrice functionparams.getProduct().getStockId() empty value ,params={}", JSONObject.toJSON(params));
                return -1;
            }
            String code=ACTIVITY_KEY+"_"+product.getStockId();
            CarActivity carActivity = carActivityService.getById(product.getStockId());
            if(carActivity==null){
                logger.error("car_activity_vip_2024：getfinalPrice function dataItemList size is empty or >1 ,code={}",code);
                return -1;
            }
            return carActivity.getPrice().doubleValue();
        }
    }

    /**
     * 获取活动id
     *
     * @param productTypeId
     * @param cnt
     * @param price
     * @return
     */
    public String getActivityDiscountPrice(Long productTypeId,Short cnt ,Double price,Integer isContinute) {
        if(productTypeId==null||cnt==null||price==null){
            logger.error("car_activity_vip_2024：getActivityDiscountPrice function params  has empty value ");
            return "";
        }
        DecimalFormat decimalFormat = new DecimalFormat("###################.###########");
        String formatPrice=decimalFormat.format(price);
        String title=ACTIVITY_KEY+"_"+cnt+"_"+isContinute+"_"+formatPrice;
        List<DataItem> dataItemList= dataModelService.getDataItemByInfo(title);
        if(CollectionUtils.isEmpty(dataItemList)||dataItemList.size()!=1){
            logger.error("car_activity_vip_2024：getActivityDiscountPrice getDatItemByTitle function dataItemList size is empty or >1 ,code={}", JSONObject.toJSON(title));
            return "";
        }
        DataItem dataItem= dataItemList.get(0);
        String content= dataItem.getContent();
        JSONObject jsonObject = JSONObject.parseObject(content);
        return jsonObject.getString("discount_price");
    }

    /**
     * 初始化
     *
     * @throws Exception
     */
    public void afterPropertiesSet() throws Exception{
        ActivityManager.registerActivity(getActivityCode().getOpStr(), this);
    }

    /**
     * 验证日期有效性
     *
     * @param params
     * @return
     */
    @Override
    public boolean validateInPeriod(ActivityParams params) {
        if(params==null||StringUtils.isBlank(params.getPaySrc())){
            logger.error("car_activity_vip_2024：validateInPeriod function params has empty value ,params={}",JSONObject.toJSON(params));
            return false;
        }
        String paySrc = params.getPaySrc();
        paySrc=paySrc.toLowerCase();
        if(VehicleConstant.lianyouChannels.contains(paySrc)){
            paySrc=VehicleConstant.lianyouChannel;
        }else if(VehicleConstant.jiaChannels.contains(paySrc)){
            paySrc=VehicleConstant.jiaChannel;
        }else if(VehicleConstant.wuFenChannels.contains(paySrc)){
            paySrc=VehicleConstant.wuFenChannel;
        }else if(VehicleConstant.bydChannels.contains(paySrc)){
            paySrc=VehicleConstant.bydChannel;
        }else if(VehicleConstant.caocaoChannels.contains(paySrc)){
            paySrc=VehicleConstant.caocaoChannel;
        }else if(VehicleConstant.fangyiChannels.contains(paySrc)){
            paySrc=VehicleConstant.fangyiChannel;
        }else if(VehicleConstant.kedaxunfeis.contains(paySrc)){
            paySrc=VehicleConstant.kedaxunfeichannel;
        }
        String src=params.getType();
        CarActivity activity= carActivityService.getCarActivity(src,paySrc);
        return isInPeroid(params.getCurrentDate(), activity.getStartTime(), activity.getEndTime());
    }



    /**
     * 判断当前时间是否在活动有效期内
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2020/10/26 16:33
     */
    public static boolean isInPeroid(Date date, Date startTime, Date endTime) {
        try {
        } catch (Exception e) {
            logger.error("日期转换错误", e);
        }
        if (startTime == null || endTime == null)
            return false;
        if (date.getTime() - startTime.getTime() > 0 && date.getTime() - endTime.getTime() < 0)
            return true;
        else
            return false;
    }


    @Override
    public CarActivitySuperEnum getActivityCode(){
        return CarActivitySuperEnum.VEHICLE_VIP_2024;
    }


    @Override
    public boolean isPhysicTimeExpire() {
        return false;
    }

    /**
     * 校验src, 默认购买的都是车载vip
     *
     * @return
     */
    @Override
    public boolean validateSrc(ActivityParams activityParams){
        if(activityParams==null){
            logger.error("car_activity_vip_2024：activityParams function params has empty value ");
            return false;
        }
        Product product = activityParams.getProduct();
        Long productTypeId = product.getProductTypeId();
        if (productTypeId== ProductEnum.VIP_VEHICLE.getId()||productTypeId== ProductEnum.SUPER_VIP.getId()){
            return true;
        }
        return false;
    }

    /**
     * 获取次月续费价格信息
     * 默认价格策略, 按照 caclNormalMonth方式进行扣费
     *  1： 19.9
     *  3： 57
     *  6：  108
     *  12： 204
     */
    public RenewalPrice getRenewalPriceInfo(RenewalParams renewalParams){
        RenewalPrice renewalPrice = new RenewalPrice();
        short cnt = renewalParams.getCnt();
        if(renewalParams.getProductTypeId() == ProductEnum.SUPER_VIP.getId()){
            CarActivitySuperVipAutoPayMonthPriceEnum instance = CarActivitySuperVipAutoPayMonthPriceEnum.getInstance(Integer.valueOf(cnt));
            renewalPrice.setCnt(cnt);
            renewalPrice.setDuration((short) (cnt * 31));
            renewalPrice.setPrice(instance.getFinalAmount());
            return renewalPrice;
        }
        CarActivityMonthPriceEnum instance = CarActivityMonthPriceEnum.getInstance(Integer.valueOf(cnt));
        renewalPrice.setCnt(cnt);
        renewalPrice.setDuration((short) (cnt * 31));
        renewalPrice.setPrice(instance.getFinalAmount());
        return renewalPrice;
    }


}
