package com.memberintergral.carservice.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.memberintergral.carservice.domain.entity.CarActivity;
import com.memberintergral.carservice.mapper.CarActivityMapper;
import com.memberintergral.carservice.service.CarActivityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 车载活动表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-17
 */
@Service
@Slf4j
@DS("master")
public class CarActivityServiceImpl extends ServiceImpl<CarActivityMapper, CarActivity> implements CarActivityService {

    public List<CarActivity> getCarActivities(String src, String channel,int gearType,int fromType,int autoPay) {
        List<CarActivity> carActivities = this.lambdaQuery().
                eq(CarActivity::getSrc, src).
                eq(CarActivity::getBigChannel, channel).
                eq(CarActivity::getGearType, gearType).
                eq(CarActivity::getType, fromType).
                eq(CarActivity::getAutoPay, autoPay==1?0:1).
                eq(CarActivity::getStatus, 1).list();
        return carActivities;
    }

    @Override
    public CarActivity getCarActivity(String src, String channel) {
        CarActivity carActivity = this.lambdaQuery().
                eq(CarActivity::getSrc, src).
                eq(CarActivity::getBigChannel, channel).
                eq(CarActivity::getStatus, 1).orderByDesc(CarActivity::getId).last(" limit 1").one();;
        return carActivity;
    }

    @Override
    public CarActivity getCarActivityByCode(String code) {
        CarActivity carActivity = this.lambdaQuery().
                eq(CarActivity::getCode, code).one();
        return carActivity;
    }

    @Override
    public boolean updateCarActivityByCode(CarActivity carActivity) {
        LambdaUpdateWrapper<CarActivity> wrapper = Wrappers.lambdaUpdate();
        wrapper.set(CarActivity::getActivityName, carActivity.getActivityName())
                .set(CarActivity::getAlert, carActivity.getAlert())
                .set(CarActivity::getExtend, carActivity.getExtend())
                .set(CarActivity::getAutoPay, carActivity.getAutoPay())
                .set(CarActivity::getRank, carActivity.getRank())
                .set(CarActivity::getStartTime, carActivity.getStartTime())
                .set(CarActivity::getEndTime,carActivity.getEndTime())
                .set(CarActivity::getGearType,carActivity.getGearType())
                .set(CarActivity::getSrc,carActivity.getSrc())
                .set(CarActivity::getVipType,carActivity.getVipType())
                .set(CarActivity::getPrice,carActivity.getPrice())
                .set(CarActivity::getLinePrice,carActivity.getLinePrice())
                .set(CarActivity::getRenewalPrice,carActivity.getRenewalPrice())
                .set(CarActivity::getAutoPay,carActivity.getAutoPay())
                .set(CarActivity::getOperateName,carActivity.getOperateName())
                .set(CarActivity::getMonth,carActivity.getMonth())
                .set(CarActivity::getStatus,carActivity.getStatus())
                .set(CarActivity::getBigChannel,carActivity.getBigChannel())
                .set(CarActivity::getType,carActivity.getType())
                .set(CarActivity::getFirstPay,carActivity.getFirstPay())
                .set(CarActivity::getUpdateTime,carActivity.getUpdateTime())
                .eq(CarActivity::getCode, carActivity.getCode());
        return this.update(wrapper);
    }
    @Override
    public boolean updateCarStatusByCode(String code){
        LambdaUpdateWrapper<CarActivity> wrapper = Wrappers.lambdaUpdate();
        wrapper.set(CarActivity::getStatus, 0)
                .eq(CarActivity::getCode, code);
        return this.update(wrapper);

    }

    @Override
    public List<CarActivity> getRemoveByCodes(List<String> codes) {
        return this.lambdaQuery().notIn(CarActivity::getCode, codes).list();
    }

}