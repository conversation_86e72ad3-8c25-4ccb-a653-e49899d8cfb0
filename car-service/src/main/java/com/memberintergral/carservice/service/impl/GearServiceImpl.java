package com.memberintergral.carservice.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.memberintergral.carservice.domain.VO.PriceGearVO;
import com.memberintergral.carservice.domain.entity.Gear;
import com.memberintergral.carservice.enums.VipConfConstant;
import com.memberintergral.carservice.enums.VipTypeEnum;
import com.memberintergral.carservice.mapper.GearMapper;
import com.memberintergral.carservice.service.GearService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

import static com.memberintergral.carservice.service.impl.NewFilterServiceImpl.CASH_H5_RETAIN;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-31
 */
@DS("vipconf")
@Service
@Slf4j
public class GearServiceImpl extends ServiceImpl<GearMapper, Gear> implements GearService {
    @Autowired
    private GearMapper gearMapper;
    @Autowired
    private VehicleActivityService vehicleActivityService;

    @Override
    public List<Gear> getGearList(Integer payDeskId, String platform, String vipType, PriceGearVO priceGearVO) {
        LambdaQueryWrapper<Gear> wrapper= Wrappers.lambdaQuery();
        wrapper.eq(Gear::getPayDeskId,payDeskId);
        wrapper.eq(Gear::getPlatform,platform);
        wrapper.eq(Gear::getVipType,vipType);
        wrapper.eq(Gear::getIsDelete, VipConfConstant.ONLINE);
        if(StringUtils.equals(priceGearVO.getPayDeskSign(),"carengine")){
            int autoPay= vehicleActivityService.getCarAutoPay(priceGearVO.getUid(),priceGearVO.getVirtualUid());
            if(vipType.equals(VipTypeEnum.SUPER_VIP.getType())&&autoPay==1){
                wrapper.eq(Gear::getAutoPay,0);
            }else if (vipType.equals(VipTypeEnum.VEHICLE_VIP.getType())){
                wrapper.eq(Gear::getAutoPay,autoPay==1?0:1);
            }
        }
        if(StringUtils.equals(priceGearVO.getPayDeskSign(),"caocaotravel")){
            int autoPay= vehicleActivityService.getCarAutoPay(priceGearVO.getUid(),priceGearVO.getVirtualUid());
            if(autoPay==1){
                wrapper.eq(Gear::getAutoPay,0);
            }
        }
        wrapper.orderByDesc(Gear::getOrderRank);
        return gearMapper.selectList(wrapper);
    }

    @Override
    public List<Gear> getCarGearList(Integer payDeskId, String platform, String vipType, PriceGearVO priceGearVO,int cnt) {
        LambdaQueryWrapper<Gear> wrapper= Wrappers.lambdaQuery();
        wrapper.eq(Gear::getPayDeskId,payDeskId);
        wrapper.eq(Gear::getPlatform,platform);
        wrapper.eq(Gear::getVipType,vipType);
        wrapper.eq(Gear::getGearType,cnt);
        wrapper.eq(Gear::getIsDelete, VipConfConstant.ONLINE);
        int autoPay= vehicleActivityService.getCarAutoPay(priceGearVO.getUid(),priceGearVO.getVirtualUid());
        wrapper.eq(Gear::getAutoPay,autoPay==1?0:1);
        wrapper.orderByDesc(Gear::getOrderRank);
        return gearMapper.selectList(wrapper);
    }

    @Override
    public List<Gear> getCarMobileGearList(Integer payDeskId, String platform,  List<String> vipTypes, PriceGearVO priceGearVO,int autoPay) {
        LambdaQueryWrapper<Gear> wrapper= Wrappers.lambdaQuery();
        wrapper.eq(Gear::getPayDeskId,payDeskId);
        wrapper.eq(Gear::getPlatform,platform);
        wrapper.in(Gear::getVipType,vipTypes);
        wrapper.eq(Gear::getIsDelete, VipConfConstant.ONLINE);
        wrapper.eq(Gear::getAutoPay,autoPay==1?0:1);
        wrapper.orderByDesc(Gear::getOrderRank);
        return gearMapper.selectList(wrapper);
    }

    @Override
    public List<Gear> getNewCarGearList(Integer payDeskId, String platform, String vipType,Integer autoPay,String payDeskSign) {
        LambdaQueryWrapper<Gear> wrapper= Wrappers.lambdaQuery();
        wrapper.eq(Gear::getPayDeskId,payDeskId);
        wrapper.eq(Gear::getPlatform,platform);
        wrapper.eq(Gear::getVipType,vipType);
        wrapper.eq(Gear::getIsDelete, VipConfConstant.ONLINE);
        if(StringUtils.equals(payDeskSign,"carengine")){
            if(vipType.equals(VipTypeEnum.SUPER_VIP.getType())&&autoPay==1){
                wrapper.eq(Gear::getAutoPay,0);
            }else if (vipType.equals(VipTypeEnum.VEHICLE_VIP.getType())){
                wrapper.eq(Gear::getAutoPay,autoPay==1?0:1);
            }
        }
        if(StringUtils.equals(payDeskSign,"caocaotravel")){
            if(autoPay==1){
                wrapper.eq(Gear::getAutoPay,0);
            }
        }
        if (StringUtils.equals(payDeskSign, CASH_H5_RETAIN) && autoPay != null) {
            wrapper.eq(Gear::getAutoPay,autoPay);
        }
        wrapper.orderByDesc(Gear::getOrderRank);
        return gearMapper.selectList(wrapper);
    }

    @Override
    public Gear getCarGearById(Integer id) {
        return gearMapper.selectById(id);
    }

}

