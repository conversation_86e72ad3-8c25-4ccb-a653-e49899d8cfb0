package com.memberintergral.carservice.service.impl;

import com.alibaba.fastjson.JSONObject;

import com.memberintergral.carservice.config.constant.*;
import com.memberintergral.carservice.config.enums.*;
import com.memberintergral.carservice.config.constant.*;
import com.memberintergral.carservice.config.enums.*;
import com.memberintergral.carservice.config.exception.VehicleException;
import com.memberintergral.carservice.config.monitor.CarMonitor;
import com.memberintergral.carservice.config.monitor.MonitorUtil;
import com.memberintergral.carservice.config.nacos.CarConfigNacos;
import com.memberintergral.carservice.config.redis.RedisKey;
import com.memberintergral.carservice.config.redis.added.RedisDAO;
import com.memberintergral.carservice.domain.BO.SchemeConfigBO;
import com.memberintergral.carservice.domain.BO.UserInfoBO;
import com.memberintergral.carservice.domain.DTO.PayInfoDTO;
import com.memberintergral.carservice.domain.DTO.PayOrderDTO;
import com.memberintergral.carservice.domain.entity.*;
import com.memberintergral.carservice.mapper.*;
import com.memberintergral.carservice.util.*;
import com.memberintergral.carservice.domain.entity.*;
import com.memberintergral.carservice.mapper.*;
import com.memberintergral.carservice.util.*;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.net.URLEncoder;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @program: vip_adv
 * @description: 车载业务辅助类 MASTER
 * @author: <EMAIL>
 * @create: 2018-10-06 19:09
 */
@Service("vehicleAssistant")
public class VehicleAssistant {
    private static final Logger logger = LoggerFactory.getLogger(VehicleAssistant.class);

    @Autowired
    private VehicleProductMapper vehicleProductMapper;
    @Autowired
    private VehicleOrderMapper vehicleOrderMapper;
    @Autowired
    private AutoRenewalService renewalService;
    @Autowired
    private InvokingRemoteService invokingRemoteService;
    @Value("${vip.server.domain}")
    private String vipDomain;
    @Value("${album.price.check.url}")
    private String albumCheckUrl;
    @Autowired
    private VehicleBusinessOrderMapper vehicleBusinessOrderMapper;
    @Autowired
    private VehicleSerialCodeMapper serialCodeMapper;
    @Autowired
    private UpdateDataRecordMapper updateDataRecordMapper;
    @Autowired
    private AutoRenewalInfoVehicleMapper renewalMapper;
    @Autowired
    private UserProductMapper userProductMapper;
    @Autowired
    private CarActivityType carActivityType;

    @Autowired
    private ThirdNotifyService thirdNotifyService;

    @Autowired
    private VehicleNotifyMapper vehicleNotifyMapper;

    @Autowired
    private VehicleOrderExtendMapper vehicleOrderExtendMapper;

    @Autowired
    private VehicleService vehicleService;

    @Autowired
    private CarMonitor carMonitor;

    @Autowired
    private CarConfigNacos carConfigNacos;

    /**
     * @Description: 操作订单
     * @Param: [order]
     * @return: cn.kuwo.vip1.util.common.constants.MessageModel
     * @Author: <EMAIL>
     * @Date: 2018/10/6 19:10
     */
    @Transactional(value = "masterTransactionManager", rollbackFor = Exception.class)
    public MessageModel operateOrder(VehicleOrder order, String orderKey) throws Exception {
        //校验订单是否正在处理
        if (RedisDAO.exists(orderKey)) {
            logger.error("支付服务重复通知，返回成功，记录error，传入的order:{}", JsonUtil.bean2Json(order));
            return new MessageModel();
        }
        RedisDAO.getInstance().setObject(orderKey, order.getId(), 3600);
        MessageModel messageModel = new MessageModel();
        //更新用户购买商品表   1,查询订单对应的商品 2，查询该用户所有的该商品 3更新
        List<VehicleProduct> productsList = vehicleProductMapper.getProductsByOrderId(order.getId());
        if (productsList.size() < 1) {
            return new MessageModel(SystemCodeErrorConstant.PRODUCT_DATA_ERRER);
        }

        int productTypeId = 0;
        int days = 0;
        short cnt = 0;
        Long uid = order.getUserId();
        Long virtualUid = order.getPid();

        final Timestamp currentTime = TimeUtils.getCurrentTime();
        List<VehicleProduct> toUpdateProducts = new ArrayList<>();
        for (VehicleProduct product : productsList) {
            final short duration = product.getDuration();
            ProductEnum productType = ProductEnum.getProductType(product.getProductTypeId().intValue());
            productTypeId = productType.getId();
            if (productType == ProductEnum.VIP_VEHICLE || productType == ProductEnum.SUPER_VIP){
                vehicleProcessProduct(product, duration, currentTime, uid, virtualUid, productType);
                days = product.getDuration();
                cnt = product.getCnt();
            }
            if (productType == ProductEnum.ALBUM){
                product.setStartDate(currentTime);
                product.setExpireDate(TimeUtils.addDays(currentTime, duration));
            }

            //如果该笔订单是自动续费订单，并且原订单也是自动续费订单，将原订单修改为done
            if ("yes".equals(order.getAutoPay())) {
                Long orderId = MyNumberUtils.toLong(order.getSrc());
                vehicleOrderMapper.autoPayDone(orderId);
                //更新自动续费信息表状态
                renewalService.updateCodeAndDescAfterPaySucc(uid, virtualUid, order.getId());
            }
            product.setBuyDate(currentTime);
            product.setStatus(OrderStatusEnum.PAIED.getStatus());
            toUpdateProducts.add(product);
        }
        //更新订单
        vehicleOrderMapper.updateOrder(order);
        //更新vehicle_product
        vehicleProductMapper.updateBatch(toUpdateProducts);

        boolean isCouponValid=true;
        if((StringUtils.isNotBlank(order.getSrc())&&order.getSrc().equals("calculateMonth"))){
            CarActivityAbs activity = ActivityManager.getActivity(order.getSrc());
            isCouponValid=activity.isCouponValid(order);
        }
        // check库存
        if(StringUtils.isNotBlank(order.getSrc())&&order.getSrc().equals(CarActivitySuperEnum.ACTIVITY_VEHICLE_SEND.getOpStr())&&order.getPaySrc().equals("yiqidazhong_h5")){
            CarActivityAbs activity = ActivityManager.getActivity(order.getSrc());
            ActivityParams params=new ActivityParams();
            params.setUid(order.getUserId());
            params.setCnt(toUpdateProducts.get(0).getCnt().intValue());
            params.setPaySrc("yiqidazhong_h5");
            if(!activity.checkStock(params)){
                throw new VehicleException("SVW:库存限制");
            }
        }
        if ((productTypeId == ProductEnum.VIP_VEHICLE.getId() || productTypeId == ProductEnum.SUPER_VIP.getId()) && !CarActivitySuperEnum.VIP_UPGRADE_VEHICLE.getOpStr().equals(order.getSrc())&& !VipUpdatePriceEnum.VIPM_VVIP.getSrc().equals(order.getSrc())) {
            if(VipUpdatePriceEnum.VIPM_SVIP.getSrc().equals(order.getSrc())){
                UserInfoBO userInfoBO= vehicleService.getUserVIPInfo(order.getUserId());
                CarActivityAbs activity = ActivityManager.getActivity(order.getSrc());
                int upGradeId=activity.getUpgradeId(order);
                if(new Date(userInfoBO.getSvipExpire()).after(new Date())){
                    logger.info("vehicle_vip_update:已经是超级会员 无需再次开通,order_id={}",order.getId());
                }else if(order.getAutoPay().equals("yes")||upGradeId==VipUpdatePriceEnum.VEHICLE_AUTO_SVIP.getId()||upGradeId==VipUpdatePriceEnum.VEHICLE_AUTO_ONE_MONTH_SVIP.getId()) {
                    // 如果是连续车载VIP升级超级会员 需要更新之前订单的autoPay和payType
                    if(upGradeId==VipUpdatePriceEnum.VEHICLE_AUTO_SVIP.getId()||upGradeId==VipUpdatePriceEnum.VEHICLE_AUTO_ONE_MONTH_SVIP.getId()){
                        VehicleOrder vehicleOrder= vehicleOrderMapper.getLastAutoPayOrder(uid);
                        if(vehicleOrder!=null&&!vehicleOrder.getAutoPay().equals("transToSvip")){
                            vehicleOrderMapper.updateOrderAutoPayById("transToSvip",vehicleOrder.getId());
                        }
                        if(vehicleOrder!=null&&vehicleOrder.getPayType()!=null){
                            vehicleOrderMapper.updateOrderAutoPayPayTypeById(vehicleOrder.getPayType(),"yes",order.getId());
                        }
                    }
                    VehicleOrderExtend vehicleOrderExtend = vehicleOrderExtendMapper.getVehicleOrderExtendByOid(order.getId());
                    if (StringUtils.isBlank(vehicleOrderExtend.getExt1())) {
                        throw new VehicleException("vehicle_vip_update:caculateUpdateSVIP 缺少upgradeId");
                    }
                    String centent = vehicleOrderExtend.getExt1();
                    JSONObject json = JSONObject.parseObject(centent);
                    Integer upgradeId= json.getInteger("upGradeId");
                    VipUpdatePriceEnum op= VipUpdatePriceEnum.getVipInstance(upgradeId);
                    if(op!=null){
                        invokingVipSend(productTypeId, days, order, uid ,virtualUid,op);
                    }
                    logger.info("vehicle_vip_update:调用升级超会方法,order_id={}",order.getId());
                }
            }else{
                if(productTypeId == ProductEnum.VIP_VEHICLE.getId()){
                    if(isCouponValid){
                        invokingVipSend(productTypeId, days, order, uid ,virtualUid,null);
                    }else{
                        logger.error("vehicle_coupon_error:优惠券Id使用重复,order_id={}",order.getId());
                        throw new VehicleException("优惠券Id使用重复");
                    }
                }else{
                    invokingVipSend(productTypeId, days, order, uid ,virtualUid,null);
                }
            }

        } else if (productTypeId == ProductEnum.ALBUM.getId()) {
            //目前只有单一产品购买
            for (VehicleProduct product : productsList) {
                messageModel = invokingRemoteService.invokingVipBuyAlbum(uid.toString(), product.getProductId(), String.valueOf(product.getProductTypeId()));
                if (!messageModel.getCode().equals(SystemCodeErrorConstant.SUCCESS.getCode())) {
                    logger.error("调用VIP服务，返回错误");
                }
            }
        }
        // 更新购买记录
        updateBuyRecord(order);
        // 减去库存
        if(StringUtils.isNotBlank(order.getSrc())&&order.getSrc().equals(CarActivitySuperEnum.ACTIVITY_VEHICLE_SEND.getOpStr())){
            CarActivityAbs activity = ActivityManager.getActivity(order.getSrc());
            activity.subStock(order,toUpdateProducts);
        }
        if (order.getPaySrc().contains("shangqidazhong") && "0.01".equals(String.valueOf(order.getCredit()))){
            SimpleDateFormat myFormatter = new SimpleDateFormat("yyyy-MM-dd");
            Calendar cal = Calendar.getInstance();
            cal.set(Calendar.DAY_OF_MONTH, 1);
            String startDate = myFormatter.format(cal.getTime());
            String loadKey = String.format(RedisKey.CARMUSIC_MINUTE_1_STOCK,order.getPaySrc(), startDate);
            if (RedisDAO.exists(loadKey)){
                RedisDAO.getInstance().incrTotalCntAndGetNew(loadKey, Long.valueOf(cnt));
            }
        }

        // 获取通知的地址,然后进行通知，记录返回结果
        try {
            if (messageModel.getCode() == SystemCodeErrorConstant.SUCCESS.getCode()){
                String url = thirdNotifyService.NOTIFY_URL;
                String md5key = thirdNotifyService.MD5KEY;
                // 加参数进行通知
                String src = order.getSrc();
                Long firstOrderId = order.getId();
                Short payType = order.getPayType();
                //判断是否为自动续费
                if (StringUtils.isNumeric(src)&&"autoTask".equals(order.getClientAct())){
                    // 返回first orderid
                    VehicleOrder firstOrderInfo = this.getFirstRenewalOriginOrderInfo(firstOrderId);
                    if (firstOrderInfo!=null){
                        firstOrderId = firstOrderInfo.getId();
                    }
                }
                VehicleOrderExtend orderExtend = vehicleOrderExtendMapper.getVehicleOrderExtendByOid(firstOrderId);
                long pid = order.getPid();
                VehicleAllExpireInfo vehicleInnerVipExpireTime = vehicleService.getVehicleInnerVipExpireTime(uid, pid);
                VehicleBusinessOrder businessOrder = vehicleBusinessOrderMapper.getBusinessOrderByOid(firstOrderId);
                Map<String, Object> params = new HashMap<>();
                String paySrc = order.getPaySrc();
                String op = "yes".equals(order.getAutoPay())?"renewal":"normal";
                params.put("uid",uid);
                params.put("pid",pid);
                params.put("orderId",order.getId());
                if (businessOrder!=null){
                    params.put("businessId",businessOrder.getBusinessId());
                }
                params.put("vehicleExpireTime", vehicleInnerVipExpireTime.getVipVehicleExpire());
                if (orderExtend!=null){
                    String developerPayload = orderExtend.getDeveloperPayload();
                    String vinCode = orderExtend.getVinCode();
                    if (StringUtils.isNotBlank(developerPayload)){
                        params.put("developerPayload", URLEncoder.encode(developerPayload,"UTF-8"));
                    }
                    if (StringUtils.isNotBlank(vinCode)){
                        params.put("vinCode", URLEncoder.encode(vinCode,"UTF-8"));
                    }
                }
                params.put("thirdOrderId",order.getThirdOrderId());
                params.put("credit",order.getCredit());
                params.put("status",1);
                params.put("op",op);
                params.put("ts",System.currentTimeMillis());
                params.put("paySrc",paySrc);
                params.put("payType",payType);
                params.put("nonce", RandomStringUtils.randomAlphanumeric(6));
                params.put("duration", days);
                String notifyParams = HttpUtil2.generateNotifyUrl(url,params,md5key);
                String respone = HttpUtil2.postHttpUrlContent(notifyParams, "",1000 * 5, 3);
                logger.info("vehicle notify notify, paySrc: "+ paySrc+"  -> "+notifyParams + " | result -> " + respone);
                Date now = new Date();
                VehicleNotify vehicleNotify = new VehicleNotify();
                vehicleNotify.setCreate_time(now);
                vehicleNotify.setUpdate_time(now);
                vehicleNotify.setExt1(paySrc);
                vehicleNotify.setExec_num(1);
                vehicleNotify.setStatus(0);
                vehicleNotify.setNotify_params(notifyParams);
                vehicleNotify.setNotify_result("");
                vehicleNotifyMapper.save(vehicleNotify);
                if(StringUtils.isNotBlank(respone)){
                    JSONObject jsonObject = JSONObject.parseObject(respone);
                    String code = String.valueOf(jsonObject.get("code"));
                    //然后记录通知记录入库处理
                    if (200==Integer.parseInt(code)){
                        vehicleNotifyMapper.updateNotify(vehicleNotify.getId(), code,1);
                    }else {
                        vehicleNotifyMapper.updateNotify(vehicleNotify.getId(), code,0);
                    }
                }
            }

        } catch (Exception e) {
            logger.error("pay notify exception ,maybe this paysrc not have notify url ");
            // 这块儿还不能进行抛出异常，不然的话影响老的业务
        }

        return messageModel;
    }

    /**
     * 更新购买记录
     *
     * @param order
     */
    private void updateBuyRecord(VehicleOrder order){
        try{
            if(order!=null&&order.getStatus()==1){
                Long buyUserId =  order.getUserId() > 0 ?  order.getUserId() : order.getPid();
                // 购买记录
                RedisDAO.getInstance().addString(VehicleConstant.VEHICLE_BUY_RECORD+buyUserId,String.valueOf(order.getId()),5*60);
                if(order.getSrc().equals(VehicleConstant.VEHICLE_ACTIVITY_2023)){
                    RedisDAO.getInstance().addString(VehicleConstant.VEHICLE_ACTIVITY_2023+buyUserId,String.valueOf(order.getId()),60*60*24*30);
                }
                if(order.getSrc().equals(VehicleConstant.VEHICLE_ACTIVITY_2023_51)){
                    RedisDAO.getInstance().addString(VehicleConstant.VEHICLE_ACTIVITY_2023_51+buyUserId,String.valueOf(order.getId()),60*60*24*30);
                }
            }
        }catch (Exception e){
            logger.error("updateBuyRecord has error ,order_id={}",order.getId(),e);
        }
    }

    /**
     * 公用购买vip时处理product的时间
     *
     * @param product, duration, tsNow, uid, virtualUid, productType
     * @return cn.kuwo.vip1.entity.master.VehicleProduct
     * <AUTHOR>
     * @date 2019/8/26 18:43
     */
    public VehicleProduct vehicleProcessProduct(VehicleProduct product, short duration, Timestamp tsNow, Long uid, Long virtualUid, ProductEnum productType) {
        List<VehicleProduct> formerProducts = vehicleProductMapper.getLimitByPtypeIdAndUidOrVid(uid, virtualUid, OrderStatusEnum.PAIED.getStatus(), productType.getId());
        if (null == formerProducts || formerProducts.isEmpty()) {
            product.setStartDate(tsNow);
            product.setExpireDate(TimeUtils.addDays(tsNow, duration));
        } else {
            VehicleProduct formerProduct = formerProducts.get(0);
            Timestamp expireTs = formerProduct.getExpireDate();
            //如果vip到期时间在现在之后，则不修改vip开始时间 直接延长到期时间
            //如果VIP到期时间在现在之前，则设置现在为开始时间，相当于开通
            if (expireTs.before(tsNow)) {
                product.setStartDate(tsNow);
                product.setExpireDate(TimeUtils.addDays(tsNow, duration));
            } else {
                product.setStartDate(expireTs);
                product.setExpireDate(TimeUtils.addDays(expireTs, duration));
            }
        }
        return product;
    }


    /**
     * 校验uid和virtualUid
     *
     * @param uid
     * @param virtualUid
     * @return
     */
    public boolean checkQueryUid(String uid, String virtualUid) {
        boolean allow = true;

        if (StringUtils.isNotEmpty(uid) && StringUtils.isNotEmpty(virtualUid)) {
            allow = false;
        }

        if (StringUtils.isEmpty(uid) && StringUtils.isEmpty(virtualUid)) {
            allow = false;
        }

        Long uuid = 0L;
        Long vvid = 0L;
        try {
            if (StringUtils.isNotEmpty(uid)) {
                uuid = Long.parseLong(uid);
            }
            if (StringUtils.isNotEmpty(virtualUid)) {
                vvid = Long.parseLong(virtualUid);
            }
        } catch (Exception e) {
            logger.error("用户id参数转换异常", e);
            return false;
        }


        if (StringUtils.isNotEmpty(uid) && uuid < 1) {
            allow = false;
        }
        if (StringUtils.isNotEmpty(virtualUid) && vvid < 1) {
            allow = false;
        }

        return allow;
    }


    /**
     * 用户合并，校验用户id和虚拟id
     *
     * @param uid
     * @param virtualUid
     * @return boolean
     * <AUTHOR>
     * @date 2018/11/7 17:21
     */
    public boolean mergeCheckUidAndVid(String uid, String virtualUid) {
        boolean allow = true;
        if (StringUtils.isEmpty(uid) || StringUtils.isEmpty(virtualUid)) {
            allow = false;
        }

        Long uuid = 0L;
        Long vvid = 0L;

        try {
            if (StringUtils.isNotEmpty(uid)) {
                Long.parseLong(uid);
            }
            if (StringUtils.isNotEmpty(virtualUid)) {
                Long.parseLong(virtualUid);
            }
        } catch (Exception e) {
            logger.error("用户id参数转换异常", e);
            return false;
        }


        return allow;

    }


    /**
     * @param uid        用户id
     * @param virtualUid 虚拟id
     * @description: 从vip服务中查询用户自动续费状态
     * @return: cn.kuwo.vip1.util.common.constants.MessageModel
     * @author: <EMAIL>
     * @date: 2018/9/28 10:22
     */
    public MessageModel getAutoPayStatusFromVip(String uid, Long virtualUid) {
        MessageModel messageModel = new MessageModel();
        String url = "http://" + vipDomain + ThirdPartConstant.VIP_AUTOPAY_URI;
        String result = "";
        try {
            Map<String, Object> map = new HashMap<>(3);
            if (StringUtils.isNotEmpty(uid)) {
                map.put("uid", uid);
                map.put("op", ThirdPartConstant.VIP_AUTOPAY_UID_OPTION);
            } else if (virtualUid > 0) {
                map.put("virtualUid", virtualUid);
                map.put("op", ThirdPartConstant.VIP_AUTOPAY_VID_OPTION);
            }

            /**调用vip服务*/
            logger.info("调用vip服务查询用户自动续费信息，url:{}, map:{}", url, JsonUtil.map2Json(map));
            result = MyHttpUtil.doPostRequest(url, map);
            logger.info("调用vip服务查询用户自动续费信息，返回值： result: {} ", result);
            JSONObject jsonObject = JSONObject.parseObject(result);
            Map<String, Object> mapMeta = (Map) jsonObject.get("meta");
            Map<String, Object> mapData = (Map) jsonObject.get("data");
            if (mapMeta.get("code").equals(SystemCodeErrorConstant.SUCCESS.getCode())) {
                messageModel.setData(mapData);
            } else {
                logger.error("调用vip服务,查询自动续费信息不成功,返回值为: {}", JsonUtil.map2Json(mapMeta));
                messageModel.setSystemErrorCode(SystemCodeErrorConstant.QUERY_FAIL);
            }
        } catch (Exception e) {
            logger.error("查询vip服务异常: result:{}", result, e);
            messageModel.setSystemErrorCode(SystemCodeErrorConstant.INVOKING_EXCEPTION);
        }
        return messageModel;
    }


    /**
     * @Description: 检查价格是否符合
     * @Param: [payinfo, uid, virtualUid, orderId]
     * @return: java.lang.String
     * @Author: <EMAIL>
     * @Date: 2018/9/27 18:42
     */
    public MessageModel checkPrice(PayInfoDTO payinfo, Long uid, Long virtualUid, Long orderId, PayOrderDTO payOrderDTO,Integer fromType,OrderReqEntity orderReq) {
        logger.info("VehicleService-checkPrice-func5  进入检查价格方法, 入参: payinfo:{},uid:{}, virtualUid:{},orderId:{}", JsonUtil.bean2Json(payinfo), uid, virtualUid, orderId);
        MessageModel model = new MessageModel();
        String sparam = "";

        List<VehicleProduct> savers = new ArrayList();
        for (Product product : payinfo.getProducts()) {
            Long productTypeId = product.getProductTypeId();
            ProductEnum productType = ProductEnum.getProductType(productTypeId.intValue());
            double normalPrice = -1;
            boolean checkStock = true;
            /**如果用户购买的是车载vip*/
            if (CarActivitySuperEnum.VIP_UPGRADE_VEHICLE.getOpStr().equals(payinfo.getSrc())) {
                if (product.getDuration() != null)
                    normalPrice = product.getDuration() * 0.2;
            } else if (ProductTypeCategory.VEHICLE_VIP.equals(productType.getCategory()) || ProductTypeCategory.VIP.equals(productType.getCategory())) {
                //购买monthCnt个月
                short monthCnt = product.getCnt();
                normalPrice = productType.getSpecial(product.getCnt(), payinfo.getPlatform());
                //车载的价格在此计算
                try {
                    ActivityParams activityParams = new ActivityParams();
                    activityParams.setType(payinfo.getSrc());
                    activityParams.setAutoPay(payinfo.getAutoPay());
                    activityParams.setUid(uid);
                    activityParams.setVirtualUid(virtualUid);
                    activityParams.setPaySrc(payinfo.getPaySrc());
                    activityParams.setCnt((int) monthCnt);
                    activityParams.setRequestCredit(payinfo.getCredit());
                    activityParams.setCurrentDate(new Date());
                    activityParams.setProduct(product);
                    activityParams.setProductTypeId(productTypeId);
                    activityParams.setCredit(payinfo.getCredit());
                    activityParams.setPlatform(payinfo.getPlatform());
                    activityParams.setSrc(payinfo.getSrc());
                    activityParams.setFromType(fromType);
                    activityParams.setFilterId(orderReq.getFilterId());
                    if(payOrderDTO!=null&&StringUtils.isNotBlank(payOrderDTO.getCouponUniqueId())){
                        activityParams.setCouponUniqueId(payOrderDTO.getCouponUniqueId());
                    }
                    String activityCode = payinfo.getSrc();
                    CarActivityAbs activity = ActivityManager.getActivity(activityCode);
                    if (activity == null || !activity.validateInPeriod(activityParams)){
                        logger.error("价格校验失败,未找到对应的活动type类,uid:{}, virtualUid:{},src:{}", uid, virtualUid, activityCode);
                        return new MessageModel(SystemCodeErrorConstant.PRICE_CHECK_ERROR);
                    }
                    // 校验下src是否适配
                    if (!activity.validateSrc(activityParams)){
                        logger.error("非法src, src适配产品错误,uid:{}, virtualUid:{},src:{}", uid, virtualUid, activityCode);
                        return new MessageModel(SystemCodeErrorConstant.PARAM_CHECK_ERROR);
                    }
                    product = activity.updateProductInfoBeforeCreateProduct(product, activityParams);
                    normalPrice = activity.getfinalPrice(activityParams);
                    checkStock=activity.checkStock(activityParams);
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("VehicleService-checkPrice-func5  获取车载产品价格异常，uid:{}, virtualUid:{}  payinfo.getSrc() = {}", uid, virtualUid, payinfo.getSrc(), e);
                }

            } else {
                //如果用户购买的是单曲或专辑  后续可能购买小说等
                String rid = product.getId();
                //专辑不再参与重复购买验证
                if (ProductTypeCategory.ALBUM.getId() != productTypeId.intValue()) {
                    List<VehicleProduct> ups = null;
                    try {
                        //如果uid>0,则判断是正常登陆用户购买，否则，则判断为虚拟用户购买
                        if (uid > 0) {
                            ups = vehicleProductMapper.getUserProductsByUidAndProductId(Long.valueOf(uid), new Date(), OrderStatusEnum.PAIED.getStatus(), rid);
                        } else {
                            ups = vehicleProductMapper.getUserProductsByVirtualUidAndProductId(virtualUid, new Date(), OrderStatusEnum.PAIED.getStatus(), rid);
                        }

                    } catch (Exception e) {
                        logger.error("VehicleService-checkPrice-func5  购买专辑获取用户以往购买信息错误，uid:{}, virtualUid:{}  payinfo.getSrc() = {}", uid, virtualUid, payinfo.getSrc(), e);
                    }

                    if (null != ups && ups.size() > 0) {
                        logger.error("VehicleService-checkPrice-func5  用户已经购买过此产品，uid:{}, virtualUid:{}  payinfo.getSrc() = {}", uid, virtualUid, payinfo.getSrc());
                        return new MessageModel(SystemCodeErrorConstant.PRODUCT_ALREADY_EXIST_ERROR);
                    }
                }


                //买专辑  rid 都没有带 ablum_的前缀， 所以都要加上
                if (ProductTypeCategory.ALBUM.getId() == product.getProductTypeId()) {
                    if (!rid.toUpperCase().startsWith("ALBUM_")) {
                        rid = "ALBUM_" + rid;
                    }
                }

                int cnt = (null == product.getCnt()) ? 0 : product.getCnt();
                for (int i = 0; i < cnt; i++) {
                    sparam += String.format("(%s,%s)", rid, product.getPid());
                }
            }
            if (StringUtils.isNotEmpty(sparam)) {
                String url = albumCheckUrl;
                String postbody = String.format("op=check&data=%s", sparam);
                logger.info("order_info  2.1={}------------",url);
                String result = HttpUtil.getHttpUrlContent(url, "utf-8", true, postbody, 1500);
                logger.info("VehicleService-checkPrice-func5 购买专辑获取远程价格 url=> " + url + "?" + postbody + "  返回值: " + result);

                Map minfo = JsonUtil.json2Map(result);
                if (null != minfo && "ok".equalsIgnoreCase((String) minfo.get("result"))) {
                    String pString = String.valueOf(minfo.get("price"));
                    normalPrice = MyNumberUtils.toDouble(pString);
                }

            }

            //TODO 金额校验，正式环境需放开此校验
            List<String> ignoreChannels = Arrays.asList("shangqidazhong");
            boolean toCheck = true;
//            for (String channel:ignoreChannels){
//                if(payinfo.getPaySrc().contains(channel)){
//                    toCheck = false;
//                    break;
//                }
//            }
            if(toCheck){
                logger.info("VehicleService-checkPrice info ------------normalPrice={}  credit={}",normalPrice,payinfo.getCredit());
                if (Math.abs(normalPrice - payinfo.getCredit()) >= 1 || normalPrice == -1) {
                    CarMonitor.checkAndSendMsg(carMonitor.getVEHICLE_ORDER_PRICE_CHECK_ERROR(),
                            "vehicle_order_price_check_error",10,
                            "vehicle_order_service 最近一小时下单金额check错误数：",
                            TimeUnit.HOURS,"刘杰");
                    logger.error("VehicleService-checkPrice check failed error");
                    return new MessageModel(SystemCodeErrorConstant.CAR_PRICE_ERROR);
                }
            }
            logger.info("order_info  4------------");
            // 检查库存
            if(!checkStock){
                return new MessageModel(SystemCodeErrorConstant.VEPHONE_GEAR_LIMIT);
            }



            /** 保存vehicle_product */
            VehicleProduct vehicleProduct = new VehicleProduct();
            vehicleProduct.setUserId(uid);
            vehicleProduct.setPrice(product.getPrice());
            vehicleProduct.setProductId(product.getId());
            vehicleProduct.setProductTypeId(product.getProductTypeId());
            vehicleProduct.setOrderId(orderId);
            vehicleProduct.setDuration(product.getDuration());
            vehicleProduct.setCnt(product.getCnt());
            vehicleProduct.setStatus(OrderStatusEnum.CREATE.getStatus());
            vehicleProduct.setVirtualUid(virtualUid);
            // 曹操出行日卡周卡
            SchemeConfigBO schemeConfig= LogTraceContextHolder.getSrcConfig();
            if(StringUtils.equals(payinfo.getPaySrc(),"C_APK_CaoCao_h5")&&schemeConfig!=null){
                if(schemeConfig.getAddDay()>0&&schemeConfig.getHasFirstMonth()==0){
                    vehicleProduct.setCnt((short) 0);
                    vehicleProduct.setDuration((short) schemeConfig.getAddDay());
                }
            }
            // 0元签约
            if(StringUtils.equals(payinfo.getSrc(),"c_ar_calculateMonth0_7_19.9")&&schemeConfig!=null){
                if(schemeConfig.getAddDay()>0&&schemeConfig.getHasFirstMonth()==0){
                    vehicleProduct.setCnt((short) 0);
                    vehicleProduct.setDuration((short) schemeConfig.getAddDay());
                }

            } else if (schemeConfig!=null&&(payinfo.getSrc().startsWith("car_ar_retain_")||payinfo.getSrc().startsWith("car_activity_")||payinfo.getSrc().startsWith("car_guide_"))){
                if(schemeConfig.getAddDay()>0){
                    logger.info("car_retain: add days schemeConfig day={}",schemeConfig.getAddDay());
                    vehicleProduct.setDuration((short) ((short) schemeConfig.getAddDay()+vehicleProduct.getDuration()));
                }
            }
            // 加赠
            else if(carConfigNacos.getCarConfigBO()!=null&&carConfigNacos.getCarConfigBO().getAddDaysSrc()!=null&&carConfigNacos.getCarConfigBO().getAddDaysSrc().contains(payinfo.getSrc())&&schemeConfig!=null){
                if(schemeConfig.getAddDay()>0){
                    vehicleProduct.setDuration((short) ((short) schemeConfig.getAddDay()+vehicleProduct.getDuration()));
                }
            }
            savers.add(vehicleProduct);
        }
        model.setData(savers);
        logger.info("VehicleService-checkPrice-func5  检查价格方法结束, uid:{}, virtualUid:{},返回{}", uid, virtualUid, JsonUtil.bean2Json(model));
        return model;
    }


    /**
     * @Description: 合并业务更新相关单据
     * @Param: [listProduct, virId, userId, mergeDate]
     * @return: cn.kuwo.vip1.util.common.constants.MessageModel
     * @Author: <EMAIL>
     * @Date: 2018/10/17 11:18
     */
    @Transactional(value = "masterTransactionManager", rollbackFor = Exception.class)
    public void updateMerge(List<VehicleProduct> listProduct, long virId, long userId, Date mergeDate) {
        if (listProduct.size() > 0) {
            vehicleProductMapper.updateBatch(listProduct);
        }
        vehicleOrderMapper.updateVidOrderToUidOrder(virId, userId, mergeDate);
        vehicleProductMapper.updateUserProductForMergerVirtualUser(virId, userId, mergeDate);
    }


    /**
     * @Description: 查询订单是否已经存在
     * @Param: [businessId]
     * @return: boolean
     * @Author: <EMAIL>
     * @Date: 2018/10/17 17:45
     */
    public boolean checkOrderAlreadyIsExists(String businessId, String paySrc) {
        boolean isExists = true;
        VehicleBusinessOrder order = vehicleBusinessOrderMapper.getOrderById(businessId, paySrc);
        if (null == order) {
            isExists = false;
        }
        return isExists;
    }

    /**
     * @Description: 兑换码开通车载vip操作订单
     * @Param: [userId, vipTypeId, days, arc]
     * @return: cn.kuwo.vip1.entity.master.VehicleOrder
     * @Author: <EMAIL>
     * @Date: 2018/10/10 9:54
     */
    @Transactional(value = "masterTransactionManager", propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = false)
    public VehicleOrder codeExchangeVip(String userId, String src, VehicleSerialCode serialCode, String platVersion) throws Exception {
        Timestamp now = TimeUtils.getCurrentTime();
        long userid = Long.parseLong(userId);
        int days = serialCode.getDays();
        String vipTypeId = serialCode.getProductTypeId();
        int viptypeId = MyNumberUtils.toInt(vipTypeId);
        //7天体验会员类型，需要判断车载会员是否在期
        if (viptypeId == ProductEnum.VIP_7DAYS.getId()) {
            List<QueryExpireRes> resList = vehicleProductMapper.getAllExpire(userid, 0L);
            Timestamp vehicleExpire = null;
            if (resList != null && resList.size() > 0) {
                for (QueryExpireRes queryExpireRes : resList) {
                    if (ProductEnum.VIP_VEHICLE.getId() == queryExpireRes.getProductType().longValue()) {
                        vehicleExpire = new Timestamp(queryExpireRes.getExpire().getTime());
                    }
                }
            }

            if (vehicleExpire != null && vehicleExpire.after(now)) {
                throw new RuntimeException("会员在期，无法开通7天体验会员！");
            }
        }

        VehicleOrder vehicleOrder = saveOrderAndProduct(userId, vipTypeId, days, src, serialCode.getChannel(), platVersion);

        //兑换码更新兑换码状态并调用vip赠送;
        serialCode.setOrderInfoId(vehicleOrder.getId());
        serialCode.setUsed(true);
        serialCode.setUpdateTime(new Date());
        serialCode.setCreator(userId);
        serialCodeMapper.updateCode(serialCode);
        logger.info("使用兑换码 : 订单id: {}, userId : {} ", vehicleOrder.getId(), userId);
        invokingVipSend(viptypeId, days, vehicleOrder, userid, -1,null);
        return vehicleOrder;
    }

    /**
     * 保存订单和产品列表
     *
     * @param userId
     * @param vipTypeId   产品id,车载是17
     * @param days
     * @param src
     * @param chanel      渠道
     * @param platVersion
     * @return
     */
    public VehicleOrder saveOrderAndProduct(String userId, String vipTypeId, int days, String src, String chanel, String platVersion) {
        long userid = Long.parseLong(userId);
        int viptypeId = MyNumberUtils.toInt(vipTypeId);
        ProductEnum productType = ProductEnum.getProductType(viptypeId);

        Timestamp now = TimeUtils.getCurrentTime();
        Timestamp startDateT = now;
        Timestamp expireDateT = TimeUtils.addDays(startDateT, days);
        List<VehicleProduct> objects = new ArrayList<>();
        List<ProductEnum> productTypes = new ArrayList<>();
        List<VehicleProduct> userProducts = this.getUserVipProducts(userId);

        String autoPay = "no";
        VehicleOrder vehicleOrder = new VehicleOrder();
        vehicleOrder.setUserId(userid);
        vehicleOrder.setPid(userid);
        vehicleOrder.setCredit(0d);
        vehicleOrder.setTime(now);
        vehicleOrder.setPayDate(null);
        vehicleOrder.setPayType((short) 0);
        vehicleOrder.setStatus(OrderStatusEnum.FINISH.getStatus());
        vehicleOrder.setPlatform("");
        vehicleOrder.setSrc(src);
        vehicleOrder.setClientAct("");
        vehicleOrder.setAutoPay(autoPay);
        vehicleOrder.setPlatVersion(platVersion);
        vehicleOrder.setPaySrc(chanel);
        vehicleOrderMapper.insert(vehicleOrder);
        Long orderId = vehicleOrder.getId();
        //符合类型处理
        productTypes.add(productType);
        for (ProductEnum pt : productTypes) {
            viptypeId = pt.getId();
            if (null != userProducts && !userProducts.isEmpty()) {
                for (VehicleProduct userProduct : userProducts) {
                    long vtId = userProduct.getProductTypeId();
                    if (viptypeId == vtId) {
                        if (userProduct.getExpireDate().after(now)) {
                            startDateT = userProduct.getExpireDate();
                            expireDateT = TimeUtils.addDays(startDateT, days);
                        } else {
                            expireDateT = TimeUtils.addDays(now, days);
                        }
                    }
                }
            }

            VehicleProduct vehicleProduct = new VehicleProduct();
            vehicleProduct.setUserId(userid);
            vehicleProduct.setPrice(0d);
            vehicleProduct.setProductId(String.valueOf(viptypeId));
            vehicleProduct.setProductTypeId(Long.valueOf(viptypeId));
            vehicleProduct.setOrderId(orderId);
            vehicleProduct.setBuyDate(now);
            vehicleProduct.setStartDate(startDateT);
            vehicleProduct.setExpireDate(expireDateT);
            vehicleProduct.setDuration((short) days);
            short cnt = (short) (days / 31);
            vehicleProduct.setCnt(cnt);
            vehicleProduct.setStatus(OrderStatusEnum.PAIED.getStatus());
            objects.add(vehicleProduct);
        }
        vehicleProductMapper.insertBatch(objects);
        return vehicleOrder;
    }


    /**
     * 兑换码更新兑换码状态并调用vip赠送
     *
     * @param productTypeId
     * @param days
     * @param orderInfo
     * @param uid
     * @return void
     * <AUTHOR>
     * @date 2018/11/2 14:54
     */
    public void invokingVipSend(int productTypeId, int days, VehicleOrder orderInfo, long uid, long virtualUid ,VipUpdatePriceEnum vipUpdatePriceEnum) throws Exception {
        if (productTypeId != ProductEnum.VIP_VEHICLE.getId()
                && productTypeId != ProductEnum.VIP_7DAYS.getId()
                && productTypeId != ProductEnum.VIP_TV.getId()
                && productTypeId !=ProductEnum.VIP_MUSIC.getId()
                && productTypeId !=ProductEnum.SUPER_VIP.getId()) {
            return;
        }
        Map<String, Object> map = new HashMap<>(14);
        map.put("businessOrderId", orderInfo.getId());
        map.put("channel", productTypeId == ProductEnum.VIP_TV.getId() ? "tv" : "cheZai");
        map.put("days", days);
        if (uid > 0) {
            map.put("uid", uid);
            map.put("service", "getVip");
        } else {
            map.put("virtualUid", virtualUid);
            map.put("service", "getVipForVirtualUser");
        }
        String product = "vipm";
        if (productTypeId == ProductEnum.VIP_7DAYS.getId()){
            product = "vip7Exp";
        }
        if (productTypeId == ProductEnum.SUPER_VIP.getId()){
            product = "svipForVehicle";
        }
        if (vipUpdatePriceEnum!=null&&productTypeId == ProductEnum.SUPER_VIP.getId()){
            product = vipUpdatePriceEnum.getProductName();
        }
        map.put("product", product);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        map.put("timestamp", sdf.format(new Date()));
        MessageModel messageModel = invokingRemoteService.invokingVipSend(map);
        logger.info("兑换码调用远程赠送音乐包参数, params = {} result ={}", map, JsonUtil.bean2Json(messageModel));
        // 或者7002  重复的订单号 .这个也算为成功
        if (!( messageModel.getCode().equals(SystemCodeErrorConstant.SUCCESS.getCode())
                || messageModel.getCode().equals(7002))) {
            logger.error("兑换码调用远程赠送音乐包失败, messageModel ={}", JsonUtil.bean2Json(messageModel));
            throw new VehicleException("兑换码赠送音乐包失败");
        }
    }


    /**
     * 调用vip赠送音乐包
     *
     * @param product
     * @param orderInfo
     * @param uidStr
     * @return void
     * <AUTHOR>
     * @date 2018/11/2 14:54
     */
    public void invokingVipSend(VehicleProduct product, VehicleOrder orderInfo, String uidStr) throws Exception {
        if (product.getProductTypeId().toString().equals(String.valueOf(ProductEnum.VIP_VEHICLE.getId()))) {
            Map<String, Object> map = new HashMap<>(14);
            map.put("businessOrderId", orderInfo.getId());
            map.put("channel", "cheZai");
            map.put("days", product.getDuration());
            map.put("uid", uidStr);
            map.put("product", "vipm");
            map.put("service", "getVip");
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            map.put("timestamp", sdf.format(new Date()));
            MessageModel messageModel = invokingRemoteService.invokingVipSend(map);
            if (!( messageModel.getCode().equals(SystemCodeErrorConstant.SUCCESS.getCode())
                    || messageModel.getCode().equals(7002)) ) {
                logger.error("调用远程赠送音乐包失败, messageModel ={}", JsonUtil.bean2Json(messageModel));
                throw new VehicleException("赠送音乐包失败");
            }
        }
    }

    public Map<String, Object> invokingVipGetOrderInfo(VehicleOrder orderInfo) {
        try {
            Map<String, Object> map = new HashMap<>(14);
            map.put("businessOrderId", orderInfo.getId());
            map.put("channel", "cheZai");
            map.put("service", "searchOrderDetail");
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            map.put("timestamp", sdf.format(new Date()));
            String url = "http://" + vipDomain + ThirdPartConstant.VIP_SEND_URI;
            List<String> keyList = new ArrayList<>(map.keySet());
            Collections.sort(keyList);
            StringBuffer sb = new StringBuffer();
            for (int i = 0; i < keyList.size(); i++) {
                String key = keyList.get(i);
                Object value = map.get(key);
                sb.append(key + "=" + value + "&");
            }
            String unSignStr = sb.toString() + "key=" + ThirdPartConstant.VIP_SEND_KEY;
            String signStr = MD5.getMD5ofStr(unSignStr).toUpperCase();
            logger.info("加密前串：unSignStr={}, 加密后的串: signStr={}", unSignStr, signStr);
            map.put("sign", signStr);
            logger.info("调用vip服务查询订单数据， url:{}, map:{}", url, map);
            String result = MyHttpUtil.doPostRequest(url, map);
            logger.info("调用vip服务查询订单数据，传参: {}  返回值： result: {} ", map, result);
            JSONObject jsonObject = JSONObject.parseObject(result);
            if (null == jsonObject) {
                logger.error("调用vip服务查询订单数据,参数:{}", map);
                return new HashMap<>();
            }
            Map<String, Object> mapMeta = (Map) jsonObject.get("meta");
            Map<String, Object> mapData = (Map) jsonObject.get("data");
            if (mapMeta.get("code").equals(SystemCodeErrorConstant.SUCCESS.getCode())  && "0".equals(String.valueOf(mapData.get("code")))){
                return mapData;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return new HashMap<>();
        }
        return new HashMap<>();
    }


    /**
     * 获取用户购买过的vip vip1 vip2 vip4 只显示一条 去时间最大的
     *
     * @param uid
     * @return
     * @throws NumberFormatException
     * @throws Exception
     * <AUTHOR> Wanyu
     */
    public List<VehicleProduct> getUserVipProducts(String uid) {
        if(StringUtils.equals(uid,"0")){
            logger.error(String.format("getUserProducts userId=0, uid = %s", uid));
            return null;
        }
        return getUserProducts(uid);
    }

    /**
     *
     * 获取用户购买商品信息
     * @param uid
     * @return
     * @throws NumberFormatException
     * @throws Exception
     */
    public List<VehicleProduct> getUserProducts(String uid) {
        if(StringUtils.equals(uid,"0")){
            logger.error(String.format("getUserProducts userId=0, uid = %s", uid));
            return null;
        }
        List<VehicleProduct> userProductList = vehicleProductMapper.getUserProductByTime(Long.valueOf(uid), new Date(), OrderStatusEnum.PAIED.getStatus());
        if (userProductList == null || userProductList.isEmpty()) {
            logger.warn(String.format("查询用户购买的过期日期大于今天的产品为 Null, uid = %s", uid));
        }
        return userProductList;
    }


    /**
     * 获取用户VIP商品信息
     *
     * @param uid
     * @return
     * @throws NumberFormatException
     * @throws Exception
     */
    public List<UserProduct> getVipProductsByUid(String uid) {

        List<UserProduct> userProductList = userProductMapper.getUserProductsByTime(Long.valueOf(uid), new Date(), OrderStatusEnum.PAIED.getStatus());
        if (userProductList == null || userProductList.isEmpty()) {
            logger.warn(String.format("查询VIP-UserProduct 查询用户购买的过期日期大于今天的产品为 Null, uid = {}", uid));
        }
        return userProductList;
    }

    /**
     * 获取虚拟id购买的VIP商品信息
     *
     * @param virtualUid
     * @return
     * @throws NumberFormatException
     * @throws Exception
     */
    public List<UserProduct> getVirtualUserProductsByTime(String virtualUid) {
        List<UserProduct> userProductList = userProductMapper.getVirtualUserProductsByTime(Long.valueOf(virtualUid), new Date(), OrderStatusEnum.PAIED.getStatus());
        if (userProductList == null || userProductList.isEmpty()) {
            logger.warn(String.format("查询VIP-UserProduct 查询虚拟用户购买的过期日期大于今天的产品为 Null, uid = {}", virtualUid));
        }
        return userProductList;
    }


    /**
     * 操作订单取消并退款
     *
     * @param orderId
     * @return cn.kuwo.vip1.util.common.constants.MessageModel
     * <AUTHOR>
     * @date 2018/12/17 20:25
     */
    @Transactional(value = "masterTransactionManager", rollbackFor = Exception.class)
    public MessageModel doCancelOrder(Long orderId,String textfield) throws Exception {

        VehicleOrder vehicleOrder = vehicleOrderMapper.getInfoById(orderId);
        if (OrderStatusEnum.CREATE.getStatus() == vehicleOrder.getStatus()) {
            return new MessageModel(SystemCodeErrorConstant.PARAM_CHECK_ERROR);
        }

        List<VehicleProduct> vps = vehicleProductMapper.getProductsByOrderId(orderId);
        if (vps.size() > 5) {
            return new MessageModel(SystemCodeErrorConstant.CANCEL_OPTION_NOT_ALLOW);
        }
        for (VehicleProduct vp : vps) {
            int productTypeId = vp.getProductTypeId().intValue();
            if (productTypeId != ProductEnum.VIP_VEHICLE.getId()) {
                return new MessageModel(SystemCodeErrorConstant.PRODUCT_NOT_ALLOW_CANCLE);
            }
        }

        List<VehicleOrder> dbVo = new ArrayList<>();
        List<VehicleProduct> dbVp = new ArrayList<>();
        List<UpdateDataRecord> dbUdr = new ArrayList<>();

        //设置订单为已注销状态
        vehicleOrder.setStatus(OrderStatusEnum.CANCLE.getStatus());
        vehicleOrder.setSrc("CancelOrder");
        vehicleOrder.setClientAct("admin");
        dbVo.add(vehicleOrder);
        Timestamp now = TimeUtils.getCurrentTime();
        Random r = new Random();
        for (VehicleProduct vehicleProduct : vps) {
            Timestamp expireDate = vehicleProduct.getExpireDate();
            if (expireDate.after(now)) {
                Timestamp newExpireTime = TimeUtils.addMins(now, 1);
                vehicleProduct.setExpireDate(newExpireTime);
                vehicleProduct.setStatus(OrderStatusEnum.CANCLE.getStatus());
                dbVp.add(vehicleProduct);

                UpdateDataRecord updateDataRecord = new UpdateDataRecord();
                updateDataRecord.setDataId(vehicleProduct.getId());
                updateDataRecord.setDataTableName("VEHICLE_PRODUCT");
                updateDataRecord.setDataColumnNames("EXPIRE_DATE");
                updateDataRecord.setDataColumnSourceValues(String.valueOf(expireDate.getTime()));
                updateDataRecord.setDescription("注销用户订单权限 textfield："+textfield);
                updateDataRecord.setCreatedTime(new Date());
                updateDataRecord.setUpdateTime(new Date());
                dbUdr.add(updateDataRecord);
            }

        }

        //1.更新订单表
        vehicleOrderMapper.updateBatch(dbVo);
        //2.更新产品表
        if (dbVp.size() > 0) {
            vehicleProductMapper.updateBatch(dbVp);
        }
        //3.插入记录表
        if (dbUdr.size() > 0) {
            updateDataRecordMapper.insertBatch(dbUdr);
        }
        return new MessageModel();
    }


    /**
     * 三方确认订单，检查参数
     *
     * @param orderId
     * @param credit
     * @param thirdOrderId
     * @return
     */
    public MessageModel checkThirdParam(String orderId, String credit, String thirdOrderId, String sign) {
        MessageModel model = new MessageModel();
        /**step-1:校验参数*/
        if (StringUtils.isEmpty(orderId) || StringUtils.isEmpty(credit) || StringUtils.isEmpty(thirdOrderId)) {
            return new MessageModel(SystemCodeErrorConstant.PARAM_CHECK_ERROR);
        }
        /**step-2:校验订单*/
        VehicleOrder order;
        try {
            order = vehicleOrderMapper.getOrderById(Long.parseLong(orderId));
        } catch (Exception e) {
            logger.error("订单编号转换错误", e);
            return new MessageModel(SystemCodeErrorConstant.ORDER_ID_ERROR);
        }
        if (null == order) {
            logger.error("没有找到对应订单，  订单id：{}", orderId);
            return new MessageModel(SystemCodeErrorConstant.NO_ORDER_FOUND_ERROR);
        }
        if (order.getStatus() != 0) {
            logger.error("订单已经被处理，订单id：{}", orderId);
            return new MessageModel(SystemCodeErrorConstant.ORDER_IS_ALREADY_PROCESSED);
        }
        /** 检查第三方订单是否重复处理 **/
        if(StringUtils.isNotBlank(order.getPaySrc())&&thirdOrderId.length()>=10){
            VehicleBusinessOrder businessOrder = vehicleBusinessOrderMapper.getOrderById(thirdOrderId, order.getPaySrc());
            if (businessOrder!=null) {
                logger.error("第三方订单重复处理，第三方订单thirdOrderId：{}", thirdOrderId);
                return new MessageModel(SystemCodeErrorConstant.THREE_ORDER_IS_ALREADY_PROCESSED);
            }
        }

        /**step-3:校验金额*/
        Double c1 = order.getCredit();
        Double c3 = Double.valueOf(credit);
        //TODO 金额校验，正式环境需放开此校验
        if (Math.abs(c1 - c3) >= 1) {
            logger.error("三方确认订单, 金额有误，查询出的订单金额：{}, 传入的金额：{}", c1, c3);
            return new MessageModel(SystemCodeErrorConstant.CREDIT_CHECK_ERROR);
        }

        /**若product为电视vip，校验签名*/
        List<VehicleProduct> products = vehicleProductMapper.getProductsByOrderId(order.getId());
        if (products == null || products.size() < 1) {
            return new MessageModel(SystemCodeErrorConstant.PRODUCT_DATA_ERRER);
        }
        for (VehicleProduct product : products) {
            ProductEnum productType = ProductEnum.getProductType(product.getProductTypeId().intValue());
            if (productType == ProductEnum.VIP_TV) {
                String beforeEncrypte = orderId + credit + thirdOrderId + "vip_TV";
                String afterEncrypte = Md5Encrypt.md5(beforeEncrypte);
                if (!afterEncrypte.equals(sign)) {
                    logger.error("产品包含电视vip,签名错误,加密前:{},加密后:{},传入签名:{}", beforeEncrypte, afterEncrypte, sign);
                    return new MessageModel(SystemCodeErrorConstant.VIP_TV_SIGN_ERROR);
                }
            }
        }
        order.setStatus(OrderStatusEnum.PAIED.getStatus());
        order.setPayDate(new Timestamp(System.currentTimeMillis()));
        order.setPayType(Short.parseShort("-1"));
        model.setData(order);
        return model;

    }


    /**
     * 三方确认订单，操作订单、产品及通知vip赠送
     *
     * @param order, orderKey
     * @return cn.kuwo.vip1.util.common.constants.MessageModel
     * <AUTHOR>
     * @date 2018/12/19 15:50
     */
    @Transactional(value = "masterTransactionManager", rollbackFor = Exception.class)
    public MessageModel thirdConfirm(VehicleOrder order, String orderKey, String thirdOrderId, String channelOrderId) throws Exception {

        //校验订单是否正在处理
        if (RedisDAO.exists(orderKey)) {
            return new MessageModel(SystemCodeErrorConstant.ORDER_IS_PROCECING_ERROR);
        }
        RedisDAO.getInstance().setObject(orderKey, order.getId(), 3600);
        logger.info("三方确认订单， 进入操作订单和用户产品表");

        MessageModel messageModel = new MessageModel();
        //更新用户购买商品表   1,查询订单对应的商品 2，查询该用户所有的该商品 3更新
        List<VehicleProduct> productsList = vehicleProductMapper.getProductsByOrderId(order.getId());
        if (productsList.size() < 1) {
            return new MessageModel(SystemCodeErrorConstant.PRODUCT_DATA_ERRER);
        }
        int productTypeId = 0;
        int days = 0;
        short cnt = 0;
        Long uid = order.getUserId();
        Long virtualUid = order.getPid();

        final Timestamp currentTime = TimeUtils.getCurrentTime();
        List<VehicleProduct> toUpdateProducts = new ArrayList<>();
        for (VehicleProduct product : productsList) {
            ProductEnum productType = ProductEnum.getProductType(product.getProductTypeId().intValue());
            if (productType == ProductEnum.VIP_VEHICLE || productType == ProductEnum.VIP_TV || productType == ProductEnum.SUPER_VIP) {
                final short duration = product.getDuration();
                product = vehicleProcessProduct(product, duration, currentTime, uid, virtualUid, productType);
                productTypeId = productType.getId();
                days = product.getDuration();
                cnt = product.getCnt();
            }

            product.setBuyDate(currentTime);
            product.setStatus(OrderStatusEnum.PAIED.getStatus());
            toUpdateProducts.add(product);
        }
        //更新订单
        order.setThirdOrderId(thirdOrderId);
        vehicleOrderMapper.updateOrder(order);
        //更新vehicle_product
        vehicleProductMapper.updateBatch(toUpdateProducts);
        //更新businessOrder表
        VehicleBusinessOrder vehicleBusinessOrder = vehicleBusinessOrderMapper.getBusinessOrderByOid(order.getId());
        vehicleBusinessOrder.setBusinessId(thirdOrderId);
        vehicleBusinessOrderMapper.updateSetBusId(vehicleBusinessOrder);

        if (productTypeId == ProductEnum.VIP_VEHICLE.getId() || productTypeId == ProductEnum.VIP_TV.getId() || productTypeId == ProductEnum.SUPER_VIP.getId()) {
            invokingVipSend(productTypeId, days, order, uid ,virtualUid,null);
        }

        // 车载上汽大众1分钱限制 优化  将已用数据放到redis 里进行判断
        if (order.getPaySrc().contains("shangqidazhong") && "0.01".equals(String.valueOf(order.getCredit()))){
            SimpleDateFormat myFormatter = new SimpleDateFormat("yyyy-MM-dd");
            Calendar cal = Calendar.getInstance();
            cal.set(Calendar.DAY_OF_MONTH, 1);
            String startDate = myFormatter.format(cal.getTime());
            String loadKey = String.format(RedisKey.CARMUSIC_MINUTE_1_STOCK, order.getPaySrc(),startDate);
            if (RedisDAO.exists(loadKey)){
                RedisDAO.getInstance().incrTotalCntAndGetNew(loadKey, Long.valueOf(cnt));
            }
        }

        return messageModel;
    }


    /**
     * 查看该笔订单是否是用户的最后一笔到期订单
     * 如果不是,修改待续费的订单的autoPay为transfer,修改最后一笔的订单的autoPay为yes
     * 并更新 自动续费信息
     * <p>
     * 取用户的userId 和orgOrderId
     */
    @Transactional(value = "masterTransactionManager", propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean isLastAutoPayOrder(AutoRenewalInfoVehicle arInfo) throws Exception {
        //获取最大到期日期的订单id
        long maxOrderId = getMaxExpireOrderId(arInfo.getUserId(), arInfo.getPid(), arInfo.getProductTypeId());
        if (maxOrderId == -1L) {
            logger.error("userId:{},Pid:{} 未找到用户订单列表", arInfo.getUserId(), arInfo.getPid());
            throw new VehicleException("未找到用户订单列表");
        }
        long orgOrderId = arInfo.getOrgOrderId();
        logger.info("userId:{},Pid:{} 看是不是最后一笔 maxOrderId:{}, orgOrderId:{} ", arInfo.getUserId(), arInfo.getPid(), maxOrderId, orgOrderId);
        if (maxOrderId != orgOrderId) {
            //如果不是最后一笔
            //更新最后一笔订单的信息
            vehicleOrderMapper.updateAutoPay(AutoRenewalConst.AUTO_PAY_YES, maxOrderId);
            vehicleOrderMapper.updateAutoPay(AutoRenewalConst.AUTO_PAY_TRANSFER, orgOrderId);
            logger.info("ST_A1C1_TransferInfo - orgOrderId:{}, transferTo:{},userId:{},Pid:{}", orgOrderId, maxOrderId, arInfo.getUserId(), arInfo.getPid());
            //进行自动续费信息的保存
            //这个id为转移的订单id
            arInfo.setOrderId(maxOrderId);
            arInfo.setRespCode(AutoRenewalConst.TRANSFER_CODE);
            arInfo.setRespDesc(AutoRenewalConst.AUTO_PAY_TRANSFER);
            arInfo.setOperInfo(AutoRenewalConst.AUTO_PAY_TRANSFER);
            arInfo.setCompleteTime(new Date());
            renewalMapper.insert(arInfo);
            return false;
        }
        return true;
    }

    /**
     * 查看该笔订单是否是用户的最后一笔到期订单
     * productTypeId 产品id
     * 合并之前用各自的,合并之后用用户id
     * 这里两者查询必须分开，续到虚拟id,合并后,后面有主动购买，用虚拟id进行查询就查不出来后续订单了   user_product表中有 虚拟id
     *
     * <AUTHOR> zeren
     */
    public Long getMaxExpireOrderId(long userId, long pid, long productTypeId) {
        List<Long> list = vehicleProductMapper.getMaxExpireOrderId(userId, pid, productTypeId);
        if (list.size() > 0) {
            Object maxOrderId = list.get(0);
            return MyNumberUtils.toLONG(maxOrderId);
        } else {
            return -1L;
        }
    }


    @Transactional
    public boolean giveVehicleMember(String uid, ProductEnum productEnum, int days, String src, String channel) throws Exception {
        VehicleOrder orderInfo = saveOrderAndProduct(uid, String.valueOf(productEnum.getId()), days, src, channel, "");
        invokingVipSend(productEnum.getId(), days, orderInfo, Long.parseLong(uid),-1,null);
        return true;
    }

    /**
     * 查询第一笔的续费订单
     * @param orderId
     * @return
     */
    public VehicleOrder  getFirstRenewalOriginOrderInfo(Long orderId){
        VehicleOrder orderInfo = vehicleOrderMapper.getOrderById(orderId);
        if (orderInfo!=null){
            Long pid = orderInfo.getPid();
            String lastOrderId = orderInfo.getSrc();
            if (NumberUtils.isNumber(lastOrderId)){
                // 查找第一笔订单
                return deepSearchFirstRenewalOrder(pid, lastOrderId);
            }
            // 不是数字说明 当前是第一笔订单
            return orderInfo;
        }
        return null;
    }

    public VehicleOrder deepSearchFirstRenewalOrder(Long pid, String lastOrderId){
        VehicleOrder orderInfo =  vehicleOrderMapper.getLastRenewalOrder(pid, lastOrderId);
        if (orderInfo!=null && orderInfo.getClientAct().equals("autoTask")){
            return deepSearchFirstRenewalOrder(pid, orderInfo.getSrc());
        }
        return orderInfo;
    }
}