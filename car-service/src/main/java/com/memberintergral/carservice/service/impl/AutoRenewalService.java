package com.memberintergral.carservice.service.impl;

import cn.hutool.json.JSONUtil;
import com.memberintergral.carservice.config.constant.AutoRenewalConst;
import com.memberintergral.carservice.config.constant.MessageModel;
import com.memberintergral.carservice.config.constant.SystemCodeErrorConstant;
import com.memberintergral.carservice.config.constant.ThirdPartConstant;
import com.memberintergral.carservice.config.enums.*;
import com.memberintergral.carservice.config.exception.VehicleException;
import com.memberintergral.carservice.domain.DTO.PayInfoDTO;
import com.memberintergral.carservice.domain.VO.IOSAutoPayVO;
import com.memberintergral.carservice.domain.entity.*;
import com.memberintergral.carservice.mapper.AutoRenewalInfoVehicleMapper;
import com.memberintergral.carservice.mapper.VehicleOrderMapper;
import com.memberintergral.carservice.mapper.VehicleProductMapper;
import com.memberintergral.carservice.util.JsonUtil;
import com.memberintergral.carservice.util.MyNumberUtils;
import com.memberintergral.carservice.util.TimeUtils;
import com.memberintergral.carservice.config.enums.*;
import com.memberintergral.carservice.domain.entity.*;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.*;

/**
 * @program: vip_adv
 * @description: 自动续费操作类
 * @author: <EMAIL>
 * @create: 2018-10-09 11:51
 */
@Service
public class AutoRenewalService {
    private Logger logger = LoggerFactory.getLogger(AutoRenewalService.class);

    @Autowired
    private AutoRenewalInfoVehicleMapper vehicleMapper;
    @Autowired
    private VehicleOrderMapper vehicleOrderMapper;
    @Autowired
    private VehicleProductMapper vehicleProductMapper;
    @Autowired
    private VehicleAssistant assistant;
    @Autowired
    private AutoPayOrderService autoPayOrderService;
    @Autowired
    private InvokingRemoteService invokingRemoteService;
    @Autowired
    private AutoRenewalInfoVehicleMapper renewalMapper;

    @Autowired
    private VehicleAssistant vehicleAssistant;

    /**
     * 成功回调，更新自动续费code和desc内容
     */
    public void updateCodeAndDescAfterPaySucc(Long userId, Long virtualUid, Long orderId) {
        List<AutoRenewalInfoVehicle> listInfo = vehicleMapper.getLastWaitAR(userId, virtualUid, orderId);
        if (listInfo.size() > 0) {
            AutoRenewalInfoVehicle arInfo = listInfo.get(0);
            arInfo.setRespCode(ThirdPartConstant.PAY_SUCCESS_CODE);
            arInfo.setRespDesc(ThirdPartConstant.PAY_SUCCESS_DESC);
            arInfo.setCompleteTime(new Date());
            vehicleMapper.updateCodeAndDesc(arInfo.getRespCode(), arInfo.getRespDesc(), arInfo.getCompleteTime(), arInfo.getId());
        }

    }


    /**
     * @Description: 脚本访问操作自动续费
     * @Param: [request]
     * @return: cn.kuwo.vip1.util.common.constants.MessageModel
     * @Author: <EMAIL>
     * @Date: 2018/10/19 21:12
     */
    public MessageModel oprationAutoPay(String uId, String pId, String flag) throws VehicleException {
        long uid = MyNumberUtils.toLONG(uId);
        long pid = MyNumberUtils.toLONG(pId);
        int limitDay = 1;
        MessageModel result = null;
        logger.info("脚本操作自动续费入参 : uid:{},pid:{},", uId, pId);
        UnionOrderProduct orderProduct = queryAutoPayObject(pid, limitDay);
        if (orderProduct != null) {
            try {
                //执行自动续费
                result = this.execAutoPay(orderProduct,flag);
            } catch (VehicleException e) {
                logger.error(e.getMessage());
                result = new MessageModel(SystemCodeErrorConstant.FAIL);
            } catch (Exception e) {
                result = new MessageModel(SystemCodeErrorConstant.FAIL);
                logger.error("uid:" + uId + ",pid:" + pId + " 自动续费异常", e);
            }

        } else {
            logger.error("uid:{},pid:{} 没有找到用户自动续费订单！", uId, pId);
            result = new MessageModel(SystemCodeErrorConstant.NO_ORDER_FOUND);
            throw new VehicleException("没有找到用户自动续费订单！");
        }

        logger.info("TaskEnd_4User - uid:{}, pid:{}, time: {}", uid, pid, System.currentTimeMillis());
        return result;
    }


    /**
     * pid 签约id
     * 获取单个用户的续费信息
     * 获取具体的距离当前时间差距{@code limitDay}天之内的数据
     * 用于自动续费。  order by UP.product_type_id 解决主动组合购买的情况,不排序，取出自动续费类型为 7
     *
     * <AUTHOR> zeren
     */
    public UnionOrderProduct queryAutoPayObject(long pid, int limitDay) {
        List<UnionOrderProduct> orderProductList = vehicleOrderMapper.selectOrderProductByPidAndLimitDay(pid, limitDay);
        if (null == orderProductList || orderProductList.size() < 1) {
            return null;
        }
        return orderProductList.get(0);
    }


    /**
     * 执行自动续费流程
     *
     * @param orderObject 用户续费订单详情
     * @return 返回有没有执行此次(逻辑)任务，true就是已经执行（只代表执行逻辑，不一定成功），false代表未执行
     */
    public MessageModel execAutoPay(UnionOrderProduct orderObject, String flag) throws VehicleException {
        MessageModel messageModel = new MessageModel();

        logger.info("执行execAutoPay  对象参数为,  orderObject:{}", JsonUtil.bean2Json(orderObject));

        final String _logTag = RandomStringUtils.randomAlphanumeric(8);

        //日志脚本记录  ADD BY Jiang
        logger.info("ST_A0_Begin - tag:{}, time: {}", _logTag, System.currentTimeMillis());
        //自动续费bean
        AutoRenewalInfoVehicle autoRenewalInfo = new AutoRenewalInfoVehicle();
        //支付类型
        short payType;
        //查看该笔订单是否是用户最后一笔到期订单
        boolean isLast = false;
        long log_uid = 0;
        long log_pid = 0;
        try {
            //将object信息设置到arInfo
            payType = this.transObjectToArInfo(orderObject, autoRenewalInfo);

            final long uid = autoRenewalInfo.getUserId();
            log_uid = uid;
            //签约id
            long pid = autoRenewalInfo.getPid();
            log_pid = pid;
            /* -·-·-·-·-·-  4Log 日志脚本记录  ADD BY Jiang  -·-·-·-·-·-  */
            logger.info("ST_A1_InitDBlog - tag:{}, uid:{}, pid:{}, time:{}", _logTag, uid, pid, System.currentTimeMillis());

            // 第0步:寻找真正的签约id 和 payType
            payType = lookUpRealPidAndPayType(payType, autoRenewalInfo);

            /* 如果当前指定不是最后一笔，则不进行扣费流程 (同类型业务的转移) */
            // 苹果订阅的不进行转移
            if (payType != PayTypeCategray.MOBILE_IOS_AUTO_PAY.getId()) {
                isLast = assistant.isLastAutoPayOrder(autoRenewalInfo);
                if (!isLast) {
                    messageModel.setCode(SystemCodeErrorConstant.NOT_ALLOW_OPTION_ERROR.getCode());
                    messageModel.setData(SystemCodeErrorConstant.NOT_ALLOW_OPTION_ERROR.getMessage());
                    return messageModel;
                }
            }

            //////////////////////////////////////////////////////////////////
            //////	第一步：构建dto数据	//////////////////////////////////////
            /////////////////////////////////////////////////////////////////
            PayInfoDTO payInfoDTO = new PayInfoDTO();
            Long orderId = makeUpPayInfoDto(orderObject, payInfoDTO, autoRenewalInfo, payType);

            //日志脚本记录  ADD BY Jiang
            logger.info("ST_A2_FormatPayInfo - tag:{}, uid:{}, pid:{}, time:{}", _logTag, uid, pid, System.currentTimeMillis());
            //////////////////////////////////////////////////////////////////
            //////	第二步：创建自动续费订单	/////////////////////////////////
            /////////////////////////////////////////////////////////////////
            //如果没有创建过
            if (orderId == null) {
                orderId = autoPayOrderService.createAutoOrder(payInfoDTO);

                /* -·-·-·-·-·-  4Log 日志脚本记录  ADD BY Jiang  -·-·-·-·-·-  */
                logger.info("ST_A2B1_CreateNewAutoOrder - tag:{}, uid:{}, pid:{}, time:{}", _logTag, uid, pid, System.currentTimeMillis());
            }
            autoRenewalInfo.setOrderId(orderId);
            //进行自动续费信息的保存(这里在请求支付之前先进行保存，防止异步通知比同步执行逻辑快的情况)
            renewalMapper.insert(autoRenewalInfo);

            if (payType != PayTypeCategray.MOBILE_IOS_AUTO_PAY.getId()) {
                //////////////////////////////////////////////////////////////////
                //////	第三步：进行自动扣费支付请求	/////////////////////////////
                /////////////////////////////////////////////////////////////////

                @SuppressWarnings("rawtypes")
                Map payBackMap = invokingRemoteService.renewal(payInfoDTO.getUid(), payInfoDTO.getPid(),
                        payInfoDTO.getCredit(), payType + "", orderId,
                        payInfoDTO.getPlatform(), autoRenewalInfo.getOrgOrderId() + "");
                /* -·-·-·-·-·-  4Log 日志脚本记录  ADD BY Jiang  -·-·-·-·-·-  */
                logger.info("ST_A3_PayRequest - tag:{}, uid:{}, pid:{}, price:{}, orderId:{}, time:{}",
                        _logTag, uid, pid, payInfoDTO.getCredit(), autoRenewalInfo.getOrgOrderId(), System.currentTimeMillis());

                //////////////////////////////////////////////////////////////////
                //////	第四部：请求完毕，设置bean信息，并进行 ////////////////////
                /////////////////////////////////////////////////////////////////
                setAutoRenewalInfoAfterPay(payBackMap, autoRenewalInfo);
            }


            //进行返回的处理,需要进行的操作
            String operInfo = dealWithDiffPayStd(autoRenewalInfo);
            autoRenewalInfo.setOperInfo(operInfo);
            logger.info("判断是否进入更新标识位,uid:{}, pid:{},operInfo:{},autoRenewalInfo:{}", uid, pid, operInfo, JsonUtil.bean2Json(autoRenewalInfo));
            if (operInfo != null) {
                //更新标识位信息
                renewalMapper.updateOperInfo(operInfo, autoRenewalInfo.getId());
            }
            /* -·-·-·-·-·-  4Log 日志脚本记录  ADD BY Jiang  -·-·-·-·-·-  */
            logger.info("ST_A4_AfterPayRequest - tag:{}, operInfo:{}, uid:{}, pid:{}, time:{}",
                    _logTag, operInfo, uid, pid, System.currentTimeMillis());
            if (!"MA".equals(flag)){
                //直接解约
                if (AutoRenewalConst.UNSIGN_USER.equals(operInfo)) {

                    unsignAutoPayPre(autoRenewalInfo.getUserId(), autoRenewalInfo.getVirtualUid());


                    /* -·-·-·-·-·-  4Log 日志脚本记录  ADD BY Jiang  -·-·-·-·-·-  */
                    logger.info("ST_A4B1_Unsign - tag:{}, operInfo:{}, uid:{}, pid:{}, time:{}",
                            _logTag, operInfo, uid, pid, System.currentTimeMillis());
                }
            }


            //////////////////////////////////////////////////////////////////
            //////	第五部：流程执行完毕 //////////////////////////////////////
            /////////////////////////////////////////////////////////////////

            /* -·-·-·-·-·-  4Log 日志脚本记录  ADD BY Jiang  -·-·-·-·-·-  */
            logger.info("ST_A5_END - tag:{}, uid:{}, pid:{}, time:{}", _logTag, uid, pid, System.currentTimeMillis());
            messageModel.setCode(SystemCodeErrorConstant.SUCCESS.getCode());
            messageModel.setDesc(SystemCodeErrorConstant.SUCCESS.getMessage());
            Map res = new HashMap();
            res.put("orderId",orderId);
            messageModel.setData(res);
            return messageModel;
        } catch (Exception e) {
            logger.error("tag:" + _logTag + ", uid:" + log_uid + ", pid:" + log_pid + ", time:{} 执行自动续费异常", e);
            throw new VehicleException(e);
        } finally {
            if (isLast) {
                //更新自动续费信息，异步成功返回不进行更新
                updateHasNotNotify(autoRenewalInfo);
            }
            /* -·-·-·-·-·-  4Log 日志脚本记录  ADD BY Jiang  -·-·-·-·-·-  */
            logger.info("ST_Finally_End - tag:" + _logTag + ", time: " + System.currentTimeMillis() + ", uid:" + log_uid + ", pid:" + log_pid);
        }
    }


    /**
     * 将查出的订单信息转换为autoRenewalInfo
     * return 支付类型
     */
    public short transObjectToArInfo(UnionOrderProduct orderObject, AutoRenewalInfoVehicle arInfo) {
        logger.info("查出来的订单信息转换: orderObject:{}, arInfo:{}", JsonUtil.bean2Json(orderObject), JsonUtil.bean2Json(arInfo));
        //将查询出的字段信息拆出来
//        Object[] order = (Object[]) orderObject;
//        long userId = MyNumberUtils.toLONG(order[0]);
        long userId = MyNumberUtils.toLONG(orderObject.getUid());
        //签约id
//        long pid = MyNumberUtils.toLONG(order[1]);
        long pid = MyNumberUtils.toLONG(orderObject.getPid());
        //支付类型
//        short payType = MyNumberUtils.toShort(order[2] + "");
        short payType = MyNumberUtils.toShort(orderObject.getPayType() + "");
        //用来区别唯一性
//        long orgOrderId = MyNumberUtils.toLONG(order[4]);
        long orgOrderId = MyNumberUtils.toLONG(orderObject.getOrderId());
        //产品类型
//        long productTypeId = MyNumberUtils.toLONG(order[5]);
        long productTypeId = MyNumberUtils.toLONG(orderObject.getProductTypeId());

        //剩余的天数
//        short mtrtDays = MyNumberUtils.toShort(order[9] + "");
        short mtrtDays = MyNumberUtils.toShort(orderObject.getSubdays() + "");

        arInfo.setOrgOrderId(orgOrderId);
        arInfo.setOrgBuyOrderId(orgOrderId);
        arInfo.setOrgPaySrc(orderObject.getPaySrc());
        arInfo.setOrgPlatVersion(orderObject.getPlatVersion());
        arInfo.setUserId(userId);
        arInfo.setMtrtDays(mtrtDays);
        arInfo.setProductTypeId(productTypeId);
        //代表唯一签约id
        arInfo.setPid(pid);
        //虚拟用户id
        if (userId != pid) {
            arInfo.setVirtualUid(pid);
        }
        logger.info("订单信息转换之后: orderObject:{}, arInfo:{}", JsonUtil.bean2Json(orderObject), JsonUtil.bean2Json(arInfo));
        return payType;
    }


    /**
     * 寻找真正的签约 id和payType (都签约的取用户id)
     * 虚拟订单的 用户id为0
     * 情况1:虚拟,真实的都签约,签约id用真实用户id
     * 情况2:主动购买的时候,订单转移到不是续费payType的订单上,寻找payType
     */
    public short lookUpRealPidAndPayType(short payType, AutoRenewalInfoVehicle arInfo) throws Exception {
        //自动续费类型 payType
        String renewPayType = getRenewPayType();
        long uid = arInfo.getUserId();
        long pid = arInfo.getPid();

        // 情况1,uid和pid相同,代表是真实用户id,并且是自动续费类型,不用进行查找
        // 情况2 虚拟id未合并,不用进行查找
        // 情况3 虚拟,真实合并,执行到虚拟真实的这一笔,需要进行查找,该真实用户是否有过自动续费,有的话,优先真实用户,进行查找
        // 情况4 payType不是的情况,寻找payType
        if (uid != pid && uid != 0 || !isAutoPayType(payType)) {
            AnswerRes mergeData = getAfterMergePid(uid, pid, renewPayType);
            if (mergeData != null) {
                pid = MyNumberUtils.toLONG(mergeData.getPid());
                payType = MyNumberUtils.toShort(mergeData.getPayType() + "");
                arInfo.setOrgBuyOrderId(mergeData.getOrderId());
                // 合并之后,转移到虚拟订单上,两者都是自动续费,取用户id
                if (uid == pid) {
                    //把虚拟id 置为空
                    arInfo.setVirtualUid(null);
                    arInfo.setPid(uid);
                }
            }
        }
        return payType;
    }


    /**
     * @Description: 看支付类型 是否是 自动续费类型 payType (39:支付宝 ,54:微信h5,122:微信,127:苹果自动续费)
     * @Param:
     * @return:
     * @Author: <EMAIL>
     * @Date: 2018/10/22 17:10
     */
    public static boolean isAutoPayType(short payType) {
        if (payType == PayTypeCategray.MOBILE_ALI_AUTO_PAY.getId() || payType == PayTypeCategray.MOBILE_WEIXIN_AUTO_PAY.getId()
                || payType == PayTypeCategray.MOBILE_IOS_AUTO_PAY.getId() || payType == PayTypeCategray.MOBILE_WEIXIN_WEB_AUTO_PAY.getId()) {
            return true;
        }
        return false;

    }


    /**
     * @Description: 获取自动续费类型的payType
     * @Param: []
     * @return: java.lang.String
     * @Author: <EMAIL>
     * @Date: 2018/10/22 18:06
     */
    public static String getRenewPayType() {
        return PayTypeCategray.MOBILE_ALI_AUTO_PAY.getId() + "," + PayTypeCategray.MOBILE_WEIXIN_AUTO_PAY.getId() + "," + PayTypeCategray.MOBILE_IOS_AUTO_PAY.getId() + "," + PayTypeCategray.MOBILE_WEIXIN_WEB_AUTO_PAY.getId();
    }


    /**
     * 情况1: 合并之后，看虚拟id 对应的用户id 有没有签约过，  优先选取用户id
     * 情况2: 虚拟id 主动购买,当前payType不是自动续费类型
     * merge_date 虚拟的订单合并才有值
     *
     * <AUTHOR> zeren
     */
    public AnswerRes getAfterMergePid(long userId, long pid, String autoPayType) {
        List<AnswerRes> answerResList = vehicleOrderMapper.getAfterMergePid(userId, pid, autoPayType);
        if (answerResList.isEmpty()) {
            return null;
        }
        return answerResList.get(0);
    }


    /**
     * 构建订单数据 payInfoDTO
     * 这里分已构建和未构建
     */
    public Long makeUpPayInfoDto(UnionOrderProduct object, PayInfoDTO payInfoDTO, AutoRenewalInfoVehicle arInfo, short payType) throws Exception {
        logger.info("构建订单数据，object:{},payInfoDTO:{},arInfo:{},payType:{}", JsonUtil.bean2Json(object), JsonUtil.bean2Json(payInfoDTO), JsonUtil.bean2Json(arInfo), payType);
        // 唯一签约id
        long pid = arInfo.getPid();
        //平台
        String platform = object.getPlatForm() + "";

        // 产品id
        long productTypeId = arInfo.getProductTypeId();
        //开通月数
        short cnt = 0;
        if (null != object.getCnt()) {
            cnt = object.getCnt().shortValue();
        }
        //开通价格 (单价)
        double price = MyNumberUtils.toDouble(object.getCredit() + "");
        //开通时间
        int duration = object.getDuration();
        //订单来源,目前用到一处,豪华vip,src为renewalOne,后续则只续费一个月 12元
        String src = object.getSrc() + "";
        //续费策略
        int paySrc = MyNumberUtils.toInt(object.getPaySrc() + "");

        //构建之前 查看是否已经构建过
        VehicleOrder orderInfo = getArOrderByUserId(pid, arInfo.getOrgBuyOrderId() + "", AutoRenewalConst.CLIENT_ACT);
        //已经构建过
        if (orderInfo != null) {
            logger.info("已经构建过,pid:{}", pid);
            payInfoDTO.setUid(orderInfo.getUserId());
            //签约id
            payInfoDTO.setPid(orderInfo.getPid());
            payInfoDTO.setCredit(orderInfo.getCredit());
            payInfoDTO.setPayType(orderInfo.getPayType());
            payInfoDTO.setPlatform(orderInfo.getPlatform());
            payInfoDTO.setSrc(orderInfo.getSrc());
            payInfoDTO.setPaySrc(orderInfo.getPaySrc());
            payInfoDTO.setPlatVersion(orderInfo.getPlatVersion());
            payInfoDTO.setProductType(orderInfo.getProductType());
            return orderInfo.getId();
        } else {
            logger.info("未构建过数据手动构建,pid:{}", pid);
            //手动构建
            List<Product> productList = new ArrayList<Product>();
            //拼凑 Product
            Product product = new Product();
            // 这个字段取数据库中的 product_id
            product.setId(productTypeId + "");
            // 产品id
            product.setProductTypeId(productTypeId);
            //设置价格策略
            this.getPriceStrategy(arInfo, productTypeId, price, cnt,duration, product, payType, src);
            product.setPid(pid + "");
            productList.add(product);

            //拼凑数据，进行请求
            payInfoDTO.setUid(arInfo.getUserId());
            payInfoDTO.setAct(VipAction.RENEW.getValue());
            payInfoDTO.setCredit(product.getPrice());
            payInfoDTO.setPayType(payType);
            payInfoDTO.setPid(pid);
            // platform加个判断，如果自动续费转移到赠送的那笔，平台为空，查询用户自动续费的平台
            if (StringUtils.isEmpty(platform)) {
                //自动续费类型
                String autoPayType = getRenewPayType();
                platform = getAutoPlatform(pid, autoPayType);
            }
            payInfoDTO.setPlatform(platform);
            payInfoDTO.setSrc(arInfo.getOrgBuyOrderId() + "");
            payInfoDTO.setClientAct(AutoRenewalConst.CLIENT_ACT);
            payInfoDTO.setAutoPay(AutoRenewalConst.AUTO_PAY_YES);
            payInfoDTO.setUrlParam("");
            payInfoDTO.setType(0);
            payInfoDTO.setProducts(productList);
            payInfoDTO.setVirtualUid(arInfo.getVirtualUid());
            payInfoDTO.setPaySrc(arInfo.getOrgPaySrc());
            payInfoDTO.setPlatVersion(arInfo.getOrgPlatVersion());
            payInfoDTO.setProductType(object.getProductType());
        }
        logger.info("构建完的数据,payInfoDTO : {} ", JsonUtil.bean2Json(payInfoDTO));
        return null;
    }


    /**
     * 查看续费订单是否已经构建过
     *
     * <AUTHOR> zeren
     */
    public VehicleOrder getArOrderByUserId(long pid, String orgOrderId, String clientAct) {
        List<VehicleOrder> piList = vehicleOrderMapper.getOrderInfoListByPidAndSrcAndClientAct(pid, orgOrderId, clientAct);
        if (piList.isEmpty()) {
            return null;
        }
        return piList.get(0);

    }


    /**
     * 以下为最新 (价格策略设置)
     * ios的扣 18 (ios 只有1个月的)
     * 最新价格策略,用户多少买的，就给用户续多少金额的订单
     * 额外一种特定规则:用户买的豪华vip,订单的src为renewalOne,则后续只续费1个月的豪华vip
     */
    public void getPriceStrategy(AutoRenewalInfoVehicle arInfo, long productTypeId, double price, short cnt,int duration, Product product, short payType, String src) throws Exception {
        Long orgBuyOrderId = arInfo.getOrgBuyOrderId();
        if (orgBuyOrderId!=null && orgBuyOrderId> 0){
            VehicleOrder firstOrderInfo = assistant.getFirstRenewalOriginOrderInfo(orgBuyOrderId);
            if (firstOrderInfo!=null){
                // 设置成第一笔订单的src
                src = firstOrderInfo.getSrc();
                logger.info("first order src -> {}", src);
                List<VehicleProduct> products = vehicleProductMapper.getProductsByOrderId(firstOrderInfo.getId());
                if (products!=null && products.size()>0){
                    cnt = products.get(0).getCnt();
                    price = products.get(0).getPrice();
                    productTypeId = products.get(0).getProductTypeId();
                    arInfo.setOrgPaySrc(firstOrderInfo.getPaySrc());
                    arInfo.setOrgPlatVersion(firstOrderInfo.getPlatVersion());
                }
            }
        }
        //这里找到第一笔订单
        // 设置一个初始价格
        product.setPrice(CarActivityMonthPriceEnum.ONE_MONTH.getPrice());
        product.setDuration((short) 31);
        product.setCnt((short) 1);

        boolean isEnterActivy = false;
        // 微信 支付宝 自动续费

        // 车载vip的
        // todo: 新增会员类型注意 需要增加新的会员部分 做个标注
        if (productTypeId == ProductEnum.VIP_VEHICLE.getId() || productTypeId == ProductEnum.SUPER_VIP.getId()) {
            //设置金额
            product.setPrice(price);
            //设置天数
            product.setDuration((short) duration);
            // 设置月份
            product.setCnt(cnt);

            // 设置 额外一种特定规则:用户买的豪华vip,订单的src为renewalOne,则后续只续费1个月的豪华vip
            if (AutoRenewalConst.RENEWAL_ONE.equals(src)) {
                product.setPrice(12.0); //设置金额
                product.setDuration((short) 31); //设置天数
                product.setCnt((short) 1);            // 设置月份
                isEnterActivy = true;
            }

            // 设置 额外第二种特定规则:用户买的豪华vip,订单的src为renewOneLux11.4,则后续只续费1个月的豪华vip(11.4元) (pc端)
            if (AutoRenewalConst.RENEWAL_ONE_LUX.equals(src) && cnt == 1) {
                product.setPrice(11.4); //设置金额
                product.setDuration((short) 31); //设置天数
                product.setCnt((short) 1);            // 设置月份
                isEnterActivy = true;
            }

            // 活动续费 规则都提取到 activty 里面,这块儿整个流程都会修改
            RenewalParams renewalParams = new RenewalParams();
            renewalParams.setCnt(cnt);
            renewalParams.setProductTypeId(productTypeId);
            renewalParams.setSrc(src);
            CarActivityAbs activity = ActivityManager.getRenewalActivity(src);
            if (activity!=null){
                RenewalPrice renewalPrice = activity.getRenewalPriceInfo(renewalParams);
                if (renewalPrice != null){
                    product.setPrice(renewalPrice.getPrice());
                    product.setDuration(renewalPrice.getDuration());
                    product.setCnt(renewalPrice.getCnt());
                    product.setProductTypeId(productTypeId);
                }
            }else {
                // 针对上一笔订单是是数字 匹配不到活动(这种一般是由于之前续费订单价格有误,此时需要进行修复)，此时取默认的价格
                if (!isEnterActivy){
                    if (productTypeId == ProductEnum.SUPER_VIP.getId()){
                        CarActivitySuperVipAutoPayMonthPriceEnum instance = CarActivitySuperVipAutoPayMonthPriceEnum.getInstance(Integer.valueOf(cnt));
                        product.setCnt(cnt);
                        product.setDuration((short) (cnt * 31));
                        product.setPrice(instance.getFinalAmount());
                    }else{
                        CarActivityMonthPriceEnum instance = CarActivityMonthPriceEnum.getInstance(Integer.valueOf(cnt));
                        product.setCnt(cnt);
                        product.setDuration((short) (cnt * 31));
                        product.setPrice(instance.getFinalAmount());
                    }
                }
            }

            //设置 额外第三种特定规则：用户购买618打折商品，订单的src为activity618,则后续变为正常价格续费
//            if (CarActivitySuperEnum.ACTIVITY618.getOpStr().equals(src)
//                    || CarActivitySuperEnum.ACTIVITY_DOUBLE11.getOpStr().equals(src)
//                    || CarActivitySuperEnum.ACTIVITY_DOUBLE11_RETURN.getOpStr().equals(src)
//                    || CarActivitySuperEnum.ACTIVITY_BANMA.getOpStr().equals(src)
//                    || CarActivitySuperEnum.ACTIVITY618_21.getOpStr().equals(src)
//                    || CarActivitySuperEnum.SHANGQI_ACTIVITY.getOpStr().equals(src)) {
//                CarActivityMonthPriceEnum instance = CarActivityMonthPriceEnum.getInstance(Integer.valueOf(cnt));
//                Integer monthCnt = instance.getMonthCnt();
//                product.setPrice(instance.getFinalAmount());
//                product.setDuration((short) (monthCnt * 31));
//                product.setCnt(monthCnt.shortValue());
//            }
//
//            if (CarActivitySuperEnum.BYD_ACTIVITY.getOpStr().equals(src)) {
//                CarActivityMonthPriceEnum instance = CarActivityMonthPriceEnum.getInstance(Integer.valueOf(cnt));
//                Integer monthCnt = instance.getMonthCnt();
//                product.setPrice(instance.getFinalAmount());
//                product.setDuration((short) (monthCnt * (31 + 15)));
//                product.setCnt(monthCnt.shortValue());
//            }

            //容错和转移到赠送的订单上的设置
            // 遇到异常情况在进行转移
            if (price <= 0) {
                logger.info("[AutoRenewalException] maybe exception auto renewal order ->"+orgBuyOrderId);
                product.setPrice(19.9); //设置金额
                product.setDuration((short) 31); //设置天数
                product.setCnt((short) 1);        // 设置月份
            }

        }
        logger.info("组建完 product :{}", JsonUtil.bean2Json(product));
    }


    /**
     * 获取自动续费用户的平台(当前自动续费订单转移到 赠送订单，平台为空的情况)
     */
    public String getAutoPlatform(long pid, String autoPayType) throws Exception {

        List<VehicleOrder> ptList = vehicleOrderMapper.getAutoPlatForm(pid, autoPayType);
        if (ptList.isEmpty()) {
            return "ar";
        }
        return ptList.get(0).getPlatform() + "";
    }


    /**
     * 请求自动扣费结束，设置自动续费bean信息
     * 参考串
     * {"responseCode":1000,"desc":"FAIL","responseContent2":"ORDER_SUCCESS_PAY_INPROCESS","responseContent1":"501"}
     * 取 responseContent1,responseContent2
     */
    @SuppressWarnings("rawtypes")
    public void setAutoRenewalInfoAfterPay(Map payBackMap, AutoRenewalInfoVehicle arInfo) {
        if (payBackMap == null || null == payBackMap.get("responseCode")) {
            arInfo.setRespCode("408");
            arInfo.setRespDesc("Read timed out");
            logger.error("userId:{} pid:{} 自动续费调用支付，返回空", arInfo.getUserId(), arInfo.getPid());
            return;
        }
        String respCode = payBackMap.get("responseCode").toString();
        //成功的话，信息描述取 desc 失败的话取 responseContent2
        String respDesc = (String) payBackMap.get("desc");
        if (!"200".equals(respCode)) {
            //这里存的是错误的respCode,如果有的话
            String respCont1 = (String) payBackMap.get("responseContent1");
            String respCont2 = (String) payBackMap.get("responseContent2");
            //给code设置值
            respCode = respCont1 == null ? respCode : respCont1;
            //如果content2为空，还是取desc
            respDesc = respCont2 == null ? respDesc : respCont2;
        } else {    //如果成功的话
            respCode = AutoRenewalConst.WAITING_CALLBACK_CODE;
            respDesc = AutoRenewalConst.WAITING_CALLBACK_DESC;
        }
        arInfo.setRespCode(respCode);
        arInfo.setRespDesc(respDesc);
    }

    /**
     * 成功直接返回
     * 情况1:余额不足，第一次到最后一天进行免费延期4天，还不足，解约
     * 情况2：用户已解约，直接解约
     * 情况3：唤起收银台失败/支付失败/用户钱包版本过低 也是扣到最后一天的第三次
     * return 返回需要处理对应逻辑的字符串
     */
    public String dealWithDiffPayStd(AutoRenewalInfoVehicle arInfo) throws Exception {
        String respDesc = arInfo.getRespDesc();
        //等待回调的直接返回(相当于同步返回成功) 重复调用的也不做处理
        if (AutoRenewalConst.WAITING_CALLBACK_CODE.equals(arInfo.getRespCode()) || AutoRenewalConst.RENEWAL_ONGOING.equals(arInfo.getRespCode())) {
            //判断如果是属于自动延期的情况，则返回自动延期标记
            Long autoRequestedCount = renewalMapper.getCount(arInfo.getOrderId(), AutoRenewalConst.RENEWAL_ONGOING);
            if (autoRequestedCount > AutoRenewalConst.REQUEST_DAY_COUNT_BEFORE_AUTO_EXTEND * AutoRenewalConst.EVERY_DAY_RENEWAL_REQUEST_COUNT) {
                return AutoRenewalConst.AUTO_EXTEND_FALG;
            } else {
                return null;
            }

        }
        //首先处理 不按天数来的
        //自动续费时，已解约
        if (AutoRenewalConst.isContractError(respDesc)) {
            //直接解约
            return AutoRenewalConst.UNSIGN_USER;
        }
        //payType不是自动续费的类型
        if (AutoRenewalConst.ILLEGAL_PAY_TYPE_DESC.equals(respDesc)) {
            //直接解约
            return AutoRenewalConst.UNSIGN_USER;
        }

        //这里处理按到期天数的情况
        /*if(arInfo.getMtrtDays() == 0){
            String isOpenAutoExtendRenewal= RedisDAO.getIsOpenAutoExtendRenewal();
            if(isOpenAutoExtendRenewal!=null && isOpenAutoExtendRenewal.equals("1")){
                //如果开启订单延期，则进行订单延期业务处理
                return dealWithAutoExtendRenewal(arInfo);
            }else{
                //如果没有开启，则判断订单是否延期过，如果延期过，也按照订单延期业务处理
                Long autoRequestedCount = renewalMapper.getCount(arInfo.getOrderId(),AutoRenewalConst.RENEWAL_ONGOING);
                if(autoRequestedCount>AutoRenewalConst.REQUEST_DAY_COUNT_BEFORE_AUTO_EXTEND*AutoRenewalConst.EVERY_DAY_RENEWAL_REQUEST_COUNT){
                    //已请求延期数量大于原本最大延期请求的数量，证明已经自动延期过
                    return dealWithAutoExtendRenewal(arInfo);
                }else{
                    //自动延期以前的业务逻辑

                    int count = getTodayAutoRequestCnt(arInfo.getUserId(),arInfo.getVirtualUid(),AutoRenewalConst.RENEWAL_ONGOING);
                    //情况1：这里处理余额不足的情况
                    //请求到最后一天，第二次，还未成功处理，都进行解约
                    if(count==2){
                        return AutoRenewalConst.UNSIGN_USER;
                    }
                }
            }
        }*/
        return null;
    }


    /**
     * 处理自动延期续费的逻辑
     * 自动续费的到期订单延期七天
     *
     * @param arInfo
     * @return
     * @throws Exception
     */
    private String dealWithAutoExtendRenewal(AutoRenewalInfoVehicle arInfo) throws Exception {

        int count = getTodayAutoRequestCnt(arInfo.getUserId(), arInfo.getVirtualUid(), AutoRenewalConst.RENEWAL_ONGOING);
        //当天的请求自动续费次数是否达到上限
        boolean isTodayAutoRequestHasCompleted = (count == AutoRenewalConst.EVERY_DAY_RENEWAL_REQUEST_COUNT);

        //已经请求过自动续费的次数
        Long autoRequestedCount = renewalMapper.getCount(arInfo.getOrderId(), AutoRenewalConst.RENEWAL_ONGOING);
        //最多能够请求自动续费的次数
        int autoRequestedCountMaxLimit = (AutoRenewalConst.AUTO_EXTEND_DAY_COUNT + AutoRenewalConst.REQUEST_DAY_COUNT_BEFORE_AUTO_EXTEND) * AutoRenewalConst.EVERY_DAY_RENEWAL_REQUEST_COUNT;
        //查看已经请求过延期的次数是否大于等于上限
        if (autoRequestedCount >= autoRequestedCountMaxLimit) {
            if (isTodayAutoRequestHasCompleted) {
                //如果续约过，则进行解约
                return AutoRenewalConst.UNSIGN_USER;
            }

        } else {

            if (autoRequestedCount >= AutoRenewalConst.REQUEST_DAY_COUNT_BEFORE_AUTO_EXTEND * AutoRenewalConst.EVERY_DAY_RENEWAL_REQUEST_COUNT) {
                if (isTodayAutoRequestHasCompleted) {
                    //已经需要开始免费延期或者已经开始免费延期的情况，且请求总数小于续约次数上限，则免费延期一天

                    //为原始的订单延期一天

                }
                //刚好需要延期的那一次请求，还处于正常请求范围，不能打上自动延期标签
                if (autoRequestedCount != AutoRenewalConst.REQUEST_DAY_COUNT_BEFORE_AUTO_EXTEND * AutoRenewalConst.EVERY_DAY_RENEWAL_REQUEST_COUNT) {
                    return AutoRenewalConst.AUTO_EXTEND_FALG;
                }

            }


        }

        return null;
    }


    /**
     * 获取当天自动续费请求次数
     * uid 用户id (有用户id的以用户id为准)
     * virId 虚拟id
     * 连续重复请求的不计数
     *
     * <AUTHOR> zeren
     */
    public Integer getTodayAutoRequestCnt(long uid, Long virId, String respDesc) {
        List<AutoRenewalInfoVehicle> ariList = renewalMapper.getTodayAutoRequestCnt(uid, virId, respDesc);
        return ariList.size();
    }


    public List<VehicleProduct> getVipmByOrderId(Long orderId) throws Exception {
        List<VehicleProduct> list = vehicleProductMapper.getVipmByOrderId(orderId);
        return list;
    }


    /**
     * uid 用户id
     * pid 签约id
     * uid,pid明确的情况下的解约
     */
    public void unsignAutoPayPre(long uid, Long pid) throws Exception {
        List<VehicleOrder> orderInfos = vehicleOrderMapper.queryByUserAuto(uid, pid);
        if (orderInfos.isEmpty()) {
            return;
        }
        VehicleOrder orderInfo = orderInfos.get(0);
        //执行解约方法
        unsignAutoPay(orderInfo.getUserId(), orderInfo.getPid());
    }


    /**
     * uid 用户id
     * virId 虚拟id
     * 解约,该方法是 已经明确用户id,虚拟id的情况,并且符合解约条件
     *
     * <AUTHOR> zeren
     */
    private void unsignAutoPay(long uid, Long virId) throws Exception {
        String renewPayType = AutoRenewalConst.getRenewPayType();
        // 传进来的id不一定是签约id,寻找签约id和payType
        //根据传进来的id 找当时签约的那条记录
        Object obj = getUnsignPidAndPayType(uid, virId, renewPayType);
        if (obj != null) {
            //查到待解约的记录,再进行订单autoPay的更新,要不然先更新了,没有自动续费过的找签约的找不到了
            unsign(uid, virId);
            Object[] objInfo = (Object[]) obj;
            // 第一个pid 第二个payType
            boolean dounsign = invokingRemoteService.unsign(objInfo[0] + "", objInfo[1] + "");
            if (!dounsign) {
                logger.error("unsign failure, pid:" + objInfo[0] + ",payType:" + objInfo[1]);
            }
        }
    }


    /**
     * 获取签约的id (真实,虚拟都签约了,用真实用户id,其它用签约的id)
     */
    public Object getUnsignPidAndPayType(long uid, Long virId, String renewPayType) throws Exception {
        StringBuffer sbf = new StringBuffer("select pid,pay_type from ORDER_INFO where ");
        if (uid != 0) {
            sbf.append("user_id = ? ");
        } else {
            sbf.append("pid = ? ");
        }
        sbf.append("and status = 1 and auto_pay in('yes','done','transfer') and find_in_set(pay_type,?) order by merge_date,pay_date desc");


        List<AnswerRes> list = vehicleOrderMapper.getUnsignPidAndPayType(uid, virId, renewPayType);
        if (list.isEmpty()) {
            return null;
        }
        return list.get(0);
    }


    /**
     * 设置解约
     *
     * @param uid
     * @throws Exception
     * <AUTHOR> Wanyu
     */
    public void unsign(long uid, Long virId) throws Exception {
        vehicleOrderMapper.updateCancel(uid, virId);
    }


    /**
     * 其它回调还未执行过，则进行更新
     * 主要用于异步回调比同步返回快的情况(防止信息覆盖)
     *
     * @param
     * @throws Exception
     * <AUTHOR> zeren
     */
    public void updateHasNotNotify(AutoRenewalInfoVehicle arInfo) {
        renewalMapper.updateHasNotyNotify(arInfo);
    }

    /**
     * IOS 自动续费
     *
     * @param iosAutoPayVO
     * @return
     */
    @Transactional(value = "masterTransactionManager", rollbackFor = Exception.class)
    public MessageModel notifyIosAutoPay(IOSAutoPayVO iosAutoPayVO) throws Exception {
        VehicleOrder order= checkValidOrder(iosAutoPayVO);
        long count= renewalMapper.getAutoRenewalInfo(Long.parseLong(iosAutoPayVO.getOrderId()),iosAutoPayVO.getUserId());
        // VehicleOrder orderInfo = getArOrderByUserId(order.getPid(), order.getId() + "", AutoRenewalConst.CLIENT_ACT);
        if(count>0){
            logger.error("vehicle_IOS_renewal: 续费订单重复,orderId: " + order.getId());
            throw new VehicleException("续费订单重复");
        }
        List<VehicleProduct> productsList = vehicleProductMapper.getProductsByOrderId(order.getId());
        if(CollectionUtils.isEmpty(productsList)){
            logger.error("vehicle_IOS_renewal: 产品信息缺失,orderId: " + order.getId());
            throw new VehicleException("产品信息缺失");
        }
        VehicleProduct vehicleProduct= productsList.get(0);
        checkValidCredit(iosAutoPayVO,vehicleProduct);
        short cnt = vehicleProduct.getCnt();
        int days=cnt * 31;
        logger.error("vehicle_IOS_renewal: 产品信息={}: ", JSONUtil.toJsonStr(vehicleProduct));
        final Timestamp currentTime = TimeUtils.getCurrentTime();
        VehicleOrder renewalOrder=saveVehicleOrder(order,currentTime,iosAutoPayVO);
        vehicleAssistant.invokingVipSend(vehicleProduct.getProductTypeId().intValue(), days, renewalOrder, vehicleProduct.getUserId() ,-1,null);
        saveVehicleProduct(vehicleProduct,days,currentTime,renewalOrder.getId());
        AutoRenewalInfoVehicle arinfo=new AutoRenewalInfoVehicle();
        arinfo.setOrderId(Long.parseLong(iosAutoPayVO.getOrderId()));
        arinfo.setUserId(Long.parseLong(iosAutoPayVO.getUserId()));
        arinfo.setRespCode(AutoRenewalConst.IOS_RENEWAL);
        renewalMapper.insert(arinfo);
        return new MessageModel();
    }

    /**
     * 续费金额合法检查
     *
     * @param iosAutoPayVO
     * @param vehicleProduct
     * @throws VehicleException
     */
    public void checkValidCredit(IOSAutoPayVO iosAutoPayVO,  VehicleProduct vehicleProduct) throws VehicleException {
        CarActivityMonthPriceEnum instance = CarActivityMonthPriceEnum.getInstance((int)vehicleProduct.getCnt());
        double credit=Double.parseDouble(iosAutoPayVO.getCash());
        if (Math.abs(credit- instance.getFinalAmount()) >= 1) {
            logger.error("vehicle_IOS_renewal: 金额有误，orderId:{},入参金额：{}, 续费金额：{}", vehicleProduct.getOrderId(), iosAutoPayVO.getCash(), instance.getFinalAmount());
            throw new VehicleException("金额有误");
        }
    }

    /**
     * 订单数据合法检查
     *
     * @param iosAutoPayVO 订单参数
     * @return
     * @throws VehicleException
     */
    public VehicleOrder checkValidOrder(IOSAutoPayVO iosAutoPayVO) throws VehicleException {
        long userId = Long.parseLong(iosAutoPayVO.getUserId());
        VehicleOrder order;
        try {
            order= vehicleOrderMapper.getLastAutoPayOrder(userId);
        } catch (Exception e) {
            logger.error("uid订单查询异常,userId: " + userId, e);
            throw new VehicleException("订单编号转换错误");
        }
        if (null == order) {
            logger.error("uid订单查询失败， userId：{}", userId);
            throw new VehicleException("没有找到对应订单");

        }
        order=vehicleOrderMapper.getOrderById(order.getId());
        if (order.getStatus() != OrderStatusEnum.PAIED.getStatus()) {
            logger.error("订单状态不合法，  订单id：{}", order.getId());
            throw new VehicleException("订单状态不合法");
        }
        return order;
    }

    /**
     * 车载订单表save
     *
     * @param oldVehicleOrder 车载订单表
     * @returne
     */
    public VehicleOrder saveVehicleOrder(VehicleOrder oldVehicleOrder,Timestamp currentTime,IOSAutoPayVO iosAutoPayVO)  {
        //如果该笔订单是自动续费订单，并且原订单也是自动续费订单，将原订单修改为done
        if ("yes".equals(oldVehicleOrder.getAutoPay())) {
            vehicleOrderMapper.autoPayDone(oldVehicleOrder.getId());
        }
        VehicleOrder renewalOrder = new VehicleOrder();
        BeanUtils.copyProperties(oldVehicleOrder,renewalOrder);
        renewalOrder.setId(null);
        renewalOrder.setPayDate(new Date());
        renewalOrder.setAutoPay("yes");
        renewalOrder.setClientAct("autoTask");
        renewalOrder.setThirdOrderId(iosAutoPayVO.getOrderId());
        renewalOrder.setStatus(OrderStatusEnum.PAIED.getStatus());
        renewalOrder.setSrc(String.valueOf(oldVehicleOrder.getId()));
        renewalOrder.setTime(currentTime);
        vehicleOrderMapper.insert(renewalOrder);
        return renewalOrder;
    }

    /**
     * 车载产品表save
     *
     * @param oldVehicleProduct 车载产品表
     * @param days 续费天数
     */
    public void saveVehicleProduct(VehicleProduct oldVehicleProduct,int days,Timestamp currentTime,long newOrderId)  {
        VehicleProduct vehicleProduct = new VehicleProduct();
        BeanUtils.copyProperties(oldVehicleProduct,vehicleProduct);
        vehicleProduct.setDuration((short)days);
        vehicleAssistant.vehicleProcessProduct(vehicleProduct, (short)days, currentTime, oldVehicleProduct.getUserId(), oldVehicleProduct.getVirtualUid(), oldVehicleProduct.getProductType());
        vehicleProduct.setStatus(OrderStatusEnum.PAIED.getStatus());
        vehicleProduct.setBuyDate(currentTime);
        vehicleProduct.setOrderId(newOrderId);
        vehicleProductMapper.insertBatch(Collections.singletonList(vehicleProduct));
    }

}
