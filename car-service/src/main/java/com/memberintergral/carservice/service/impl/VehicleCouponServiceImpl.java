package com.memberintergral.carservice.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.memberintergral.carservice.config.constant.MessageModel;
import com.memberintergral.carservice.config.constant.SystemCodeErrorConstant;
import com.memberintergral.carservice.config.redis.added.RedisDAO;
import com.memberintergral.carservice.domain.BO.VehicleCouponBO;
import com.memberintergral.carservice.domain.entity.DataDataset;
import com.memberintergral.carservice.domain.entity.DataItem;
import com.memberintergral.carservice.domain.entity.VehicleCoupon;
import com.memberintergral.carservice.domain.entity.VehicleCouponRecord;
import com.memberintergral.carservice.mapper.VehicleCouponMapper;
import com.memberintergral.carservice.mapper.VehicleCouponRedordMapper;
import com.memberintergral.carservice.service.VehicleCouponService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.memberintergral.carservice.config.constant.VehicleConstant.VEHICLE_COUPON_KEY;


/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-24
 */
@DS("master")
@Service
@Slf4j
public class VehicleCouponServiceImpl extends ServiceImpl<VehicleCouponMapper, VehicleCoupon> implements VehicleCouponService {

    @Autowired
    private VehicleCouponMapper vehicleCouponMapper;

    @Autowired
    private VehicleCouponRedordMapper vehicleCouponRedordMapper;

    @Autowired
    private VehicleService vehicleService;

    @Autowired
    private DataModelService dataModelService;

    /**
     * 获取优惠券
     * 1. virtualUid  valid 存储  virtualUid
     * 2. user_id  valid && virtualUid  valid 存储 user_id &virtualUid
     * @param userId
     * @param channel
     * @return
     */
    public MessageModel getCouponList(Long userId, String channel, Long virtualUid, String deviceId, Map<Integer,VehicleCouponBO> conMap, Map<Integer,VehicleCouponBO> notConMap){
        if((userId==null&&virtualUid==null)|| StringUtils.isBlank(channel)) {
            log.info("vehicle_first_coupon：getCouponList function uid={} or channel={} or virtualUid={},or deviceId={} is empty value !", userId, channel, virtualUid, deviceId);
            return new MessageModel(SystemCodeErrorConstant.COUPON_PARAM_EMPTY);
        }
        // 验证id
        Long uid=virtualUid!=null&&virtualUid>0?virtualUid:userId;
        if ((uid == null || uid == 0)){
            log.info("vehicle_first_coupon：getCouponList function uid or channel is empty value ! virtualUid={},userId={}",virtualUid,userId);
            return new MessageModel(SystemCodeErrorConstant.COUPON_PARAM_EMPTY);
        }
        String devId=StringUtils.isNotBlank(deviceId)?deviceId:String.valueOf("deviceUid"+uid);
        log.info("vehicle_first_coupon：getCouponList function uid={} and virtualUid={} !",userId,virtualUid);
        List<VehicleCoupon> deviceVehicleCoupons= vehicleCouponMapper.getVehicleCouponsByDeviceId(devId);
        List<Long> userVehicleCouponIds=deviceVehicleCoupons.stream().filter(ve->ve.getUid()==null||ve.getUid()==0).map(VehicleCoupon::getId).collect(Collectors.toList());
        List<Long> vrVehicleCouponIds=deviceVehicleCoupons.stream().filter(ve->ve.getVirtualUid()==null||ve.getVirtualUid()==0).map(VehicleCoupon::getId).collect(Collectors.toList());

        //更新绑定
        if(!CollectionUtils.isEmpty(userVehicleCouponIds)&&(userId!=null&&userId>0)){
            vehicleCouponMapper.updateVehicleCouponsById(userId,userVehicleCouponIds);
            List<VehicleCouponRecord> recordList= vehicleCouponRedordMapper.getVehicleCouponRecordByDeviceId(devId);
            List<Long> userVehicleCouponReIds=recordList.stream().filter(ve->ve.getUid()==null||ve.getUid()==0).map(VehicleCouponRecord::getId).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(userVehicleCouponReIds)){
                vehicleCouponRedordMapper.updateVehicleCouponsRecordById(userId,userVehicleCouponReIds);
            }
        }
        if(!CollectionUtils.isEmpty(vrVehicleCouponIds)&&(virtualUid!=null&&virtualUid>0)){
            vehicleCouponMapper.updateVRVehicleCouponsById(virtualUid,vrVehicleCouponIds);
        }
        List<VehicleCoupon> vehicleCoupons= vehicleCouponMapper.getVehicleCouponsByUid(userId,virtualUid);
        if(CollectionUtils.isEmpty(vehicleCoupons)){
            log.info("vehicle_first_coupon：vehicleCoupons is empty ,uid{}!",virtualUid);
            return new MessageModel();
        }
        List<VehicleCouponBO> vehicleCouponList=new ArrayList<>();
        vehicleCoupons.forEach(vehicleCoupon -> {
            try{
                VehicleCouponBO vehicleCouponBO=new VehicleCouponBO();
                vehicleCouponBO.setCouponName(vehicleCoupon.getCouponName());
                vehicleCouponBO.setCouponUniqueId(vehicleCoupon.getSerialKey());
                vehicleCouponBO.setDetail(vehicleCoupon.getDetail());
                vehicleCouponBO.setDiscountAmount(vehicleCoupon.getDiscountAmount());
                vehicleCouponBO.setStartTime(vehicleCoupon.getStartTime());
                vehicleCouponBO.setEndTime(vehicleCoupon.getEndTime());
                vehicleCouponBO.setIsValidChannel(channel.equalsIgnoreCase(vehicleCoupon.getChannel())?1:0);
                vehicleCouponBO.setMonths(vehicleCoupon.getMonths());
                vehicleCouponBO.setContinuteType(vehicleCoupon.getContinuteType());
                long betweenDay = DateUtil.between(new Date(), vehicleCoupon.getEndTime(), DateUnit.DAY)+1;
                if(betweenDay==1||betweenDay==3||betweenDay==7){
                    vehicleCouponBO.setIsAlert(1);
                }else{
                    vehicleCouponBO.setIsAlert(0);
                }
                // 未使用
                int status=0;
                if(vehicleCoupon.getUsed()==1){
                    // 已使用
                    status=2;
                }
                if(vehicleCoupon.getEndTime().before(new Date())){
                    // 已过期
                    status=1;
                }
                if(vehicleCoupon.getStartTime().after(new Date())){
                    // 未到期
                    status=4;
                }
                vehicleCouponBO.setStatus(status);
                vehicleCouponBO.setChannel(vehicleCoupon.getChannel());
                vehicleCouponList.add(vehicleCouponBO);
            }catch (Exception e){
                log.error("vehicle_first_coupon：getCouponList vehicleCoupons.forEach vehicleCoupon={} is error !", JSONObject.toJSON(vehicleCoupon),e);
            }
        });
        log.info("vehicle_first_coupon：vehicleCouponList sort  ! vehicleCouponList={}",JSONObject.toJSON(vehicleCouponList));
        Collections.sort(vehicleCouponList);
        if(conMap!=null&&notConMap!=null){
            vehicleCouponList.forEach(ve->{
                if((ve.getContinuteType()==0||ve.getContinuteType()==1)&&ve.getStatus()==0&&ve.getIsValidChannel()==1){
                    conMap.putIfAbsent(ve.getMonths(),ve);
                }
                if((ve.getContinuteType()==0||ve.getContinuteType()==2)&&ve.getStatus()==0&&ve.getIsValidChannel()==1){
                    notConMap.putIfAbsent(ve.getMonths(),ve);
                }
            });
        }
        return new MessageModel(vehicleCouponList);
    }

    @Override
    public VehicleCoupon getVehicleCouponsBySerialKey(String serialKey, Long uid, Long virtualUid) {
        return vehicleCouponMapper.getVehicleCouponsBySerialKey(serialKey,uid,virtualUid);
    }

    /**
     * 首次发送优惠
     * 1. virtualUid  valid 存储  virtualUid
     * 2. user_id  valid && virtualUid  valid 存储 user_id &virtualUid
     * @param userId
     * @param channel
     * @return
     */
    public MessageModel getFirstCoupon(Long userId,String channel,Long virtualUid,String deviceId){
        if((userId==null&&virtualUid==null)||StringUtils.isBlank(channel)){
            log.error("vehicle_first_coupon：getFirstCoupon function uid={} or channel={} or virtualUid={},or deviceId={} is empty value !",userId,channel,virtualUid,deviceId);
            return new MessageModel(SystemCodeErrorConstant.COUPON_PARAM_EMPTY);
        }
        Long uid=virtualUid!=null&&virtualUid>0?virtualUid:userId;
        if ((uid == null || uid == 0)){
            log.info("vehicle_first_coupon：getFirstCoupon uid is ZERO!userId={},virtualUid={}",userId,virtualUid);
            return new MessageModel(SystemCodeErrorConstant.VEHICLE_DOUBLE11_DATASET_EMPTY);
        }
        String devId=StringUtils.isNotBlank(deviceId)?deviceId:String.valueOf("deviceUid"+uid);
        String lowChannel=channel.toLowerCase();
        String redisLockKey= VEHICLE_COUPON_KEY+devId+lowChannel;
        String requestId = UUID.randomUUID().toString();
        try {
            if (RedisDAO.tryGetDistributedLock(redisLockKey, requestId, 5)){
                List<DataDataset> dataDatasets= dataModelService.getDataSetByActivityCode("vehicle_channel_coupon");
                if(CollectionUtils.isEmpty(dataDatasets)){
                    log.info("vehicle_first_coupon：getFirstCoupon dataDatasets is empty!");
                    return new MessageModel(SystemCodeErrorConstant.VEHICLE_DOUBLE11_DATASET_EMPTY);
                }
                // 获取渠道对应挡位配置 数据集
                DataDataset dataset =dataDatasets.get(0);
                List<DataItem>  dataItemList= dataModelService.getVehicleDataItemByMid(dataset.getId());
                if(CollectionUtils.isEmpty(dataItemList)){
                    log.info("vehicle_first_couponn：getFirstCoupon dataItemList is empty!");
                    return new MessageModel(SystemCodeErrorConstant.VEHICLE_DOUBLE11_DATASET_EMPTY);
                }
                List<VehicleCouponBO> vehicleCoupons=new ArrayList<>();
                dataItemList.forEach(dataItem -> {
                    try{
                        JSONObject jsonObject= JSONObject.parseObject(dataItem.getContent());
                        // 是否发放优惠券
                        Integer isSend=jsonObject.getInteger("is_send");
                        if(isSend.equals(0)){
                            log.info("vehicle_first_coupon：getFirstCoupon dataItem  isSend =0 .id={}!",dataItem.getId());
                            return;
                        }
                        Integer newUserSend=jsonObject.getInteger("new_user_send");
                        if(newUserSend!=null&&newUserSend.equals(1)&&!isNewUserSend(userId)){
                            log.info("vehicle_first_coupon：getFirstCoupon isNewUserSend is false,userId={}",userId);
                            return;
                        }
                        String channels=jsonObject.getString("channel");
                        String[] channelArray=channels.split(",");
                        List<String> channelList=Arrays.stream(channelArray).filter(StringUtils::isNotBlank).map(StringUtils::lowerCase).collect(Collectors.toList());
                        if(!channelList.contains(lowChannel)){
                            log.info("vehicle_first_coupon：getFirstCoupon dataItem channels is empty .id={}!",dataItem.getId());
                            return;
                        }
                        String  startTime=jsonObject.getString("start_time");
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
                        Date startTimeDate = sdf.parse(startTime);

                        String  endTime=jsonObject.getString("end_time");
                        Date endTimeDate = sdf.parse(endTime);
                        if(endTimeDate.before(new Date())){
                            log.info("vehicle_first_coupon：getFirstCoupon dateTime.before .id={}!",dataItem.getId());
                            return;
                        }
                        String batchId=jsonObject.getString("batch_id");
                        String batchDetail=jsonObject.getString("detail");
                        if(StringUtils.isBlank(batchId)){
                            log.info("vehicle_first_coupon：batchId is empty ,dataItem{}!",JSONObject.toJSON(dataItem));
                            return;
                        }
                        // 判断该批次是否发放
                        String rediskey= devId +lowChannel+batchId;
                        if(RedisDAO.exists(rediskey)){
                            log.info("vehicle_first_coupon：rediskey has send! rediskey={}!",rediskey);
                            return;
                        }
                        if(userId!=null&&userId!=0){
                            String userIdRediskey= userId +lowChannel+batchId;
                            if(RedisDAO.exists(userIdRediskey)){
                                log.info("vehicle_first_coupon：user_id rediskey has send! rediskey={}!",rediskey);
                                return;
                            }
                        }
                        Integer isAbt=jsonObject.getInteger("is_abt");
                        String couponTemplateId=jsonObject.getString("coupon_templeate_id");
                        if(isAbt!=null&&isAbt==1&&StringUtils.isNotBlank(devId)){
                            String abtValue=jsonObject.getString("abt_value");
                            couponTemplateId=checkABTest(devId, abtValue);
                            if(StringUtils.isBlank(couponTemplateId)){
                                log.info("vehicle_first_coupon：couponTemplateId is empty={}!",couponTemplateId);
                                return;
                            }
                        }
                        List<VehicleCouponRecord> vehicleCouponRecords= vehicleCouponRedordMapper.getVehicleCouponRecordByUid(userId,devId,lowChannel,batchId);
                        if(!CollectionUtils.isEmpty(vehicleCouponRecords)){
                            RedisDAO.getInstance().addString(rediskey,"1",60*60*24*60);
                            log.info("vehicle_first_coupon：rediskey has send! rediskey={}!",rediskey);
                            return ;
                        }
                        String[] couponTemplateIdArray=couponTemplateId.split(",");
                        List<String> couponTemplateIdlList=Arrays.stream(couponTemplateIdArray).filter(StringUtils::isNotBlank).collect(Collectors.toList());
                        log.info("vehicle_first_coupon：couponTemplateIdlList ,couponTemplateIdlList{}!",JSONObject.toJSON(couponTemplateIdlList));
                        couponTemplateIdlList.forEach(templateId->{
                            try{
                                String vehicleKey=VEHICLE_COUPON_KEY+templateId;
                                List<DataItem> dataItems= dataModelService.getVehicleDataItemByActivityCode(vehicleKey);
                                if(CollectionUtils.isEmpty(dataItems)||dataItems.size()!=1){
                                    log.error("vehicle vehicle_first_coupon coupon：getFirstCoupon  function dataItems  is empty or  size >1 ,vehicleKey={}", vehicleKey);
                                    return ;
                                }
                                DataItem item= dataItems.get(0);
                                JSONObject jsonItemObject = JSONObject.parseObject(item.getContent());
                                Integer temId=jsonItemObject.getInteger("coupon_templeate_id");
                                if(!temId.equals(Integer.parseInt(templateId))){
                                    return;
                                }
                                String name=jsonItemObject.getString("name");
                                Integer vipType=jsonItemObject.getInteger("vip_type");
                                Integer months=jsonItemObject.getInteger("months");
                                Integer templeateId=jsonItemObject.getInteger("coupon_templeate_id");
                                Integer continuteType=jsonItemObject.getInteger("continute_type");
                                // 优惠金额
                                Double discountAmount=jsonItemObject.getDouble("discount_amount");
                                VehicleCoupon vehicleCoupon=new VehicleCoupon();
                                VehicleCouponBO vehicleCouponBO=new VehicleCouponBO();
                                // 渠道
                                vehicleCoupon.setChannel(lowChannel);
                                // 优惠券名称
                                vehicleCoupon.setCouponName(name);
                                vehicleCoupon.setDiscountAmount(discountAmount);
                                vehicleCoupon.setSerialKey(IdUtil.simpleUUID());
                                vehicleCoupon.setType(vipType);
                                vehicleCoupon.setMonths(months);
                                vehicleCoupon.setTemplateId(templeateId);
                                vehicleCoupon.setUid(userId);
                                vehicleCoupon.setVirtualUid(virtualUid);
                                vehicleCoupon.setCreateTime(new Date());
                                vehicleCoupon.setUpdateTime(new Date());
                                vehicleCoupon.setStartTime(startTimeDate);
                                vehicleCoupon.setEndTime(endTimeDate);
                                vehicleCoupon.setUsed(0);
                                vehicleCoupon.setBatchId(batchId);
                                vehicleCoupon.setContinuteType(continuteType);
                                vehicleCoupon.setDetail(batchDetail);
                                vehicleCoupon.setDeviceId(devId);
                                vehicleCouponMapper.insert(vehicleCoupon);
                                if(vehicleCoupon.getId()!=null){
                                    vehicleCouponBO.setCouponName(name);
                                    vehicleCouponBO.setCouponUniqueId(vehicleCoupon.getSerialKey());
                                    vehicleCouponBO.setDetail(batchDetail);
                                    vehicleCouponBO.setDiscountAmount(discountAmount);
                                    vehicleCouponBO.setStartTime(startTimeDate);
                                    vehicleCouponBO.setEndTime(endTimeDate);
                                    vehicleCouponBO.setIsValidChannel(1);
                                    vehicleCouponBO.setStatus(0);
                                    vehicleCouponBO.setChannel(lowChannel);
                                    vehicleCouponBO.setMonths(months);
                                    vehicleCouponBO.setContinuteType(continuteType);
                                }
                                vehicleCoupons.add(vehicleCouponBO);
                            }catch (Exception e){
                                log.error("vehicle_first_coupon：couponTemplateIdlList.forEach templateId={} is error !",templateId,e);
                            }
                        });
                        if(userId!=null&&userId!=0){
                            String userIdRediskey= userId +lowChannel+batchId;
                            RedisDAO.getInstance().addString(userIdRediskey,"1",60*60*24*60);
                        }
                        RedisDAO.getInstance().addString(rediskey,"1",60*60*24*60);
                        VehicleCouponRecord vehicleCouponRecord=new VehicleCouponRecord();
                        vehicleCouponRecord.setBatchId(batchId);
                        vehicleCouponRecord.setChannel(lowChannel);
                        vehicleCouponRecord.setUid(userId);
                        vehicleCouponRecord.setVirtualUid(virtualUid);
                        vehicleCouponRecord.setDeviceId(devId);
                        vehicleCouponRecord.setCreateTime(new Date());
                        vehicleCouponRecord.setUpdateTime(new Date());
                        vehicleCouponRedordMapper.insert(vehicleCouponRecord);
                    }catch (Exception e){
                        log.error("vehicle_first_coupon： dataItemList.forEach dataItem={} is error !",JSONObject.toJSON(dataItem),e);
                    }
                });
                return new MessageModel(vehicleCoupons);
            }else{
                log.info("vehicle_first_coupon getFirstCoupon lock -----failed!----");
            }
        } catch (Exception e) {
            log.error("vehicle_first_coupon getFirstCoupon lock  exception, exception ->", e);
        } finally {
            RedisDAO.releaseDistributedLock(redisLockKey, requestId);
        }
        return new MessageModel();
    }

    /**
     * 检查AB—Test分流
     */
    private String checkABTest(String deviceId,String abtValue){
        try {
            if(StringUtils.isBlank(deviceId)||StringUtils.isBlank(abtValue)){
                log.info("checkABTest：deviceId={} or abtValue={} is empty!",deviceId,abtValue);
                return "";
            }
            JSONObject abtJSON=JSONObject.parseObject(abtValue);
            String abtSign=abtJSON.getString("abtSign");
            JSONArray abtValueJSON= abtJSON.getJSONArray("abtValue");
            Map<String, String> abtMap =abtValueJSON.stream().collect(Collectors.toMap(object -> {
                        JSONObject item = (JSONObject) object;
                        return item.getString("abtKey");
                    },
                    object -> {
                        JSONObject item = (JSONObject) object;
                        return item.getString("templateId");
                    }));
            String url = "http://newabapi.kuwo.cn:8081/abtest/kuwo/algorithm/info";
            Map<String, Object> param = new HashMap<>();
            param.put("businessId", 14);
            param.put("uid", "0");
            param.put("device", deviceId);
            param.put("platform", "ar");
            param.put("channelId", 336);
            List<String> paramList = new ArrayList<>();
            paramList.add(abtSign);
            param.put("moduleKeys", paramList);
            String json = JSONUtil.toJsonStr(param);
            String result = HttpRequest.post(url)
                    .body(json)
                    .timeout(600)
                    .execute().body();
            log.info("checkABTest json={},result={}", json, result);
            JSONObject resultObject = JSONObject.parseObject(result);
            if (resultObject.getInteger("code") != 0) {
                log.error("checkABTest code error");
                return "";
            }
            JSONObject dataJSONObject = resultObject.getJSONObject("data");
            JSONObject infoJSONObject = dataJSONObject.getJSONObject("mapTestInfo");
            JSONObject aduJSONObject = infoJSONObject.getJSONObject(abtSign);
            JSONObject mapParamsJSONObject = aduJSONObject.getJSONObject("mapParams");
            String group = mapParamsJSONObject.getString("group");
            ///String group = RandomStringUtils.random(1,"AB");
            log.info("checkABTest deviceId={}, group={}，templeate={}",deviceId, group,  abtMap.get(group));
            return abtMap.get(group);
        }
        catch (Exception e){
            log.error("checkABTest invoke  has error",e );
        }
        return "";
    }
    /**
     * 是否是新用户发放
     *
     * @param userId
     * @return
     */
    public boolean isNewUserSend(Long userId){
        try{
            if(userId==null||userId==0){
                log.info("vehicle_first_coupon：isNewUserSend newUserSend userId =0 .userId={}!",userId);
                return false;
            }
            MessageModel messageModel=vehicleService.getVehicleVipInfo(String.valueOf(userId),null);
            if (!messageModel.getCode().equals(SystemCodeErrorConstant.SUCCESS.getCode())) {
                log.info("vehicle_first_coupon：isNewUserSend" +
                        " newUserSend userId =0 .userId={}!",userId);
                return false;
            }
            Map<String, Object> map= (Map<String, Object>) messageModel.getData();
            long vipLuxuryExpire = 0;
            long svipExpire = 0;
            long vipExpire = 0;
            long  vipVehicleExpire=0;
            long  vipmExpire=0;
            if (map.containsKey("vipLuxuryExpire")){
                vipLuxuryExpire = Long.parseLong(String.valueOf(map.get("vipLuxuryExpire")));
            }
            if (map.containsKey("svipExpire")){
                svipExpire =  Long.parseLong(String.valueOf(map.get("svipExpire")));
            }
            if (map.containsKey("vipExpire")){
                vipExpire = Long.parseLong(String.valueOf(map.get("vipExpire")));
            }
            if (map.containsKey("vipVehicleExpire")){
                vipVehicleExpire = Long.parseLong(String.valueOf(map.get("vipVehicleExpire")));
            }
            if (map.containsKey("vipmExpire")){
                vipmExpire = Long.parseLong(String.valueOf(map.get("vipmExpire")));
            }
            if(vipLuxuryExpire==0&&svipExpire==0&&vipExpire==0&&vipVehicleExpire==0&&vipmExpire==0){
                return true;
            }
        }catch (Exception e){
            log.error("vehicle_first_coupon：getFirstCoupon newUserSend is not a valid new user!user_id={}",userId,e);
        }
        return false;
    }
}
