package com.memberintergral.carservice.service.impl;


import com.memberintergral.carservice.domain.DTO.PayInfoDTO;
import com.memberintergral.carservice.domain.entity.Product;

import java.util.Date;

/**
 * 活动参数
 *
 */
public class ActivityParams {
    /**
     *  渠道, 会携带
     */
    private String paySrc;
    /**
     *  当前时间, 会携带
     */
    private Date currentDate;
    /**
     * 活动码 会携带
     */
    private String type;

    /**
     * 自动续费标识 （校验价格时有, 查询活动有效期没有）
     */
    private String autoPay;
    /**
     * 用户Id（校验价格时有, 查询活动有效期没有）
     */
    private Long uid;
    /**
     * 虚拟 （校验价格时有, 查询活动有效期没有）
     */
    private Long virtualUid;

    /**
     * 当前月份 （校验价格时有, 查询活动有效期没有）
     */
    private Integer cnt;

    /**
     * 请求金额
     */
    private double requestCredit;

    /**
     * 适配的product
     */
    private Product product;

    /**
     * 优惠券Id
     */
    private String couponUniqueId;
    /**
     * 判断来源
     */
    private boolean isActivityCheck = false;

    /**
     * 价格
     */
    private double credit;

    private String platform;

    private String src;

    private Long productTypeId;

    private Integer fromType;

    /**
     * 策略id
     */
    private String filterId;

    public String getFilterId() {
        return filterId;
    }

    public void setFilterId(String filterId) {
        this.filterId = filterId;
    }

    public Integer getFromType() {
        return fromType;
    }

    public void setFromType(Integer fromType) {
        this.fromType = fromType;
    }

    public String getSrc() {
        return src;
    }

    public void setSrc(String src) {
        this.src = src;
    }


    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public double getCredit() {
        return credit;
    }

    public void setCredit(double credit) {
        this.credit = credit;
    }

    public boolean isActivityCheck() {
        return isActivityCheck;
    }

    public void setActivityCheck(boolean activityCheck) {
        isActivityCheck = activityCheck;
    }

    public Product getProduct() {
        return product;
    }

    public void setProduct(Product product) {
        this.product = product;
    }
    /**
     * payInfo dto实体（校验价格时有, 查询活动有效期没有）
     */
    private PayInfoDTO payInfoDTO;

    /**
     *  校验调用来源
      */
    private boolean checkPriceSource = false;

    public boolean isCheckPriceSource() {
        return checkPriceSource;
    }

    public void setCheckPriceSource(boolean checkPriceSource) {
        this.checkPriceSource = checkPriceSource;
    }

    public PayInfoDTO getPayInfoDTO() {
        return payInfoDTO;
    }

    public void setPayInfoDTO(PayInfoDTO payInfoDTO) {
        this.payInfoDTO = payInfoDTO;
    }

    public String getPaySrc() {
        return paySrc;
    }

    public void setPaySrc(String paySrc) {
        this.paySrc = paySrc;
    }

    public Date getCurrentDate() {
        return currentDate;
    }

    public void setCurrentDate(Date currentDate) {
        this.currentDate = currentDate;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getAutoPay() {
        return autoPay;
    }

    public void setAutoPay(String autoPay) {
        this.autoPay = autoPay;
    }

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public Long getVirtualUid() {
        return virtualUid;
    }

    public void setVirtualUid(Long virtualUid) {
        this.virtualUid = virtualUid;
    }

    public Integer getCnt() {
        return cnt;
    }

    public void setCnt(Integer cnt) {
        this.cnt = cnt;
    }

    public double getRequestCredit() {
        return requestCredit;
    }

    public void setRequestCredit(double requestCredit) {
        this.requestCredit = requestCredit;
    }

    public String getCouponUniqueId() {
        return couponUniqueId;
    }

    public void setCouponUniqueId(String couponUniqueId) {
        this.couponUniqueId = couponUniqueId;
    }

    public Long getProductTypeId() {
        return productTypeId;
    }

    public void setProductTypeId(Long productTypeId) {
        this.productTypeId = productTypeId;
    }

}
