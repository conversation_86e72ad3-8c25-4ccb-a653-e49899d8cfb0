package com.memberintergral.carservice.service.impl;

import com.memberintergral.carservice.config.constant.MessageModel;
import com.memberintergral.carservice.config.nacos.CarConfigNacos;
import com.memberintergral.carservice.domain.BO.CarConfigBO;
import com.memberintergral.carservice.service.ChannelService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.http.HttpStatus;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Service
public class ChannelServiceImpl implements ChannelService {

    @Autowired
    private CarConfigNacos carConfigNacos;

    @Autowired
    private VehicleActivityService vehicleActivityService;

    @Override
    public MessageModel checkSvipSupport(String channel, String uid) {
        Map<String, Object> result = new HashMap<>();
        boolean isSvipChannel = isSvipChannel(channel);
        result.put("isSVIPchannel", isSvipChannel);
        result.put("isSVIPshow", isShowChannel(channel));
        result.put("carVipDesc", StringUtils.defaultIfBlank(carConfigNacos.getCarConfigBO().carVipDesc,""));
        result.put("svipDesc", StringUtils.defaultIfBlank(carConfigNacos.getCarConfigBO().svipDesc,""));
        result.put("ctime", System.currentTimeMillis());
        if (isSvipChannel && isRecallUser(uid)) {
            result.put("isSVIPshow", false);
        } else {
            result.put("isSVIPshow", isShowChannel(channel));
        }
        return new MessageModel(result);
    }

    public boolean isShowChannel(String channel) {
        CarConfigBO carConfigBO = Optional.ofNullable(carConfigNacos.getCarConfigBO())
                .orElse(new CarConfigBO());
        List<String> svipShowChannelList = carConfigBO.getSvipShowChannelList();
        if (CollectionUtils.isNotEmpty(svipShowChannelList)&&svipShowChannelList.contains(channel)) {
            return true;
        }
        return false;
    }

    public boolean isSvipChannel(String channel) {
        CarConfigBO carConfigBO = Optional.ofNullable(carConfigNacos.getCarConfigBO())
                .orElse(new CarConfigBO());
       List<String> channelList = carConfigBO.getSvipChannelList();
        if (CollectionUtils.isNotEmpty(channelList)&&channelList.contains(channel)) {
            return true;
        }
        return false;
    }

    public boolean isRecallUser(String uid) {
        MessageModel message = vehicleActivityService.getAllVipVehicleExpire(uid, "2BD9A720A6D04B0880082BE9E8420C34");
        if (message.getCode().equals(HttpStatus.SC_OK)) {
            Object data = message.getData();
            if (data instanceof Map) {
                try {
                    Map<String, Object> actualMap = (Map<String, Object>) data;
                    long svipVehicleExpire = (long) actualMap.get("svipVehicleExpire");
                    long vipVehicleExpire = (long) actualMap.get("vipVehicleExpire");
                    if (svipVehicleExpire > 0 && vipVehicleExpire - svipVehicleExpire <= 60000) { // 60 * 1000 时间差超过1分钟才算真正超过(排除数据录入时差)
                        return false;
                    }
                    long currentTimeMillis = System.currentTimeMillis();
                    if (vipVehicleExpire > 0 && vipVehicleExpire < currentTimeMillis) {
                        return true;
                    }
                } catch (NumberFormatException e) {
                    log.info("isRecallUser1 is fail, err is " + e.getMessage());
                    return false;
                }
            }
        }
        return false;
    }
}
