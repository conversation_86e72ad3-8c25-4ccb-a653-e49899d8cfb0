package com.memberintergral.carservice.service;

import com.memberintergral.carservice.config.nacos.CarConfigNacos;
import com.memberintergral.carservice.config.redis.RedisKey;
import com.memberintergral.carservice.config.redis.added.RedisDAO;
import com.memberintergral.carservice.service.impl.AsyncService;
import com.memberintergral.carservice.service.impl.TagStarService;
import com.memberintergral.carservice.service.impl.UserService;
import com.memberintergral.carservice.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class FreeModeService {

    @Autowired
    private UserService userService;

    @Autowired
    private TagStarService tagStarService;

    @Autowired
    private CarConfigNacos carConfigNacos;

    @Autowired
    private AsyncService asyncService;

    public  boolean openFreeMode(String uid,String virtualUid){


        List<String> whiteList=carConfigNacos.getCarConfigBO().getFreeModeUids();
        if (!whiteList.isEmpty() &&  !whiteList.contains(uid)){
            log.info("openFreeMode blacklist uid=>{},virtualUid=>{}",uid,virtualUid);
            return false;
        }

        long virtualUidNum= NumberUtils.toLong(virtualUid);
        long uidLong=NumberUtils.toLong(uid);
        if (virtualUidNum<1 && uidLong <1){
            log.info("openFreeMode params error uid=>{},virtualUid=>{}",uidLong,virtualUidNum);
            return false;
        }

        int vidEnterTimes=0;
        int uidEnterTimes=0;
        /*
        boolean tagStarResult=tagStarService.checkTagStar("10004144",uid);
        if (!tagStarResult){
            return false;
        }*/
        //近7天用户[登录、未登录均包含]累计进入会员中心次数
        try {
            if (virtualUidNum>0){
                String vidKey=String.format(RedisKey.CAR_CENTER_VID_ENTER_TIMES_MAP,virtualUid);
                Map<String,String>vidMap =RedisDAO.getInstance().hgetall(vidKey);
                for (Map.Entry<String,String> entry:vidMap.entrySet()){
                    //兜底操作:再次判断日期是否超过7天
                    if (DateUtils.passOverSevenDate(entry.getKey())){
                        continue;
                    }
                    vidEnterTimes+=NumberUtils.toInt(entry.getValue());
                }

            }
            if (uidLong>0){
                String uidKey=String.format(RedisKey.CAR_CENTER_UID_ENTER_TIMES_MAP,uid);
                Map<String,String>uidMap =RedisDAO.getInstance().hgetall(uidKey);
                for (Map.Entry<String,String> entry:uidMap.entrySet()){
                    //兜底操作:再次判断日期是否超过7天
                    if (DateUtils.passOverSevenDate(entry.getKey())){
                        continue;
                    }
                    vidEnterTimes+=NumberUtils.toInt(entry.getValue());
                }
            }
        } catch (Exception e) {
            log.error("免费模式获取登录日期出错",e);
        }
        //统计次数
        asyncService.loginStatis(uidLong,virtualUidNum);
        //展示免模的阈值
        int freeModeShowTotal=carConfigNacos.getCarConfigBO().getFreeModeShowTotal();
        //+1是将本次访问次数自动加上
        int currentTotal= vidEnterTimes+uidEnterTimes+1;
        log.info("openFreeMode data:  uid:{},vid:{},vidEnterTimes:{},uidEnterTimes:{},currentTotal:{},freeModeShowTotal:{}",uid,virtualUid,vidEnterTimes,uidEnterTimes,currentTotal,freeModeShowTotal);
        if (currentTotal<freeModeShowTotal){
            log.info("openFreeMode error:  show total uid=>{},vid=>{},currentTotal=>{}",uidLong,virtualUidNum,currentTotal);
            return false;
        }
        //用户是否开通过VIP
        long vipmExpire=userService.getVipmExpire(uid,virtualUid);
        if (vipmExpire!=0){
            log.info("openFreeMode error:  is vip user uid=>{},vid=>{},vipmExpire=>{}",uidLong,virtualUidNum,vipmExpire);
            return false;
        }
        return true;
    }

}
