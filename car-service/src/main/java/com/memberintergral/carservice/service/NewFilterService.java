package com.memberintergral.carservice.service;


import com.alibaba.nacos.shaded.com.google.protobuf.ServiceException;
import com.baomidou.mybatisplus.extension.service.IService;
import com.memberintergral.carservice.config.constant.MessageModel;
import com.memberintergral.carservice.config.exception.VehicleException;
import com.memberintergral.carservice.domain.VO.PriceGearVO;
import com.memberintergral.carservice.domain.entity.Filter;


/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-04
 */
public interface NewFilterService extends IService<Filter> {

    /**
     * 获取车载价格档位
     *
     * @param priceGearVO
     * @return
     * @throws ServiceException
     * @throws VehicleException
     */
    MessageModel getNewPriceGearInfo(PriceGearVO priceGearVO);

    MessageModel handlePaymentRetention(PriceGearVO priceGearVO, String filterId);

}
