package com.memberintergral.carservice.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.comparator.VersionComparator;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.com.google.protobuf.ServiceException;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.commercal.uiservice.enity.BasicUInfo;
import com.commercal.uiservice.enity.SignStateInfo;
import com.commercal.uiservice.enity.VipInfo;
import com.commercal.uiservice.service.UiInfoService;
import com.commerical.abserviceapi.req.AbRequest;
import com.commerical.abserviceapi.resp.AbResponse;
import com.commerical.abserviceapi.service.AbRuleService;
import com.memberintergral.carservice.config.constant.MessageModel;
import com.memberintergral.carservice.config.constant.PayConstant;
import com.memberintergral.carservice.config.constant.SystemCodeErrorConstant;
import com.memberintergral.carservice.config.enums.*;
import com.memberintergral.carservice.config.monitor.CarMonitor;
import com.memberintergral.carservice.config.nacos.CarConfigNacos;
import com.memberintergral.carservice.config.nacos.LowPriceChannelNacos;
import com.memberintergral.carservice.config.redis.RedisKey;
import com.memberintergral.carservice.config.redis.added.RedisIndicatorDAO;
import com.memberintergral.carservice.config.redis.added.RedisLowPriceDAO;
import com.memberintergral.carservice.domain.BO.*;
import com.memberintergral.carservice.domain.VO.PriceGearVO;
import com.memberintergral.carservice.domain.entity.Filter;
import com.memberintergral.carservice.domain.entity.Gear;
import com.memberintergral.carservice.domain.entity.PayDesk;
import com.memberintergral.carservice.domain.entity.VehicleOrder;
import com.memberintergral.carservice.enums.VipTypeEnum;
import com.memberintergral.carservice.mapper.FilterMapper;
import com.memberintergral.carservice.mapper.VehicleOrderMapper;
import com.memberintergral.carservice.service.*;
import com.memberintergral.carservice.upgrade.LuxVIPUpdateInterceptor;
import com.memberintergral.carservice.upgrade.PriceInterceptor;
import com.memberintergral.carservice.upgrade.VIPMUpdateInterceptor;
import com.memberintergral.carservice.upgrade.VehicleUpdateInterceptor;
import com.memberintergral.carservice.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.stream.Collectors;

import static com.memberintergral.carservice.config.constant.VehicleConstant.*;

/**
 * <p>
 * 策略服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-04
 */
@Service
@Slf4j
@DS("vipconf")
public class NewFilterServiceImpl extends ServiceImpl<FilterMapper, Filter> implements NewFilterService {
    private static final String TRACE_ID = "TRACE_ID";
    @Autowired
    private PayDeskService payDeskService;

    @Autowired
    private GearService gearService;

    @DubboReference
    private AbRuleService abRuleService;

    @Autowired
    private FilterMapper filterMapper;

    @Autowired
    private VehicleCouponService vehicleCouponService;

    @Autowired
    private VehicleService vehicleService;

    @Autowired
    private CarConfigNacos carConfigNacos;

    @Autowired
    private VehicleActivityService vehicleActivityService;

    @Autowired
    private LowPriceChannelNacos lowPriceChannelNacos;

    @Autowired
    private VirtualUserLoginUtil virtualUserLoginUtil;

    @Autowired
    private ChannelServiceImpl channelService;

    @Autowired
    private CacheUtils cacheUtils;

    @Autowired
    private CarMonitor carMonitor;

    @Autowired
    private VehicleOrderMapper vehicleOrderMapper;

    @DubboReference
    private UiInfoService uiInfoService;

    public static final String CASH_H5_RETAIN = "carh5retain";

    @Override
    public MessageModel getNewPriceGearInfo(PriceGearVO priceGearVO) {
        try {
            log.info("getNewPriceGearInfo param={}",JSONUtil.toJsonStr(priceGearVO));
            setDefaultValuesAndHandleSpecialCases(priceGearVO);
            long virtualUid=0;
            long uid=0;
            if(StringUtils.isNotBlank(priceGearVO.getVirtualUid())){
                virtualUid= Long.parseLong(priceGearVO.getVirtualUid());
            }
            if(StringUtils.isNotBlank(priceGearVO.getUid())){
                uid= Long.parseLong(priceGearVO.getUid());
            }
            Map<Integer,VehicleCouponBO> conMap=new HashMap<>();
            Map<Integer,VehicleCouponBO> notConMap=new HashMap<>();
            Map<String,List<VehiclePriceBO>> result=new LinkedHashMap<>();
            List<VehiclePriceBO> vehiclePriceVIPBOS=new ArrayList<>();
            List<VehiclePriceBO> vehiclePriceSVIPBOS=new ArrayList<>();
            if(StringUtils.isBlank(priceGearVO.getPayDeskSign())||StringUtils.isBlank(priceGearVO.getPlatform())){
                log.error("getNewPriceGearInfo error param empty ,priceGearVO={}",JSONUtil.toJsonStr(priceGearVO));
                carMonitor.THIRD_ORDER_ERROR.increment();
                return new MessageModel(SystemCodeErrorConstant.CAR_PARAM_ERROR);
            }
            result.put("vipPrice",vehiclePriceVIPBOS);
            result.put("svipPrice",vehiclePriceSVIPBOS);
            handleCouponLogic(priceGearVO,uid, virtualUid,conMap,notConMap);
            int autoPay= vehicleActivityService.getCarAutoPay(priceGearVO.getUid(),priceGearVO.getVirtualUid());
            if (carConfigNacos.getCarConfigBO().getUpgradeChannel() == null || !carConfigNacos.getCarConfigBO().getUpgradeChannel().contains(priceGearVO.getChannel())) {
                if (StringUtils.isNotBlank(priceGearVO.getUid()) && MyNumberUtils.toLONG(priceGearVO.getUid()) > 0 && (PayConstant.channelList.contains(priceGearVO.getChannel()))) {
                    checkUpdateSVIP(uid, vehiclePriceVIPBOS, autoPay);
                    log.info("getNewPriceGearInfo checkUpdateSVIP userId={} ,virtualUid={}, vehiclePriceVIPBOS={}!", priceGearVO.getUid(), priceGearVO.getVirtualSid(), JSONUtil.toJsonStr(vehiclePriceVIPBOS));
                }
            }
            if(StringUtils.equals(priceGearVO.isChild,"1")){
                checkUpdateVehicleSVIP(uid,vehiclePriceSVIPBOS,autoPay);
            }
            PayDesk payDesk =cacheUtils.getPayDeskCache.get(priceGearVO.getPayDeskSign());
            CarParamBO carParamBO=new CarParamBO();
            carParamBO.setVipType(VipTypeEnum.VEHICLE_VIP.getType());
            carParamBO.setPayDeskId(payDesk.getId());
            carParamBO.setPlatform(priceGearVO.getPlatform());
            carParamBO.setAutoPay(autoPay);
            carParamBO.setPayDeskSign(priceGearVO.getPayDeskSign());
            List<Gear> vehicleGears= cacheUtils.getGearCache.get(carParamBO);
            carParamBO.setVipType(VipTypeEnum.SUPER_VIP.getType());
            List<Gear> svipgears= cacheUtils.getGearCache.get(carParamBO);
            List<Gear> carGears= ListUtils.merageList(vehicleGears,svipgears);
            CalculatePriceBO calculatePriceBO=new CalculatePriceBO();
            calculatePriceBO(priceGearVO, calculatePriceBO);
            List<Filter> filters = invokeValidFilter(priceGearVO, carGears,  calculatePriceBO);
            Collections.sort(filters);
            log.info("getNewPriceGearInfo gears={}",JSONUtil.toJsonStr(carGears));
            log.info("getNewPriceGearInfo filters={}",JSONUtil.toJsonStr(filters));
            Map<Integer, List<Filter>> filterMap = filters.stream().collect((Collectors.groupingBy(Filter::getGearId)));
            groupByVipTypes(carGears, filterMap,priceGearVO,result, conMap,notConMap,  calculatePriceBO);
            log.info("getNewPriceGearInfo result={}",JSONUtil.toJsonStr(result));
            return new MessageModel(result);
        }catch (Exception e){
            log.error("getNewPriceGearInfo error doudi price! priceGearVO={}",JSONUtil.toJsonStr(priceGearVO),e);
            carMonitor.THIRD_ORDER_ERROR.increment();
        }
        Map<Integer,Double> conMap=new HashMap<>();
        Map<Integer,Double> notConMap=new HashMap<>();
        Map<String,List<VehiclePriceBO>> result=new LinkedHashMap<>();
        List<VehiclePriceBO> vehiclePriceVIPBOS=new ArrayList<>();
        List<VehiclePriceBO> vehiclePriceSVIPBOS=new ArrayList<>();
        CarActivityMonthPriceEnum.getShowPrice(conMap,notConMap,vehiclePriceVIPBOS,true);
        CarActivityAutoPayMonthPriceEnum.getShowPrice(conMap,notConMap,vehiclePriceVIPBOS,true);
        CarActivitySuperVipMonthPriceEnum.getShowPrice(conMap,notConMap,vehiclePriceSVIPBOS,false);
        CarActivitySuperVipAutoPayMonthPriceEnum.getShowPrice(conMap,notConMap,vehiclePriceSVIPBOS,false);
        result.put("vipPrice",vehiclePriceVIPBOS);
        result.put("svipPrice",vehiclePriceSVIPBOS);
        return new MessageModel(result);
    }

    private void checkUpdateVehicleSVIP(Long userId,List<VehiclePriceBO> vehiclePriceVIPBOS,int autoPay){
        UserInfoBO userInfoBO= invokeUserInfo(userId);
        userInfoBO.setVipVehicleAutoPayUser(autoPay);
        if(userInfoBO==null){
            return;
        }
        PriceInterceptor interceptor= new VehicleUpdateInterceptor(userInfoBO);
        interceptor.doAuth(userId,vehiclePriceVIPBOS);
    }

    /**
     * 端外收银台支付挽留
     *
     * @param priceGearVO
     * @param filterId
     * @return
     */
    public MessageModel handlePaymentRetention(PriceGearVO priceGearVO, String filterId) {
        try {
            if (StringUtils.isBlank(filterId) || "null" .equals(filterId)) {
                log.info("handlePaymentRetention filterId is null");
                new MessageModel();
            }
            log.info("handlePaymentRetention priceGearVO={}, filterId={}", JSONUtil.toJsonStr(priceGearVO), filterId);
            setDefaultValuesAndHandleSpecialCases(priceGearVO);
            if (StringUtils.isBlank(priceGearVO.getPayDeskSign()) || StringUtils.isBlank(priceGearVO.getPlatform())) {
                log.error("handlePaymentRetention error param empty ,priceGearVO={}", JSONUtil.toJsonStr(priceGearVO));
                carMonitor.CASH_RETENTION.increment();
                return new MessageModel(SystemCodeErrorConstant.CAR_PARAM_ERROR);
            }

            Map<Integer, VehicleCouponBO> conMap = new HashMap<>();
            Map<Integer, VehicleCouponBO> notConMap = new HashMap<>();
            Map<String, List<VehiclePriceBO>> result = new LinkedHashMap<>();
            List<VehiclePriceBO> vehiclePriceVIPBOS = new ArrayList<>();
            List<VehiclePriceBO> vehiclePriceSVIPBOS = new ArrayList<>();
            result.put("vipPrice", vehiclePriceVIPBOS);
            result.put("svipPrice", vehiclePriceSVIPBOS);

            Gear filterGear = findGearByFilterId(filterId);
            if (filterGear == null) {
                log.error("handlePaymentRetention gear empty , gear={}", JSONUtil.toJsonStr(filterGear));
                carMonitor.CASH_RETENTION.increment();
                return new MessageModel();
            }

            int autoPay = vehicleActivityService.getCarAutoPay(priceGearVO.getUid(), priceGearVO.getVirtualUid());
            log.info("handlePaymentRetention uid={}, autoPay={}", priceGearVO.getUid(), autoPay);

            // 取出收银台的 会员类型
            Integer autoPayGear = filterGear.getAutoPay();
            String vipTypeGear = filterGear.getVipType();
            Integer gearType = filterGear.getGearType();

            CalculatePriceBO calculatePriceBO = new CalculatePriceBO();
            calculatePriceBO(priceGearVO, calculatePriceBO);

            List<Gear> gears = Collections.emptyList();
            List<Filter> filters = Collections.emptyList();
            if (StringUtils.isNotBlank(priceGearVO.getUid()) && Long.parseLong(priceGearVO.getUid()) > 0) {
                // 已登录，走配置档位
                // 获取 gears
                priceGearVO.setPayDeskSign(CASH_H5_RETAIN);
                PayDesk payDesk = cacheUtils.getPayDeskCache.get(priceGearVO.getPayDeskSign());
                CarParamBO carParamBOH5 = new CarParamBO();
                carParamBOH5.setVipType(VipTypeEnum.VEHICLE_VIP.getType());
                carParamBOH5.setPayDeskId(payDesk.getId());
                if (autoPay == 1) {
                    carParamBOH5.setAutoPay(0);
                }
                carParamBOH5.setPlatform(priceGearVO.getPlatform());
                carParamBOH5.setPayDeskSign(priceGearVO.getPayDeskSign());
                List<Gear> vehicleGearsH5 = cacheUtils.getGearCache.get(carParamBOH5);
                carParamBOH5.setVipType(VipTypeEnum.SUPER_VIP.getType());
                List<Gear> svipgearsH5 = cacheUtils.getGearCache.get(carParamBOH5);
                List<Gear> carGearsH5 = ListUtils.merageList(vehicleGearsH5, svipgearsH5);
                filters = invokeValidFilter(priceGearVO, carGearsH5, calculatePriceBO);
                Collections.sort(filters);
                gears = carGearsH5.stream()
                        .filter(g -> Objects.equals(g.getVipType(), vipTypeGear))
                        .collect(Collectors.toList());
            } else {
                // 未登录，走默认档位
                // 筛选符合条件的 Gears
                priceGearVO.setPayDeskSign("carengine");
                PayDesk payDesk = cacheUtils.getPayDeskCache.get(priceGearVO.getPayDeskSign());
                CarParamBO carParamBO = new CarParamBO();
                carParamBO.setVipType(VipTypeEnum.VEHICLE_VIP.getType());
                carParamBO.setPayDeskId(payDesk.getId());
                carParamBO.setPlatform(priceGearVO.getPlatform());
                carParamBO.setAutoPay(autoPay);
                carParamBO.setPayDeskSign(priceGearVO.getPayDeskSign());
                List<Gear> vehicleGears = cacheUtils.getGearCache.get(carParamBO);
                carParamBO.setVipType(VipTypeEnum.SUPER_VIP.getType());
                List<Gear> svipgears = cacheUtils.getGearCache.get(carParamBO);
                List<Gear> carGears = ListUtils.merageList(vehicleGears, svipgears);
                List<Gear> carGearsFilter = carGears.stream()
                        .filter(g -> Objects.equals(g.getVipType(), vipTypeGear))
                        .collect(Collectors.toList());

                filters = invokeValidFilter(priceGearVO, carGearsFilter, calculatePriceBO);

                Collections.sort(filters);
                // 提取 filters 中所有 gearId
                Set<Integer> filterGearIds = filters.stream()
                        .map(Filter::getGearId)
                        .collect(Collectors.toSet());

                List<Integer> priorityOrder = Arrays.asList(1, 3, 6, 12);
                gears = carGearsFilter.stream()
                        // 只保留优先级范围内的 gearType
                        .filter(g -> {
                            if (g.getGearType() == 1 && gearType.equals(1)) {
                                return autoPayGear == 0 ? g.getAutoPay() == 1 : g.getAutoPay() == 0;
                            } else {
                                return priorityOrder.contains(g.getGearType()) && !g.getGearType().equals(gearType);
                            }
                        })
                        // 按 gearType 分组，只保留 rank 最大的 Gear
                        .filter(g -> filterGearIds.contains(g.getId()))
                        // 按照优先级排序
                        .sorted(Comparator.comparingInt(g -> priorityOrder.indexOf(g.getGearType())))
                        .collect(Collectors.toList());

            }
            if (CollectionUtils.isEmpty(filters) || CollectionUtils.isEmpty(gears)){
                log.info("handlePaymentRetention filters is {}, firstTwoGear is {}", filters, gears);
                return new MessageModel();
            }

            Map<Integer, List<Filter>> filterMap = filters.stream()
                    .filter(f -> {
                        // 过滤渠道
                        if (StringUtils.isBlank(f.getFilterChannel())) {
                            return true;
                        }
                        List<String> filterChannels = Arrays.asList(f.getFilterChannel().split(","));
                        if (CollectionUtil.isNotEmpty(filterChannels)) {
                            return filterChannels.contains(priceGearVO.getChannel());
                        }
                        if (StringUtils.isBlank(f.getBlockedChannel())) {
                            return true;
                        }
                        List<String> blockedChannels = Arrays.asList(f.getBlockedChannel().split(","));
                        if (CollectionUtil.isNotEmpty(blockedChannels)) {
                            return !filterChannels.contains(priceGearVO.getChannel());
                        }
                        return false;
                    })
                    .collect((Collectors.groupingBy(Filter::getGearId)));
            groupByVipTypes(gears, filterMap, priceGearVO, result, conMap, notConMap, calculatePriceBO);
            log.info("handlePaymentRetention result={}", JSONUtil.toJsonStr(result));

            List<JSONObject> mergedList = new ArrayList<>();
            result.getOrDefault("vipPrice", Collections.emptyList()).stream()
                    .filter(v -> {
                        if ("carengine".equals(priceGearVO.getPayDeskSign())) {
                            return StringUtils.isBlank(v.getQrcodeLink()) && (v.getForceLogin() == null || Objects.equals(v.getForceLogin(), 0));
                        }
                        return true;
                    })
                    .limit(2)
                    .map(v -> {
                        JSONObject jsonObject = (JSONObject) JSONObject.toJSON(v);
                        jsonObject.put("vipType", "vip_17");
                        String doc = jsonObject.getString("doc");
                        if (StringUtils.isNotBlank(doc) && JSONUtil.isTypeJSON(doc)) {
                            JSONObject docObject = JSONObject.parseObject(doc);
                            jsonObject.put("autoPayBtnShow", docObject.getString("autoPayBtnShow"));
                            jsonObject.put("autoPayChecked", docObject.getString("autoPayChecked"));
                            jsonObject.put("imgUrl", docObject.getString("imgUrl"));
                            jsonObject.put("btnEvent", docObject.getString("btnEvent"));
                            jsonObject.put("link", docObject.getString("link"));
                        }
                        return jsonObject;
                    })
                    .forEach(mergedList::add);

            result.getOrDefault("svipPrice", Collections.emptyList()).stream()
                    .filter(v -> {
                        if ("carengine".equals(priceGearVO.getPayDeskSign())) {
                            return StringUtils.isBlank(v.getQrcodeLink()) && (v.getForceLogin() == null || Objects.equals(v.getForceLogin(), 0));
                        }
                        return true;
                    })
                    .limit(2)
                    .map(v -> {
                        JSONObject jsonObject = (JSONObject) JSONObject.toJSON(v);
                        jsonObject.put("vipType", "vip_34");
                        String doc = jsonObject.getString("doc");
                        if (StringUtils.isNotBlank(doc) && JSONUtil.isTypeJSON(doc)) {
                            JSONObject docObject = JSONObject.parseObject(doc);
                            jsonObject.put("autoPayBtnShow", docObject.getString("autoPayBtnShow"));
                            jsonObject.put("autoPayChecked", docObject.getString("autoPayChecked"));
                            jsonObject.put("imgUrl", docObject.getString("imgUrl"));
                            jsonObject.put("btnEvent", docObject.getString("btnEvent"));
                            jsonObject.put("link", docObject.getString("link"));
                        }
                        return jsonObject;
                    })
                    .forEach(mergedList::add);

            return new MessageModel(mergedList);
        } catch (Exception e) {
            log.error("handlePaymentRetention error doudi price! priceGearVO={}", JSONUtil.toJsonStr(priceGearVO), e);
            carMonitor.CASH_RETENTION_ERROR.increment();
        }
        return new MessageModel();
    }

    /**
     * 通过 filterId 获取 Gear 信息
     *
     * @param filterId
     * @return
     */
    private Gear findGearByFilterId(String filterId) {
        try {
            Integer filterIdInt = Integer.parseInt(filterId);

            CarParamBO carFilterParamBO = new CarParamBO();
            carFilterParamBO.setIsActivity(0);
            carFilterParamBO.setFilterStr(filterId);
            List<Filter> cacheFilters = cacheUtils.filterCache.get(carFilterParamBO);
            Filter filter = cacheFilters.stream()
                    .filter(f -> Objects.equals(f.getId(), filterIdInt))
                    .findFirst()
                    .orElse(null);
            if (filter == null) {
                log.error("handlePaymentRetention filter empty ,filter={}", JSONUtil.toJsonStr(filter));
                carMonitor.CASH_RETENTION.increment();
                return null;
            }
            return cacheUtils.getGearCacheById.get(filter.getGearId());
        } catch (Exception e) {
            log.error("findGearByFilterId error is ", e);
            carMonitor.CASH_RETENTION_ERROR.increment();
        }
        return null;
    }

    /**
     * 优惠券相关
     *
     * @param priceGearVO
     * @param uid
     * @param virtualUid
     * @param conMap
     * @param notConMap
     */
    private void handleCouponLogic(PriceGearVO priceGearVO,long uid, long virtualUid, Map<Integer,VehicleCouponBO> conMap, Map<Integer,VehicleCouponBO> notConMap){
        if(carConfigNacos.getCarConfigBO()!=null&&carConfigNacos.getCarConfigBO().isCouponForCar==1){
            log.info("getPriceGearInfo isCouponForCar={}!",carConfigNacos.getCarConfigBO().isCouponForCar);
            if(StringUtils.isNotBlank(priceGearVO.getDeviceId())&& StringUtils.isNotBlank(priceGearVO.getChannel())){
                vehicleCouponService.getCouponList(uid, priceGearVO.getChannel(), virtualUid, priceGearVO.getDeviceId(), conMap,notConMap);
            }
        }
    }

    /**
     * 修改默认值
     *
     * @param priceGearVO 价格参数
     */
    public void setDefaultValuesAndHandleSpecialCases(PriceGearVO priceGearVO){
        if(StringUtils.isBlank(priceGearVO.getPayDeskSign())){
            priceGearVO.setPayDeskSign("carengine");
            priceGearVO.setPlatform("ar");
        }
        if(priceGearVO.getFromType()!= PayConstant.CarVIPCenter&&StringUtils.equals(priceGearVO.getChannel(),"C_APK_CaoCao_h5")){
            priceGearVO.setPayDeskSign("caocaotravel");
            priceGearVO.setPlatform("ar");
        }
        if (StringUtils.equals(priceGearVO.getPayDeskSign(), CASH_H5_RETAIN)) {
            priceGearVO.setPlatform("ar");
        }

    }

    /**
     * 计算价格参数
     *
     * @param priceGearVO
     * @param calculatePriceBO
     */
    private void calculatePriceBO(PriceGearVO priceGearVO, CalculatePriceBO calculatePriceBO){
        try {
            // 未登录
            boolean isVirtualUidLogin = LoginUtil.isVirtualUidLogin(priceGearVO.getUid(),priceGearVO.getVirtualUid());
            // 是否符合引导登录条件
            boolean loginPopup = LoginUtil.checkGuideLoginRule(priceGearVO.getFromType(),priceGearVO.getSource(),priceGearVO.getChannel());
            calculatePriceBO.setVirtualUidLogin(isVirtualUidLogin);
            calculatePriceBO.setGuideLoginPopup(loginPopup);
        }catch (Exception e){
            log.error("calculatePriceBO has error!",e);
        }
    }

    /**
     * 过滤有效filter
     *
     * @param priceGearVO 请求参数
     * @return
     * @throws ServiceException
     */
    private List<Filter> invokeValidFilter(PriceGearVO priceGearVO, List<Gear> gear, CalculatePriceBO calculatePriceBO) throws ServiceException {
        String gearIds = gear.stream().map(x -> String.valueOf(x.getId())).collect(Collectors.joining(","));
        List<Filter> filters = null;
        try {
            // 远程调用接口过滤filter
            filters = invokeFilterGears(priceGearVO, gearIds, calculatePriceBO,gear);
        } catch (Exception e) {
            carMonitor.PRICEINFO_PARAM_ERROR.increment();
            log.error("getNewPriceGearInfo error invoke filters is error ! priceGearVO={}", JSONUtil.toJsonStr(priceGearVO), e);
        }
        return filters;
    }

    /**
     * 获取过滤后的filter
     *
     * @param priceGearVO
     * @param gearId
     * @return
     */
    public List<Filter> invokeFilterGears(PriceGearVO priceGearVO, String gearId,CalculatePriceBO calculatePriceBO,List<Gear> gears) throws Exception {
        AbRequest abRequest = new AbRequest();
        abRequest.setRuleType("CAR_RULE_TYPE");
        abRequest.setUserId(priceGearVO.getUid());
        abRequest.setGearId(gearId);
        abRequest.setRSource("");
        abRequest.setSource(priceGearVO.getSource());
        abRequest.setDeviceId(priceGearVO.getDeviceId());
        abRequest.setVirtualUserId(priceGearVO.getVirtualUid());
        abRequest.setFromsrc(priceGearVO.getFromsrc());
        abRequest.setChannel(priceGearVO.getChannel());
        abRequest.setFromType(priceGearVO.getFromType());
        abRequest.setPayDeskSign(priceGearVO.getPayDeskSign());
        abRequest.setPlatform(priceGearVO.getPlatform());
        String channel=priceGearVO.getChannel();
        boolean isSvipChannle= channelService.isSvipChannel(channel);
        if(isSvipChannle){
            abRequest.setVipType(VipTypeEnum.SUPER_VIP.getType());
        }
        if(calculatePriceBO!=null){
            abRequest.setGuideLoginPopup(calculatePriceBO.guideLoginPopup);
        }
//        abRequest.setUserInfoBO(new com.commerical.abserviceapi.domain.UserInfoBO());
        AbResponse eval = abRuleService.evalCar(abRequest);
        log.info("paydesk:invokeFilterGears after RPC filterIds={}", JSONUtil.toJsonStr(eval));
        List<Object> maps = eval.getHits();
        String guideLogin= eval.getGuideLogin();
        Map<String,String> guideLoginRankMap= eval.getGuideLoginRankMap();
        List<Integer> filterIds = maps.stream().map(x -> Integer.parseInt(String.valueOf(x))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterIds)) {
            return new ArrayList<Filter>();
        }
        String cacheKey = filterIds.stream()
                .sorted()
                .map(String::valueOf)
                .collect(Collectors.joining(","));
        CarParamBO carParamBO=new CarParamBO();
        carParamBO.setIsActivity(0);
        carParamBO.setFilterStr(cacheKey);
        List<Filter> cacheFilters= cacheUtils.filterCache.get(carParamBO);
        List<Filter> filters= new ArrayList<>(cacheFilters);
        if((StringUtils.equals(guideLogin,"1")||StringUtils.equals(guideLogin,"2"))&&guideLoginRankMap!=null){
            guideLoginRank( gears,guideLoginRankMap,calculatePriceBO);
            calculatePriceBO.setGuideLoginRankMap(guideLoginRankMap);
        }
        return filters;
    }

    /**
     * 引导登录挡位顺序
     *
     * @param gears
     * @param guideLoginRankMap
     * @param calculatePriceBO
     */
    public void guideLoginRank(List<Gear> gears,Map<String,String> guideLoginRankMap ,CalculatePriceBO calculatePriceBO){
        try{
            for(Gear gear:gears){
                String rank= guideLoginRankMap.get(String.valueOf(gear.getId()));
                if(StringUtils.isNotBlank(rank)){
                    gear.setOrderRank(Integer.parseInt(rank));
                }
            }
            Collections.sort(gears);
        }catch (Exception e){
            log.error("guideLoginRank has error!",e);
        }
    }

    /**
     * 分类模式 单挡位
     *
     * @param gears 挡位
     * @param filterMap
     */
    public void groupByVipTypes(List<Gear> gears, Map<Integer, List<Filter>> filterMap,PriceGearVO priceGearVO, Map<String,List<VehiclePriceBO>> result, Map<Integer,VehicleCouponBO> conMap,Map<Integer,VehicleCouponBO> notConMap, CalculatePriceBO calculatePriceBO) {
        // 所有的档位按类型进行分类
        try {
            Map<String, List<Gear>> gearMap = gears.stream().collect(Collectors.groupingBy(Gear::getVipType, LinkedHashMap::new, Collectors.toList()));
            boolean forceLogin=forceLoginByChannel(priceGearVO);
            for (String vipType : gearMap.keySet()) {
                List<VehiclePriceBO> vipList=null;
                if(vipType.equals(VipTypeEnum.VEHICLE_VIP.getType())){
                    vipList= result.get("vipPrice");
                }
                if(vipType.equals(VipTypeEnum.SUPER_VIP.getType())){
                    vipList= result.get("svipPrice");
                }
                try {
                    List<Gear> vipTypeGears = gearMap.get(vipType);
                    List<VehiclePriceBO> finalVipList = vipList;
                    vipTypeGears.forEach(itemGear -> {
                        try {
                            // 屏蔽渠道
                            if (StringUtils.isNotBlank(itemGear.getBlockedChannel())) {
                                List<String> blockedKeys = Arrays.stream(itemGear.getBlockedChannel().split(",")).collect(Collectors.toList());
                                if (!CollectionUtils.isEmpty(blockedKeys) && blockedKeys.contains(priceGearVO.getChannel())) {
                                    return;
                                }
                            }
                            VehiclePriceBO vehiclePriceBO=new VehiclePriceBO();
                            // 档位对应的策略
                            List<Filter> filters = filterMap.get(itemGear.getId());
                            if (CollectionUtils.isEmpty(filters)) {
                                return;
                            }
                            parseData(vehiclePriceBO, itemGear, filters, priceGearVO, conMap, notConMap, calculatePriceBO,forceLogin);
                            if(vehiclePriceBO.getCurrentPrice()==null){
                                log.info("new car groupByVipTypes price is not valid!");
                                return;
                            }
                            boolean isPass=isPassSpecialWork(priceGearVO,vehiclePriceBO);
                            if(isPass){
                                return;
                            }
                            boolean isUpgradeVipLuxury = false;
                            boolean isUpgradeVipm = false;
                            boolean isUpgradeSvip = checkCarToSvip(itemGear, priceGearVO, finalVipList, vehiclePriceBO);
                            if (carConfigNacos.getCarConfigBO().getUpgradeChannel() != null && carConfigNacos.getCarConfigBO().getUpgradeChannel().contains(priceGearVO.getChannel())) {
                                isUpgradeVipLuxury = checkVipLuxuryUpgrade(itemGear, priceGearVO, finalVipList, vehiclePriceBO);
                                isUpgradeVipm = checkVipmUpgrade(itemGear, priceGearVO, finalVipList, vehiclePriceBO);
                            }
                            if (!isUpgradeSvip && !isUpgradeVipLuxury && !isUpgradeVipm) {
                                finalVipList.add(vehiclePriceBO);
                            }
                        } catch (Exception e) {
                            log.error("getNewPriceGearInfo error new car itemGear has error ", e);
                            carMonitor.PRICEINFO_PARAM_ERROR.increment();
                        }
                    });
                } catch (Exception e) {
                    log.error("getNewPriceGearInfo error new car vipType has error", e);
                    carMonitor.PRICEINFO_PARAM_ERROR.increment();
                }
            }

        } catch (Exception e) {
            log.error("getNewPriceGearInfo error groupByVipTypes !",e);
        }
    }

    /**
     * 车载升级超会
     *
     * @param itemGear
     * @param priceGearVO
     * @param finalVipList
     */
    public boolean checkCarToSvip(Gear itemGear,PriceGearVO priceGearVO, List<VehiclePriceBO> finalVipList,  VehiclePriceBO vehiclePriceBO){
        try{
            if(StringUtils.isNotBlank(itemGear.getExtend())&&JSONUtil.isTypeJSON(itemGear.getExtend())){
                JSONObject gearJson= JSONObject.parseObject(itemGear.getExtend());
                if(gearJson.containsKey("carToSvip")&&StringUtils.isNotBlank(gearJson.getString("carToSvip"))){
                    log.info("checkCarToSvip 1" );
                    List<VehiclePriceBO> svipList=new ArrayList<>();
                    checkCarToSVIP(priceGearVO.getUid(),svipList,null,vehiclePriceBO);
                    if(!CollectionUtils.isEmpty(svipList)){
                        VehiclePriceBO vBo= svipList.get(0);
                        BeanUtils.copyProperties(vehiclePriceBO,vBo,"currentPrice","wxTflg","paySrc","upGradeId");
                        vipUpgradeAfter(vehiclePriceBO, vBo);
                        finalVipList.addAll(0,svipList);
                    }
                    return true;
                }
            }
        }catch (Exception e){
            log.error("checkCarToSvip has error!",e);
        }
        return false;
    }

    /**
     * 豪v升级档位
     *
     * @param itemGear
     * @param priceGearVO
     * @param finalVipList
     * @param vehiclePriceBO
     * @return
     */
    public boolean checkVipLuxuryUpgrade(Gear itemGear, PriceGearVO priceGearVO, List<VehiclePriceBO> finalVipList, VehiclePriceBO vehiclePriceBO) {
        try {
            if (StringUtils.isNotBlank(itemGear.getExtend()) && JSONUtil.isTypeJSON(itemGear.getExtend())) {
                JSONObject gearJson = JSONObject.parseObject(itemGear.getExtend());
                if (gearJson.containsKey("vipLuxuryTo") && StringUtils.isNotBlank(gearJson.getString("vipLuxuryTo"))) {
                    log.info("checkVipLuxuryUpgrade");
                    List<VehiclePriceBO> svipList = new ArrayList<>();
                    vipLuxuryUpgradeHandle(priceGearVO.getUid(), svipList, vehiclePriceBO);
                    if (!CollectionUtils.isEmpty(svipList)) {
                        VehiclePriceBO vBo = svipList.get(0);
                        BeanUtils.copyProperties(vehiclePriceBO, vBo, "currentPrice", "wxTflg", "paySrc", "upGradeId");
                        vipUpgradeAfter(vehiclePriceBO, vBo);
                        finalVipList.addAll(0, svipList);
                    }
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("checkVipLuxuryUpgrade has error!", e);
        }
        return false;
    }

    /**
     * 豪v升级档位处理
     *
     * @param userId
     * @param vehiclePriceVIPBOS
     * @param vPriceBO
     */
    public void vipLuxuryUpgradeHandle(String userId, List<VehiclePriceBO> vehiclePriceVIPBOS, VehiclePriceBO vPriceBO) {
        if (StringUtils.isBlank(userId) || MyNumberUtils.toLONG(userId) <= 0) {
            return;
        }
        Long uid = Long.parseLong(userId);
        UserInfoBO userInfoBO = invokeUserInfo(uid);
        log.info("vipLuxuryUpgradeHandle userInfoBO={}", JSONUtil.toJsonStr(userInfoBO));
        if (userInfoBO == null) {
            return;
        }

        int vipVehicleAutoPayUser = userInfoBO.getVipVehicleAutoPayUser();
        int luxAutoPayUser = userInfoBO.getLuxAutoPayUser();
        long vipLuxVExpire = userInfoBO.getVipluxuryexpire();
        long vipVehicleExpire = userInfoBO.getVipVehicleExpire();
        Date luxDate = new Date(vipLuxVExpire);
        Date vehData = new Date(vipVehicleExpire);
        Date nowDate = new Date();
        Date svipExpire = new Date(userInfoBO.getSvipExpire());
        if (luxDate.before(nowDate) || svipExpire.after(nowDate)) {
            return;
        }

        if (luxAutoPayUser == 0 && DateUtils.checkOther(vipLuxVExpire, nowDate)) {
            // 豪华VIP 非连续续费 升级为连续续费超级会员
            long vehbetweenDay = DateUtil.between(vehData, nowDate, DateUnit.DAY);
            if (vipVehicleAutoPayUser == 1 || (vipVehicleAutoPayUser == 0 && vehbetweenDay > 0 && vehData.after(nowDate))) {
                log.info("vipLuxuryUpgradeHandle Vehicle update first vipVehicleAutoPayUser={} betweenDay={}", vipVehicleAutoPayUser, vehbetweenDay);
            } else {
                BigDecimal monthPrice = new BigDecimal(vPriceBO.getVipLuxuryToSvipMonthPrice());
                BigDecimal dayPrice = new BigDecimal(vPriceBO.getVipLuxuryToSvipDayPrice());
                VipUpdatePriceEnum.getShowPriceNew(VipUpdatePriceEnum.LUXURY_SVIP, vehiclePriceVIPBOS, monthPrice.doubleValue(), dayPrice.doubleValue());
            }
        }
    }


    /**
     * 音乐包升级档位
     *
     * @param itemGear
     * @param priceGearVO
     * @param finalVipList
     * @param vehiclePriceBO
     * @return
     */
    public boolean checkVipmUpgrade(Gear itemGear, PriceGearVO priceGearVO, List<VehiclePriceBO> finalVipList, VehiclePriceBO vehiclePriceBO) {
        try {
            if (StringUtils.isNotBlank(itemGear.getExtend()) && JSONUtil.isTypeJSON(itemGear.getExtend())) {
                JSONObject gearJson = JSONObject.parseObject(itemGear.getExtend());
                if (gearJson.containsKey("vipmTo") && StringUtils.isNotBlank(gearJson.getString("vipmTo"))) {
                    log.info("checkVipmUpgrade");
                    List<VehiclePriceBO> svipList = new ArrayList<>();
                    vipmUpgradeHandle(priceGearVO.getUid(), svipList, vehiclePriceBO);
                    if (!CollectionUtils.isEmpty(svipList)) {
                        VehiclePriceBO vBo = svipList.get(0);
                        BeanUtils.copyProperties(vehiclePriceBO, vBo, "currentPrice", "wxTflg", "paySrc", "upGradeId", "title", "renewPrice");
                        vipUpgradeAfter(vehiclePriceBO, vBo);
                        finalVipList.addAll(0, svipList);
                    }
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("checkVipmUpgrade has error!", e);
        }
        return false;
    }

    /**
     * 音乐包升级档位处理
     *
     * @param userId
     * @param vehiclePriceVIPBOS
     * @param vPriceBO
     */
    public void vipmUpgradeHandle(String userId, List<VehiclePriceBO> vehiclePriceVIPBOS, VehiclePriceBO vPriceBO) {
        if (StringUtils.isBlank(userId) || MyNumberUtils.toLONG(userId) <= 0) {
            return;
        }
        Long uid = Long.parseLong(userId);
        UserInfoBO userInfoBO = invokeUserInfo(uid);
        log.info("vipmUpgradeHandle userInfoBO={}", JSONUtil.toJsonStr(userInfoBO));
        if (userInfoBO == null) {
            return;
        }

        int vipmAutoPayUser = userInfoBO.getVipmAutoPayUser();
        long vipmexpire = userInfoBO.getVipmexpire();
        Date vipmexpireDate = new Date(vipmexpire);
        Date nowDate = new Date();
        // 音乐包已经过期跳过
        if (vipmexpireDate.before(nowDate)) {
            return;
        }
        // 如果车载VIP会员或者豪华VIP也满足升级超会条件则不返回音乐包升级超会挡位
        if (vipmAutoPayUser == 0 && DateUtils.checkOther(userInfoBO.getVipmexpire(), nowDate) && userInfoBO.getVipVehicleAutoPayUser() == 0 && userInfoBO.getLuxAutoPayUser() == 0) {
            if ((DateUtils.checkOther(userInfoBO.getVipluxuryexpire(), nowDate) && userInfoBO.getLuxAutoPayUser() == 0) || DateUtils.checkOtherVehicle(userInfoBO.getVipVehicleExpire(), nowDate)) {
                log.info("vipmUpgradeHandle 满足车载VIP会员或者豪华VIP也满足升级超会条件则不返回音乐包升级超会挡位 跳过音乐包升级超会档位，uid={}", uid);
            } else {
                Date svipExpire = new Date(userInfoBO.getSvipExpire());
                // 如果超会已过期 则显示超会档位
                if (svipExpire.before(nowDate)) {
                    // 音乐包非连续续费挡位升级为超级会员
                    BigDecimal monthPrice = new BigDecimal(vPriceBO.getVipmToSvipMonthPrice());
                    BigDecimal dayPrice = new BigDecimal(vPriceBO.getVipmToSvipDayPrice());
                    VipUpdatePriceEnum.getShowPriceNew(VipUpdatePriceEnum.VIPM_SVIP, vehiclePriceVIPBOS, monthPrice.doubleValue(), dayPrice.doubleValue());
                }
            }
            Date vipVehicleExpire = new Date(userInfoBO.getVipVehicleExpire());
            Date vipLux = new Date(userInfoBO.getVipluxuryexpire());
            if (vipVehicleExpire.before(nowDate) && vipLux.before(nowDate)) {
                // 音乐包非连续续费挡位升级为车载VIP
                if (StringUtils.isNotBlank(vPriceBO.getVipmToVehicleVipMonthPrice()) || StringUtils.isNotBlank(vPriceBO.getVipmToVehicleVipDayPrice())) {
                    BigDecimal monthPrice = new BigDecimal(vPriceBO.getVipmToVehicleVipMonthPrice());
                    BigDecimal dayPrice = new BigDecimal(vPriceBO.getVipmToVehicleVipDayPrice());
                    VipUpdatePriceEnum.getShowPriceNew(VipUpdatePriceEnum.VIPM_VVIP, vehiclePriceVIPBOS, monthPrice.doubleValue(), dayPrice.doubleValue());
                }
            }
        }
        if (vehiclePriceVIPBOS.size() > 1) {
            vehiclePriceVIPBOS.sort(Comparator.comparing(VehiclePriceBO::getCurrentPrice));
            VehiclePriceBO first = vehiclePriceVIPBOS.get(0);
            vehiclePriceVIPBOS.clear();
            vehiclePriceVIPBOS.add(first);
        }
    }

    /**
     * 车载升级超会挡位
     *
     * @param userId
     * @param vehiclePriceVIPBOS
     * @param vehicleActivityItemBO
     * @param vPriceBO
     */
    public void checkCarToSVIP(String userId,List<VehiclePriceBO> vehiclePriceVIPBOS,VehicleActivityItemBO vehicleActivityItemBO, VehiclePriceBO vPriceBO){
        if(StringUtils.isBlank(userId)||MyNumberUtils.toLONG(userId)<=0){
            return;
        }
        Long uid=Long.parseLong(userId);
        UserInfoBO userInfoBO= invokeUserInfo(uid);
        log.info("checkCarToSvip userInfoBO={}",JSONUtil.toJsonStr(userInfoBO) );
        if(userInfoBO==null){
            return;
        }
        int vipVehicleAutoPayUser= userInfoBO.getVipVehicleAutoPayUser();
        long vipVehicleExpire= userInfoBO.getVipVehicleExpire();
        Date vehicleDate =new Date(vipVehicleExpire);
        Date svipExpire =new Date(userInfoBO.getSvipExpire());
        Date nowDate=new Date();
        if(svipExpire.after(nowDate)||nowDate.after(vehicleDate)){
            log.info("checkCarToSvip 3" );
            return;
        }
        long betweenDay = DateUtil.between(vehicleDate,nowDate, DateUnit.DAY);
        double price= 0;
        VehiclePriceBO vehiclePriceBO=new VehiclePriceBO();
        vehiclePriceBO.setPaySrc("caculateUpdateSVIP");
        vehiclePriceBO.setSelected(true);
        vehiclePriceBO.setCurrentPrice(price);
        String carToSvipDayPrice="";
        String carToSvipMonthPrice="";
        if(vehicleActivityItemBO!=null){
            vehiclePriceBO.setAutoPay(vehicleActivityItemBO.getIsContinute()==1?true:false);
            vehiclePriceBO.setTag(vehicleActivityItemBO.getTagOne());
            vehiclePriceBO.setLabel(vehicleActivityItemBO.getTagTwo());
            vehiclePriceBO.setMonth(vehicleActivityItemBO.getMonths());
            vehiclePriceBO.setTitle(vehicleActivityItemBO.getTagOne());
            vehiclePriceBO.setSubTitle(vehicleActivityItemBO.getTagTwo());
            vehiclePriceBO.setFilterId(Integer.parseInt(vehicleActivityItemBO.getFilterId()));
            vehiclePriceBO.setWxTflg("svip_"+vehicleActivityItemBO.getMonths());
            vehiclePriceBO.setRenewPrice(vehicleActivityItemBO.getRenewPrice());
            carToSvipDayPrice=vehicleActivityItemBO.getCarToSvipDayPrice();
            carToSvipMonthPrice=vehicleActivityItemBO.getCarToSvipMonthPrice();
        }else if (vPriceBO!=null){
            vehiclePriceBO.setAutoPay(vPriceBO.getAutoPay());
            vehiclePriceBO.setTag(vPriceBO.getTag());
            vehiclePriceBO.setLabel(vPriceBO.getLabel());
            vehiclePriceBO.setMonth(vPriceBO.getMonth());
            vehiclePriceBO.setTitle(vPriceBO.getTitle());
            vehiclePriceBO.setSubTitle(vPriceBO.getSubTitle());
            vehiclePriceBO.setFilterId(vPriceBO.getFilterId());
            vehiclePriceBO.setWxTflg("svip_"+vPriceBO.getMonth());
            vehiclePriceBO.setRenewPrice(vPriceBO.getRenewPrice());
            carToSvipDayPrice=vPriceBO.getCarToSvipDayPrice();
            carToSvipMonthPrice=vPriceBO.getCarToSvipMonthPrice();
        }
        // 连续续费挡位升级为超级会员
        if(vipVehicleAutoPayUser==1&&StringUtils.isNotBlank(carToSvipDayPrice)){
            if(betweenDay<=31){
                // 连续续费挡位升级为超级会员 补差价0.3n
                BigDecimal daysUp=new BigDecimal(carToSvipDayPrice);
                BigDecimal upPrice= daysUp.multiply(new BigDecimal(betweenDay)).setScale(2, RoundingMode.DOWN).stripTrailingZeros();
                price=upPrice.doubleValue();
                vehiclePriceBO.setUpGradeId(9);
            }else{
                BigDecimal decimal=new BigDecimal(carToSvipMonthPrice).setScale(2, RoundingMode.DOWN).stripTrailingZeros();
                price= decimal.doubleValue();
                vehiclePriceBO.setUpGradeId(8);
            }

        } else if (vipVehicleAutoPayUser == 0) {
            if (StringUtils.isNotBlank(carToSvipDayPrice) && betweenDay > 0 && betweenDay <= 31) {
                BigDecimal daysUp = new BigDecimal(carToSvipDayPrice);
                BigDecimal decimal = daysUp.multiply(new BigDecimal(betweenDay)).setScale(2, RoundingMode.DOWN).stripTrailingZeros();
                price = decimal.doubleValue();
                vehiclePriceBO.setUpGradeId(10);
            } else if (StringUtils.isNotBlank(carToSvipMonthPrice) && betweenDay > 31) {
                // 非连续 首月升级次月30扣费
                BigDecimal decimal = new BigDecimal(carToSvipMonthPrice).setScale(2, RoundingMode.DOWN).stripTrailingZeros();
                price = decimal.doubleValue();
                vehiclePriceBO.setUpGradeId(7);
            }
        }
        if(price<=0){
            log.info("checkCarToSvip 5" );
            return;
        }
        vehiclePriceBO.setCurrentPrice(price);
        vehiclePriceVIPBOS.add(vehiclePriceBO);
        log.info("checkCarToSVIP  car to svip vehiclePriceBO={}",JSONUtil.toJsonStr(vehiclePriceBO));
    }

    // vip升级后赋值
    private void vipUpgradeAfter(VehiclePriceBO vehiclePriceBO, VehiclePriceBO vBo) {
        vBo.setFilterId(vehiclePriceBO.getFilterId());
        vBo.setSubTitle("专享优惠");
        vBo.setSelected(true);
        if (vBo.getUpGradeId() != null && !vBo.getUpGradeId().equals(9)) {
            vBo.setLabel("首月");
        }
    }

    /**
     * 特殊逻辑
     *
     * @param priceGearVO 价格参数
     * @param vehiclePriceBO
     * @return
     */
    public boolean isPassSpecialWork(PriceGearVO priceGearVO,VehiclePriceBO vehiclePriceBO){
        if(priceGearVO.getFromType()==PayConstant.H5VIPCenter&&vehiclePriceBO.getCurrentPrice()!=null&&BigDecimal.valueOf(vehiclePriceBO.getCurrentPrice()).compareTo(BigDecimal.ZERO)==0){
            return true;
        }
        //听书
        if(priceGearVO.getFromType()!=PayConstant.CarVIPCenter&&priceGearVO.getFromType()!=PayConstant.H5VIPCenter&&StringUtils.isNotBlank(vehiclePriceBO.getPaySrc())
                &&carConfigNacos.getCarConfigBO()!=null&&carConfigNacos.getCarConfigBO().getTsSrcList().contains(vehiclePriceBO.getPaySrc())){
            return true;
        }
        if(((priceGearVO.getFromType()==PayConstant.CarVIPCenter||priceGearVO.getFromType()==PayConstant.H5VIPCenter)&&carConfigNacos.getCarConfigBO()!=null&&carConfigNacos.getCarConfigBO().getTsSrcList().contains(vehiclePriceBO.getPaySrc()))||BigDecimal.valueOf(vehiclePriceBO.getCurrentPrice()).compareTo(BigDecimal.ZERO)==0){
            if(priceGearVO.getIsVirtualUidBuy()==0&&(priceGearVO.getUid().equals("0")||StringUtils.isBlank(priceGearVO.getUid()))){
                log.info("groupByVipTypes price continute ! ");
                return true;
            }
            vehiclePriceBO.setStyle("wide");
        }
        if(sendDaysSrc.contains(vehiclePriceBO.getPaySrc())||(vehiclePriceBO.getFilterId()!=null&&vehiclePriceBO.getFilterId()== needLoginFilterId)){
            vehiclePriceBO.setStyle("wide");
        }
        return false;
    }


    /**
     * 解析parse
     *
     * @param vehiclePriceBO
     * @param gear
     * @param filterLists
     * @param priceGearVO
     */
    public void parseData(VehiclePriceBO vehiclePriceBO, Gear gear, List<Filter> filterLists, PriceGearVO priceGearVO,Map<Integer,VehicleCouponBO> conMap,Map<Integer,VehicleCouponBO> notConMap, CalculatePriceBO calculatePriceBO,boolean forceLogin) {
        try {
            DocBO docBO=new DocBO();
            parseGearDoc(gear.getDoc(),docBO);
            filterLists.forEach(filter -> {
                try {
                    if(filter.getIsActivity()!=null&&filter.getIsActivity()==1&&priceGearVO.getFromType()!=PayConstant.CarVIPCenter){
                        log.info("filter id ={} fromType={}",filter.getId(),priceGearVO.getFromType());
                        return;
                    }
                    parseFilter(filter, gear, vehiclePriceBO,priceGearVO,conMap, notConMap,docBO,  calculatePriceBO, forceLogin);
                } catch (Exception e) {
                    log.error("getNewPriceGearInfo error filterType has error! filterType", e);
                    carMonitor.PRICEINFO_PARAM_ERROR.increment();
                }
            });
        } catch (Exception e) {
            log.error("getNewPriceGearInfo error parseData has error! gear={}", JSONObject.toJSON(gear), e);
            carMonitor.PRICEINFO_PARAM_ERROR.increment();
        }
    }

    /**
     * 解析扩展字段
     *
     * @param doc
     * @param docBO
     */
    public void parseGearDoc(String doc,DocBO docBO){
        try{
            if(StringUtils.isNotBlank(doc)){
                JSONObject docJS=JSONObject.parseObject(doc);
                //竖屏
                String descImgTall= docJS.getString("verticalScreenImg");
                //宽屏
                String descImgWide= docJS.getString("wideScreenImg");
                //超宽屏
                String superWideScreenImg= docJS.getString("superWideScreenImg");
                docBO.setDescImgTall(descImgTall);
                docBO.setDescImgWide(descImgWide);
                docBO.setSuperWideScreenImg(superWideScreenImg);
            }
        }catch (Exception e){
            log.error("parseDoc has error!",e);
        }
    }

    /**
     * 价格解析
     * @param filter
     * @param gear
     * @param vehiclePriceBO
     * @param priceGearVO
     */
    public void parseFilter(Filter filter, Gear gear, VehiclePriceBO vehiclePriceBO, PriceGearVO priceGearVO,Map<Integer,VehicleCouponBO> conMap,Map<Integer, VehicleCouponBO> notConMap,DocBO docBO,CalculatePriceBO calculatePriceBO,boolean forceLogin){
        // abData 普通
        if(vehiclePriceBO.getFilterId()!=null){
            return;
        }
        try{
            JSONObject filterDoc=JSONObject.parseObject(filter.getDoc());
            String topTag=filterDoc.getString("topTag");
            String lotteryQrCode=filterDoc.getString("lotteryQrCode");
            String src=filterDoc.getString("src");
//            if(filter.getId()==needLoginFilterId&&checkVehicleLogin(priceGearVO.getUid(),src)){
//                return;
//            }
            parseDoc(filterDoc,vehiclePriceBO,gear,docBO,calculatePriceBO,priceGearVO,filter, src);
            if (forceLogin){
                vehiclePriceBO.setForceLogin(1);
            }
            filterForceLogin(vehiclePriceBO, gear, priceGearVO);
            String subTitle=filterDoc.getString("subTitle");
            String bottomTag=filterDoc.getString("bottomTag");
            JSONObject gearDoc=JSONObject.parseObject(gear.getDoc());
            String oPrice=gearDoc.getString("oPrice");
            String autoPayBtnShow=gearDoc.getString("autoPayBtnShow");
            DecimalFormat decimalFormat = new DecimalFormat("###################.###########");
            String formatPrice=decimalFormat.format(filter.getPrice());
            String topLeftTag=filterDoc.getString("topLeftTag");
            String discount=filterDoc.getString("discount");
            String autoPayDesc=filterDoc.getString("autoPayDesc");
            vehiclePriceBO.setCurrentPrice(Double.parseDouble(formatPrice));
            vehiclePriceBO.setRenewPrice(filter.getRenewalPrice());
            vehiclePriceBO.setFilterId(filter.getId());
            vehiclePriceBO.setDoc(filter.getDoc());
            vehiclePriceBO.setMonth(gear.getGearType());
            vehiclePriceBO.setUnderPrice(StringUtils.isBlank(oPrice)?0:Double.parseDouble(oPrice));
            if(gear.getGearName().contains("听书")){
                String title=gearDoc.getString("title");
                vehiclePriceBO.setTitle(title);
            }else{
                vehiclePriceBO.setTitle(gear.getGearName());
            }
            vehiclePriceBO.setSubTitle(subTitle);
            vehiclePriceBO.setSelected(StringUtils.isNotBlank(autoPayBtnShow) && autoPayBtnShow.equals("1"));
            Map<String,String> map= calculatePriceBO.getGuideLoginRankMap();
            if(map!=null&&calculatePriceBO.isVirtualUidLogin&&map.containsKey(String.valueOf(gear.getId()))){
                vehiclePriceBO.setSelected(true);
            }
            vehiclePriceBO.setGearId(gear.getId());
            vehiclePriceBO.setPaySrc(src);
            vehiclePriceBO.setAutoPay(gear.getAutoPay().equals(1));
            vehiclePriceBO.setUpGradeId(null);
            vehiclePriceBO.setLabel(null);
            vehiclePriceBO.setBottomTag(bottomTag);
            if(gear.getVipType().equals(VipTypeEnum.SUPER_VIP.getType())){
                vehiclePriceBO.setWxTflg("svip_"+gear.getGearType());
            }
            if(gear.getVipType().equals(VipTypeEnum.VEHICLE_VIP.getType())){
                vehiclePriceBO.setWxTflg("vehicle_"+gear.getGearType());
            }
            if(gear.getVipType().equals(VipTypeEnum.SUPER_VIP.getType())&&gear.getGearType()==1
                    &&StringUtils.isNotBlank(gear.getExtend())
                    &&JSONUtil.isTypeJSON(gear.getExtend())){
                JSONObject gearJson= JSONObject.parseObject(gear.getExtend());
                if(gearJson.containsKey("wxTflag")&&StringUtils.isNotBlank(gearJson.getString("wxTflag"))){
                    String wxTflag=gearJson.getString("wxTflag");
                    if(StringUtils.isNotBlank(wxTflag)){
                        vehiclePriceBO.setWxTflg(wxTflag);
                    }
                }
            }
            if(notConMap.get(gear.getGearType())!=null&&gear.getAutoPay()==0&&(isUserCoupon(formatPrice))) {
                vehiclePriceBO.setCouponUniqueId(notConMap.get(gear.getGearType()).getCouponUniqueId());
                vehiclePriceBO.setDiscountAmount(notConMap.get(gear.getGearType()).getDiscountAmount()!=null?new BigDecimal(filter.getPrice()-notConMap.get(gear.getGearType()).getDiscountAmount()).setScale(3,BigDecimal.ROUND_HALF_UP) .doubleValue():null);
            }
            if(conMap.get(gear.getGearType())!=null&&gear.getAutoPay()==1&&(isUserCoupon(formatPrice))){
                vehiclePriceBO.setCouponUniqueId(conMap.get(gear.getGearType()).getCouponUniqueId());
                vehiclePriceBO.setDiscountAmount(conMap.get(gear.getGearType()).getDiscountAmount()!=null?new BigDecimal(filter.getPrice()-conMap.get(gear.getGearType()).getDiscountAmount()).setScale(3,BigDecimal.ROUND_HALF_UP) .doubleValue():null);
            }
            if(ObjectUtils.isNotNull(filter.getEndTime())){
                long nowTime= System.currentTimeMillis();
                long endTime=filter.getEndTime().getTime();
                if(endTime-nowTime>0){
                    // 剩余时间
                    vehiclePriceBO.setLastTime(endTime-nowTime);
                }
            }
            vehiclePriceBO.setTag(topTag);
            vehiclePriceBO.setLabel(autoPayBtnShow);
            vehiclePriceBO.setFilterId(filter.getId());
            vehiclePriceBO.setShowQrcode(lotteryQrCode);
            vehiclePriceBO.setDiscount(discount);
            vehiclePriceBO.setTopLeftTag(topLeftTag);
            vehiclePriceBO.setAutoPayDesc(autoPayDesc);
        }catch (Exception e){
            log.error("getNewPriceGearInfo error parseFilter has  error !,filter={}",JSONUtil.toJsonStr(filter),e);
            carMonitor.PRICEINFO_PARAM_ERROR.increment();
        }
    }

    /**
     * 强制购买超会登录
     * @param vehiclePriceBO 返回的bo
     * @param gear 档位信息
     * @param priceGearVO  参数
     */
    private void filterForceLogin(VehiclePriceBO vehiclePriceBO, Gear gear, PriceGearVO priceGearVO) {
        try{
            String uid = priceGearVO.getUid();
            String source = priceGearVO.getSource();
            if (StringUtils.isBlank(source)) {
                return;
            }
            if (Convert.toInt(uid, 0) > 0) {
                return;
            }
            if (!Objects.equals(VipTypeEnum.SUPER_VIP.getType(), gear.getVipType())) {
                return;
            }
            if (StringUtils.equals(VipTypeEnum.SUPER_VIP.getType(),gear.getVipType())&&
                    CollectionUtils.isNotEmpty(carConfigNacos.getCarConfigBO().getForceLoginChannel())
                    &&!carConfigNacos.getCarConfigBO().getForceLoginChannel().contains(priceGearVO.getChannel())) {
                log.info("lsh=> channel is not super vip ,channel={}",priceGearVO.getChannel());
                vehiclePriceBO.setForceLogin(1);
                return;
            }

            Matcher matcher = PayConstant.pattern.matcher(source);
            if (!matcher.find()) {
                return;
            }
            String verStr = matcher.group();
            Integer version = Convert.toInt(Objects.isNull(verStr) ? null : verStr.replaceAll("\\.", ""), 0);
            if (version > PayConstant.SHIELD_VERSION_THRESHOLD||verStr.startsWith("6")) {
               // log.info("lsh=> version is greater than {} and force login", PayConstant.SHIELD_VERSION_THRESHOLD);
                vehiclePriceBO.setForceLogin(1);
                return;
            }
            int indexOf = source.indexOf(verStr);
            int lastIndexOf = source.lastIndexOf(".");
            if (PayConstant.SHIELD_SUPER_VIP_CHANNELS_2_FORCE_LOGIN.contains(source.substring(indexOf + verStr.length() + 1, lastIndexOf))) {
               // log.info("lsh=> channel is in shield table");
                vehiclePriceBO.setForceLogin(1);
            }
            if(PayConstant.SHIELD_SUPER_VIP_CHANNELS_2_FORCE_LOGIN.contains(priceGearVO.getChannel())&&vehiclePriceBO.getForceLogin()!=1){
                log.info("lsh=> login channel is error!channel={}",priceGearVO.getChannel());
            }
        }catch (Exception e){
            carMonitor.SVIP_FORCE_LOGIN_ERROR.increment();
            log.error("filterForceLogin has error!",e);
        }
    }

    /**
     * 强制登陆渠道
     *
     * @param priceGearVO
     */
    private boolean forceLoginByChannel(PriceGearVO priceGearVO) {
        try {
            String uid = priceGearVO.getUid();
            String source = priceGearVO.getSource();
            //log.info("forceLoginByChannel=> uid:{} source:{}, vipType:{} judge super vip no login shield", uid, source, gear.getVipType());
            if (StringUtils.isBlank(source)) {
                //log.info("forceLoginByChannel=> source is blank");
                return false;
            }
            if (Convert.toInt(uid, 0) > 0) {
                //log.info("forceLoginByChannel=> user is login");
                return false;
            }
            Matcher matcher = PayConstant.pattern.matcher(source);
            if (!matcher.find()) {
                //log.info("forceLoginByChannel=> not regex ");
                return false;
            }
            String verStr = matcher.group();
            //log.info("forceLoginByChannel=> version===>{}",verStr);
            String cacheData=RedisLowPriceDAO.getInstance().getString(RedisKey.CAR_STRONG_LOGIN_CHANNEL_LIST);
            JSONArray channelList=null;
            if (JSONUtil.isTypeJSON(cacheData)){
                channelList=JSON.parseArray(cacheData);
            }
            //log.info("forceLoginByChannel => channelList list:{}",JSON.toJSON(channelList));
            if (channelList!=null && StringUtils.isNotBlank(verStr) ){
                for (int i=0,size=channelList.size();i<size;i++){

                    JSONObject item=channelList.getJSONObject(i);
                    if ( StringUtils.equals("apk",item.getString("appType"))
                            &&(StringUtils.isBlank(item.getString("minVersion")) || (VersionComparator.INSTANCE.compare(verStr, item.getString("minVersion")) >= 0))
                            &&(StringUtils.isBlank(item.getString("maxVersion")) || (VersionComparator.INSTANCE.compare(verStr, item.getString("maxVersion")) <= 0))
                            &&PayConstant. loginTypeList.contains(item.getString("loginType"))
                            && str2List(item.getString("channel")).contains(priceGearVO.getChannel())){
                            //log.info("forceLoginByChannel=> need login channel");
                        return true;
                    }

                }
            }
        }catch (Exception e){
            log.error("获取登录渠道失败",e);
        }
        return false;
    }

    public static List<String> str2List(String channel){
        if (StringUtils.isBlank(channel)){
            return Collections.emptyList();
        }
        return Arrays.stream(StringUtils.split(channel, ",")).collect(Collectors.toList());

    }

    /**
     * 解析策略doc
     *
     * @param filterDoc
     * @param vehiclePriceBO
     * @param docBO
     */
    public void parseDoc(JSONObject filterDoc , VehiclePriceBO vehiclePriceBO, Gear gear,DocBO docBO,CalculatePriceBO calculatePriceBO, PriceGearVO priceGearVO,Filter filter,String src){
        try {
            //竖屏
            String descImgTall = filterDoc.getString("verticalScreenImg");
            //宽屏
            String descImgWide = filterDoc.getString("wideScreenImg");
            //超宽屏
            String superWideScreenImg = filterDoc.getString("superWideScreenImg");
            //是否活动抽奖弹窗
            String showActivityPopup = filterDoc.getString("openLotteryPopup");
            //是否活动挡位
            String isActivity = gear.getIsActivity();
            //是否未登录购买
            String forceLogin = filterDoc.getString("forceLogin");
            //登录弹窗
            String loginPopup = filterDoc.getString("loginPopup");
            //二维码链接
            String qrcodeLink=filterDoc.getString("qrcodeLink");
            //支付成功弹窗图片
            String payFinishedPopupImg=filterDoc.getString("payFinishedPopupImg");
            //二维码未登录遮罩文案
            String qrcodeUnloginText=filterDoc.getString("qrcodeUnloginText");
            //支付成功弹窗标题
            String payFinishedPopupTitle=filterDoc.getString("payFinishedPopupTitle");
            //支付成功弹窗描述
            String payFinishedPopupDesc=filterDoc.getString("payFinishedPopupDesc");
            //支付成功弹窗提示
            String payFinishedPopupTips=filterDoc.getString("payFinishedPopupTips");
            vehiclePriceBO.setDescImgTall(StringUtils.isBlank(descImgTall) ? docBO.getDescImgTall() : descImgTall);
            vehiclePriceBO.setDescImgWide(StringUtils.isBlank(descImgWide) ? docBO.getDescImgWide() : descImgWide);
            vehiclePriceBO.setDescImgLong(StringUtils.isBlank(superWideScreenImg) ? docBO.getSuperWideScreenImg() : superWideScreenImg);
            vehiclePriceBO.setShowActivityPopup(showActivityPopup);
            vehiclePriceBO.setIsActivity(isActivity);
            vehiclePriceBO.setQrcodeLink(qrcodeLink);
            vehiclePriceBO.setPayFinishedPopupImg(payFinishedPopupImg);
            vehiclePriceBO.setQrcodeUnloginText(qrcodeUnloginText);
            vehiclePriceBO.setPayFinishedPopupTitle(payFinishedPopupTitle);
            vehiclePriceBO.setPayFinishedPopupDesc(payFinishedPopupDesc);
            vehiclePriceBO.setPayFinishedPopupTips(payFinishedPopupTips);
            virtualUidLogin(calculatePriceBO, forceLogin , vehiclePriceBO, priceGearVO, filter,src,loginPopup);
            if (StringUtils.isNotBlank(filter.getExtend())&&JSONUtil.isTypeJSON(filter.getExtend())) {
                String extend= filter.getExtend().replace("\\","");
                JSONObject js= JSONObject.parseObject(extend);
                if(js.containsKey("carToSvipDayPrice")||js.containsKey("carToSvipMonthPrice")){
                    String carToSvipDayPrice=js.getString("carToSvipDayPrice");
                    vehiclePriceBO.setCarToSvipDayPrice(carToSvipDayPrice);
                    String carToSvipMonthPrice=js.getString("carToSvipMonthPrice");
                    vehiclePriceBO.setCarToSvipMonthPrice(carToSvipMonthPrice);
                }
                if (js.containsKey("vipLuxuryToSvipDayPrice") || js.containsKey("vipLuxuryToSvipMonthPrice")) {
                    vehiclePriceBO.setVipLuxuryToSvipDayPrice(js.getString("vipLuxuryToSvipDayPrice"));
                    vehiclePriceBO.setVipLuxuryToSvipMonthPrice(js.getString("vipLuxuryToSvipMonthPrice"));
                }
                if (js.containsKey("vipmToSvipDayPrice") || js.containsKey("vipmToSvipMonthPrice") || js.containsKey("vipmToVehicleVipDayPrice") || js.containsKey("vipmToVehicleVipMonthPrice")) {
                    vehiclePriceBO.setVipmToSvipDayPrice(js.getString("vipmToSvipDayPrice"));
                    vehiclePriceBO.setVipmToSvipMonthPrice(js.getString("vipmToSvipMonthPrice"));
                    vehiclePriceBO.setVipmToVehicleVipDayPrice(js.getString("vipmToVehicleVipDayPrice"));
                    vehiclePriceBO.setVipmToVehicleVipMonthPrice(js.getString("vipmToVehicleVipMonthPrice"));
                }
            }
            // 活动二维码显示条件限制
            JSONObject coupons = filterDoc.getJSONObject("coupons");
            if (coupons != null) {
                Integer showLimitType = coupons.getInteger("showLimitType");
                vehiclePriceBO.setShowLimitType(showLimitType);
                if (showLimitType != null && showLimitType == 1) {
                    if (StringUtils.isNotBlank(priceGearVO.getUid()) && !"0".equals(priceGearVO.getUid())) {
                        String redisKey = String.format("mem_info_%s", priceGearVO.getUid());
                        String cacheData = RedisIndicatorDAO.getInstance().hget(redisKey, String.format("buyCnt_%s",src));
                        if (StringUtils.isNotBlank(cacheData) && Integer.parseInt(cacheData) > 0) {
                            // 已购买，显示二维码
                            vehiclePriceBO.setHasBuy(1);
                        }
                    } else if (StringUtils.isNotBlank(priceGearVO.getVirtualUid()) && !"0".equals(priceGearVO.getVirtualUid())) {
                        long virtualUid = Long.parseLong(priceGearVO.getVirtualUid());
                        List<VehicleOrder> vehicleOrders = vehicleService.getVehicleByUserIdSrc(0L, virtualUid, src);
                        if (vehicleOrders != null && !vehicleOrders.isEmpty()) {
                            // 已购买，显示二维码
                            vehiclePriceBO.setHasBuy(1);
                        }
                    }
                }
            }
        }catch (Exception e){
            log.error("parseDoc doc has error!",e);
        }
    }

    /**
     * 未登录引导登录
     *
     * @param calculatePriceBO
     * @param forceLogin
     * @param vehiclePriceBO
     * @param priceGearVO
     * @param filter
     */
    public void virtualUidLogin(CalculatePriceBO calculatePriceBO,String forceLogin ,VehiclePriceBO vehiclePriceBO,PriceGearVO priceGearVO,Filter filter,String src,String loginPopup){
        try{
            // 未登录forceLogin=1/0 加蒙层1 未登录可购买0
            if (calculatePriceBO.isVirtualUidLogin) {
                if(StringUtils.equals(forceLogin, "1") || StringUtils.equals(forceLogin, "0")){
                    vehiclePriceBO.setForceLogin(Integer.parseInt(forceLogin));
                }
                if(calculatePriceBO.isGuideLoginPopup()){
                    // 支付完是否弹窗
                    if(StringUtils.equals(loginPopup,"1")){
                        vehiclePriceBO.setShowLoginPopup(loginPopup);
                    }
                    // 曝光引导挡位存储缓存
                    if (StringUtils.equals(forceLogin, "1")&&StringUtils.isNotBlank(filter.getExtend())) {
                        String extend= filter.getExtend().replace("\\","");
                        JSONObject js= JSONObject.parseObject(extend);
                        String guideLogin=js.getString("guideLogin");
                        if(StringUtils.equals(guideLogin,"1")){
                            //曝光引导挡位存储缓存
                            if(filter.getId()!=null&&filter.getId()>0&&StringUtils.isNotBlank(src)){
                                String key= String.format(CarConstant.CAR_GUVID_KEY ,filter.getId(),src,priceGearVO.getVirtualUid());
                                RedisLowPriceDAO.getInstance().addString(key,"1",60*60*24*7);
                            }
                        }
                    }
                }
            }
        }catch (Exception e){
            if(filter!=null&&filter.getId()!=null){
                log.error("virtualUidLogin has error filterId={}",filter.getId());
            }
            log.error("virtualUidLogin has error! ",e);
        }

    }

    /**
     * 判断给定的价格是否为用户优惠价
     *
     * @param formatPrice 价格字符串（已格式化）
     * @return 如果给定的价格为用户优惠价，则返回true；否则返回false
     */
    public boolean isUserCoupon(String formatPrice){
        return new BigDecimal(formatPrice).compareTo(new BigDecimal("19.9"))==0
                ||new BigDecimal(formatPrice).compareTo(new BigDecimal("57"))==0
                ||new BigDecimal(formatPrice).compareTo(new BigDecimal("108"))==0
                ||new BigDecimal(formatPrice).compareTo(new BigDecimal("204"))==0;
    }

    /**
     * 车载/音乐包升级档位
     *
     * @param userId  用户id
     * @param vehiclePriceVIPBOS 用户会员信息
     */
    private void checkUpdateSVIP(Long userId,List<VehiclePriceBO> vehiclePriceVIPBOS,int autoPay){
        UserInfoBO userInfoBO= invokeUserInfo(userId);
        userInfoBO.setVipVehicleAutoPayUser(autoPay);
        //log.info("userInfoBO userId={} userInfoBO={}",userId,JSONUtil.toJsonStr(userInfoBO));
        if(userInfoBO==null){
            return;
        }
        PriceInterceptor interceptor= new LuxVIPUpdateInterceptor(userInfoBO).appendNext(new VIPMUpdateInterceptor(userInfoBO));
        interceptor.doAuth(userId,vehiclePriceVIPBOS);
    }

    /**
     * 查询用户信息
     *
     * @param uid  用户id
     * @return  Boolean 返回查询结果，成功返回true，失败返回false
     */
    public UserInfoBO invokeUserInfo(Long uid) {
        UserInfoBO userInfoBO=new UserInfoBO();
        try {
            VipInfo vipInfo=getVipInfo(String.valueOf(uid));
            BasicUInfo basicUInfo= vipInfo.getBasicUInfo();
            SignStateInfo signStateInfo= vipInfo.getSignStateInfo();
            userInfoBO.setVipluxuryexpire(basicUInfo.getVipLuxuryExpireTime());
            userInfoBO.setVipmexpire(basicUInfo.getVipmExpireTime());
            userInfoBO.setSvipExpire(basicUInfo.getSvipExpireTime());
            userInfoBO.setVipVehicleExpire(basicUInfo.getVehicleExpireTime());
            userInfoBO.setVipmAutoPayUser(signStateInfo.getVipmAutoPayUser());
            userInfoBO.setSvipAutoPayUser(signStateInfo.getSvipAutoPayUser());
            userInfoBO.setLuxAutoPayUser(signStateInfo.getLuxAutoPayUser());
            userInfoBO.setVipVehicleAutoPayUser(signStateInfo.getVehicleAutoPayUser());
        } catch (Exception e) {
            log.error("getNewPriceGearInfo error invokeUserInfo has error! ", e);
            carMonitor.PRICEINFO_PARAM_ERROR.increment();
        }
        return userInfoBO;
    }

    /**
     * 会员信息获取
     *
     * @param uid
     * @return
     */
    public VipInfo getVipInfo(String uid){
        try{
            CompletableFuture<VipInfo> newUIVipInfoFuture= uiInfoService.getAndValidateUIAllInfo(uid);
            return newUIVipInfoFuture.get(500, TimeUnit.MILLISECONDS);
        }catch (Exception e){
            log.error("getVipInfo has error! uid={}",uid,e);
        }
        return null;
    }

}
