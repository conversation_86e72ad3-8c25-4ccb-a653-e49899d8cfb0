package com.memberintergral.carservice.service.impl;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 第三方service异步通知列表
 */
@Service
@Deprecated
public class ThirdNotifyService {

    @Value("${carmusic.unsign.url}")
    public String UNSIGN_URL;
    @Value("${carmusic.sign.url}")
    public String SIGN_URL;
    @Value("${carmusic.md5key}")
    public String MD5KEY;
    @Value("${carmusic.notify.url}")
    public String NOTIFY_URL;



}
