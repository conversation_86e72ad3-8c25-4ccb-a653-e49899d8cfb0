package com.memberintergral.carservice.service.impl;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.memberintergral.carservice.domain.entity.DataDataset;
import com.memberintergral.carservice.domain.entity.DataItem;
import com.memberintergral.carservice.mapper.DataDatasetMapper;
import com.memberintergral.carservice.mapper.DataItemMapper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @program: vip_adv
 * @description: 数据模型管理service
 * @author: <EMAIL>>
 * @create: 2020-05-21 09:42
 **/
@DS("abRead")
@Service
public class DataModelService {
    public static final Logger logger = LoggerFactory.getLogger(DataModelService.class);


    @Autowired
    private DataDatasetMapper dataSetMapper;

    @Autowired
    private DataItemMapper dataItemMapper;

    /**
     * 根据code查询DataSet数据
     *
     * @param code
     * @return
     */
    public List<DataDataset> getDataSetByActivityCode(String code) {
        logger.info("getDataSetByActivityCode, code:{}", code);
        if (StringUtils.isBlank(code)) {
            return null;
        }
        return dataSetMapper.getDataSetByActivityCode(code);
    }

    /**
     * 根据code查询数据集
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2020/5/21 16:55
     */
    public  List<DataItem> getVehicleDataItemByMid(Integer mid) {
        logger.info("查询数据集，code:{}", mid);
        if (mid==null) {
            return null;
        }
        return dataItemMapper.getVehicleDataItemByMid(mid);
    }

    /**
     * 根据code查询数据集
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2020/5/21 16:55
     */
    public  List<DataItem> getVehicleDataItemByActivityCode(String code) {
        logger.info("查询数据集，code:{}", code);
        if (StringUtils.isBlank(code)) {
            return null;
        }
        return  dataItemMapper.getVehicleDataItemByActivityCode(code);
    }

    /**
     * 根据title查询数据集
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2020/5/21 16:55
     */
    public  List<DataItem> getDataItemByInfo(String info) {
        logger.info("查询数据集，info:{}", info);
        if (StringUtils.isBlank(info)) {
            return null;
        }
        return  dataItemMapper.getDataItemByInfo(info);
    }

    public List<DataItem> getDataItemByCode(Map<String, Object> data) {
        return dataItemMapper.getDataItemByCode(data);
    }

}
