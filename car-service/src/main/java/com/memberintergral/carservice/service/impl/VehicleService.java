package com.memberintergral.carservice.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.memberintergral.carservice.config.constant.MessageModel;
import com.memberintergral.carservice.config.constant.SystemCodeErrorConstant;
import com.memberintergral.carservice.config.constant.ThirdPartConstant;
import com.memberintergral.carservice.config.constant.VehicleConstant;
import com.memberintergral.carservice.config.enums.*;
import com.memberintergral.carservice.config.exception.VehicleCouponException;
import com.memberintergral.carservice.config.exception.VehicleException;
import com.memberintergral.carservice.config.exception.VehiclePriceException;
import com.memberintergral.carservice.config.helper.CollectionHelper;
import com.memberintergral.carservice.config.nacos.CarConfigNacos;
import com.memberintergral.carservice.config.redis.RedisKey;
import com.memberintergral.carservice.config.redis.added.RedisDAO;
import com.memberintergral.carservice.config.redis.added.RedisLowPriceDAO;
import com.memberintergral.carservice.domain.BO.CarParamBO;
import com.memberintergral.carservice.domain.BO.UserInfoBO;
import com.memberintergral.carservice.domain.DTO.PayInfoDTO;
import com.memberintergral.carservice.domain.DTO.PayOrderDTO;
import com.memberintergral.carservice.domain.entity.*;
import com.memberintergral.carservice.mapper.*;
import com.memberintergral.carservice.util.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kuwo.commercialization.common.utill.TimeLogger;
import com.memberintergral.carservice.domain.entity.*;
import com.memberintergral.carservice.mapper.*;
import com.memberintergral.carservice.util.*;
import com.memberintergral.carservice.util.pay.PrePayReq;
import com.memberintergral.carservice.util.pay.PrePayResp;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PropertiesLoaderUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;


/**
 * @program: vip_adv
 * @description: 车载业务逻辑类
 * @author: <EMAIL>
 * @create: 2018-09-26 10:54
 **/
@Service
@DS("master")
public class VehicleService {
    public static final Logger logger = LoggerFactory.getLogger(VehicleService.class);
    @Autowired
    private VehicleOrderMapper vehicleOrderMapper;
    @Value("${vip.server.domain}")
    private String vipDomain;
    @Value("${pay.server.domain}")
    private String payUrl;
    @Value("${qrcode.image.path}")
    private String qrcodeImagePath;
    @Value("${image.access.path}")
    private String imageAccessPath;
    @Value("${vip.server.boxOutPayUrl}")
    private String boxOutPayUrl;

    @Value("${pay.wx.callback.url}")
    private String payCallbackUrl;

    @Autowired
    private CarConfigNacos carConfigNacos;

    @Autowired
    private VehicleOrderExtendMapper vehicleOrderExtendMapper;

    @Autowired
    private VehicleProductMapper vehicleProductMapper;
    @Autowired
    private VehicleAssistant vehicleAssistant;
    @Autowired
    private VehicleBusinessOrderMapper vehicleBusinessOrderMapper;
    @Autowired
    private InvokingRemoteService invokingRemoteService;
    @Autowired
    private CarActivityType carActivityType;

    @Autowired
    private ThirdNotifyService thirdNotifyService;

    @Autowired
    private VehicleNotifyMapper vehicleNotifyMapper;

    @Autowired
    private VehicleAssistant assisstant;

    @Autowired
    private RemoteHelper remoteHelper;


    @Autowired
    private VehicleStockMapper vehicleStockMapper;


    @Autowired
    private VehicleActivityService vehicleActivityService;

    @Autowired
    private CacheUtils cacheUtils;

    private Logger orderLogger = LoggerFactory.getLogger("orderLogger");
    /**
     * 活动key标识
     */
    private final static String ACTIVITY_KEY = "vehicle20221111";

    /**
     * 会员5周年活动key标识
     */
    private final static String FIVE_YEAR_ACTIVITY_KEY = "vehicle_five_year_activity";

    /**
     * 春节活动key标识
     */
    private final static String SPRING_FESTIVAl_ACTIVITY_KEY = "vehicle_spring_fes_2022";


    /**
     * 优惠券key
     */
    private final static String VEHICLE_COUPON_KEY = "vehicle_coupon_templeate_";

    /**
     * 无分成渠道
     */
    private final static List<String> channelList = Arrays.asList("apktest", "B_yunzhi_h", "B_yunzhi_h_h5", "B_yunzhicheji", "B_nuoweida_vh_test", "ad_h5", "yingmo", "K_jiakong", "jiakong", "B_cheyunhulian", "B_szim", "B_szimcj", "B_zhuoxingwei_vh", "B_baihang_vh", "B_feigehz_h5", "B_feigehz", "B_feige_vh", "B_pingwang", "xingyingtong", "B_xingyingtong_h5", "B_xingyingtong", "B_tongxingzhe_vh", "B_meijia_h", "B_siwei", "B_lianyou", "B_fangxiangzi", "B_sirui", "B_qichengkeji", "B_qichengkejicj", "B_zhuoxingwei_vh_h5", "B_shouqi_cj", "B_yamei_cj", "B_yamei", "B_zhihuipaiss_h5", "B_zhihuipaiss", "zhihuipaiss", "B_chelizi", "B_chelizi_h5", "B_sibichi_vh", "B_sibichi_vh_h5", "B_sibichi_DD", "yadi", "B_lege_vh", "B_wuweizhilian_vh", "B_moguchelian_vh_h5", "B_moguchelian_vh", "B_KTE_v", "B_limcet", "B_chelianyi", "B_dezhong", "B_qingcheng", "B_carbit", "B_qcarlink", "B_chenxun2", "tengshi", "yliang", "tongxingzhe", "tongxingzhe_test", "ruiliangaoke_chejing", "yunzhisheng", "pinwangss", "chelianlian", "chexingjian", "yidong_fajue", "yidong_R631", "weishite", "fangxiangtong", "zhangrui", "banya_WM002", "banya_WM0001", "maigu_199", "B_shangli_vh_h5", "B_shangli_vh", "B_yiweitai", "B_fiegehz", "niouchejing", "szim", "B_shanghaixiaozhuochejin", "chehangjian", "B_zsjhaoche", "Yiqibenteng_X40FL_xiaomalixing", "xiaomalixing", "Yiqibenteng_X60_xiaomalixing", "xiaomalixing_HongqiH5", "siruntianlang_Yiqijunpai066", "Taima_Yiqi", "yiqibenteng_T77_xiaomalixing", "yiqijiefang_J6L_txzing", "yiqi_J6Ppingtaixiangmu_tongxingzhe", "yiqijiefang_J6P_zhonghuan_siwei", "yiqi_J6pingtaixiangmu_huayangtongyong", "dazhong_MQB_desay", "xiaomalixing_yiqibenteng_D077", "xiaomalixing_YiqibentengX40", "tongxingzhe_rongweiRX5", "Liantong_ShangqidatongSv61", "hezheng_rongweiRX5jiachangban_tongxingzhe", "guangqi_A12_maruili", "guangqi_A86_maruili", "guangqi_A86", "guangqi_A12_weishitong", "guangqi_A18_weishitong", "guangqi_A18_maruili", "guangqi_A12", "guangqi_A60_maruili", "guangqi_A55_maruili", "guangqi_A20", "guangqi_A20_desay", "guangqi_A18Y_A13_maruili", "guangqi_AM8", "guangqi_A18_M_maruili", "guangqi_A18M_maruili", "guangqi_A3K", "gaungqi_A3K", "guangqi_A39", "guangqi_A35", "guangqi_A26", "guangqi_A5_H6", "A06", "guangqi_A06", "guangqi_Gs4", "guangqisanling_chelianwan", "guangben_lingpai_tongxingzhe", "Trumpchi", "changanshangyongche_A800", "changan_C211_liantong", "changan_S301JD_liantong", "changanoushang_A002", "changanchengyongcheC301", "changan_C301ICA_liantong", "changanchengyongche_S201", "changanchengyongche_S201", "changan_S202_MCA", "changan_C301ICA_laintong", "changanchengyong_S401_MCA", "changanchengyongche_S401", "changanchengyongche_S301", "changanchengyongcheS301_JD", "changanchengyongche_C211_EV", "changanshangyongcheOuShang_F201", "changanshangyongcheOuShang_A800", "changanOushang_R111", "changanshangyongcheOuShang_V302", "changanshangyongcheOuShang_F201", "changanshangyongcheOuShang_A800", "changanoushang_A002", "changankaicheng_P201_haobangshou", "changan_S301_18_liantong", "changan_C211MCA_shop", "changan_C211MCA_rr", "changan _cs85_liantong", "changan_cs85_liantong_test", "changan_C211MCA_r", "changan_S111MCA", "yuante", "changanshangyongcheOuShang_R111", "changan_shop_wt", "changan_shop_wtS311MCA", "dongfengqichen_322EV", "dongfengqichen_GEN3", "Qiya_KC_xiaomalixing", "dongfengxiaokang_F517_liantong", "dongfengxiaokang_F537JP_liantong", "quliantong_Dongfengxiaokang_S513", "quliantong_Dongfengxiaokang_F537", "dongfengxiaokang_E70_haobangshou", "dongfen_x37_botai", "dongfengleinuo_HJE_hangsheng", "leinuorichan", "leinuorichan_shuping", "jidou_dongfengyulong", "Boshi_richan", "dongfengxiaokang_weixingSUV_hezhijuyun", "dongfengxioaknag_S560_liantong", "dongfengxiaokang_F513", "dongfengxiaokang_F505V", "dongfengxiaokang_F527_haobangshou", "Dongfengxiaokang_FG580ZG_liantong", "yaxunwangluo_dongfengD320_nandouliuxing", "huayang_Zhengzhourichan_P15", "yika_dongfengxiaokangF507S", "Dongfengxiaokang_FG580_liantong", "hangsheng_shenlongqiche", "xiaoma_dongfengAX4", "dongfangqichen", "botai_dongfengC35", "yika_dongfengxiaokangF516", "tianbao_T1A_qirui", "tongxingzhe_M1AEV_shuping", "qirui_xiaomayi_hongjing", "qirui_T1E_tainbao", "qirui_S61EV_hongjingdianzi", "qirui_T19EVFL_hongjingdianzi", "hongjing_qirui_T19", "qirui_S51EV_hongjing", "qirui_GS11", "qirui_M1AEV_T3", "qirui_HMA307A_huayang", "beidouxingtong_zhzongtaiT700", "beidouxingtong_ZhongtaiT600s", "luchang_zhongtaiA45", "Suoling_ZhongtaiSR7", "Suoling_Zhongtai_Z500ev", "Suoling_Zhongtai_T500", "luchang_hanlongL10", "Hezheng_JunmaMeet3", "haiwei_zhongtaiMa501", "zhongtai_hanteng_huayang", "tianyouwei_ZhongtaiJta10", "changchengqiche", "changcheng_CHK041_desaixiwei", "changcheng_B037_huayang", "changcheng_B037_huayang", "changchengqiche_EC01", "changcheng_M6_huayang", "changchengqiche_A01_haman", "changcheng_pika_desaixiwei", "changcheng_EC02_huayang", "changcheng_V3_xiandouzhineng", "beiqiyinxiang_C30_tongxingzhe", "hangsheng_BeiqiB20A", "hangsheng_BeiqiB20A", "xiaoma_BeiqiB20B", "Xiaoaikeji_Beiqiweiwang", "beiqi_C53FB_botai", "zhijia", "beiqi_C40_pateo", "Botai_BeiqixinnengyuanC51", "Huayang_BeiqiC10", "huayang_MK404", "beixin", "beiqichanghe_B20B_huayang", "beiqifutian_P202_tongxingzhe", "beiqifutian_P203_huayang", "jili_MPC_CN6_huayang", "Beiqi_futian", "beiqifutian_HMA202A_huayang", "beiqifutian_pingtaixm", "beiqifutian_HMA6W_huangyang", "huayang_M404K", "beiqifutianV1_huayang", "B_dongjun_v", "xiaomalixing_HuachenM82", "huachenxinyuan", "xiaomalixing_HuachenF70", "huachenxinyuan_S401", "jiangling_E300_tongxingzhe", "jiangling_E315_huayang", "jiangling_E180_tongxingzhe", "xiaomalixing_beiqiruina", "jili_MPC-CN6_huayang", "jili_BD2022_bdstar", "jili_3C1920_bdstar", "jili_BD2022_bdstar", "jili_3C1920_bdstar", "GKUI_yikatong", "botai_jiliboyue", "botai_jiliboyue", "botai_boyue", "woerwo_quanxi_huawei", "kaiwoqiche_BE11_kuwozhixing", "dongnan_DX7_huayang", "dongnan_DX9_huayang", "dongnan_DX3", "Baowoqiche_BX", "yibinkaiyi_kaiyiFX11_tongtuokeji", "hengchen_chuanqiyema", "mobilecarlink_A102B_yema", "dayun_M171_tongxingzhe", "dayunS191_tongxingzhe", "Buguniao_DayunS171", "Huayang_Jiangsusailin_AM139", "hangsheng_yijiete_KWID", "OBIGO", "Jixingkeji_Vt1", "Tiananzhilian_YujieLw01", "Lianlukeji", "sqzk_X6000_txz", "huabaoshangqidelongX3000", "xinte_DEV1", "lingtu_BX100", "hezhong_EP11_haobangshou", "hualingxingma_hangzhouhongquan_hualingzhongka", "shanxizhongxing_hangzhouhongquan_shanxizhongka", "xugong_H20_zhonghuan", "hongmeng", "changma_test", "xinte_aevs", "Yiqidazhong_EBO_wenzhong", "sanyi_328_mingshang", "sanyi_8257_mingshang", "sanyizhonggong_328", "wushiling_siwei", "haige_HQG703_hongquan", "hzjianghuai_ruifengM6_txz", "Adannengliang_MD11", "huaqin", "ruicheng_D138", "faurecia_aptoide", "xinjiayuan_P005", "dingwei_weichaiU70D", "cf_6L32_6V30", "gaodesmart", "sany_huaxing", "jiangxi_isuzu_PT025", "hangtian_CZ213A_jactruck", "CMZX_qingka_JAC", "zhengzhourichan_HMA206A_huayang", "hynex_appstore", "keluze", "zhixingchanglianss", "carlt_DamaiX7", "changanchengyongche_C301");



    /**
     * @Description: 下单逻辑
     * @Param: [request]
     * @return: cn.kuwo.vip1.util.common.constants.MessageModel
     * @Author: <EMAIL>
     * @Date: 2018/9/27 15:18
     */
    @SuppressWarnings("unchecked")
    public MessageModel order(OrderReqEntity orderReq) {
        MessageModel messageModel;
        /**Step-1 接参*/
        String uid = "null".equals(orderReq.getUid()) ? null : orderReq.getUid();
        String sid = orderReq.getSid();
        String virtualUid = "null".equals(orderReq.getVirtualUid()) ? null : orderReq.getVirtualUid();
        String platVersion = orderReq.getPlatVersion();
        String jsonStr = orderReq.getJsonStr();
        String callbackUrl = orderReq.getCallbackUrl()==null?"":orderReq.getCallbackUrl();
        String fromsrc = orderReq.getFromsrc();
        String carModel = orderReq.getCarModel();
        String filterId = orderReq.getFilterId();
        String version = orderReq.getVersion();
        String requestId = orderReq.getRequestId();
        logger.info("VehicleService-order-func1 进入车载下单方法，入参：uid:{} sid:{} virtualUid:{}  jsonStr:{} carModel={}", uid, sid, virtualUid, jsonStr,carModel);

        /**Step-2 参数校验*/
        messageModel = checkOrderParam(jsonStr, uid, virtualUid);
        if (!messageModel.getCode().equals(SystemCodeErrorConstant.SUCCESS.getCode())) {
            return messageModel;
        }

        /**Step-3 创建订单*/
        Map<String, Object> map = (Map<String, Object>) messageModel.getData();
        PayOrderDTO payOrderDTO = (PayOrderDTO) map.get("payOrderDTO");
        List<Product> products = (List<Product>) map.get("products");
        messageModel = createOrder(uid, sid, virtualUid, platVersion, payOrderDTO, products, null, DoOrderTypeEnum.NOMAL_ORDER,callbackUrl,orderReq.getFromType(),fromsrc, orderReq);
        if (messageModel.getCode().intValue() == 200){
            Map data = (Map) messageModel.getData();
            if (data.containsKey("orderId")){
                String urlParams = "";
                if (StringUtils.isNotBlank(orderReq.getUrlparams())){
                    urlParams = orderReq.getUrlparams();
                }else{
                    urlParams="duid="+orderReq.getDeviceId()+"&fromsrc="+orderReq.getFromsrc()+"&fromType="+orderReq.getFromType();
                }
                String orderId = String.valueOf(data.get("orderId"));
                saveCarActivityRedis(products,orderId);
                saveOrderFilterIdRedis(orderId,filterId);
                String logInfo = String.format("carOrderTrace>>\t%s\t%s\t%s\t%s",
                        orderId,urlParams,products.get(0).getProductTypeId() , products.get(0).getId());
                orderLogger.info(logInfo);
                //记录urlParams中的参数到缓存
                UrlParamsUtils.addExtendInfo(urlParams,payOrderDTO,orderId);
                String payType = payOrderDTO.getPayType();
                boolean isIos = "119".equals(payType) || "127".equals(payType);
                boolean isKwApp = payOrderDTO.getPaySrc().equals("kwApp");
                if (version!=null){
                    boolean isNewVersion = MyNumberUtils.toINT(version.replaceAll("\\.", ""))>=11180;
                    String reqUid = "";
                    if (MyNumberUtils.toLONG(uid) > 0){
                        reqUid = String.valueOf(uid);
                    }else {
                        reqUid = String.valueOf(virtualUid);
                    }
                    if (isIos && isKwApp && isNewVersion){
                        try {
                            PrePayReq payReq = PrePayReq.builder()
                                    .customerId(orderId)
                                    .service("newvip_carmusic")
                                    .payType(payType)
                                    .userId(reqUid)
                                    .credit(new BigDecimal(String.valueOf(payOrderDTO.getCash())))
                                    .kwb(new BigDecimal(String.valueOf(payOrderDTO.getCash())))
                                    .build();
                            PrePayResp prePayResp = remoteHelper.prePay(payReq);
                            if (prePayResp!=null){
                                data.put("payId", prePayResp.getAppAccountToken());
                            }
                        } catch (Exception e) {
                            logger.error("VehicleService-order-func1 调用预支付接口失败",e);
                        }
                    }
                }
            }
        }
        savePaySuccessAlertMsg(uid, requestId, filterId, products, payOrderDTO.getSrc());
        /**Step-4 返回*/
        logger.info("VehicleService-order-func1 车载下单方法结束,返回: {}", JsonUtil.bean2Json(messageModel));
        return messageModel;
    }

    public void saveOrderFilterIdRedis(String orderId,String filterId){
        try {
            if(StringUtils.isNotBlank(filterId)){
                RedisLowPriceDAO.getInstance().addString(VehicleConstant.CAR_ORDER_FILTER_ID+orderId,filterId,60*60*24*7);
            }
        }catch (Exception e){
            logger.error("saveOrderFilterIdRedis has error!",e);
        }
    }

    // 把支付成功弹窗信息记录到 redis
    public void savePaySuccessAlertMsg(String uid, String reqId, String filterId, List<Product> products, String src) {
        try {
            if (StringUtils.isBlank(reqId) || StringUtils.isBlank(filterId) || "null".equals(filterId) || products == null || StringUtils.isBlank(src)) {
                logger.info("savePaySuccessAlertMsg params error, reqId={},filterId={},products={},src={}", reqId, filterId, products, src);
                return;
            }
            logger.info("savePaySuccessAlertMsg uid={}, reqId={},filterId={},products={},src={}", uid, reqId, filterId, products, src);
            JSONObject data = new JSONObject();
            CarParamBO carParamBO = new CarParamBO();
            carParamBO.setIsActivity(0);
            carParamBO.setFilterStr(filterId);
            List<Filter> cacheFilters = cacheUtils.filterCache.get(carParamBO);
            if (!cacheFilters.isEmpty()) {
                Filter filter = cacheFilters.get(0);
                JSONObject filterDoc = JSONObject.parseObject(filter.getDoc());
                data.put("qrcodeLink", filterDoc.getString("qrcodeLink"));
                data.put("payFinishedPopupImg", filterDoc.getString("payFinishedPopupImg"));
                data.put("qrcodeUnloginText", filterDoc.getString("qrcodeUnloginText"));
                data.put("payFinishedPopupTitle", filterDoc.getString("payFinishedPopupTitle"));
                data.put("payFinishedPopupDesc", filterDoc.getString("payFinishedPopupDesc"));
                data.put("payFinishedPopupTips", filterDoc.getString("payFinishedPopupTips"));
                data.put("showLoginPopup", filterDoc.getString("loginPopup"));
                data.put("showActivityPopup", filterDoc.getString("openLotteryPopup"));
            }
            if (!products.isEmpty()) {
                data.put("vipType", ProductEnum.getProductType(products.get(0).getProductTypeId().intValue()).getName());
            }
            data.put("paySrc", src);
            RedisDAO.getInstance().addString(VehicleConstant.VEHICLE_ACTIVITY_NEW_ORDER + ":alertJson:" + reqId, data.toString(), 60 * 60 * 10);
        } catch (Exception e) {
            logger.error("VehicleService-order-func1 saveOrderFilterIdRedis has error!", e);
        }
    }

    /**
     * 将活动挡位ID保存到Redis中。
     *
     * @param products 产品列表，预期包含至少一个产品信息。
     * @param orderId 订单ID，用于标识特定的订单。
     * 说明：该方法主要目的是将第一个产品的挡位ID与订单ID组合的键值对存储到Redis中，有效期为一周。
     */
    public void saveCarActivityRedis( List<Product> products,String orderId){
        try {
            // 检查产品列表不为空且第一个产品的库存ID不为空，则将其保存到Redis
            if(!CollectionUtils.isEmpty(products)&&StringUtils.isNotBlank(products.get(0).getStockId())){
                RedisLowPriceDAO.getInstance().addString(VehicleConstant.CAR_ACTIVITY_STOCK_ID+orderId,products.get(0).getStockId(),60*60*24*7);
            }
        }catch (Exception e){
            // 记录保存过程中出现的异常
            logger.error("saveCarActivityRedis has error!",e);
        }
    }



    /**
     * @Description: 下单参数校验
     * @Param: [jsonStr, uid, virtualUid]
     * @return: cn.kuwo.vip1.util.common.constants.MessageModel
     * @Author: <EMAIL>
     * @Date: 2018/9/27 15:25
     */
    public MessageModel checkOrderParam(String jsonStr, String uid, String virtualUid) {
        logger.info("VehicleService-checkOrderParam-func2 进入下单参数校验方法,入参:jsonStr:{}, uid:{}, virtualUid:{}", jsonStr, uid, virtualUid);
        MessageModel messageModel = new MessageModel();
        ObjectMapper mapper = new ObjectMapper();
        PayOrderDTO payOrderDTO;
        /**Step-1 校验用户id*/
        if (!vehicleAssistant.checkQueryUid(uid, virtualUid)) {
            logger.error("VehicleService-checkOrderParam-func2 用户id校验错误,uid: {}, virtualUid: {}", uid, virtualUid);
            return new MessageModel(SystemCodeErrorConstant.PARAM_CHECK_ERROR);
        }

        /**Step-2 转换dto*/
        try {
            if (StringUtils.isEmpty(jsonStr)) {
                logger.error("VehicleService-checkOrderParam-func2 jsonStr为空 ,uid: {}, virtualUid: {}", uid, virtualUid);
                return new MessageModel(SystemCodeErrorConstant.PARAM_CHECK_ERROR);
            }
            payOrderDTO = mapper.readValue(jsonStr, PayOrderDTO.class);
        } catch (Exception e) {
            logger.error("VehicleService-checkOrderParam-func2 payOrder get payOrderDTO fail, uid: " + uid + ", virtualUid: " + virtualUid, e);
            return new MessageModel(SystemCodeErrorConstant.PARAM_CHECK_ERROR);
        }
//        if(StringUtils.isNotBlank(payOrderDTO.getCouponUniqueId())&&!payOrderDTO.getSrc().equals("calculateMonth")&&!payOrderDTO.getSrc().equals("calculateSvipMonth")){
//            return new MessageModel(SystemCodeErrorConstant.COUPON_TYPE_NOT_VALID);
//        }
        /**Step-3 转换购买产品列表*/
        List<Product> products;
        try {
            products = this.getProducts(payOrderDTO);
            if (null == products) {
                if(StringUtils.isNotBlank(payOrderDTO.getSrc())&&payOrderDTO.getSrc().equals("caculateUpdateSVIP")){
                    return new MessageModel(SystemCodeErrorConstant.CAR_PRICE_ERROR);
                }
                logger.error("VehicleService-checkOrderParam-func2 The products is null, the param  payOrderDTO : {}", JsonUtil.bean2Json(payOrderDTO));
                return new MessageModel(SystemCodeErrorConstant.PARAM_CHECK_ERROR);
            }
        } catch (Exception e) {
            logger.error("VehicleService-checkOrderParam-func2 product convert error,uid: " + uid + ", virtualUid: " + virtualUid, e);
            return new MessageModel(SystemCodeErrorConstant.PARAM_CHECK_ERROR);
        }
        // 增加一层校验  外层cash和内层cash
        if (products!=null){
            double totalPrice = 0;
            for (Product product:products){
                totalPrice += product.getPrice();
            }
            if (new BigDecimal(payOrderDTO.getCash()).doubleValue() != totalPrice){
                logger.error("总价格和product price校验失败!");
                return new MessageModel(SystemCodeErrorConstant.PRICE_CHECK_ERROR);
            }
        }

        /**Step-4 校验续费 如果是续费用户，则不能再购买续费产品 */
        boolean isAutoPay = "yes".equalsIgnoreCase(payOrderDTO.getAutoPay());
        if (isAutoPay) {
            MessageModel model = checkAutoPay(uid, StringUtils.isNotEmpty(virtualUid) ? Long.parseLong(virtualUid) : 0L,payOrderDTO.getSrc(),products);
            if (!model.getCode().equals(SystemCodeErrorConstant.SUCCESS.getCode())) {
                return new MessageModel(SystemCodeErrorConstant.INVOKING_RETURN_ERROR);
            } else {
                if ((boolean) model.getData()) {
                    return new MessageModel(SystemCodeErrorConstant.AUTOPAY_CHECK_ERROR);
                }
            }
        }
        // 针对测试包做个下单限制
        if (payOrderDTO.getPaySrc().contains("APK_lingshu_test")){
            logger.error("ILLEGAL paysrc create order !!!");
            return new MessageModel(SystemCodeErrorConstant.ILLEGAL_REQUEST);
        }

        /**Step-5 三方上汽大众1分钱购买，限制每月购买额度500个月，约合1万元 */
        if (payOrderDTO.getPaySrc().contains("shangqidazhong") && "0.01".equals(payOrderDTO.getCash())) {
            //计算每月的额度
            SimpleDateFormat myFormatter = new SimpleDateFormat("yyyy-MM-dd");
            Calendar cal = Calendar.getInstance();
            cal.set(Calendar.DAY_OF_MONTH, 1);
            String startDate = myFormatter.format(cal.getTime());

            cal.add(Calendar.MONTH, 1);
            String endDate = myFormatter.format(cal.getTime());
            System.out.println(myFormatter.format(cal.getTime()));

            String new_lock_id = UUID.randomUUID().toString();
            Integer sumCnt = null;
            try {
                if (RedisDAO.tryGetDistributedLock(RedisKey.CARMUSIC_MINUTE_1_LOCK, new_lock_id, 1 * 60)){
                    String loadKey = String.format(RedisKey.CARMUSIC_MINUTE_1_STOCK, payOrderDTO.getPaySrc(), startDate);
                    if (RedisDAO.exists(loadKey)){
                        sumCnt = (int)RedisDAO.getInstance().incrTotalCntAndGetNew(loadKey, 0l);
                    }else {
                        sumCnt = vehicleOrderMapper.getSumMonthCnt(payOrderDTO.getPaySrc(), startDate, endDate);
                        if (sumCnt == null){
                            sumCnt = 0;
                        }
                        RedisDAO.getInstance().incrTotalCntAndGetNew(loadKey, Long.valueOf(sumCnt));
                    }
                }else {
                    return new MessageModel(SystemCodeErrorConstant.REQUEST_LOCK);
                }
            } catch (Exception e) {
                logger.error("exception load 1 min stock",e);
            } finally {
                RedisDAO.releaseDistributedLock(RedisKey.CARMUSIC_MINUTE_1_LOCK, new_lock_id);
            }
            sumCnt = null == sumCnt ? 0 : sumCnt;
            int cnt = 0;
            for (Product p : products) {
                cnt += p.getCnt();
            }
            if (sumCnt + cnt > 500) {
                return new MessageModel(SystemCodeErrorConstant.OUT_OF_QUOTA_ERROR);
            }
        }

        /**Step-6 返回 */
        Map<String, Object> map = new HashMap<>(4);
        map.put("payOrderDTO", payOrderDTO);
        map.put("products", products);
        messageModel.setData(map);
        logger.info("VehicleService-checkOrderParam-func2 下单参数校验方法结束,uid:{},virtualUid:{}返回: {}", uid, virtualUid, JsonUtil.bean2Json(messageModel));
        return messageModel;
    }


    /**
     * @Description: 参数转换成产品
     * @Param: [payOrderDTO]
     * @return: java.util.List<Product>
     * @Author: <EMAIL>
     * @Date: 2018/9/27 14:48
     */
    public List<Product> getProducts(PayOrderDTO payOrderDTO) throws Exception {
        List<Product> results = new ArrayList<Product>();
        Map<?, ?> productsMap = payOrderDTO.getProducts();
        ProductEnum productType;
        List<Map<?, ?>> productArr = new ArrayList<Map<?, ?>>();
        for (Map.Entry<?, ?> entry : productsMap.entrySet()) {
            if ("vip".equalsIgnoreCase(entry.getKey().toString())) {
                productArr = (List<Map<?, ?>>) entry.getValue();
                break;
            } else if ("songs".equalsIgnoreCase(entry.getKey().toString())) {
                List<Map<?, ?>> productArrT1 = (List<Map<?, ?>>) entry.getValue();
                if (null != productArrT1 && !productArrT1.isEmpty()) {
                    CollectionHelper.mapInListAddColumn(productArrT1, "type", ProductEnum.SONG.getName());
                    productArr.addAll(productArrT1);
                }
            } else if ("albums".equalsIgnoreCase(entry.getKey().toString())) {
                List<Map<?, ?>> productArrT2 = (List<Map<?, ?>>) entry.getValue();
                if (null != productArrT2 && !productArrT2.isEmpty()) {
                    CollectionHelper.mapInListAddColumn(productArrT2, "type", ProductEnum.ALBUM.getName());
                    productArr.addAll(productArrT2);
                }
            }
        }

        if (null != productArr && !productArr.isEmpty()) {
            for (Map<?, ?> product : productArr) {
                productType = ProductEnum.getProductType((String) product.get("type"));
                int cnti = MyNumberUtils.toINT(product.get("cnt"));
                //如果没有填写数量或者数量填写的不正确，则按照默认购买1件
                if (cnti == 0) {
                    cnti = 1;
                }
                short duration = (short) (cnti * 31);
                ;
                //专辑和歌曲的情况，期限应该为TimeUtils.PAY_DURATION
                if (productType.getId() == ProductEnum.ALBUM.getId() || productType.getId() == ProductEnum.SONG.getId()) {
                    duration = TimeUtils.PAY_DURATION;
                }
                // 大众车载VIP买一赠一
                if (payOrderDTO.getSrc().equals(CarActivitySuperEnum.ACTIVITY_VEHICLE_SEND.getOpStr()) && (payOrderDTO.getPaySrc().equals("yiqidazhong_h5"))) {
                    duration = (short) (duration + duration);
                }
//                if(CarActivitySuperEnum.BYD_ACTIVITY.getOpStr().equals(payOrderDTO.getSrc())){
//                    duration=(short) (cnti * (31+15));
//                }
                if (CarActivitySuperEnum.VIP_UPGRADE_VEHICLE.getOpStr().equals(payOrderDTO.getSrc())) {
                    if (product.get("duration") == null)
                        return null;
                    duration = MyNumberUtils.toSHORT(product.get("duration"));
                }

                String pid = product.get("id") == null ? null : product.get("id").toString();
                String ppid = (String) product.get("pid");
                ppid = StringUtils.isEmpty(ppid) ? pid : ppid;

                Product p = new Product();
                p.setId(pid);
                p.setPid(ppid);
                p.setProductTypeId((long) productType.getId());
                p.setCnt((short) cnti);
                p.setDuration(duration);
                p.setPrice(null == product.get("price") ? 0 : MyNumberUtils.toDouble(product.get("price").toString()));
                if (product.get("stockId") != null) {
                    String stockId = String.valueOf(product.get("stockId"));
                    p.setStockId(stockId);
                }
                if(VehicleConstant.sendDaysSrc.contains(payOrderDTO.getSrc())){
                    SendDaysEnum sendDaysEnum= SendDaysEnum.getInstance(payOrderDTO.getSrc());
                    if(sendDaysEnum!=null){
                        duration= (short) (duration+sendDaysEnum.getSendDays());
                        p.setDuration(duration);
                    }
                }
                // 活动赠送
                if(VehicleConstant.activitySrcList.contains(payOrderDTO.getSrc())&&payOrderDTO.getAutoPay().equals("yes")){
                    String paySrc= payOrderDTO.getPaySrc().toLowerCase();
                    if(VehicleConstant.jiaChannels.contains(paySrc)){
                        if(cnti==12){
                            duration= (short) (duration+93);
                            p.setDuration(duration);
                        }
                    }
                }
                if (product.get("upGradeId") != null) {
                    Integer upGradeId = (Integer) product.get("upGradeId");
                    if (upGradeId.equals(VipUpdatePriceEnum.VEHICLE_AUTO_ONE_MONTH_SVIP.getId()) || upGradeId.equals(VipUpdatePriceEnum.ACTIVITY_VEHICLE_AUTO_ONE_MONTH_SVIP.getId())|| upGradeId.equals(VipUpdatePriceEnum.VEHICLE_ONE_MONTH_SVIP.getId())) {
                        UserInfoBO userInfoBO = getUserVIPInfo(Long.parseLong(payOrderDTO.getUid()));
                        long betweenDay = DateUtil.between(new Date(userInfoBO.getVipVehicleExpire()), new Date(), DateUnit.DAY);
                        Date VehicleExpire = new Date(userInfoBO.getVipVehicleExpire());
                        if ((VehicleExpire.after(new Date()) && betweenDay <= 31 )) {
                            p.setDuration((short) (betweenDay));
                        } else {
                            return null;
                        }
                    }
                    p.setUpGradeId(upGradeId);
                }
                results.add(p);
            }
        } else {
            results = null;
        }

        return results;
    }


    /**
     * @Description: 组建订单参数
     * @Param: [uid, sid, virtualUid, platVersion, couponId, payOrderDTO, products, thirdType]
     * @return: cn.kuwo.vip1.util.common.constants.MessageModel
     * @Author: <EMAIL>
     * @Date: 2018/9/27 16:16
     */
    private MessageModel createOrder(String uid, String sid, String virtualUid, String platVersion, PayOrderDTO payOrderDTO,
                                     List<Product> products, String businessId, DoOrderTypeEnum orderType,String callbackUrl,Integer fromType,String fromsrc,OrderReqEntity orderReq) {
        logger.info("VehicleService-createOrder-func3  进入组建订单参数方法，入参：uid:{},sid:{},virtualUid:{},platVersion:{},couponId:{},payOrderDTO:{},products:{}",
                uid, sid, virtualUid, platVersion, JsonUtil.bean2Json(payOrderDTO), JsonUtil.bean2Json(products));
        MessageModel messageModel = new MessageModel();
        String orderProductType = "";

        /**Step-1 组建PayInfoDTO参数*/
        PayInfoDTO payInfo = makeUpPayInfo(uid, virtualUid, payOrderDTO, products, businessId, platVersion, orderProductType);

        /**Step-2 保存订单和userProduct*/
        try {
            messageModel = saveOrder(payInfo, orderProductType, orderType, payOrderDTO ,callbackUrl,fromType,fromsrc, orderReq);
        } catch (Exception e) {
            if(e instanceof VehicleCouponException){
                VehicleCouponException vehicleCouponException=(VehicleCouponException)e;
                messageModel.setCode(SystemCodeErrorConstant.COUPON_PARAM_NOT_VALID.getCode());
                messageModel.setDesc(vehicleCouponException.getMessage());
            }if(e instanceof VehiclePriceException){
                messageModel.setCode(SystemCodeErrorConstant.CAR_PRICE_ERROR.getCode());
                messageModel.setDesc(SystemCodeErrorConstant.CAR_PRICE_ERROR.getMessage());
            } else{
                messageModel.setSystemErrorCode(SystemCodeErrorConstant.PARAM_CHECK_ERROR);
                logger.error("VehicleService-createOrder-func3  保存订单异常", e);
            }
        }

        /**Step-3 返回*/
        logger.info("VehicleService-createOrder-func3  组建订单参数方法结束，返回: {}", JsonUtil.bean2Json(messageModel));
        return messageModel;
    }


    /**
     * 组建PayInfoDTO参数
     *
     * @param uid, virtualUid, payOrderDTO, products, businessId, platVersion
     * @return com.memberintergral.carservice.domain.entity.PayInfoDTO
     * <AUTHOR>
     * @date 2019/8/28 14:15
     */
    private PayInfoDTO makeUpPayInfo(String uid, String virtualUid, PayOrderDTO payOrderDTO, List<Product> products, String businessId, String platVersion, String orderProductType) {
        double credit = Double.valueOf(payOrderDTO.getCash());
        short payType = NumberUtils.toShort(payOrderDTO.getPayType());
        long uidl = StringUtils.isNotEmpty(uid) ? Long.valueOf(uid) : 0L;
        long vid1 = StringUtils.isNotEmpty(virtualUid) ? Long.valueOf(virtualUid) : 0L;
        PayInfoDTO payInfo = new PayInfoDTO();
        if (uidl > 0) {
            payInfo.setPid(uidl);
        } else {
            payInfo.setPid(vid1);
        }
        payInfo.setAct(payOrderDTO.getAct());
        payInfo.setAutoPay(payOrderDTO.getAutoPay());
        payInfo.setClientAct(payOrderDTO.getClientAct());
        payInfo.setCredit(credit);
        payInfo.setPayType(payType);
        payInfo.setPlatform(payOrderDTO.getPlatform());
        payInfo.setProducts(products);
        payInfo.setSrc(payOrderDTO.getSrc());
        payInfo.setUid(uidl);
        payInfo.setPaySrc(payOrderDTO.getPaySrc());
        payInfo.setPlatVersion(platVersion);
        payInfo.setBusinessId(businessId);
        if (StringUtils.isNotBlank(payOrderDTO.getDeveloperPayload())) {
            payInfo.setDeveloperPayload(payOrderDTO.getDeveloperPayload());
        }
        if (StringUtils.isNotBlank(payOrderDTO.getVinCode())) {
            payInfo.setVinCode(payOrderDTO.getVinCode());
        }
        //判断type是否为组合购买
        int vipTypeNum = 0;
        for (int i = 0; i < products.size(); i++) {
            Product product = products.get(i);
            int productTypeId = product.getProductTypeId().intValue();
            ProductEnum productType = ProductEnum.getProductType(productTypeId);
            orderProductType = productType.getCategory().getName();
            if (ProductTypeCategory.VEHICLE_VIP.equals(productType.getCategory())) {
                vipTypeNum++;
            }
        }

        //TYPE，用来表示订单的类型，0表示单品的订单，1表示复合订单
        if (vipTypeNum <= 1) {
            payInfo.setType(0);
        } else {
            payInfo.setType(1);
        }
        return payInfo;
    }


    /**
     * @Description: 保存订单
     * @Param: [payinfo]
     * @return: cn.kuwo.vip1.util.common.constants.MessageModel
     * @Author: <EMAIL>
     * @Date: 2018/9/29 16:03
     */
    @Transactional(value = "masterTransactionManager", rollbackFor = Exception.class)
    public MessageModel saveOrder(PayInfoDTO payInfo, String orderProductType, DoOrderTypeEnum orderType,PayOrderDTO payOrderDTO ,String callbackUrl,Integer fromType ,String fromsrc,OrderReqEntity orderReq) throws Exception {
        logger.info("VehicleService-saveOrder-func4 进入保存订单方法,入参: {}", JsonUtil.bean2Json(payInfo));
        final long uid = payInfo.getUid();

        /**Step-1 组建订单数据*/
        VehicleOrder vehicleOrder = makeUpOrderInfo(payInfo, orderType, orderProductType);

        /**Step-2 保存订单*/
        vehicleOrderMapper.insert(vehicleOrder);
        Long orderId = vehicleOrder.getId();
        Long virtualUid = payInfo.getUid() > 0 ? 0L : payInfo.getPid();
        List<Product> products= payInfo.getProducts();
        boolean isTsSrc=checkIsTsSrc(payInfo.getSrc(),orderId);
        // 这里进行插入扩展信息内容,直接走payInfo插入数据
        if (StringUtils.isNotBlank(payInfo.getVinCode())||StringUtils.isNotBlank(payInfo.getDeveloperPayload())||StringUtils.isNotBlank(payOrderDTO.getCouponUniqueId())||StringUtils.isNotBlank(callbackUrl)||(!CollectionUtils.isEmpty(products)&&products.get(0).getUpGradeId()!=null)||StringUtils.isNotBlank(fromsrc)||StringUtils.isNotBlank(orderReq.getCarModel())||isTsSrc||StringUtils.isNotBlank(payInfo.getSrc())||StringUtils.isNotBlank(orderReq.getElem_area())){
            VehicleOrderExtend vehicleOrderExtend = makeUpOrderExtend(vehicleOrder.getId(), payInfo.getVinCode(), payInfo.getDeveloperPayload() ,callbackUrl ,fromsrc,payInfo.getPaySrc(),orderReq,isTsSrc,payInfo.getSrc());
            CarActivityAbs activity = ActivityManager.getActivity(payInfo.getSrc());
            if (activity != null ){
                boolean isValidCoupon=activity.makeOrderExtendCoupon(vehicleOrderExtend,payOrderDTO.getCouponUniqueId(),vehicleOrder.getId(), payInfo);
                if(!isValidCoupon){
                    logger.error("vehicle_coupon_error:优惠券验证错误");
                    throw new VehicleCouponException("优惠券验证错误");
                }
            }
            vehicleOrderExtendMapper.insert(vehicleOrderExtend);
        }

        /**Step-3 校验价格是否符合*/
        MessageModel model = vehicleAssistant.checkPrice(payInfo, uid, virtualUid, orderId , payOrderDTO,fromType,orderReq);
        if (!model.getCode().equals(SystemCodeErrorConstant.SUCCESS.getCode())) {
            if(model.getCode().equals(SystemCodeErrorConstant.VEPHONE_GEAR_LIMIT.getCode())){
                logger.info("checkstock： VEPHONE_GEAR_LIMIT,orderId={}",orderId);
                return model;
            }
            if(model.getCode().equals(SystemCodeErrorConstant.CAR_PRICE_ERROR.getCode())){
                logger.error("saveOrder price is error! orderId={}",orderId);
                throw new VehiclePriceException("订单价格校验失败");
            }
            logger.error("价格校验失败");
            throw new VehicleException("价格校验失败");
        }

        /**Step-4 保存product信息*/
        List<VehicleProduct> saveProducts = (List<VehicleProduct>) model.getData();
        vehicleProductMapper.insertBatch(saveProducts);
        orderReq.setProduct(saveProducts.get(0));
        addPayRedisRecord(vehicleOrder.getId(), fromType,saveProducts);
        Map<String, Object> map = new HashMap<>(5);
        map.put("orderId", orderId);
        map.put("price", payInfo.getCredit());
        if(StringUtils.isNotBlank(callbackUrl)){
            map.put("callbackUrl", callbackUrl.contains("?")?callbackUrl+"&orderId="+orderId:callbackUrl+"?orderId="+orderId);
        }
        /**Step-5 选择性保存三方订单信息*/
        if (orderType != DoOrderTypeEnum.NOMAL_ORDER) {
            // check businessId 是否有下过单
//            VehicleBusinessOrder vehicleBusinessOrder = null;
//            if (StringUtils.isNotBlank(payInfo.getBusinessId())){
//                vehicleBusinessOrder = vehicleBusinessOrderMapper.getOrderById(payInfo.getBusinessId(), payInfo.getPaySrc());
//            }
//            if (vehicleBusinessOrder != null) {
//                return new MessageModel(SystemCodeErrorConstant.ORDER_IS_PROCESSED);
//            }
            VehicleBusinessOrder vehicleBusinessOrder = new VehicleBusinessOrder();
            vehicleBusinessOrder.setOrderId(orderId);
            vehicleBusinessOrder.setChannel(payInfo.getPaySrc());
            vehicleBusinessOrder.setBusinessId(payInfo.getBusinessId());
            vehicleBusinessOrder.setCreateTime(new Date());
            vehicleBusinessOrder.setAmount(payInfo.getCredit());
            vehicleBusinessOrderMapper.insert(vehicleBusinessOrder);
        }
        remoteHelper.invokeVip(vehicleOrder);
        logger.info("VehicleService-saveOrder-func4 保存订单方法结束, 保存订单成功, 返回: {}", JsonUtil.map2Json(map));
        return new MessageModel(map);
    }

    /**
     * 增加支付来源记录
     * <p>
     * orderstatus接口判断支付来源
     * </p>
     */
    public void addPayRedisRecord(Long orderId, Integer fromType, List<VehicleProduct> saveProducts) {
        try {
            if (fromType != null && orderId != null && orderId != 0 && fromType != 0 && !CollectionUtils.isEmpty(saveProducts)) {
                String redisKey = "user_buy_order_from_" + fromType + "_";
                VehicleProduct product = saveProducts.get(0);
                RedisDAO.getInstance().addString(redisKey + orderId, String.valueOf(product.getProductTypeId()), 60 * 60 * 12);
            }
        } catch (Exception e) {
            logger.error("user_buy_order_from has error,order_id={}", orderId, e);
        }
    }

    /**
     * check听书src
     * 如果是听书src则 扩展表存储一条数据标识
     * 1
     * @param src
     * @param orderId
     * @return
     */
    public boolean checkIsTsSrc(String src,long orderId){
        try{
            if(StringUtils.isNotBlank(src)&&carConfigNacos.getCarConfigBO()!=null&&carConfigNacos.getCarConfigBO().getTsSrcList()!=null){
                if(carConfigNacos.getCarConfigBO().getTsSrcList().contains(src)){
                    RedisDAO.getInstance().addString(VehicleConstant.VEHICLE_TS_SRC+orderId,"1",60*60*24);
                    return true;
                }
            }
        }catch (Exception e){
            logger.error("checkIsTsSrc has error!");
        }
        return false;
    }
    /**
     * 创建扩展订单
     *
     * @param orderId
     * @param vinCode
     * @param developerPayload
     * @return
     */
    private VehicleOrderExtend makeUpOrderExtend(Long orderId, String vinCode, String developerPayload, String callbackUrl, String fromsrc, String paySrc,OrderReqEntity orderReq, boolean isTsSrc,String src) {
        Date now = new Date();
        VehicleOrderExtend vehicleOrderExtend = new VehicleOrderExtend();
        vehicleOrderExtend.setOrderId(orderId);
        vehicleOrderExtend.setDeveloperPayload(developerPayload);
        vehicleOrderExtend.setVinCode(vinCode);
        vehicleOrderExtend.setCreateTime(now);
        vehicleOrderExtend.setUpdateTime(now);
        Map<String, String> map = new HashMap<>();
        if (StringUtils.equals(orderReq.getCallbackUrlExt(), "1")&StringUtils.isNotBlank(callbackUrl)) {
            callbackUrl=callbackUrl.contains("?")?callbackUrl+"&orderId="+orderId:callbackUrl+"?orderId="+orderId;
            callbackUrl = vehicleActivityService.invokeCreateShortURL(callbackUrl, System.currentTimeMillis() + 60 * 60 * 30 * 1000);
            orderReq.setCallbackUrl(callbackUrl);
        }else if (StringUtils.equals(paySrc, "yiqidazhong_h5")) {
            callbackUrl = vehicleActivityService.invokeCreateShortURL(callbackUrl, System.currentTimeMillis() + 60 * 60 * 30 * 1000);
        }
        map.put("callbackUrl", callbackUrl);
        map.put("fromsrc", fromsrc);
        map.put("carModel",orderReq.getCarModel());
        map.put("elem_area",orderReq.getElem_area());
        if(StringUtils.isNotBlank(src)&&isTsSrc){
            map.put("tsSrc", src);
        }
        vehicleOrderExtend.setExt2(JSONObject.toJSONString(map));
        return vehicleOrderExtend;
    }

    /**
     * 保存三方订单信息
     *
     * @param orderId
     * @param channel
     * @param businessId
     * @param credit
     */
    @Transactional(value = "masterTransactionManager", propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = false)
    public void saveBusinessOrder(Long orderId, String channel, String businessId, Double credit) {
        VehicleBusinessOrder vehicleBusinessOrder = new VehicleBusinessOrder();
        vehicleBusinessOrder.setOrderId(orderId);
        vehicleBusinessOrder.setChannel(channel);
        vehicleBusinessOrder.setBusinessId(businessId);
        vehicleBusinessOrder.setCreateTime(new Date());
        vehicleBusinessOrder.setAmount(credit);
        vehicleBusinessOrderMapper.insert(vehicleBusinessOrder);
    }

    /**
     * 组建保存订单的信息
     *
     * @param payInfo, orderType, orderProductType
     * @return com.memberintergral.carservice.domain.entity.VehicleOrder
     * <AUTHOR>
     * @date 2019/8/28 14:52
     */
    public VehicleOrder makeUpOrderInfo(PayInfoDTO payInfo, DoOrderTypeEnum orderType, String orderProductType) {
        final Timestamp currentTime = TimeUtils.getCurrentTime();
        VehicleOrder vehicleOrder = new VehicleOrder();
        vehicleOrder.setUserId(payInfo.getUid());
        //当uid为空是等于virtualUid
        vehicleOrder.setPid(payInfo.getPid());
        vehicleOrder.setTime(currentTime);
        vehicleOrder.setPayDate(null);
        vehicleOrder.setPayType(payInfo.getPayType());
        if (orderType == DoOrderTypeEnum.THIRD_ORDER_SPE1) {
            vehicleOrder.setStatus(OrderStatusEnum.FINISH.getStatus());
            vehicleOrder.setCredit(0d);
        } else {
            vehicleOrder.setStatus(OrderStatusEnum.CREATE.getStatus());
            vehicleOrder.setCredit(payInfo.getCredit());
        }
        vehicleOrder.setPlatform(payInfo.getPlatform());
        vehicleOrder.setSrc(payInfo.getSrc());
        vehicleOrder.setClientAct(payInfo.getClientAct());
        vehicleOrder.setAutoPay(payInfo.getAutoPay());
        vehicleOrder.setPaySrc(payInfo.getPaySrc());
        vehicleOrder.setPlatVersion(payInfo.getPlatVersion());
        vehicleOrder.setType(payInfo.getType());
        vehicleOrder.setProductType(orderProductType);
        return vehicleOrder;
    }


    /**
     * @Description: 检查是否已有自动续费
     * @Param: [uid, virtualUid]
     * @return: java.lang.Boolean
     * @Author: <EMAIL>
     * @Date: 2018/9/28 14:53
     */
    public MessageModel checkAutoPay(String uid, Long virtualUid,String src,List<Product> products) {
//        MessageModel model;
//        model = vehicleAssistant.getAutoPayStatusFromVip(uid, virtualUid);
//        if (!model.getCode().equals(SystemCodeErrorConstant.SUCCESS.getCode())) {
//            return model;
//        }
//        Map<String, Integer> map = (Map<String, Integer>) model.getData();
//        boolean res = false;
//        for (Integer val : map.values()) {
//            if (val == 1) {
//                res = true;
//            }
//            break;
//        }
        boolean res = false;
        Long pId = 0L;
        if (virtualUid > 0) {
            pId = virtualUid;
        } else {
            pId = Long.parseLong(uid);
        }
        //检查车载系统是否已经有自动续费
        VehicleOrder order = vehicleOrderMapper.getAutoPay(pId);
        // pid查询完毕之后,用用户id进行查询
        if (null == order) {
            order = vehicleOrderMapper.getAutoPayByUserId(pId);
        }
        logger.info("music update：checkAutoPay++++ pId={} ,order={}!",pId,JSONUtil.toJsonStr(order));
        if (null != order) {
            res = true;
        }
        if(order!=null&&order.getId()!=null
                &&(StringUtils.isNotBlank(src)&&src.equals(VipUpdatePriceEnum.VEHICLE_AUTO_SVIP.getSrc())
                &&!CollectionUtils.isEmpty(products)&&products.get(0).getProductTypeId()==34&&(products.get(0).getUpGradeId()==5||products.get(0).getUpGradeId()==6))){
            List<VehicleProduct> vehicleProducts= vehicleProductMapper.getProductsByOrderId(order.getId());
            logger.info("music update：checkAutoPay userId={} ,virtualUid={}, vehicleProducts={}!",uid,virtualUid,JSONUtil.toJsonStr(vehicleProducts));
            if(!CollectionUtils.isEmpty(vehicleProducts)&&vehicleProducts.get(0).getProductTypeId()==17){
                res = false;
            }else{
                res = true;
            }
        }

        return new MessageModel(res);
    }


    /**
     * @Description: 查询车载vip自动续费
     * @Param: [uid, virtualUid]
     * @return: cn.kuwo.vip1.util.common.constants.MessageModel
     * @Author: <EMAIL>
     * @Date: 2018/9/28 15:08
     */
    public MessageModel getCarAutoPay(String uid, String virtualUid) {
        logger.info("查询用户是否有车载vip自动续费,入参: uid:{}, virtualUid:{}", uid, virtualUid);
        if (!vehicleAssistant.checkQueryUid(uid, virtualUid)) {
            return new MessageModel(SystemCodeErrorConstant.PARAM_CHECK_ERROR);
        }

        Long pid = StringUtils.isNotEmpty(uid) ? Long.parseLong(uid) : Long.parseLong(virtualUid);

        VehicleOrder vehicleOrder = vehicleOrderMapper.getAutoPay(pid);
        logger.info("查询用户是否有车载vip自动续费,入参1: uid:{}, virtualUid:{}  vehicleOrder={}", uid, virtualUid, JSONUtil.toJsonStr(vehicleOrder));
        if (null == vehicleOrder) {
            vehicleOrder = vehicleOrderMapper.getAutoPayByUserId(pid);
        }
        logger.info("查询用户是否有车载vip自动续费,入参2: uid:{}, virtualUid:{}  vehicleOrder={}", uid, virtualUid, JSONUtil.toJsonStr(vehicleOrder));
        Map<String, Object> map = new HashMap<>(3);
        int num = 1;
        if (null == vehicleOrder) {
            num = 0;
        }
        map.put("isVIPCarAutoPay", num);
        return new MessageModel(map);
    }




    /**
     * @Description: 获取用户vip到期等信息
     * @Param: [uid, virtualUid]
     * @return: cn.kuwo.vip1.util.common.constants.MessageModel
     * @Author: <EMAIL>
     * @Date: 2018/9/29 16:33
     */
    public MessageModel getVehicleVipInfo(String uid, String virtualUid) {
        TimeLogger timeLogger = TimeLogger.getInstance("查询车载vip信息", LogTraceContextHolder.getTraceId());
        MessageModel messageModel;
        if (!vehicleAssistant.checkQueryUid(uid, virtualUid)) {
            return new MessageModel(SystemCodeErrorConstant.PARAM_CHECK_ERROR);
        }
        Long userId = null;
        Long virid = null;
        if (StringUtils.isNotEmpty(uid)) {
            userId = Long.parseLong(uid);
        }
        if (StringUtils.isNotEmpty(virtualUid)) {
            virid = Long.parseLong(virtualUid);
        }
        timeLogger.step("调用vip查询豪华vip和音乐包的权限");
        timeLogger.begin();
        Map<String, Object> resultMap;
        // 查询vip服务豪华vip和音乐包的权限
        logger.info("music update：checkValidPrice 1.2 params={}!", userId);
        messageModel = queryVipInfoFromVIP(uid, virtualUid);
        timeLogger.end();
        if (!messageModel.getCode().equals(SystemCodeErrorConstant.SUCCESS.getCode())) {
            return messageModel;
        }
        resultMap = (Map<String, Object>) messageModel.getData();
        timeLogger.step("查询车载数据库车载信息");
        timeLogger.begin();
        logger.info("music update：checkValidPrice 1.3 params={}!", userId);
        //查询车载vip信息
        List<QueryExpireRes> resList = vehicleProductMapper.getAllExpire(userId, virid);
        timeLogger.end();
        //车载vip到期时间*
        Long vipVehicleExpire = 0L;
        //电视vip到期时间
        Long tvVipExpire = 0L;
        if (resList.size() > 0) {
            for (QueryExpireRes queryExpireRes : resList) {
                long productType = queryExpireRes.getProductType().longValue();
                long vipExpire = queryExpireRes.getExpire().getTime();
                if (ProductEnum.VIP_VEHICLE.getId() == productType || ProductEnum.VIP_7DAYS.getId() == productType) {
                    vipVehicleExpire = Math.max(vipExpire, vipVehicleExpire);
                }
                if (ProductEnum.VIP_TV.getId() == productType) {
                    tvVipExpire = Math.max(vipExpire, tvVipExpire);
                }
            }
        }
        logger.info("music update：checkValidPrice 1.4 params={}!", userId);
        getUserSocre(uid, resultMap);
        logger.info("music update：checkValidPrice 1.5 params={}!", userId);
        resultMap.put("vipVehicleExpire", vipVehicleExpire);
        resultMap.put("tvVipExpire", tvVipExpire);
        resultMap.put("time", System.currentTimeMillis());
        timeLogger.printMS();
        logger.info("查询vip信息，uid:{}，virtualUid:{}，返回值:{}", uid, virtualUid, JsonUtil.map2Json(resultMap));
        return new MessageModel(resultMap);
    }

    /**
     * 增加用户等级
     *
     * @param uid       用户id
     * @param resultMap
     */
    private void getUserSocre(String uid, Map<String, Object> resultMap) {
        if (StringUtils.isBlank(uid) || StringUtils.equals(uid, "null")) {
            //logger.info("查询vip信息 getUserSocre，uid has error:uid={}",uid);
            resultMap.put("level", "");
            return;
        }
        String level = "VIP1";
        try {
            String url = "http://" + vipDomain + "/commercia/vipScore/basic/noauth/info?userId=%s&vers=wBnPCy64ws09dFkzkD0Po0TTBeexomeF";
            String result = HttpUtil.get(String.format(url, uid),500);
            if (StringUtils.isBlank(result)) {
                return;
            }
            // logger.info("addUserSocre result={}",result);
            JSONObject object = JSONObject.parseObject(result);
            JSONObject dataJSON = object.getJSONObject("data");
            level = dataJSON.getString("vipTag");
            if(resultMap.containsKey("svipExpire")){
                long svipExpire=Long.parseLong(String.valueOf(resultMap.get("svipExpire")));
                if(new Date(svipExpire).after(new Date())){
                    level=level.replace("VIP","SVIP");
                }
            }
        } catch (Exception e) {
            logger.error("addUserSocre has error,uid={}", uid, e);
        }
        resultMap.put("level", level);
    }

    /**
     * 从vip服务查询用户vip音乐包等到期时间
     *
     * @param uid
     * @return
     */
    public MessageModel queryVipInfoFromVIP(String uid, String virtualUid) {
        MessageModel messageModel = new MessageModel();
        Map<String, Object> map = new HashMap<>(5);
        String url = "http://" + vipDomain + ThirdPartConstant.VIP_EXPIRE_URI;
        logger.info("music update：checkValidPrice 1.3 url={}!", url);
        if (StringUtils.isNotEmpty(uid)) {
            map.put("uid", uid);
            map.put("op", ThirdPartConstant.VIP_EXPIRE_UID_OPTION);
        } else {
            map.put("virtualUserId", virtualUid);
            map.put("op", ThirdPartConstant.VIP_EXPIRE_VID_OPTION);
        }
        try {
            /**调用vip服务*/
//            long start = System.currentTimeMillis();
            logger.info("music update：checkValidPrice 1.4 url={}!", url);
            String result = MyHttpUtil.doPostRequest(url, map);
            logger.info("music update：checkValidPrice 1.5 url={}!", url);
//            long end = System.currentTimeMillis();
//            logger.info("调用vip服务查询vip信息uid:{},virtualUid:{}，返回值： result: {},耗时：{} ", uid, virtualUid, result, (end - start));
            JSONObject jsonObject = JSONObject.parseObject(result);
            Map<String, Object> mapMeta = (Map<String, Object>) jsonObject.get("meta");
            Map<String, Object> mapData = (Map<String, Object>) jsonObject.get("data");
            if (mapMeta.get("code").equals(SystemCodeErrorConstant.SUCCESS.getCode())) {
                messageModel.setData(mapData);
            } else {
                logger.error("调用vip服务,查询不成功uid:{},virtualUid:{}，,返回值为: {}", uid, virtualUid, JsonUtil.map2Json(mapMeta));
                messageModel.setSystemErrorCode(SystemCodeErrorConstant.QUERY_FAIL);
            }
        } catch (Exception e) {
            logger.error("uid:" + uid + ", virtualUid:" + virtualUid + "，调用vip异常", e);
            messageModel.setSystemErrorCode(SystemCodeErrorConstant.INVOKING_EXCEPTION);
        }
        return messageModel;
    }

    public MessageModel queryVipInfoExtendFromVIP(String uid) {

        MessageModel messageModel = new MessageModel();
        Map<String, Object> map = new HashMap<>(5);
        String url = "http://" + vipDomain + ThirdPartConstant.VIP_EXPIRE_URI;

        if (StringUtils.isNotEmpty(uid)) {
            map.put("uid", uid);
            map.put("extend", 1);
            map.put("op", ThirdPartConstant.VIP_EXPIRE_UID_OPTION);
        }

        try {
            /**调用vip服务*/
            String result = MyHttpUtil.doPostRequest(url, map);
            JSONObject jsonObject = JSONObject.parseObject(result);
            Map<String, Object> mapMeta = (Map<String, Object>) jsonObject.get("meta");
            Map<String, Object> mapData = (Map<String, Object>) jsonObject.get("data");
            if (mapMeta.get("code").equals(SystemCodeErrorConstant.SUCCESS.getCode())) {
                messageModel.setData(mapData);
            } else {
                logger.error("调用vip服务,查询不成功uid:{},返回值为: {}", uid, JsonUtil.map2Json(mapMeta));
                messageModel.setSystemErrorCode(SystemCodeErrorConstant.QUERY_FAIL);
            }
        } catch (Exception e) {
            logger.error("uid:" + uid + "，调用vip异常", e);
            messageModel.setSystemErrorCode(SystemCodeErrorConstant.INVOKING_EXCEPTION);
        }
        return messageModel;
    }


    /**
     * @Description: 支付回调
     * @Param: [request, requestData]
     * @return: cn.kuwo.vip1.util.common.constants.MessageModel
     * @Author: <EMAIL>
     * @Date: 2018/10/3 10:39
     */
    public ResultCode payNotify(String orderId, String credit, String customerId, String key, String time, String thirdOrderId) {
        MessageModel model = new MessageModel();
        ResultCode resultCode = ResultCode.SUCCESS;
        /**Step-1 参数校验*/
        logger.info("支付回调，参数校验前, orderId:{},  model:{}", orderId, JsonUtil.bean2Json(model));
        model = this.checkNotifyParam(orderId, credit, customerId, key, time, thirdOrderId);
        logger.info("支付回调，参数校验后, orderId:{},  model:{}", orderId, JsonUtil.bean2Json(model));

        /**Step-2 更新订单和用户产品信息*/
        if (model.getCode().equals(SystemCodeErrorConstant.SUCCESS.getCode())) {
            VehicleOrder order = (VehicleOrder) model.getData();
            String orderKey = VehicleConstant.PROCESS_ORDER_NOTIFY_PREFIX + order.getThirdOrderId();
            try {
                model = vehicleAssistant.operateOrder(order, orderKey);
                logger.info("订单信息:{} 回调结束，结果: {}", JsonUtil.bean2Json(order), JsonUtil.bean2Json(model));
            } catch (Exception e) {
                logger.error("orderId:" + order.getId() + "customerid:" + order.getThirdOrderId() + " 支付通知异常", e);
                model.setSystemErrorCode(SystemCodeErrorConstant.UPDATE_DATA_ERROR);
            } finally {
                if (RedisDAO.exists(orderKey)) {
                    RedisDAO.delKey(orderKey);
                }
            }
        }

        /**Step-3 返回*/
        logger.info("支付回调，返回前打印, orderId:{},  model:{}", orderId, JsonUtil.bean2Json(model));
        resultCode.setCode(model.getCode());
        resultCode.setMsg(model.getDesc());

        if (model.getCode().equals(SystemCodeErrorConstant.ORDER_IS_ALREADY_PROCESSED.getCode())) {
            resultCode.setCode(SystemCodeErrorConstant.SUCCESS.getCode());
            resultCode.setMsg(SystemCodeErrorConstant.SUCCESS.getMessage());
        }
        return resultCode;
    }

    /**
     * 支付付在第三方，同步回调
     *
     * @param customerid
     * @param response
     * @return
     */
    public MessageModel thirdNotify(Long customerid, HttpServletResponse response) {
        VehicleOrder order = vehicleOrderMapper.getOrderById(customerid);
        if (order == null) {
            return new MessageModel(SystemCodeErrorConstant.NO_ORDER_FOUND);
        }
        VehicleOrderExtend vehicleOrderExtend = vehicleOrderExtendMapper.getVehicleOrderExtendByOid(order.getId());
        String profile = "";
        String url = "";
        String callbackUrl = "";
        if (vehicleOrderExtend != null && StringUtils.isNotBlank(vehicleOrderExtend.getExt2())) {
            String centent = vehicleOrderExtend.getExt2();
            String callback = "";
            if (JSONUtil.isTypeJSON(centent)) {
                JSONObject json = JSONObject.parseObject(centent);
                if (json.containsKey("callbackUrl")) {
                    callback = json.getString("callbackUrl");
                }
            } else {
                callback = vehicleOrderExtend.getExt2();
            }
            callbackUrl = callback.contains("?") ? callback + "&orderId=" + order.getId() : callback + "?orderId=" + order.getId();
        }
        try {
            Resource resource = new ClassPathResource("application.properties");
            Properties props = PropertiesLoaderUtils.loadProperties(resource);
            profile = props.getProperty("spring.profiles.active");
        } catch (Exception e) {
            logger.error("加载环境出错！", e);
        }

        if ("prod".equals(profile)) {
            url = "http://vip1.kuwo.cn/vip/added/activity_zy/chezai/index.html?orderId=";
        } else {
            url = "http://testvip.kuwo.cn/vip/added/activity_zy/chezai/index.html?orderId=";
        }

        if (CarActivitySuperEnum.VIP_UPGRADE_VEHICLE.getOpStr().equals(order.getSrc())) {
            url += customerid;
            response.setStatus(HttpServletResponse.SC_MOVED_TEMPORARILY);
            response.setHeader("Location", url);
            return new MessageModel();
        }
        if (StringUtils.isNotBlank(callbackUrl)) {
            response.setStatus(HttpServletResponse.SC_MOVED_TEMPORARILY);
            response.setHeader("Location", callbackUrl);
            return new MessageModel();
        }
        return new MessageModel(SystemCodeErrorConstant.SRC_MAP_URL_ERROR);
    }


    /**
     * 支付回调参数校验
     *
     * @param orderId, credit, customerId, key, time, thirdOrderId
     * @return cn.kuwo.vip1.util.common.constants.MessageModel
     * <AUTHOR>
     * @date 2019/9/9 14:06
     */
    public MessageModel checkNotifyParam(String orderId, String credit, String customerId, String key, String time, String thirdOrderId) {
        String encStr = customerId + credit + time + ThirdPartConstant.PAY_ENC_KEY;
        logger.info("支付服务回调参数校验，入参: orderId:{}, credit:{},customerid:{},key:{},time:{},thirdOrderId:{}", orderId, credit, customerId, key, time, thirdOrderId);

        /**Step-1 校验签名*/
        encStr = MD5.getMD5ofStr(encStr);
//        if (StringUtils.isEmpty(encStr) || !encStr.equals(key)) {
//            logger.error("支付服务回调参数校验，验签失败，orderId:{}, 传入的key:{},车载加密串:{}", orderId, key, encStr);
//            return new MessageModel(SystemCodeErrorConstant.CHECK_SIGN_ERROR);
//        }

        /**Step-2 订单校验*/
        VehicleOrder order;
        try {
            order = vehicleOrderMapper.getOrderById(Long.parseLong(orderId));
        } catch (Exception e) {
            logger.error("订单编号转换错误,orderId: " + orderId, e);
            return new MessageModel(SystemCodeErrorConstant.ORDER_ID_ERROR);
        }
        if (null == order) {
            logger.error("没有找到对应订单，  订单id：{}", orderId);
            return new MessageModel(SystemCodeErrorConstant.NO_ORDER_FOUND_ERROR);
        }
        if (order.getStatus() == 1) {
            logger.error("订单已经被处理，订单id：{}", orderId);
            return new MessageModel(SystemCodeErrorConstant.ORDER_IS_ALREADY_PROCESSED);
        }
        Double c1 = order.getCredit();
        Double c3 = Double.valueOf(credit);
        //TODO 金额校验，正式环境需放开此校验
//        if (Math.abs(c1 - c3) >= 1) {
//            logger.error("金额有误，orderId:{},查询出的订单金额：{}, 传入的金额：{}", orderId, c1, c3);
//            return new MessageModel(SystemCodeErrorConstant.CREDIT_CHECK_ERROR);
//        }
        if (order.getStatus().equals(OrderStatusEnum.PAIED.getStatus())) {
            return new MessageModel(SystemCodeErrorConstant.SUCCESS);
        }
        if (order.getStatus() != OrderStatusEnum.CREATE.getStatus()) {
            return new MessageModel(SystemCodeErrorConstant.ORDER_STATUS_ERROR);
        }
        order.setThirdOrderId(thirdOrderId);
        order.setStatus(OrderStatusEnum.PAIED.getStatus());
        order.setPayDate(new Timestamp(System.currentTimeMillis()));
        return new MessageModel(order);
    }



    /**
     * 更新订单状态
     *
     * @param uid
     * @throws Exception
     * <AUTHOR> Wanyu
     */
    public void unSignAutoPaySimple(long uid, String productCode) {
        if (StringUtils.isEmpty(productCode)) {
            vehicleOrderMapper.updateCancelByPid(uid);
        }
    }


    /**
     * 根据用户id
     *
     * @param uid
     * @return cn.kuwo.vip1.util.common.constants.MessageModel
     * <AUTHOR>
     * @date 2018/12/11 20:20
     */
    public MessageModel getAllOrderStatusByUserId(String uid) {
        if (StringUtils.isEmpty(uid)) {
            return new MessageModel(SystemCodeErrorConstant.PARAM_CHECK_ERROR);
        }

        List<VehicleOrder> orders = vehicleOrderMapper.getAllOrderStatusByUserId(Long.parseLong(uid));
        List<OrderStatusDTO> dtos = new ArrayList<>();
        for (VehicleOrder vo : orders) {
            OrderStatusDTO os = new OrderStatusDTO();
            os.setOrderId(vo.getId());
            os.setStatus(vo.getStatus());
            dtos.add(os);
        }
        return new MessageModel(dtos);
    }



    /**
     * 获取用户开通的VIP信息
     *
     * @param userId 用户id
     * @return UserInfoBO 封装用户VIP信息类
     */
    public UserInfoBO getUserVIPInfo(Long userId){
        logger.info("music update：checkValidPrice 1.1 params={}!", userId);
        if(userId==null||userId == 0){
            logger.info("vehicle_first_coupon：getPriceInfo user is not valid ! userId={}", userId);
            return null;
        }
        MessageModel messageModel=getVehicleVipInfo(String.valueOf(userId),null);
        logger.info("music update：checkValidPrice 1.6 params={}!", userId);
        if (!messageModel.getCode().equals(SystemCodeErrorConstant.SUCCESS.getCode())) {
            logger.info("vehicle_first_coupon：isNewUserSend" +
                    " newUserSend userId =0 .userId={}!",userId);
            return null;
        }
        UserInfoBO userInfoBO=new UserInfoBO();
        Map<String, Object> map= (Map<String, Object>) messageModel.getData();
        long vipLuxuryExpire = 0;
        long svipExpire = 0;
        long vipVehicleExpire=0;
        long vipmExpire=0;
        int svipAutoPayUser=0;
        int vipmAutoPayUser=0;
        int luxAutoPayUser=0;
        int vipVehicleAutoPayUser=0;

        if (map.containsKey("vipLuxuryExpire")){
            vipLuxuryExpire = Long.parseLong(String.valueOf(map.get("vipLuxuryExpire")));
        }
        if (map.containsKey("svipExpire")){
            svipExpire =  Long.parseLong(String.valueOf(map.get("svipExpire")));
        }
        if (map.containsKey("vipVehicleExpire")){
            vipVehicleExpire = Long.parseLong(String.valueOf(map.get("vipVehicleExpire")));
        }
        if (map.containsKey("vipmExpire")){
            vipmExpire = Long.parseLong(String.valueOf(map.get("vipmExpire")));
        }
        if (map.containsKey("svipAutoPayUser")){
            svipAutoPayUser = Integer.parseInt(String.valueOf(map.get("svipAutoPayUser")));
        }
        if (map.containsKey("vipmAutoPayUser")){
            vipmAutoPayUser = Integer.parseInt(String.valueOf(map.get("vipmAutoPayUser")));
        }
        if (map.containsKey("luxAutoPayUser")){
            luxAutoPayUser = Integer.parseInt(String.valueOf(map.get("luxAutoPayUser")));
        }
        if (map.containsKey("vipVehicleAutoPayUser")){
            vipVehicleAutoPayUser = Integer.parseInt(String.valueOf(map.get("vipVehicleAutoPayUser")));
        }
        try {
            logger.info("music update：checkValidPrice 1.7 params={}!", userId);
            MessageModel result= getCarAutoPay(String.valueOf(userId),null);
            Map<String, Integer> vipMap= (Map<String, Integer>) result.getData();
            if (vipMap.containsKey("isVIPCarAutoPay")&&vipMap.get("isVIPCarAutoPay")==1) {
                vipVehicleAutoPayUser = 1;
            }
        }catch (Exception e){
            logger.error("getUserVIPInfo vipVehicleAutoPayUser has error ! uid={}",userId,e);
        }
        userInfoBO.setLuxAutoPayUser(luxAutoPayUser);
        userInfoBO.setSvipAutoPayUser(svipAutoPayUser);
        userInfoBO.setVipmAutoPayUser(vipmAutoPayUser);
        userInfoBO.setVipVehicleAutoPayUser(vipVehicleAutoPayUser);
        userInfoBO.setVipluxuryexpire(vipLuxuryExpire);
        userInfoBO.setSvipExpire(svipExpire);
        userInfoBO.setVipmexpire(vipmExpire);
        userInfoBO.setVipVehicleExpire(vipVehicleExpire);
//        userInfoBO.setVipluxuryexpire(1680169227000L);
//        userInfoBO.setSvipExpire(0L);
//        userInfoBO.setVipmexpire(1680169227000L);
//        userInfoBO.setVipVehicleExpire(1677886027000L);
        return userInfoBO;
    }

    /**
     * 获取车载会员相关到期时间
     * @param userId
     * @param virid
     * @return
     */
    public VehicleAllExpireInfo getVehicleInnerVipExpireTime(Long userId, Long virid){
        VehicleAllExpireInfo expireInfo = new VehicleAllExpireInfo();
        List<QueryExpireRes> resList = vehicleProductMapper.getAllExpire(userId, virid);
        Long vipVehicleExpire = 0L;
        Long tvVipExpire = 0L;
        if (resList.size() > 0) {
            for (QueryExpireRes queryExpireRes : resList) {
                long productType = queryExpireRes.getProductType().longValue();
                long vipExpire = queryExpireRes.getExpire().getTime();
                if (ProductEnum.VIP_VEHICLE.getId() == productType || ProductEnum.VIP_7DAYS.getId() == productType) {
                    vipVehicleExpire = Math.max(vipExpire,vipVehicleExpire);
                }
                if (ProductEnum.VIP_TV.getId() == productType){
                    tvVipExpire = Math.max(vipExpire,tvVipExpire);
                }
            }
        }
        expireInfo.setVipVehicleExpire(vipVehicleExpire);
        expireInfo.setTvVipExpire(tvVipExpire);
        return expireInfo;
    }

    public List<VehicleOrder> getVehicleByUserIdSrc(long uid, long virtualUid, String src){
        return vehicleOrderMapper.getVehicleByUserIdSrc(uid, virtualUid, src);
    }


}
