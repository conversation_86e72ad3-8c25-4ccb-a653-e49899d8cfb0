package com.memberintergral.carservice.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.memberintergral.carservice.config.constant.MessageModel;
import com.memberintergral.carservice.domain.BO.VehicleCouponBO;
import com.memberintergral.carservice.domain.entity.VehicleCoupon;

import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-24
 */
public interface VehicleCouponService extends IService<VehicleCoupon> {
     MessageModel getFirstCoupon(Long userId, String channel, Long virtualUid, String deviceId);
     MessageModel getCouponList(Long userId, String channel, Long virtualUid, String deviceId, Map<Integer, VehicleCouponBO> conMap, Map<Integer,VehicleCouponBO> notConMap);
     VehicleCoupon getVehicleCouponsBySerialKey( String serialKey ,  Long uid, Long virtualUid);

}
