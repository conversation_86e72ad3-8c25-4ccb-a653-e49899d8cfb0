package com.memberintergral.carservice.service.impl.activity;


import com.alibaba.fastjson.JSONObject;
import com.memberintergral.carservice.config.enums.CarActivityMonthPriceEnum;
import com.memberintergral.carservice.config.enums.CarActivitySuperEnum;
import com.memberintergral.carservice.config.enums.CarActivitySuperVipAutoPayMonthPriceEnum;
import com.memberintergral.carservice.config.enums.ProductEnum;
import com.memberintergral.carservice.config.redis.added.RedisDAO;
import com.memberintergral.carservice.domain.entity.DataItem;
import com.memberintergral.carservice.domain.entity.Product;
import com.memberintergral.carservice.service.impl.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;


/**
 * @program: vip_adv
 * @description: 车载项目会员宠粉季Q1活动
 * @author: <EMAIL>
 * @create: 2022-10-25 13:37
 **/
@Component
public class VehiclePetFansQ3Activity extends CarActivityAbs {

    private static final Logger logger = LoggerFactory.getLogger(VehiclePetFansQ3Activity.class);

    /**
     * 活动key标识
     */
    private final static String ACTIVITY_KEY = "veh_pefs_2023Q3";

    /**
     * 数据集servce
     */
    @Autowired
    private DataModelService dataModelService;


    /**
     * 获取产品价格
     *
     * @param params
     * @return
     */
    @Override
    public double getfinalPrice(ActivityParams params) {
        if(params==null || StringUtils.isBlank(params.getPaySrc()) || params.getProduct()==null){
            logger.error("veh_pefs_2023Q3：getfinalPrice function params has empty value ,params={}", JSONObject.toJSON(params));
            return -1;
        }
        Product product=params.getProduct();

        if(params.getProduct().getStockId()==null){
            logger.error("veh_pefs_2023Q3：getfinalPrice functionparams.getProduct().getStockId() empty value ,params={}", JSONObject.toJSON(params));
            return -1;
        }
        String code=ACTIVITY_KEY+"_"+product.getStockId();
        List<DataItem> dataItemList= dataModelService.getVehicleDataItemByActivityCode(code);
//        try{
//            RedisDAO.getInstance().addList(ACTIVITY_KEY+":"+"getfinalPrice", dataItemList,60*60*24*62);
//        }catch (Exception e){
//            logger.error(ACTIVITY_KEY+"dataItemList has error");
//        }
        if(CollectionUtils.isEmpty(dataItemList)||dataItemList.size()!=1){
            logger.error("veh_pefs_2023Q3：getfinalPrice function dataItemList size is empty or >1 ,code={}",code);
            return -1;
        }
        DataItem dataItem= dataItemList.get(0);
        String content= dataItem.getContent();
        JSONObject jsonObject= JSONObject.parseObject(content);
        String discountPrice = jsonObject.getString("discount_price");
        return new BigDecimal(discountPrice).doubleValue();

    }



    /**
     * 初始化
     *
     * @throws Exception
     */
    public void afterPropertiesSet() throws Exception{
        ActivityManager.registerActivity(getActivityCode().getOpStr(), this);
    }

    /**
     * 验证日期有效性
     *
     * @param params
     * @return
     */
    @Override
    public boolean validateInPeriod(ActivityParams params) {
        logger.error("veh_pefs_2023Q3：validateInPeriod function  ,params={}",JSONObject.toJSON(params));
        if(params==null||StringUtils.isBlank(params.getPaySrc())){
            logger.error("veh_pefs_2023Q3：validateInPeriod function params has empty value ,params={}",JSONObject.toJSON(params));
            return false;
        }
        String paySrc = params.getPaySrc();
        paySrc=paySrc.toLowerCase();
        String timeKey=ACTIVITY_KEY+"_"+paySrc;
        String startTimeKey=ACTIVITY_KEY+"_"+timeKey+"_startTime";
        String endTimeKey=ACTIVITY_KEY+"_"+timeKey+"_endTime";
        String startTime= (String) RedisDAO.getInstance().getObject(startTimeKey, String.class);
        String endTime= (String) RedisDAO.getInstance().getObject(endTimeKey, String.class);
        if(StringUtils.isBlank(startTime)||StringUtils.isBlank(endTime)){
            List<DataItem> dataItemList= dataModelService.getVehicleDataItemByActivityCode(timeKey);
//            try{
//                RedisDAO.getInstance().addList(ACTIVITY_KEY+":"+"validateInPeriod", dataItemList,60*60*24*62);
//            }catch (Exception e){
//                logger.error(ACTIVITY_KEY+"dataItemList has error");
//            }
            logger.info("veh_pefs_2023Q3：validateInPeriod dataItemList={}",JSONObject.toJSON(dataItemList));
            if(CollectionUtils.isEmpty(dataItemList)){
                return false;
            }
            DataItem dataItem= dataItemList.get(0);
            JSONObject jsonObject = JSONObject.parseObject(dataItem.getContent());
            startTime=jsonObject.getString("start_time");
            RedisDAO.getInstance().setObject(startTimeKey,startTime,5);
            endTime=jsonObject.getString("end_time");
            RedisDAO.getInstance().setObject(endTimeKey,endTime,5);
        }
        return isInPeroid(params.getCurrentDate(), startTime, endTime);
    }

    /**
     * 判断当前时间是否在活动有效期内
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2020/10/26 16:33
     */
    public static boolean isInPeroid(Date date, String startTime, String endTime) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        Date start = null;
        Date end = null;
        try {
            start = sdf.parse(startTime);
            end = sdf.parse(endTime);
        } catch (Exception e) {
            logger.error("日期转换错误", e);
        }
        if (start == null || end == null)
            return false;
        if (date.getTime() - start.getTime() > 0 && date.getTime() - end.getTime() < 0)
            return true;
        else
            return false;
    }


    @Override
    public CarActivitySuperEnum getActivityCode(){
        return CarActivitySuperEnum.ACTIVITY_VEHICLE_PET_FANS_Q3;
    }


    @Override
    public boolean isPhysicTimeExpire() {
        return false;
    }

    /**
     * 校验src, 默认购买的都是车载vip
     *
     * @return
     */
    @Override
    public boolean validateSrc(ActivityParams activityParams){
        if(activityParams==null){
            logger.error("veh_pefs_2023Q3：activityParams function params has empty value ");
            return false;
        }
        Product product = activityParams.getProduct();
        Long productTypeId = product.getProductTypeId();
        if (productTypeId== ProductEnum.VIP_VEHICLE.getId()||productTypeId== ProductEnum.SUPER_VIP.getId()){
            return true;
        }
        return false;
    }

    /**
     * 获取次月续费价格信息
     * 默认价格策略, 按照 caclNormalMonth方式进行扣费
     *  1： 19.9
     *  3： 57
     *  6：  108
     *  12： 204
     */
    public RenewalPrice getRenewalPriceInfo(RenewalParams renewalParams){
        RenewalPrice renewalPrice = new RenewalPrice();
        short cnt = renewalParams.getCnt();
        if(renewalParams.getProductTypeId() == ProductEnum.SUPER_VIP.getId()){
            CarActivitySuperVipAutoPayMonthPriceEnum instance = CarActivitySuperVipAutoPayMonthPriceEnum.getInstance(Integer.valueOf(cnt));
            renewalPrice.setCnt(cnt);
            renewalPrice.setDuration((short) (cnt * 31));
            renewalPrice.setPrice(instance.getFinalAmount());
            return renewalPrice;
        }
        CarActivityMonthPriceEnum instance = CarActivityMonthPriceEnum.getInstance(Integer.valueOf(cnt));
        renewalPrice.setCnt(cnt);
        renewalPrice.setDuration((short) (cnt * 31));
        renewalPrice.setPrice(instance.getFinalAmount());
        return renewalPrice;
    }


}
