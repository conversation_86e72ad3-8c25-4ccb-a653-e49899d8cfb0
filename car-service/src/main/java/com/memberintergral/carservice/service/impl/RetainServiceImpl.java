package com.memberintergral.carservice.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.com.google.protobuf.ServiceException;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.commerical.abserviceapi.req.AbRequest;
import com.commerical.abserviceapi.resp.AbResponse;
import com.commerical.abserviceapi.service.AbRuleService;
import com.memberintergral.carservice.config.constant.MessageModel;
import com.memberintergral.carservice.domain.BO.VehiclePriceBO;
import com.memberintergral.carservice.domain.VO.PriceGearVO;
import com.memberintergral.carservice.domain.entity.Filter;
import com.memberintergral.carservice.domain.entity.Gear;
import com.memberintergral.carservice.domain.entity.PayDesk;
import com.memberintergral.carservice.enums.VipTypeEnum;
import com.memberintergral.carservice.service.GearService;
import com.memberintergral.carservice.service.PayDeskService;
import com.memberintergral.carservice.service.RetainService;
import com.memberintergral.carservice.util.CacheUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class RetainServiceImpl implements RetainService {

    @Autowired
   private PayDeskService payDeskService;
    @Autowired
    private GearService gearService;

    @Autowired
    private FilterServiceImpl filterService;

    @Autowired
    private CacheUtils cacheUtils;


    @DubboReference
    private AbRuleService abRuleService;


    public  final List<String> payDeskkList = Arrays.asList("carengine_retain");

    @Override
    public MessageModel getPriceGearInfo(PriceGearVO priceGearVO) {
        log.info("Retain getPriceGearInfo param={}",JSONUtil.toJsonStr(priceGearVO));
        List<VehiclePriceBO> vehiclePriceVIPBOS=new ArrayList<>();
        List<VehiclePriceBO> vehiclePriceSVIPBOS=new ArrayList<>();
        Map<String,List<VehiclePriceBO>> result=new LinkedHashMap<>();
        result.put("vipPrice",vehiclePriceVIPBOS);
        result.put("svipPrice",vehiclePriceSVIPBOS);
        try{
            if(StringUtils.isBlank(priceGearVO.getUid())||priceGearVO.getUid().equals("0")){
                log.info("Retain user not valid!");
                return new MessageModel(result);
            }
            if(StringUtils.isBlank(priceGearVO.getPaySrc())&&priceGearVO.getPaySrc().equals("caculateUpdateSVIP")||priceGearVO.getPaySrc().equals("caculateUpdateVehicleVIP")){
                log.error("Retain update gear! ={} ",JSONUtil.toJsonStr(priceGearVO));
                return new MessageModel(result);
            }
            if(!payDeskkList.contains(priceGearVO.getPayDeskSign())||StringUtils.isBlank(priceGearVO.getVipType())||priceGearVO.getCnt()==0||priceGearVO.getOldFilterId()==0){
                log.error("Retain not valid param={} ",JSONUtil.toJsonStr(priceGearVO));
                return new MessageModel(result);
            }
            Filter filter= filterService.getById(priceGearVO.getOldFilterId());
            String extend=filter.getExtend();
            String carRetain="";
            if(StringUtils.isNotBlank(extend)){
                extend= extend.replace("\\","");
                JSONObject js= JSONObject.parseObject(extend);
                carRetain=js.getString("carGearType");
            }
            log.info("Retain  carRetain={} extend={}",carRetain,extend);
            if(StringUtils.isBlank(carRetain)){
                log.error("Retain not carRetain is blank!");
                return new MessageModel(result);
            }
            priceGearVO.setPlatform("ar");
            PayDesk payDesk = payDeskService.getPayDeskInfoBySign(priceGearVO.getPayDeskSign());
            if(payDesk==null){
                log.error("Retain payDesk is null!");
                return new MessageModel(result);
            }
            List<Gear> gears= gearService.getCarGearList(payDesk.getId(),priceGearVO.getPlatform(),priceGearVO.getVipType(),priceGearVO,priceGearVO.getCnt());
            if(CollectionUtils.isEmpty(gears)){
                log.error("Retain gears is null!");
                return new MessageModel(result);
            }
            List<Filter> filters= invokeValidFilter(priceGearVO, gears);
            remove(filters,carRetain);
            log.info("Retain filters ={}",JSONUtil.toJsonStr(filters));
            if(CollectionUtils.isEmpty(filters)){
                log.error("Retain filters is null!");
                return new MessageModel(result);
            }
            Collections.sort(filters);
            Map<Integer, List<Filter>> filterMap = filters.stream().collect((Collectors.groupingBy(Filter::getGearId)));
            groupByVipTypes(gears, filterMap,priceGearVO,result);
        }catch (Exception e){
            log.error("getPriceGearInfo carretain has error",e);
        }
        return new MessageModel(result);
    }

    public void remove( List<Filter> filters,String sourceCarRetain){
        Iterator<Filter> filterIterator = filters.iterator();
        while (filterIterator.hasNext()) {
            try{
                Filter filter = filterIterator.next();
                if (StringUtils.isNotBlank(filter.getExtend())) {
                    String extend= filter.getExtend().replace("\\","");
                    JSONObject js= JSONObject.parseObject(extend);
                    String carRetain=js.getString("carGearType");
                    if(!StringUtils.equals(carRetain,sourceCarRetain)){
                        filterIterator.remove();
                    }
                }  else{
                    filterIterator.remove();
                }
            }catch (Exception e){
                log.error("remove has error！",e);
                filterIterator.remove();
            }
        }
    }

    /**
     * 过滤有效filter
     *
     * @param priceGearVO 请求参数
     * @return
     * @throws ServiceException
     */
    public List<Filter> invokeValidFilter(PriceGearVO priceGearVO, List<Gear> gear){
        String gearIds = gear.stream().map(x -> String.valueOf(x.getId())).collect(Collectors.joining(","));
        List<Filter> filters = null;
        try {
            // 远程调用接口过滤filter
            filters = getFilteredGearsByRequest(priceGearVO, gearIds);
        } catch (Exception e) {
            log.error("paydesk:invoke filters is error ! priceGearVO={}", JSONUtil.toJsonStr(priceGearVO), e);
        }
        return filters;
    }

    /**
     * 根据请求获取过滤后的filter列表
     *
     * @param priceGearVO 价格档位的值对象
     * @param gearId 挡位ID
     * @return 过滤后的filter列表
     */
    public List<Filter> getFilteredGearsByRequest(PriceGearVO priceGearVO, String gearId) {
        AbRequest abRequest = new AbRequest();
        abRequest.setRuleType("CAR_RULE_TYPE");  // 设定规则类型
        abRequest.setUserId(priceGearVO.getUid());  // 设置用户ID
        abRequest.setGearId(gearId);  // 设置挡位ID
        abRequest.setSource(priceGearVO.getSource());
        abRequest.setDeviceId(priceGearVO.getDeviceId());
        abRequest.setVirtualUserId(priceGearVO.getVirtualUid());
        abRequest.setFromsrc(priceGearVO.getFromsrc());
        abRequest.setChannel(priceGearVO.getChannel());
        abRequest.setFromType(priceGearVO.getFromType());
        abRequest.setPayDeskSign(priceGearVO.getPayDeskSign());
        AbResponse eval = abRuleService.evalCar(abRequest);
        // 增加非空校验以避免NullPointerException
        if (eval!=null) {
            List<Object> maps = eval.getHits();
            if (!CollectionUtils.isEmpty(maps)) {
                try {
                    // 使用更安全的转换方式来避免NumberFormatException
                    List<Integer> filterIds = maps.stream()
                            .map(Object::toString) // 直接使用toString，避免String.valueOf的额外包装
                            .filter(str -> !str.isEmpty() && str.matches("\\d+")) // 过滤非空且仅包含数字的字符串
                            .map(Integer::parseInt) // 安全转换为Integer
                            .collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(filterIds)) {
                        List<Integer> filterIdSet = filterIds.stream().collect(Collectors.toList());
                        try {
                            // 从Guava LoadingCache中获取filter列表
                            return filterService.getFilters(filterIdSet,priceGearVO.getIsActivity());
                        } catch (Exception e) {
                            log.error("getFilteredGearsByRequest filterId has error! ",e);
                        }
                    }
                } catch (NumberFormatException e) {
                    log.error("getFilteredGearsByRequest response has error! ",e);
                }
            }
        }
        return Collections.emptyList();
    }

    /**
     * 分类模式 单挡位
     *
     * @param gears 挡位
     * @param filterMap
     */
    public void groupByVipTypes(List<Gear> gears, Map<Integer, List<Filter>> filterMap, PriceGearVO priceGearVO, Map<String,List<VehiclePriceBO>> result) {
        // 所有的档位按类型进行分类
        try {
            Map<String, List<Gear>> gearMap = gears.stream().collect(Collectors.groupingBy(Gear::getVipType, LinkedHashMap::new, Collectors.toList()));
            for (String vipType : gearMap.keySet()) {
                List<VehiclePriceBO> vipList=null;
                if(vipType.equals(VipTypeEnum.VEHICLE_VIP.getType())){
                    vipList= result.get("vipPrice");
                }
                if(vipType.equals(VipTypeEnum.SUPER_VIP.getType())){
                    vipList= result.get("svipPrice");
                }
                try {
                    List<Gear> vipTypeGears = gearMap.get(vipType);
                    List<VehiclePriceBO> finalVipList = vipList;
                    vipTypeGears.forEach(itemGear -> {
                        try {
                            VehiclePriceBO vehiclePriceBO=new VehiclePriceBO();
                            // 档位对应的策略
                            List<Filter> filters = filterMap.get(itemGear.getId());
                            if (CollectionUtils.isEmpty(filters)) {
                                return;
                            }
                            parseData(vehiclePriceBO, itemGear, filters, priceGearVO);
                            finalVipList.add(vehiclePriceBO);
                        } catch (Exception e) {
                            log.error("SinglePriceService groupByVipTypes itemGear has error ", e);
                        }
                    });
                } catch (Exception e) {
                    log.error("SinglePriceService SinglePriceService gearMap item has error", e);
                }
            }

        } catch (Exception e) {
            log.error("SinglePriceService groupByVipTypes has error !",e);
        }
    }
    /**
     * 解析parse
     *
     * @param vehiclePriceBO
     * @param gear
     * @param filterLists
     * @param priceGearVO
     */
    public void parseData(VehiclePriceBO vehiclePriceBO, Gear gear, List<Filter> filterLists, PriceGearVO priceGearVO) {
        try {
            filterLists.forEach(filter -> {
                try {
                    parseFilter(filter, gear, vehiclePriceBO, priceGearVO);
                } catch (Exception e) {
                    log.error("paydesk：filterType has error!filterType", e);
                }
            });
        } catch (Exception e) {
            log.error("paydesk：parseData has error!gear={}", JSONObject.toJSON(gear), e);
        }
    }

    /**
     * 价格解析
     * @param filter
     * @param gear
     * @param vehiclePriceBO
     * @param priceGearVO
     */
    public void parseFilter(Filter filter, Gear gear, VehiclePriceBO vehiclePriceBO, PriceGearVO priceGearVO){
        // abData 普通
        if(vehiclePriceBO.getFilterId()!=null){
            return;
        }
        try{
            JSONObject filterDoc=JSONObject.parseObject(filter.getDoc());
            String topTag=filterDoc.getString("topTag");
            String src=filterDoc.getString("src");
            String subTitle=filterDoc.getString("subTitle");
            String bottomTag=filterDoc.getString("bottomTag");
            JSONObject gearDoc=JSONObject.parseObject(gear.getDoc());
            String oPrice=gearDoc.getString("oPrice");
            String autoPayBtnShow=gearDoc.getString("autoPayBtnShow");
            DecimalFormat decimalFormat = new DecimalFormat("###################.###########");
            String formatPrice=decimalFormat.format(filter.getPrice());
            vehiclePriceBO.setCurrentPrice(Double.parseDouble(formatPrice));
            vehiclePriceBO.setRenewPrice(filter.getRenewalPrice());
            vehiclePriceBO.setFilterId(filter.getId());
            vehiclePriceBO.setDoc(filter.getDoc());
            vehiclePriceBO.setMonth(gear.getGearType());
            vehiclePriceBO.setUnderPrice(StringUtils.isBlank(oPrice)?0:Double.parseDouble(oPrice));
            if(gear.getGearName().contains("听书")){
                String title=gearDoc.getString("title");
                vehiclePriceBO.setTitle(title);
            }else{
                vehiclePriceBO.setTitle(gear.getGearName());
            }
            vehiclePriceBO.setSubTitle(subTitle);
            vehiclePriceBO.setSelected(StringUtils.isNotBlank(autoPayBtnShow) && autoPayBtnShow.equals("1"));
            vehiclePriceBO.setPaySrc(src);
            vehiclePriceBO.setAutoPay(gear.getAutoPay().equals(1));
            vehiclePriceBO.setUpGradeId(null);
            vehiclePriceBO.setLabel(null);
            vehiclePriceBO.setBottomTag(bottomTag);
            if(gear.getVipType().equals(VipTypeEnum.SUPER_VIP.getType())){
                vehiclePriceBO.setWxTflg("svip_"+gear.getGearType());
            }
            if(gear.getVipType().equals(VipTypeEnum.VEHICLE_VIP.getType())){
                vehiclePriceBO.setWxTflg("vehicle_"+gear.getGearType());
            }
            if(ObjectUtils.isNotNull(filter.getEndTime())){
                long nowTime= System.currentTimeMillis();
                long endTime=filter.getEndTime().getTime();
                if(endTime-nowTime>0){
                    // 剩余时间
                    vehiclePriceBO.setLastTime(endTime-nowTime);
                }
            }
            vehiclePriceBO.setTag(topTag);
            vehiclePriceBO.setLabel(autoPayBtnShow);
            vehiclePriceBO.setFilterId(filter.getId());
        }catch (Exception e){
            log.error("parseFilter has  error !,filter={}",JSONUtil.toJsonStr(filter));
        }
    }

}
