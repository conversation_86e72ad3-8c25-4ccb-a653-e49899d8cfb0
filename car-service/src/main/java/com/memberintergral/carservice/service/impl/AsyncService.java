package com.memberintergral.carservice.service.impl;

import cn.hutool.core.date.DateUtil;
import com.memberintergral.carservice.config.redis.RedisKey;
import com.memberintergral.carservice.config.redis.added.RedisDAO;
import com.memberintergral.carservice.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;
@Slf4j
@Service
public class AsyncService {

    @Async("asyncThreadPoolTask")
    public void loginStatis(long uid,long virtualUid){
        try {
            if (uid<1 && virtualUid<1){
                log.info("loginStatis error params: uid={},vid={}",uid,virtualUid);
                return;
            }
            Date currDate=new Date();
            String today= DateUtil.format(currDate,"yyyy-MM-dd");
            int cacheTime=7* 86400;
            //近7天用户[登录、未登录均包含]累计进入会员中心次数
            String uidKey=String.format(RedisKey.CAR_CENTER_UID_ENTER_TIMES_MAP,uid);
            //近7天用户[登录、未登录均包含]累计进入会员中心次数
            String vidKey=String.format(RedisKey.CAR_CENTER_VID_ENTER_TIMES_MAP, virtualUid);

            long day7BeforOfDateLong=DateUtils.getPassNDate(currDate);
            //开始检查历史数据中的过期数据
            if (uid>0){
                Map<String,String> uidMap=RedisDAO.getInstance().hgetall(uidKey);
                if (uidMap!=null && !uidMap.isEmpty()){
                    for (Map.Entry<String,String> entry:uidMap.entrySet()){
                        String day=entry.getKey();
                        if (DateUtils.checkDate(day,day7BeforOfDateLong)){
                            log.info("loginStatis remove uid day uid={},day={}",uid,day);
                            RedisDAO.getInstance().hdel(uidKey,day);
                        }
                    }
                }
            }
            if (virtualUid>0){
                Map<String,String> vidMap=RedisDAO.getInstance().hgetall(vidKey);
                if (vidMap!=null && !vidMap.isEmpty()){
                    for (Map.Entry<String,String> entry:vidMap.entrySet()){
                        String day=entry.getKey();
                        if (DateUtils.checkDate(day,day7BeforOfDateLong)){
                            log.info("loginStatis remove vid day: vid={},day={}",virtualUid,day);
                            RedisDAO.getInstance().hdel(vidKey,day);
                        }
                    }
                }
            }
            if (uid>0){
                //已登录的账户把vid的数据同步给uid
                Map<String,String> vidMap=RedisDAO.getInstance().hgetall(vidKey);
                if (vidMap!=null && !vidMap.isEmpty()){
                    for (Map.Entry<String,String> entry:vidMap.entrySet()){
                        log.info("loginStatis move viddata: vid={},data={}",entry.getKey(), entry.getValue());
                        //vidCount+=NumberUtils.toInt(entry.getValue());
                        RedisDAO.getInstance().hincrBy(uidKey,entry.getKey(),NumberUtils.toLong(entry.getValue()),0);
                    }
                }
                RedisDAO.getInstance().hincrBy(uidKey,today,1,cacheTime);
                RedisDAO.delKey(vidKey);
            }else{
                //虚拟账户
                RedisDAO.getInstance().hincrBy(vidKey,today,1,cacheTime);
            }

        } catch (Exception e) {
            log.error("loginStatis error",e);
        }
    }



}
