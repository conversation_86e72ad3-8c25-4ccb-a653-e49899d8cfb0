<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.memberintergral.carservice.mapper.VehicleProductMapper">
    <resultMap id="BaseResultMap" type="com.memberintergral.carservice.domain.entity.VehicleProduct">
        <id column="ID" property="id" jdbcType="BIGINT"/>
        <result column="USER_ID" property="userId" jdbcType="BIGINT"/>
        <result column="PRICE" property="price" jdbcType="DOUBLE"/>
        <result column="PRODUCT_ID" property="productId" jdbcType="VARCHAR"/>
        <result column="PRODUCT_TYPE_ID" property="productTypeId" jdbcType="BIGINT"/>
        <result column="ORDER_ID" property="orderId" jdbcType="BIGINT"/>
        <result column="BUY_DATE" property="buyDate" jdbcType="TIMESTAMP"/>
        <result column="START_DATE" property="startDate" jdbcType="TIMESTAMP"/>
        <result column="EXPIRE_DATE" property="expireDate" jdbcType="TIMESTAMP"/>
        <result column="DURATION" property="duration" jdbcType="SMALLINT"/>
        <result column="CNT" property="cnt" jdbcType="SMALLINT"/>
        <result column="STATUS" property="status" jdbcType="TINYINT"/>
        <result column="VIRTUAL_UID" property="virtualUid" jdbcType="BIGINT"/>
        <result column="MERGE_DATE" property="mergeDate" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
        ID, USER_ID, PRICE, PRODUCT_ID, PRODUCT_TYPE_ID, ORDER_ID, BUY_DATE, START_DATE, EXPIRE_DATE,DURATION,CNT,
        STATUS,VIRTUAL_UID,MERGE_DATE
    </sql>

    <!-- 批量插入订单 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO
        `VEHICLE_PRODUCT`
        (USER_ID, PRICE, PRODUCT_ID, PRODUCT_TYPE_ID, ORDER_ID, BUY_DATE, START_DATE, EXPIRE_DATE,DURATION,CNT,
        STATUS,VIRTUAL_UID,MERGE_DATE)
        VALUES
        <foreach collection="list" item="product" index="index" separator=",">
            (#{product.userId},#{product.price}, #{product.productId}, #{product.productTypeId},
            #{product.orderId}, #{product.buyDate}, #{product.startDate},#{product.expireDate},
            #{product.duration},#{product.cnt},#{product.status},#{product.virtualUid},#{product.mergeDate});
        </foreach>

    </insert>

    <select id="getAllExpire" resultType="com.memberintergral.carservice.domain.entity.QueryExpireRes">
        SELECT
        max(EXPIRE_DATE) AS expire,
        PRODUCT_TYPE_ID AS productType
        FROM `VEHICLE_PRODUCT`
        WHERE
        <choose>
            <when test="uid != null and uid >0">
                USER_ID = #{uid}
            </when>
            <otherwise>
                VIRTUAL_UID = #{virtualUid}
            </otherwise>
        </choose>
        AND status=1 GROUP BY PRODUCT_TYPE_ID
    </select>


    <select id="getProductsByOrderId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        `VEHICLE_PRODUCT`
        WHERE
        ORDER_ID = ${orderId}
    </select>


    <select id="getLimitByPtypeIdAndUidOrVid" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        `VEHICLE_PRODUCT` u
        WHERE
        1 = 1
        <choose>
            <when test="uid != null and uid > 0">
                and USER_ID = #{uid}
            </when>
            <otherwise>
                and VIRTUAL_UID = #{virtualUid}
            </otherwise>
        </choose>
        AND u.PRODUCT_TYPE_ID = #{productTypeId}
        AND u.STATUS = #{status}
        ORDER BY
        u.EXPIRE_DATE DESC
    </select>


    <update id="updateBatch">
        <foreach collection="productList" item="product" separator=";">
            UPDATE
            `VEHICLE_PRODUCT`
            SET
            USER_ID = #{product.userId},
            PRICE = #{product.price},
            PRODUCT_ID=#{product.productId},
            PRODUCT_TYPE_ID=#{product.productTypeId},
            ORDER_ID=#{product.orderId},
            BUY_DATE=#{product.buyDate},
            START_DATE=#{product.startDate},
            EXPIRE_DATE=#{product.expireDate},
            DURATION=#{product.duration},
            CNT= #{product.cnt},
            STATUS=#{product.status},
            VIRTUAL_UID = #{product.virtualUid},
            MERGE_DATE = #{product.mergeDate}
            WHERE
            id=#{product.id}
        </foreach>
    </update>


    <select id="getUserProductByTime" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        `VEHICLE_PRODUCT`
        WHERE
        USER_ID= #{userId}
        AND
        EXPIRE_DATE >= #{expireDate}
        AND
        STATUS = #{status}
        ORDER BY
        EXPIRE_DATE ASC
    </select>

    <!--获取虚拟用户购买的所有产品-->
    <select id="getProductListByVid" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        `VEHICLE_PRODUCT`
        WHERE VIRTUAL_UID = #{virtualUid}
        AND
        EXPIRE_DATE &gt; #{expireDate}
        AND
        MERGE_DATE IS NULL
    </select>


    <!--获取真实用户某个类型的产品 最近的过期时间-->
    <select id="getLimitByPtypeId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        `VEHICLE_PRODUCT`
        WHERE
        USER_ID= #{userId}
        AND
        PRODUCT_TYPE_ID = #{productTypeId}
        AND
        STATUS = #{status}
        ORDER BY
        EXPIRE_DATE
        DESC
    </select>


    <update id="updateUserProductForMergerVirtualUser">
        UPDATE
        `VEHICLE_PRODUCT`
        SET
        USER_ID=#{userId},
        MERGE_DATE=#{mergeDate}
        WHERE
        VIRTUAL_UID = #{virtualUid}
    </update>


    <select id="getMaxExpireOrderId" resultType="java.lang.Long">
        SELECT
        order_id
        FROM
        `VEHICLE_PRODUCT`
        WHERE
        <choose>
            <when test="userId != 0">
                user_id = #{userId}
            </when>
            <when test="userId == 0">
                virtual_uid = #{pid}
            </when>
        </choose>
        <choose>
            <when test="productTypeId == 17">
                and product_type_id = 17
            </when>
            <when test="productTypeId == 34">
                and product_type_id = 34
            </when>
            <when test="productTypeId != 12">
                and product_type_id in (17)
            </when>
        </choose>
        AND
        STATUS=1
        ORDER BY
        expire_date
        desc
    </select>

    <select id="getVipmByOrderId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from `VEHICLE_PRODUCT` u
        where
        u.ORDER_ID = #{orderId}
        and u.STATUS=1
        and (u.PRODUCT_TYPE_ID=1 or u.PRODUCT_TYPE_ID=2)
        order by u.EXPIRE_DATE
        desc
    </select>

    <select id="getProductsByVid" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from `VEHICLE_PRODUCT` u
        where
        u.VIRTUAL_UID= #{virtualUid}
        and u.EXPIRE_DATE >= #{expireDate}
        and u.STATUS = #{status}
        order by u.EXPIRE_DATE ASC
    </select>

    <select id="selectByPageAndSelections" resultType="com.memberintergral.carservice.domain.entity.ResVehicleProduct">
        SELECT
        p.ID AS id,
        p.USER_ID AS userId,
        p.PRICE AS price,
        p.PRODUCT_ID AS productId,
        p.PRODUCT_TYPE_ID AS productTypeId,
        p.ORDER_ID AS orderId,
        p.BUY_DATE AS buyDate,
        p.START_DATE AS startDate,
        p.EXPIRE_DATE AS expireDate,
        p.DURATION AS duration,
        p.CNT AS cnt,
        p. STATUS AS STATUS,
        p.VIRTUAL_UID AS virtualUid,
        p.MERGE_DATE AS mergeDate,
        o.auto_pay AS desc3,
        o.TIME AS remainingDays,
        date_format(
        p.BUY_DATE,
        '%Y-%m-%d %H:%m'
        ) AS buyDateStr,
        date_format(
        p.START_DATE,
        '%Y-%m-%d %H:%m'
        ) AS startDateStr,
        date_format(
        p.EXPIRE_DATE,
        '%Y-%m-%d %H:%m'
        ) AS expireDateStr,
        o.PAY_SRC as paySrc
        FROM
        `VEHICLE_PRODUCT` p,
        `VEHICLE_ORDER` o
        WHERE
        p.ORDER_ID = o.ID
        AND p. STATUS in (1,2)
        AND p.USER_ID = #{uid}
        ORDER BY
        p.ORDER_ID DESC
    </select>


    <select id="selectByPageAndSelectionsVid" resultType="com.memberintergral.carservice.domain.entity.ResVehicleProduct">
        SELECT
        p.ID AS id,
        p.USER_ID AS userId,
        p.PRICE AS price,
        p.PRODUCT_ID AS productId,
        p.PRODUCT_TYPE_ID AS productTypeId,
        p.ORDER_ID AS orderId,
        p.BUY_DATE AS buyDate,
        p.START_DATE AS startDate,
        p.EXPIRE_DATE AS expireDate,
        p.DURATION AS duration,
        p.CNT AS cnt,
        p. STATUS AS STATUS,
        p.VIRTUAL_UID AS virtualUid,
        p.MERGE_DATE AS mergeDate,
        o.auto_pay AS desc3,
        o.TIME AS remainingDays,
        date_format(
        p.BUY_DATE,
        '%Y-%m-%d %H:%m'
        ) AS buyDateStr,
        date_format(
        p.START_DATE,
        '%Y-%m-%d %H:%m'
        ) AS startDateStr,
        date_format(
        p.EXPIRE_DATE,
        '%Y-%m-%d %H:%m'
        ) AS expireDateStr,
        o.PAY_SRC as paySrc
        FROM
        `VEHICLE_PRODUCT` p,
        `VEHICLE_ORDER` o
        WHERE
        p.ORDER_ID = o.ID
        AND p. STATUS in (1,2)
        AND p.VIRTUAL_UID = #{vid}
        ORDER BY
        p.ORDER_ID DESC
    </select>

    <select id="getUserProductsByUidAndProductId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from `VEHICLE_PRODUCT`
        where
        USER_ID=#{uid}
        and STATUS =#{status}
        and EXPIRE_DATE >= #{nowDate}
        and PRODUCT_ID = #{productId}

    </select>

    <select id="getUserProductsByVirtualUidAndProductId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from `VEHICLE_PRODUCT`
        where
        VIRTUAL_UID=#{vid}
        and STATUS =#{status}
        and EXPIRE_DATE >= #{nowDate}
        and PRODUCT_ID = #{productId}
        and MERGEDATE IS NULL
    </select>

    <select id="getVehicleVIPExpireDate" resultType="java.util.Date">
        SELECT max(EXPIRE_DATE) AS expire FROM `VEHICLE_PRODUCT` where user_id=#{uid} and status=1 and
        PRODUCT_TYPE_ID=17;
    </select>

    <select id="getAllCarExpire" resultType="com.memberintergral.carservice.domain.entity.QueryExpireRes">
        SELECT
        EXPIRE_DATE AS expire,
        PRODUCT_TYPE_ID AS productType
        FROM `VEHICLE_PRODUCT`
        WHERE
        <choose>
            <when test="uid != null and uid >0">
                USER_ID = #{uid}
            </when>
            <otherwise>
                VIRTUAL_UID = #{virtualUid}
            </otherwise>
        </choose>
        AND status=1 and  PRODUCT_TYPE_ID in (17,28)
        order by EXPIRE_DATE desc limit 1
    </select>

    <select id="updateVehicleExpireDate" >
        UPDATE
        `VEHICLE_PRODUCT`
        SET
        `EXPIRE_DATE` = #{expireDate},
        `DURATION` = #{duration}
        WHERE
        ID = #{id}
    </select>

    <select id="updateUserExpireDate" >
        UPDATE
        `USER_PRODUCT`
        SET
        `EXPIRE_DATE` = #{expireDate},
        `DURATION` = #{duration}
        WHERE
        ID = #{id}
    </select>


</mapper>