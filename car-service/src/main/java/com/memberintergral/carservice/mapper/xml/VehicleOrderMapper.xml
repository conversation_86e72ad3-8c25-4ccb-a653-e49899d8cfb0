<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.memberintergral.carservice.mapper.VehicleOrderMapper">
    <resultMap id="BaseResultMap" type="com.memberintergral.carservice.domain.entity.VehicleOrder">

        <id column="ID" property="id" jdbcType="BIGINT"/>
        <result column="USER_ID" property="userId" jdbcType="BIGINT"/>
        <result column="PID" property="pid" jdbcType="BIGINT"/>
        <result column="CREDIT" property="credit" jdbcType="DOUBLE"/>
        <result column="TIME" property="time" jdbcType="TIMESTAMP"/>
        <result column="PAY_DATE" property="payDate" jdbcType="TIMESTAMP"/>
        <result column="PAY_TYPE" property="payType" jdbcType="TINYINT"/>
        <result column="STATUS" property="status" jdbcType="TINYINT"/>
        <result column="PLATFORM" property="platform" jdbcType="VARCHAR"/>
        <result column="SRC" property="src" jdbcType="VARCHAR"/>
        <result column="CLIENT_ACT" property="clientAct" jdbcType="VARCHAR"/>
        <result column="auto_pay" property="autoPay" jdbcType="VARCHAR"/>
        <result column="PAY_SRC" property="paySrc" jdbcType="VARCHAR"/>
        <result column="THIRD_ORDERID" property="thirdOrderId" jdbcType="VARCHAR"/>
        <result column="merge_date" property="mergeDate" jdbcType="TIMESTAMP"/>
        <result column="plat_version" property="platVersion" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="product_type" property="productType" jdbcType="VARCHAR"/>

    </resultMap>
    <sql id="Base_Column_List">
        ID,USER_ID, PID, CREDIT, TIME, PAY_DATE, PAY_TYPE, STATUS,PLATFORM,SRC,
        CLIENT_ACT,auto_pay,PAY_SRC,THIRD_ORDERID,merge_date,plat_version,type,product_type
    </sql>
    <select id="getInfoById" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from VEHICLE_ORDER
        where id = #{id}
    </select>


    <!-- 插入订单 -->
    <insert id="insert" parameterType="com.memberintergral.carservice.domain.entity.VehicleOrder" useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO
        `VEHICLE_ORDER`
        (`USER_ID`,`PID`,`CREDIT`, `TIME`, `PAY_DATE`, `PAY_TYPE`, `STATUS`,
        `PLATFORM`, `SRC`, `CLIENT_ACT`, `auto_pay`, `PAY_SRC`, `THIRD_ORDERID`, `merge_date`,
        `plat_version`,`type`,`product_type`)
        VALUES
        (#{userId},#{pid}, #{credit}, #{time}, #{payDate}, #{payType}, #{status},
        #{platform},#{src},#{clientAct},#{autoPay},#{paySrc},
        #{thirdOrderId},#{mergeDate},#{platVersion},#{type},#{productType});
    </insert>


    <select id="getAutoPay" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        `VEHICLE_ORDER`
        WHERE
        PID = #{pid}
        and (status = 1 or status = 4) and auto_pay='yes'
        order by id desc limit 1
    </select>
    <select id="getLastAutoPayOrder" resultMap="BaseResultMap">
        SELECT DISTINCT
        OI.ID id,
        OI.USER_ID uid,
        OI.PAY_TYPE payType,
        OI.auto_pay autoPay
        FROM
        `VEHICLE_ORDER` OI
        LEFT JOIN
        `VEHICLE_PRODUCT` UP
        ON OI.ID = UP.ORDER_ID
        WHERE
        OI.USER_ID = #{uid}
        AND OI.auto_pay='yes'
        AND OI.STATUS=1
        AND UP.PRODUCT_TYPE_ID=17
        ORDER BY
        OI.ID DESC limit 1

    </select>

    <update id="dissolution">
        UPDATE
        `VEHICLE_ORDER`
        SET
        `auto_pay` = `cancel`
        WHERE
        ID = #{id}

    </update>

    <select id="getOrderById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"></include>
        FROM
        `VEHICLE_ORDER`
        WHERE
        id = #{id}
    </select>

    <update id="updateOrder" parameterType="com.memberintergral.carservice.domain.entity.VehicleOrder">
        UPDATE
        `VEHICLE_ORDER`
        SET
        PAY_DATE = #{payDate},
        THIRD_ORDERID = #{thirdOrderId},
        STATUS = #{status}
        WHERE
        ID = #{id}
    </update>

    <update id="autoPayDone">
        UPDATE
        `VEHICLE_ORDER`
        Set
        auto_pay='done'
        Where
        id=#{id}
        AND
        (status = 1 or status=4)
        AND auto_pay = 'yes'
    </update>

    <!--将虚拟用户下的订单更新为正式用户的订单-->
    <update id="updateVidOrderToUidOrder">
        UPDATE
        `VEHICLE_ORDER`
        SET
        USER_ID=#{uid},
        merge_date=#{mergeDate}
        WHERE
        PID = #{virtualUid}
        AND
        USER_ID = 0;
    </update>


    <select id="selectOrderProductByPidAndLimitDay" resultType="com.memberintergral.carservice.domain.entity.UnionOrderProduct">
        SELECT DISTINCT
        OI.USER_ID uid,
        OI.pid pid,
        OI.PAY_TYPE payType,
        OI.PLATFORM platForm,
        UP.ORDER_ID orderId,
        UP.product_type_id productTypeId,
        UP.CNT cnt,
        UP.DURATION duration,
        OI.credit,
        UP.EXPIRE_DATE exDate,
        TO_DAYS(UP.EXPIRE_DATE) - TO_DAYS(now()) subdays,
        OI.src,
        OI.pay_src paySrc,
        OI.plat_version platVersion,
        OI.product_type productType
        FROM
        `VEHICLE_ORDER` OI
        LEFT JOIN
        `VEHICLE_PRODUCT` UP
        ON OI.ID = UP.ORDER_ID
        WHERE
        OI.PID = #{pid}
        AND OI.auto_pay='yes'
        AND (OI.STATUS=1 OR OI.STATUS=4)
        AND
        TO_DAYS(UP.EXPIRE_DATE) - TO_DAYS(now()) BETWEEN -10 AND #{limitDay}
        ORDER BY
        UP.product_type_id
    </select>


    <select id="getAfterMergePid" resultType="com.memberintergral.carservice.domain.entity.AnswerRes">
        SELECT
        pid,
        pay_type payType,
        ID orderId,
        CREDIT credit
        FROM
        `VEHICLE_ORDER`
        WHERE
        <choose>
            <when test="userId != 0">
                user_id = #{userId}
            </when>
            <otherwise>
                pid = #{pid}
            </otherwise>
        </choose>
        AND
        status = 1
        AND
        auto_pay IN ('yes','done','transfer')
        AND
        find_in_set(pay_type,#{array})
        ORDER BY
        merge_date,pay_date
        DESC

    </select>

    <update id="updateAutoPay">
        UPDATE
        `VEHICLE_ORDER`
        SET
        auto_pay=#{autoPay}
        WHERE
        id=#{orderId}
        AND
        (status=1 or status =4)
    </update>

    <select id="getOrderInfoListByPidAndSrcAndClientAct" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        `VEHICLE_ORDER`
        WHERE
        PID=#{pid}
        AND
        SRC = #{src}
        AND
        CLIENT_ACT= #{clientAct}
    </select>


    <select id="getAutoPlatForm" resultMap="BaseResultMap">
        SELECT
        platform
        FROM
        `VEHICLE_ORDER`
        WHERE
        pid = #{pid}
        AND
        status = 1
        AND
        find_in_set(pay_type,#{autoPayType})
        ORDER BY
        pay_date
        DESC
        limit 1
    </select>


    <select id="queryByUserAuto" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        `VEHICLE_ORDER`
        WHERE
        <choose>
            <when test="uid!=0">
                USER_ID = #{uid}
            </when>
            <otherwise>
                pid = #{pid}
            </otherwise>
        </choose>
        AND
        (status = 1 OR status = 4)
        AND auto_pay='yes'
    </select>


    <select id="getUnsignPidAndPayType" resultType="com.memberintergral.carservice.domain.entity.AnswerRes">
        SELECT
        pid,
        pay_type payType
        FROM
        `VEHICLE_ORDER`
        WHERE
        <choose>
            <when test="uid!=0">
                USER_ID = #{uid}
            </when>
            <otherwise>
                pid = #{pid}
            </otherwise>
        </choose>
        AND
        status = 1
        AND auto_pay IN('yes','done','transfer')
        AND find_in_set(pay_type,#{renewPayType})
        ORDER BY merge_date,pay_date DESC
    </select>


    <update id="updateCancel">
        UPDATE
        `VEHICLE_ORDER`
        Set auto_pay='cancel'
        Where
        <choose>
            <when test="uid!=0">
                USER_ID = #{uid}
            </when>
            <otherwise>
                pid = #{pid}
            </otherwise>
        </choose>
        AND (status = 1 or status = 4)
        AND auto_pay = 'yes'
    </update>


    <update id="updateCancelByPid">
        UPDATE
        `VEHICLE_ORDER`
        SET auto_pay='cancel'
        WHERE
        pid = #{pid}
        AND (status = 1 OR status = 4)
        AND auto_pay = 'yes'
    </update>

    <select id="getAutoPayByUserId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        `VEHICLE_ORDER`
        WHERE
        USER_ID = #{pid}
        and (status = 1 or status = 4) and auto_pay='yes'
        order by id desc limit 1

    </select>


    <!--批量更新-->
    <update id="updateBatch" parameterType="java.util.List">
        update `VEHICLE_ORDER`
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="STATUS =case" suffix="end,">
                <foreach collection="vosList" item="item" index="index">
                    when id=#{item.id} then #{item.status}
                </foreach>
            </trim>
            <trim prefix="SRC =case" suffix="end,">
                <foreach collection="vosList" item="item" index="index">
                    when id=#{item.id} then #{item.src}
                </foreach>
            </trim>
            <trim prefix="CLIENT_ACT =case" suffix="end,">
                <foreach collection="vosList" item="item" index="index">
                    when id=#{item.id} then #{item.clientAct}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="vosList" index="index" item="item" separator="," open="(" close=")">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>


    <select id="getAllOrderStatusByUserId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        `VEHICLE_ORDER`
        WHERE
        pid = #{uid}
    </select>

    <select id="getVehicleByUserIdSrc" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        `VEHICLE_ORDER`
        WHERE
        <choose>
            <when test="uid!=0">
                USER_ID = #{uid}
            </when>
            <otherwise>
                pid = #{pid}
            </otherwise>
        </choose>
        and src = #{src}
        and status = 1
    </select>


    <!--计算某个渠道日期内购买的总月数-->
    <select id="getSumMonthCnt" resultType="java.lang.Integer">
        SELECT
        SUM(P.CNT)
        FROM
        VEHICLE_ORDER O,
        VEHICLE_PRODUCT P
        WHERE
        O.ID = P.ORDER_ID
        AND O.PAY_SRC = #{paySrc}
        AND O.TIME &gt; #{startDate}
        AND O.TIME &lt; #{endDate}
        AND O. STATUS = 1
    </select>

    <select id="queryUserDirectChargeCount" resultType="java.lang.Integer">
        select IFNULL(count(ID), 0)
        from VEHICLE_ORDER
        <where>
            PAY_SRC = #{channel}
            and STATUS = 4
            and USER_ID = #{uid}
            and SRC =#{src}
        </where>
    </select>


    <select id="searchOrderExchangeCount" resultType="java.lang.Integer">
        select IFNULL(count(O.ID), 0)
        from VEHICLE_ORDER O, VEHICLE_PRODUCT P
        <where>
            O.ID = P.ORDER_ID
            AND O.PAY_SRC = #{paySrc}
            AND O.TIME &gt; #{date}
            AND O.STATUS = 4
            AND P.CNT = #{months}
            AND O.SRC = #{src}
        </where>
    </select>

    <select id="getVehicleOrder" resultType="com.memberintergral.carservice.domain.entity.VehicleOrder">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        `VEHICLE_ORDER`
        <where>
            USER_ID = #{uid}
            and SRC = #{src}
            limit 1
        </where>

    </select>

    <select id="getLastRenewalOrder" resultType="com.memberintergral.carservice.domain.entity.VehicleOrder">
        SELECT
        ID,USER_ID, PID, CREDIT, TIME, PAY_DATE, PAY_TYPE, STATUS,PLATFORM,SRC,
        CLIENT_ACT clientAct,auto_pay,PAY_SRC paySrc,THIRD_ORDERID,merge_date,plat_version platVersion,type,product_type
        FROM
        `VEHICLE_ORDER`
        <where>
            PID = #{pid}
            and ID = #{lastOrderId}
            and STATUS = 1
        </where>
    </select>

    <select id="getChannelCount" resultType="java.lang.Long">
        SELECT IFNULL(count(*), 0) FROM
        `VEHICLE_ORDER`
        <where>
            SRC = #{src}
            and PAY_SRC = #{paySrc}
            and STATUS='4'
            and TIME &gt; '2022-12-09'
        </where>
    </select>

    <update id="updateOrderAutoPayById">
        UPDATE
        `VEHICLE_ORDER`
        SET
        `auto_pay` = #{autoPay}
        WHERE
        ID = #{id}
    </update>

    <update id="updateOrderAutoPayPayTypeById">
        UPDATE
        `VEHICLE_ORDER`
        SET
        `PAY_TYPE` =#{payType},
        `AUTO_PAY` =#{autoPay}
        WHERE
        ID = #{id}
    </update>

    <select id="getVehicleAutoPay" resultType="java.lang.Integer">
        SELECT
        count(ID)
        FROM
        `VEHICLE_ORDER`
        WHERE
        USER_ID = #{uid}
        and (status = 1 or status = 4) and auto_pay='yes'
    </select>

    <select id="getSVWChargeCount" resultType="java.lang.Integer">
        SELECT count(v.ID) FROM `VEHICLE_ORDER` as v left join `VEHICLE_PRODUCT` as p on v.id=p.order_id where
        v.user_id=#{uid} and v.status=4 and p.cnt=#{month} and v.pay_src='yiqidazhong_zengsong';
    </select>

    <update id="updateOrderPayTypeById">
        UPDATE
            `VEHICLE_ORDER`
        SET
            `PAY_TYPE` = #{payType}
        WHERE
            ID = #{orderId}
    </update>

    <select id="queryRenewalByUserAuto" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        `VEHICLE_ORDER`
        WHERE pid = #{pid}
        and AUTO_PAY = 'yes'
        and CLIENT_ACT = 'autoTask'
        and PAY_TYPE= 122
    </select>
</mapper>