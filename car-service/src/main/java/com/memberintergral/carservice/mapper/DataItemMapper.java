package com.memberintergral.carservice.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.memberintergral.carservice.domain.entity.DataItem;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-24
 */
public interface DataItemMapper extends BaseMapper<DataItem> {

    /**
     * 根据code查询dataItem
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2020/6/8 10:33
     */
    List<DataItem> getVehicleDataItemByMid(@Param("mid") Integer mid);

    /**
     * 根据code查询dataItem
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2020/6/8 10:33
     */
    List<DataItem> getVehicleDataItemByActivityCode(@Param("code") String code);

    /**
     * 根据code查询dataItem
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2020/6/8 10:33
     */
    List<DataItem> getDataItemByCode(Map<String, Object> data);


    /**
     * 根据info查询dataItem
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2020/6/8 10:33
     */
    List<DataItem> getDataItemByInfo(@Param("info") String info);
}
