package com.memberintergral.carservice.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.memberintergral.carservice.domain.entity.VehicleCoupon;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-24
 */
public interface VehicleCouponMapper extends BaseMapper<VehicleCoupon> {
    List<VehicleCoupon> getVehicleCouponsByUid(@Param("uid") Long uid, @Param("virtualUid") Long  virtualUid);

    List<VehicleCoupon> getVehicleCouponsByDeviceId(@Param("deviceId") String  deviceId);

    int insert(VehicleCoupon vehicleCoupon);

    Long updateVehicleCouponsBySerialKey(@Param("serialKey") String serialKey, @Param("used") Integer used,@Param("orderId") Long orderId);

    Long updateVehicleCouponsById( @Param("uid") Long uid,  @Param("vehicleCouponIds") List<Long> vehicleCouponIds);

    Long updateVRVehicleCouponsById( @Param("virtualUid") Long virtualUid,  @Param("vehicleCouponIds") List<Long> vehicleCouponIds);

    VehicleCoupon getVehicleCouponsBySerialKey(@Param("serialKey") String serialKey , @Param("uid") Long uid,@Param("virtualUid") Long virtualUid);
}
