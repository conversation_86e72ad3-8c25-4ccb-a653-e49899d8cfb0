<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.memberintergral.carservice.mapper.VehicleOrderExtendMapper">

    <!-- 插入order extend -->
    <insert id="insert" parameterType="com.memberintergral.carservice.domain.entity.VehicleOrderExtend" useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO
        `VEHICLE_ORDER_EXTEND`
        (`ORDER_ID`,`VIN_CODE`,`DEVELOPER_PAYLOAD`, `EXT1`, `EXT2`, `CREATE_TIME`, `UPDATE_TIME`)
        VALUES
        (#{orderId},#{vinCode}, #{developerPayload}, #{ext1}, #{ext2}, #{createTime}, #{updateTime});
    </insert>

    <select id="getVehicleOrderExtendByOid" resultType="com.memberintergral.carservice.domain.entity.VehicleOrderExtend">
        select
        ID id,
        ORDER_ID orderId,
        VIN_CODE vinCode,
        DEVELOPER_PAYLOAD developerPayload,
        EXT1 ext1,
        EXT2 ext2,
        CREATE_TIME createTime,
        UPDATE_TIME updateTime
        from VEHICLE_ORDER_EXTEND
        <where>
            ORDER_ID = #{orderId}
        </where>
        limit 1
    </select>


</mapper>