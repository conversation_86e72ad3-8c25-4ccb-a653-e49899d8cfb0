<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.memberintergral.carservice.mapper.TBatchExchangeCodeMapper">
    <resultMap id="BaseResultMap" type="com.memberintergral.carservice.domain.entity.BatchExchageCode">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="channel" property="channel" jdbcType="VARCHAR"/>
        <result column="count" property="count" jdbcType="INTEGER"/>
        <result column="valid_begin" property="validBegin" jdbcType="TIMESTAMP"/>
        <result column="valid_end" property="validEnd" jdbcType="TIMESTAMP"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="is_valid" property="isValid" jdbcType="INTEGER"/>
        <result column="exchange_name" property="exchangeName" jdbcType="VARCHAR"/>
        <result column="exchanged_product_valid_days" property="exchangedProductValidDays" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="mark" property="mark" jdbcType="VARCHAR"/>
        <result column="alert_time" property="alertTime" jdbcType="VARCHAR"/>
        <result column="device_tail" property="deviceTail" jdbcType="VARCHAR"/>
        <result column="function_type" property="functionType" jdbcType="INTEGER"/>
        <result column="platform" property="platform" jdbcType="VARCHAR"/>
        <result column="surface_type" property="surfaceType" jdbcType="INTEGER"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,channel, count, valid_begin, valid_end, type, is_valid, exchange_name,
        exchanged_product_valid_days,create_time,mark,
        alert_time,device_tail,function_type,platform,surface_type,update_time
    </sql>

    <select id="getBatCodeByChanel" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        t_batch_exchange_code
        WHERE
        channel = #{channel}
    </select>


</mapper>