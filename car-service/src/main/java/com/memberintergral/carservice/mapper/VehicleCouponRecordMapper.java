package com.memberintergral.carservice.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.memberintergral.carservice.domain.entity.VehicleCouponRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-24
 */
public interface VehicleCouponRecordMapper extends BaseMapper<VehicleCouponRecord> {
    List<VehicleCouponRecord> getVehicleCouponRecordByUid(@Param("uid") Long uid, @Param("deviceId") String deviceId, @Param("channel")String channel, @Param("batchId")String batchId);

    List<VehicleCouponRecord> getVehicleCouponRecordByDeviceId(@Param("deviceId")String deviceId);

    Long updateVehicleCouponsRecordById( @Param("uid") Long uid,  @Param("vehicleCouponRecordIds") List<Long> vehicleCouponRecordIds);

    int insert(VehicleCouponRecord vehicleCouponRecord);
}
