package com.memberintergral.carservice.mapper;

import com.memberintergral.carservice.domain.entity.VehicleBusinessOrder;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * @program: vip_adv
 * @description: 第三方订单Mapper
 * @author: <EMAIL>
 * @create: 2018-10-16 14:59
 */
@Repository
public interface VehicleBusinessOrderMapper {
    /**
     * 插入订单
     *
     * @param vehicleBusinessOrder
     * @return
     */
    int insert(VehicleBusinessOrder vehicleBusinessOrder);


    /**
     * 通过三方订单号和渠道查询订单
     *
     * @param businessId
     * @param paySrc
     * @return
     */
    VehicleBusinessOrder getOrderById(@Param("businessId") String businessId, @Param("paySrc") String paySrc);

    /**
     * 通过订单号查询第三方订单信息
     *
     * @param orderId
     * @return com.memberintergral.carservice.domain.entity.VehicleBusinessOrder
     * <AUTHOR>
     * @date 2018/12/19 16:13
     */
    VehicleBusinessOrder getBusinessOrderByOid(@Param("orderId") Long orderId);


    /**
     * 更新设置三方订单表
     *
     * @param businessOrder
     * @return void
     * <AUTHOR>
     * @date 2018/12/19 16:49
     */
    void updateSetBusId(VehicleBusinessOrder businessOrder);
}
