<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.memberintergral.carservice.mapper.VehicleStockMapper">
    <resultMap id="BaseResultMap" type="com.memberintergral.carservice.domain.entity.VehicleStock">

        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="UNIQUE_ACTIVITY_KEY" property="uniqueActivityKey" jdbcType="VARCHAR"/>
        <result column="TOTAL_STOCK" property="totalStock" jdbcType="INTEGER"/>
        <result column="CONSUME_STOCK" property="consumeStock" jdbcType="INTEGER"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, UNIQUE_ACTIVITY_KEY, TOTAL_STOCK, CONSUME_STOCK, CREATE_TIME, UPDATE_TIME
    </sql>

    <select id="getVehicleStockByKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from `VEHICLE_STOCK`
        where UNIQUE_ACTIVITY_KEY = #{uniqueActivityKey}
        limit 1;
    </select>

    <select id="getAllStock" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from `VEHICLE_STOCK`
    </select>

    <insert id="insert" parameterType="com.memberintergral.carservice.domain.entity.VehicleStock" useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO
        `VEHICLE_STOCK`
        (`UNIQUE_ACTIVITY_KEY`,`TOTAL_STOCK`,`CONSUME_STOCK`,`create_time`,`update_time`)
        VALUES
        (#{uniqueActivityKey},#{totalStock}, #{consumeStock},#{createTime},#{updateTime});
    </insert>

    <update id="updateVehicleStock" parameterType="com.memberintergral.carservice.domain.entity.VehicleStock">
        UPDATE
        `VEHICLE_STOCK`
        SET
        UNIQUE_ACTIVITY_KEY = #{uniqueActivityKey},
        TOTAL_STOCK = #{totalStock},
        CONSUME_STOCK = #{consumeStock},
        UPDATE_TIME = #{updateTime}
        WHERE
        ID = #{id}
    </update>
</mapper>