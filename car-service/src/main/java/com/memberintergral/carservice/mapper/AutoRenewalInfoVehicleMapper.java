package com.memberintergral.carservice.mapper;

import com.memberintergral.carservice.domain.entity.AutoRenewalInfoVehicle;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * @program: vip_adv
 * @description: 自动续费mapper
 * @author: <EMAIL>
 * @create: 2018-10-09 13:03
 */
@Repository
public interface AutoRenewalInfoVehicleMapper {

    /**
     * 查询最后一笔待通知的自动续费订单
     *
     * @param virtualUid
     * @param userId
     * @param orderId
     * @return
     */
    List<AutoRenewalInfoVehicle> getLastWaitAR(@Param("userId") long userId, @Param("virtualUid") Long virtualUid, @Param("orderId") long orderId);


    /**
     * 更新code和desc
     *
     * @param respCode
     * @param respDesc
     * @param completeTime
     * @param id
     * @return
     */
    int updateCodeAndDesc(@Param("respCode") String respCode, @Param("respDesc") String respDesc,
                          @Param("completeTime") Date completeTime, @Param("id") Long id);


    /**
     * 插入数据
     *
     * @param autoRenewalInfoVehicle
     * @return int
     * <AUTHOR>
     * @date 2018/10/23 11:02
     */
    int insert(AutoRenewalInfoVehicle autoRenewalInfoVehicle);


    /**
     * 根据订单id获取请求自动续费的次数，连续请求不计数
     *
     * @param orderId
     * @param respDesc
     * @return java.lang.Long
     * <AUTHOR>
     * @date 2018/10/23 19:27
     */
    Long getCount(@Param("orderId") long orderId, @Param("respDesc") String respDesc);


    /**
     * 获取当天自动续费请求次数
     *
     * @param uid
     * @param virtualUid
     * @param respDesc
     * @return java.util.List<com.memberintergral.carservice.domain.entity.AutoRenewalInfoVehicle>
     * <AUTHOR>
     * @date 2018/10/23 20:16
     */
    List<AutoRenewalInfoVehicle> getTodayAutoRequestCnt(@Param("uid") long uid, @Param("virtualUid") long virtualUid, @Param("respDesc") String respDesc);


    /**
     * 更新operInfo标识位信息
     * [operInfo, id]@param operIintnfo
     *
     * @param id
     * @return
     * @2018/10/23author <EMAIL>
     * @date20:35
     */
    int updateOperInfo(@Param("operInfo") String operInfo, @Param("id") long id);


    /**
     * 其它回调还未执行过，则进行更新
     *
     * @param autoRenewalInfoVehicle
     * @return int
     * <AUTHOR>
     * @date 2018/10/24 10:28
     */
    int updateHasNotyNotify(AutoRenewalInfoVehicle autoRenewalInfoVehicle);


    /**
     * 查询续费记录
     *
     * @param orderId
     * @param userId
     * @return java.lang.Long
     * <AUTHOR>
     * @date 2018/10/23 19:27
     */
    Long getAutoRenewalInfo(@Param("orderId") long orderId, @Param("userId") String userId);


}
