<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.memberintergral.carservice.mapper.VehicleNotifyMapper">
    <resultMap id="BaseResultMap" type="com.memberintergral.carservice.domain.entity.VehicleNotify">
        <id column="ID" property="id" jdbcType="BIGINT"/>
        <result column="NOTIFY_PARAMS" property="notify_params" jdbcType="VARCHAR"/>
        <result column="EXEC_NUM" property="exec_num" jdbcType="INTEGER"/>
        <result column="STATUS" property="status" jdbcType="INTEGER"/>
        <result column="NOTIFY_RESULT" property="notify_result" jdbcType="DOUBLE"/>
        <result column="CREATE_TIME" property="create_time" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_TIME" property="update_time" jdbcType="TIMESTAMP"/>
        <result column="EXT1" property="ext1" jdbcType="VARCHAR"/>
        <result column="EXT2" property="ext2" jdbcType="VARCHAR"/>
    </resultMap>


    <insert id="save" parameterType="com.memberintergral.carservice.domain.entity.VehicleNotify" useGeneratedKeys="true" keyProperty="id"
            keyColumn="ID">
        insert into VEHICLE_NOTIFY (NOTIFY_PARAMS,EXEC_NUM,STATUS,NOTIFY_RESULT,CREATE_TIME,UPDATE_TIME,EXT1,EXT2)
        values (#{notify_params},#{exec_num},#{status},#{notify_result},#{create_time},#{update_time},#{ext1},#{ext2})
    </insert>

    <update id="updateNotify">
        update VEHICLE_NOTIFY
        <set>
            <if test="notify_result != null">
                NOTIFY_RESULT = #{notify_result},
            </if>
            <if test="status != null">
                STATUS = #{status},
            </if>
            EXEC_NUM = EXEC_NUM +1 ,
            UPDATE_TIME = NOW()
        </set>
        <where>
            ID = #{id}
        </where>
    </update>

    <select id="getLastUnsuccessNotify" resultType="com.memberintergral.carservice.domain.entity.VehicleNotify">
        select
        ID id,
        NOTIFY_PARAMS notify_params,
        EXEC_NUM exec_num,
        STATUS status,
        NOTIFY_RESULT notify_result,
        CREATE_TIME create_time,
        UPDATE_TIME update_time,
        EXT1 ext1,
        EXT2 ext2
        from VEHICLE_NOTIFY
        <where>
            status = 0 and EXEC_NUM &lt;= 17 and UPDATE_TIME &gt;= #{lastTime}
        </where>
    </select>

</mapper>