package com.memberintergral.carservice.mapper;

import com.memberintergral.carservice.domain.entity.AnswerRes;
import com.memberintergral.carservice.domain.entity.UnionOrderProduct;
import com.memberintergral.carservice.domain.entity.VehicleOrder;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * @program: vip_adv
 * @description: order_info表Mapper类
 * @author: <EMAIL>
 * @create: 2018-09-26 11:58
 **/
@Repository
public interface VehicleOrderMapper {
    /**
     * 通过id查询订单
     *
     * @param id
     * @return
     */
    VehicleOrder getInfoById(Long id);


    /**
     * 插入订单
     *
     * @param vehicleOrder
     * @return
     */
    Long insert(VehicleOrder vehicleOrder);


    /**
     * 获取车载自动续费
     *
     * @param pid 用户id或者虚拟id
     * @return
     */
    VehicleOrder getAutoPay(@Param("pid") Long pid);

    /**
     * 解约
     *
     * @param id
     * @return
     */
    int dissolution(@Param(("id")) Long id);

    /**
     * 根据订单编号查询订单
     *
     * @param id
     * @return
     */
    VehicleOrder getOrderById(@Param("id") Long id);

    /**
     * 更新订单信息
     *
     * @param order
     * @return
     */
    int updateOrder(VehicleOrder order);


    /**
     * 设置自动续费状态
     *
     * @param id
     * @return
     */
    int autoPayDone(@Param("id") Long id);


    /**
     * 获取真实用户某个类型的产品 最近的过期时间
     *
     * @param virtualUid
     * @param uid
     * @param mergeDate
     * @return
     */
    int updateVidOrderToUidOrder(@Param("virtualUid") long virtualUid, @Param("uid") long uid, @Param("mergeDate") Date mergeDate);


    /**
     * 查询用户购买的自动续费的产品
     *
     * @param pid
     * @param limitDay
     * @return com.memberintergral.carservice.domain.entity.UnionOrderProduct
     * <AUTHOR>
     * @date 2018/10/22 11:02
     */
    List<UnionOrderProduct> selectOrderProductByPidAndLimitDay(@Param("pid") Long pid, @Param("limitDay") Integer limitDay);


    /**
     * 查询合并后的订单
     *
     * @param userId
     * @param pid
     * @param array
     * @return java.util.List<com.memberintergral.carservice.domain.entity.AnswerRes>
     * <AUTHOR>
     * @date 2018/10/22 17:50
     */
    List<AnswerRes> getAfterMergePid(@Param("userId") long userId, @Param("pid") long pid, @Param("array") String array);


    /**
     * 更新订单的状态
     *
     * @param autoPay
     * @param orderId
     * @return void
     * <AUTHOR>
     * @date 2018/10/23 10:36
     */
    void updateAutoPay(@Param("autoPay") String autoPay, @Param("orderId") long orderId);


    /**
     * 通过pid和src和clientAct查询用户用户订单列表
     *
     * @param pid
     * @param src
     * @param clientAct
     * @return java.util.List<com.memberintergral.carservice.domain.entity.VehicleOrder>
     * <AUTHOR>
     * @date 2018/10/23 11:21
     */
    List<VehicleOrder> getOrderInfoListByPidAndSrcAndClientAct(@Param("pid") long pid, @Param("src") String src, @Param("clientAct") String clientAct);


    /**
     * 获取自动续费用户的平台
     *
     * @param pid
     * @param autoPayType
     * @return java.util.List<com.memberintergral.carservice.domain.entity.VehicleOrder>
     * <AUTHOR>
     * @date 2018/10/23 14:57
     */
    List<VehicleOrder> getAutoPlatForm(@Param("pid") long pid, @Param("autoPayType") String autoPayType);


    /**
     * 查询用户自动续费订单
     *
     * @param uid
     * @param pid
     * @return java.util.List<com.memberintergral.carservice.domain.entity.VehicleOrder>
     * <AUTHOR>
     * @date 2018/10/23 20:36
     */
    List<VehicleOrder> queryByUserAuto(@Param("uid") long uid, @Param("pid") long pid);


    /**
     * 获取签约的id (真实,虚拟都签约了,用真实用户id,其它用签约的id)
     *
     * @param uid
     * @param pid
     * @param renewPayType
     * @return java.util.List<com.memberintergral.carservice.domain.entity.VehicleOrder>
     * <AUTHOR>
     * @date 2018/10/23 20:36
     */
    List<AnswerRes> getUnsignPidAndPayType(@Param("uid") long uid, @Param("pid") long pid, @Param("renewPayType") String renewPayType);


    /**
     * 设置解约
     *
     * @param uid
     * @param pid
     * @return
     * <AUTHOR>
     * @date
     */
    int updateCancel(@Param("uid") long uid, @Param("pid") long pid);


    /**
     * 解约,更新订单状态为cancel，pid为条件
     *
     * @param pid
     * @return int
     * <AUTHOR>
     * @date 2018/11/5 15:37
     */
    int updateCancelByPid(@Param("pid") long pid);


    /**
     * 根据userid查询用户自动续费等信息
     *
     * @return
     */
    VehicleOrder getAutoPayByUserId(@Param("pid") long pid);


    /**
     * 批量更新订单表
     *
     * @param vosList
     * @return void
     * <AUTHOR>
     * @date 2018/12/18 10:46
     */
    void updateBatch(@Param("vosList") List<VehicleOrder> vosList);


    /**
     * 根据用户id查询用户所有订单
     *
     * @param uid
     * @return java.util.List<com.memberintergral.carservice.domain.entity.VehicleOrder>
     * <AUTHOR>
     * @date 2018/12/18 21:08
     */
    List<VehicleOrder> getAllOrderStatusByUserId(@Param("uid") Long uid);


    /**
     * 计算某个渠道某段时间购买的月数总和
     *
     * @param [paySrc, startDate, endDate]
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 2019/7/24 11:47
     */
    Integer getSumMonthCnt(@Param("paySrc") String paySrc, @Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 查询用户直充购买次数
     *
     * @param uid
     * @param channel
     * @param src
     * @return
     */
    int queryUserDirectChargeCount(@Param("uid") String uid, @Param("channel") String channel, @Param("src") String src);

    /**
     * 查询用户兑换码兑换的总数
     *
     * @param channel
     * @param months
     * @param src
     * @param date
     * @return
     */
    int searchOrderExchangeCount(@Param("paySrc") String channel, @Param("months") int months, @Param("src") String src, @Param("date") String date);


    VehicleOrder getVehicleOrder(@Param("src") String src, @Param("uid") String uid);

    /**
     * 查询当前续费的上一笔订单
     *
     * @param pid
     * @param lastOrderId
     * @return
     */
    VehicleOrder getLastRenewalOrder(@Param("pid") Long pid, @Param("lastOrderId") String lastOrderId);


    /**
     * 购买订单数
     *
     * @param
     * @return
     */
    int getVehicleDouble11YesOrderCount(@Param("src") String src, @Param("paySrc") String paySrc, @Param("credit") String credit);

    /**
     * 购买订单数
     *
     * @param
     * @return
     */
    int getVehicleDouble11NoOrderCount(@Param("src") String src, @Param("paySrc") String paySrc, @Param("credit") String credit);

    /**
     * 查询赠送的总数
     *
     * @param channel
     * @param src
     * @return
     */
    long getChannelCount(@Param("paySrc") String channel, @Param("src") String src);

    /**
     * 获取上次用户购买的自动续费订单
     *
     * @param uid 用户id
     * @return 订单
     */
    VehicleOrder getLastAutoPayOrder(@Param("uid") long uid);

    /**
     * 更新auto_pay字段
     *
     * @param id 主键id
     * @return
     */
    int updateOrderAutoPayById(@Param(("autoPay")) String autoPay, @Param(("id")) Long id);

    /**
     * 更新订单PayType
     *
     * @param payType
     * @return
     */
    int updateOrderAutoPayPayTypeById(@Param(("payType")) Short payType, @Param(("autoPay")) String autoPay, @Param(("id")) Long id);

    /**
     * 判断车载用户是否为自动续费
     *
     * @param uid 用户id
     * @return
     */
    int getVehicleAutoPay(@Param("uid") long uid);

    /**
     * 大众兑换次数查询
     *
     * @param uid
     * @return
     */
    int getSVWChargeCount(@Param("uid") long uid, @Param("month") int month);

    void updateOrderPayTypeById(@Param("orderId") long  orderId,@Param("payType") int payType);

    List<VehicleOrder> queryRenewalByUserAuto(@Param("pid") long pid);

    /**
     * 通过 uid 和 src 查询用户是否购买过
     *
     * @param uid
     * @param pid
     * @param src
     * @return
     */
    List<VehicleOrder> getVehicleByUserIdSrc(@Param("uid") long uid, @Param("pid") long pid, @Param("src") String src);
}
