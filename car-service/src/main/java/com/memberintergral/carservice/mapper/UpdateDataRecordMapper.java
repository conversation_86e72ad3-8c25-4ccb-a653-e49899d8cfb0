package com.memberintergral.carservice.mapper;

import com.memberintergral.carservice.domain.entity.UpdateDataRecord;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @program: vip_adv
 * @description: 取消记录表
 * @author: <EMAIL>
 * @create: 2018-12-18 15:41
 */
@Repository
public interface UpdateDataRecordMapper {
    /**
     * 批量插入
     *
     * @param recordList
     */
    void insertBatch(List<UpdateDataRecord> recordList);
}
