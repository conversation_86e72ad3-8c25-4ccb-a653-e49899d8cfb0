package com.memberintergral.carservice.mapper;

import com.memberintergral.carservice.domain.entity.VehicleNotify;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface VehicleNotifyMapper {

    public int save(VehicleNotify vehicleNotify);


    public int updateNotify(@Param("id") Long id, @Param("notify_result") String notify_result, @Param("status") int status);


    List<VehicleNotify> getLastUnsuccessNotify(@Param("lastTime") Date lastTime);
}
