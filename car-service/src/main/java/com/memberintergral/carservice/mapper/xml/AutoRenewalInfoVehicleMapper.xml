<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.memberintergral.carservice.mapper.AutoRenewalInfoVehicleMapper">
    <resultMap id="BaseResultMap" type="com.memberintergral.carservice.domain.entity.AutoRenewalInfoVehicle">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="createdTime" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="updateTime" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="completeTime" property="completeTime" jdbcType="TIMESTAMP"/>
        <result column="mtrtDays" property="mtrtDays" jdbcType="SMALLINT"/>
        <result column="operInfo" property="operInfo" jdbcType="VARCHAR"/>
        <result column="orderId" property="orderId" jdbcType="BIGINT"/>
        <result column="orgOrderId" property="orgOrderId" jdbcType="BIGINT"/>
        <result column="respCode" property="respCode" jdbcType="VARCHAR"/>
        <result column="respDesc" property="respDesc" jdbcType="VARCHAR"/>
        <result column="userId" property="userId" jdbcType="BIGINT"/>
        <result column="virtualUid" property="virtualUid" jdbcType="BIGINT"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,createdTime, updateTime, completeTime, mtrtDays, operInfo, orderId, orgOrderId,respCode,respDesc,
        userId,virtualUid
    </sql>

    <select id="getLastWaitAR" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        `AUTO_RENEWAL_INFO_VEHICLE`
        WHERE
        <choose>
            <when test="userId > 0">
                userId = #{userId}
            </when>
            <otherwise>
                virtualUid = #{virtualUid}
            </otherwise>
        </choose>
        AND
        orderId = #{orderId}
        AND
        (respDesc IS NULL OR respDesc='WAITING')
        ORDER BY createdTime DESC
    </select>

    <update id="updateCodeAndDesc">
        UPDATE
        `AUTO_RENEWAL_INFO_VEHICLE`
        SET
        respCode=#{respCode},
        respDesc =#{respDesc},
        completeTime=#{completeTime}
        WHERE id=#{id}
    </update>

    <insert id="insert" parameterType="com.memberintergral.carservice.domain.entity.AutoRenewalInfoVehicle" useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO
        `AUTO_RENEWAL_INFO_VEHICLE`
        (createdTime, completeTime, mtrtDays, operInfo, orderId, orgOrderId,respCode,respDesc,
        userId,virtualUid)
        VALUES
        (now(),#{completeTime},#{mtrtDays},#{operInfo},#{orderId},#{orgOrderId},#{respCode},
        #{respDesc},#{userId},#{virtualUid})
    </insert>

    <select id="getCount" resultType="java.lang.Long">
        SELECT
        count(*)
        FROM
        `AUTO_RENEWAL_INFO_VEHICLE`
        WHERE orderId= #{orderId}
        AND (respDesc != #{respDesc} OR respDesc IS NULL)
    </select>

    <select id="getTodayAutoRequestCnt" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        `AUTO_RENEWAL_INFO_VEHICLE`
        WHERE
        <choose>
            <when test=" uid!=0 ">
                userId=#{uid}
            </when>
            <otherwise>
                virtualUid = #{virtualUid}
            </otherwise>
        </choose>
        AND
        (respDesc != #{respDesc} OR respDesc IS NULL)
        AND TO_DAYS(createdTime)=TO_DAYS(now())
    </select>


    <update id="updateOperInfo">
        UPDATE
        `AUTO_RENEWAL_INFO_VEHICLE`
        SET operInfo=#{operInfo}
        WHERE id=#{id}
    </update>


    <update id="updateHasNotyNotify" parameterType="com.memberintergral.carservice.domain.entity.AutoRenewalInfoVehicle">
        UPDATE
        `AUTO_RENEWAL_INFO_VEHICLE`
        SET
        respCode = #{respCode},
        respDesc = #{respDesc},
        completeTime = #{completeTime},
        operInfo = #{operInfo}
        WHERE
        id = #{id}
        AND respDesc IS NULL
    </update>

    <select id="getAutoRenewalInfo" resultType="java.lang.Long">
        SELECT
            count(*)
        FROM
            `AUTO_RENEWAL_INFO_VEHICLE`
        WHERE orderId= #{orderId}
          AND  userId=#{userId}
    </select>
</mapper>