package com.memberintergral.carservice.mapper;

import com.memberintergral.carservice.domain.entity.QueryExpireRes;
import com.memberintergral.carservice.domain.entity.ResVehicleProduct;
import com.memberintergral.carservice.domain.entity.VehicleProduct;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * @program: vip_adv
 * @description: VehicleProductMapper类
 * @author: <EMAIL>
 * @create: 2018-09-29 11:34
 */
@Repository
public interface VehicleProductMapper {
    /**
     * 批量插入
     *
     * @return
     */
    int insertBatch(List<VehicleProduct> productList);


    /**
     * 查询各产品类型到期时间
     *
     * @return
     */
    List<QueryExpireRes> getAllExpire(@Param("uid") Long uid, @Param("virtualUid") Long virtualUid);

    /**
     * 通过订单号查出所有商品
     */
    List<VehicleProduct> getProductsByOrderId(@Param("orderId") Long orderId);


    /**
     * 通过uid或virtualUid和支付状态产品类型查询用户购买的产品
     *
     * @return
     */
    List<VehicleProduct> getLimitByPtypeIdAndUidOrVid(@Param("uid") Long uid, @Param("virtualUid") Long virtualUid, @Param("status") short status, @Param("productTypeId") Integer productTypeId);

    /**
     * 批量更新
     *
     * @param productList
     * @return
     */
    int updateBatch(@Param("productList") List<VehicleProduct> productList);


    /**
     * 查询用户购买过的 未过期的产品
     *
     * @param userId
     * @param expireDate
     * @param status
     * @return
     */
    List<VehicleProduct> getUserProductByTime(@Param("userId") Long userId, @Param("expireDate") Date expireDate, @Param("status") Short status);


    /**
     * 获取虚拟用户购买过的商品列表
     * 只会获取未合并的记录
     *
     * @param virtualUid
     * @param expireDate
     * @return
     */
    List<VehicleProduct> getProductListByVid(@Param("virtualUid") Long virtualUid, @Param("expireDate") Date expireDate);


    /**
     * 获取真实用户某个类型的产品 最近的过期时间
     *
     * @param userId
     * @param productTypeId
     * @param status
     * @return
     */
    List<VehicleProduct> getLimitByPtypeId(@Param("userId") long userId, @Param("productTypeId") long productTypeId, @Param("status") short status);


    /**
     * 合并用户购买产品
     *
     * @param userId
     * @param mergeDate
     * @param virtualUid
     * @return
     */
    int updateUserProductForMergerVirtualUser(@Param("userId") long userId, @Param("virtualUid") long virtualUid, @Param("mergeDate") Date mergeDate);


    /**
     * 查看该笔订单是否是用户的最后一笔到期订单
     * 合并之前用各自的,合并之后用用户id
     * 这里两者查询必须分开，续到虚拟id,合并后,后面有主动购买，用虚拟id进行查询就查不出来后续订单了   user_product表中有 虚拟id
     *
     * @param userId
     * @param pid
     * @param productTypeId
     * @return java.lang.Long
     * <AUTHOR>
     * @date 2018/10/22 18:47
     */
    List<Long> getMaxExpireOrderId(@Param("userId") long userId, @Param("pid") long pid, @Param("productTypeId") long productTypeId);


    /**
     * 通过订单id查询音乐包和vip的产品，倒序排列
     *
     * @param orderId
     * @return
     */
    List<VehicleProduct> getVipmByOrderId(@Param("orderId") long orderId);


    /**
     * 根据虚拟id查询用户购买所有产品
     *
     * @param virtualUid
     * @param expireDate
     * @param status
     * @return
     */
    List<VehicleProduct> getProductsByVid(@Param("virtualUid") Long virtualUid, @Param("expireDate") Date expireDate, @Param("status") Short status);


    /**
     * 根据用户Id和状态分页
     *
     * @param uid
     * @param status
     * @return java.util.List<com.memberintergral.carservice.domain.entity.VehicleProduct>
     * <AUTHOR>
     * @date 2018/12/17 14:12
     */
    List<ResVehicleProduct> selectByPageAndSelections(@Param("uid") Long uid, @Param("status") Short status);

    /**
     * 根据用户Id和状态分页
     *
     * @param virtualUid
     * @param status
     * @return java.util.List<com.memberintergral.carservice.domain.entity.VehicleProduct>
     * <AUTHOR>
     * @date 2018/12/17 14:12
     */
    List<ResVehicleProduct> selectByPageAndSelectionsVid(@Param("vid") Long virtualUid, @Param("status") Short status);


    /**
     * 获取真实用户购买的未过期的对应产品
     *
     * @param uid, nowDate, status, productId
     * @return java.util.List<com.memberintergral.carservice.domain.entity.VehicleProduct>
     * <AUTHOR>
     * @date 2019/8/26 16:31
     */
    List<VehicleProduct> getUserProductsByUidAndProductId(@Param("uid") Long uid, @Param("nowDate") Date nowDate, @Param("status") Short status, @Param("productId") String productId);

    /**
     * 获取虚拟用户购买的未过期的对应产品(未合并的)
     * <p>
     * [vid, nowDate, status, productId]productId
     * java.util.List<com.memberintergral.carservice.domain.entity.VehicleProduct>2019/8/26 * @return
     *
     * <AUTHOR>
     * @date21:32
     */
    List<VehicleProduct> getUserProductsByVirtualUidAndProductId(@Param("vid") Long vid, @Param("nowDate") Date nowDate, @Param("status") Short status, @Param("productId") String productId);

    /**
     * 获取车载到期时间
     *
     * @param uid 用户id
     * @return
     */
    Date getVehicleVIPExpireDate(@Param("uid") Long uid);


    /**
     * 查询各产品类型到期时间
     *
     * @return
     */
    List<QueryExpireRes> getAllCarExpire(@Param("uid") Long uid, @Param("virtualUid") Long virtualUid);

    void updateVehicleExpireDate(@Param("id") Long id,@Param("expireDate") String expireDate,@Param("duration")int duration);

    void updateUserExpireDate(@Param("id") Long id,@Param("expireDate") String expireDate,@Param("duration")int duration);
}
