package com.memberintergral.carservice.mapper;

import com.memberintergral.carservice.domain.entity.UserProduct;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * @program: vip_adv
 * @description: UserProduct Mapper接口类
 * @author: <EMAIL>
 * @create: 2019-03-05 18:42
 */
@Repository("productMapper")
public interface UserProductMapper {

    /**
     * 获取用户购买过得未过期的产品
     *
     * @param userId 用户id
     * @return java.util.List<com.memberintergral.carservice.domain.entity.UserProduct>
     * <AUTHOR>
     * @date 2019/3/5 19:03
     */
    List<UserProduct> getListByUserId(@Param("userId") Long userId);


    /**
     * 获取用户购买过得未过期的豪华vip
     *
     * @param userId 用户id
     * @return java.util.List<com.memberintergral.carservice.domain.entity.UserProduct>
     * <AUTHOR>
     * @date 2019/3/5 19:16
     */
    List<UserProduct> getLuxuryListByUserId(@Param("userId") Long userId);


    /**
     * 根据用户id查询用户未过期的大于参数天的产品(包括音乐包和豪华vip)
     *
     * @param userId 用户id
     * @param days   天数
     * @return java.util.List<com.memberintergral.carservice.domain.entity.UserProduct>
     * <AUTHOR>
     * @date 2019/3/5 20:52
     */
    List<UserProduct> getListByUserIdAndDuration(@Param("userId") Long userId, @Param("days") Short days);


    /**
     * 根据用户id查询用户未过期的大于参数天的豪华VIP产品
     *
     * @param userId 用户id
     * @param days   天数
     * @return java.util.List<com.memberintergral.carservice.domain.entity.UserProduct>
     * <AUTHOR>
     * @date 2019/3/5 21:02
     */
    List<UserProduct> getVIPListByUserIdAndDuration(@Param("userId") Long userId, @Param("days") Short days);

    /**
     * 根据用户id查询用户未过期的音乐包信息 按过期时间倒序
     *
     * @param userId 用户id
     * @return java.util.List<com.memberintergral.carservice.domain.entity.UserProduct>
     * <AUTHOR>
     * @date 2019/3/19 11:32
     */
    List<UserProduct> getMusicPackageListByUserId(@Param("userId") Long userId);


    /**
     * 查询用户购买的豪华VIP 按过期时间倒序
     *
     * @param userId 用户id
     * @return java.util.List<com.memberintergral.carservice.domain.entity.UserProduct>
     * <AUTHOR>
     * @date 2019/3/19 20:50
     */
    List<UserProduct> getLastVipListByUserId(@Param("userId") Long userId);

    /**
     * 获取用户最后购买的产品按过期时间倒序
     *
     * @param userId 用户id
     * @return java.util.List
     * <AUTHOR>
     * @date 2019/3/21 11:37
     */
    List<UserProduct> getLastInfoByUserId(@Param("userId") Long userId);


    /**
     * 查询用户购买的音乐包 按过期时间倒序
     *
     * @param userId 用户id
     * @return java.util.List<com.memberintergral.carservice.domain.entity.UserProduct>
     * <AUTHOR>
     * @date 2019/3/21 11:59
     */
    List<UserProduct> getLastPackageByUserId(@Param("userId") Long userId);

    /**
     * 通过用户id查询用户自动续费产品并按时间倒序排列
     *
     * @param orderId 订单编号
     * @return com.memberintergral.carservice.domain.entity.UserProduct
     * <AUTHOR>
     * @date 2019/3/22 14:58
     */
    UserProduct getLastAutoPayByOrderId(@Param("orderId") Long orderId);


    /**
     * 通过用户id和周期查询用户购买的相关产品列表并按到期时间倒序排雷
     *
     * @param userId 用户id
     * @param days   天数
     * @param type   类型
     * @return java.util.List<com.memberintergral.carservice.domain.entity.UserProduct>
     * <AUTHOR>
     * @date 2019/3/25 10:49
     */
    List<UserProduct> getListByDurAndUserIdAndProType(@Param("userId") Long userId, @Param("days") Short days, @Param("type") Integer type);


    /**
     * 通过用户id查询过期时间大于现在且状态为1的产品信息
     *
     * @param uid
     * @param today
     * @param status
     * @return java.util.List<com.memberintergral.carservice.domain.entity.UserProduct>
     * <AUTHOR>
     * @date 2019/5/27 15:47
     */
    List<UserProduct> getUserProductsByTime(@Param("uid") Long uid, @Param("today") Date today, @Param("status") short status);


    /**
     * 通过虚拟id查询过期时间大于现在且状态为1的产品信息
     *
     * @param vid
     * @param today
     * @param status
     * @return java.util.List<com.memberintergral.carservice.domain.entity.UserProduct>
     * <AUTHOR>
     * @date 2019/5/27 16:56
     */
    List<UserProduct> getVirtualUserProductsByTime(@Param("vid") Long vid, @Param("today") Date today, @Param("status") short status);

    /**
     * 通过订单id查询UserProduct列表
     *
     * @param orderId 订单编号
     * <AUTHOR>
     */
    List<UserProduct> getUserProductsByOrderId(@Param("orderId") Long orderId);

    /**
     * 获取用户购买过得未过期的大会员
     *
     * @param userId 用户id
     * @return java.util.List<com.memberintergral.carservice.domain.entity.UserProduct>
     * <AUTHOR>
     */
    List<UserProduct> getBigVipListByUserId(@Param("userId") Long userId);

    /**
     * 根据条件查询用户购买信息
     *
     * @param userProduct
     * @return
     */
    List<UserProduct> selectByCondition(UserProduct userProduct);
}
