<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.memberintergral.carservice.mapper.UpdateDataRecordMapper">
    <resultMap id="BaseResultMap" type="com.memberintergral.carservice.domain.entity.UpdateDataRecord">

        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="createdTime" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="updateTime" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="dataColumnNames" property="dataColumnNames" jdbcType="VARCHAR"/>
        <result column="dataColumnSourceValues" property="dataColumnSourceValues" jdbcType="VARCHAR"/>
        <result column="dataId" property="dataId" jdbcType="BIGINT"/>
        <result column="dataTableName" property="dataTableName" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>


    </resultMap>
    <sql id="Base_Column_List">
        id,createdTime, updateTime, dataColumnNames, dataColumnSourceValues, dataId, dataTableName,description
    </sql>


    <!-- 插入订单 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO `vehicle_update_data_record`
        (createdTime, updateTime, dataColumnNames, dataColumnSourceValues, dataId, dataTableName,description)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.createdTime,jdbcType=BIGINT},
            #{item.updateTime,jdbcType=BIGINT},
            #{item.dataColumnNames,jdbcType=TIMESTAMP},
            #{item.dataColumnSourceValues,jdbcType=TIMESTAMP},
            #{item.dataId,jdbcType=TIMESTAMP},
            #{item.dataTableName,jdbcType=TIMESTAMP},
            #{item.description,jdbcType=TIMESTAMP}
            )
        </foreach>

    </insert>

</mapper>