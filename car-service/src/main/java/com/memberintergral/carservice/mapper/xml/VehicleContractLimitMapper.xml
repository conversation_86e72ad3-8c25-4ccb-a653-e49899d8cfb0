<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.memberintergral.carservice.mapper.VehicleContractLimitMapper">

    <!-- 查询可用的限制条件 -->
    <select id="findContractLimit" resultType="com.memberintergral.carservice.domain.entity.VehicleContractLimit">
        select
        ID id,
        CONTRACT_ID contractId,
        CONTENT content,
        STATUS status,
        CREATE_DATE createDate,
        UPDATE_DATE updateDate
        from vehicle_contract_limit
        <where>
            CONTRACT_ID = #{id}
            and STATUS = 1
        </where>
        limit 1
    </select>
</mapper>