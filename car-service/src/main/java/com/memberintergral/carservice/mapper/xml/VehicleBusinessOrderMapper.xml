<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.memberintergral.carservice.mapper.VehicleBusinessOrderMapper">
    <resultMap id="BaseResultMap" type="com.memberintergral.carservice.domain.entity.VehicleBusinessOrder">

        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="order_id" property="orderId" jdbcType="BIGINT"/>
        <result column="channel" property="channel" jdbcType="VARCHAR"/>
        <result column="business_id" property="businessId" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="amount" property="amount" jdbcType="DOUBLE"/>

    </resultMap>
    <sql id="Base_Column_List">
        id,order_id, channel, business_id, create_time, amount
    </sql>

    <insert id="insert" parameterType="com.memberintergral.carservice.domain.entity.VehicleBusinessOrder">
        INSERT INTO
        `VEHICLE_BUSINESS_ORDER`
        (order_id, channel, business_id, create_time, amount)
        VALUES
        (#{orderId}, #{channel}, #{businessId}, #{createTime}, #{amount})
    </insert>

    <select id="getOrderById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        `VEHICLE_BUSINESS_ORDER`
        WHERE
        business_id = #{businessId}
        AND
        channel = #{paySrc}
        limit 1
    </select>

    <select id="getBusinessOrderByOid" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        `VEHICLE_BUSINESS_ORDER`
        WHERE
        order_id = #{orderId}
    </select>

    <update id="updateSetBusId" parameterType="com.memberintergral.carservice.domain.entity.VehicleBusinessOrder">
        UPDATE
        `VEHICLE_BUSINESS_ORDER`
        SET
        business_id = #{businessId}
        WHERE
        order_id = #{orderId}
    </update>


</mapper>