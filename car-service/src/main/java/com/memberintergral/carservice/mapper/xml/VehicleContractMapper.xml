<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.memberintergral.carservice.mapper.VehicleContractMapper">

    <!-- 查询可用的合同列表 -->
    <select id="findAvaliableContract" resultType="com.memberintergral.carservice.domain.entity.VehicleContract">
        select
        ID id,
        NAME name,
        REMARK remark,
        STATUS status,
        SERVICE_START_TIME serviceStartTime,
        SERVICE_END_TIME serviceEndTime,
        CONTRACT_TYPE contractType,
        CREATE_DATE createDate,
        UPDATE_DATE updateDate,
        PAY_SRC paySrc
        from vehicle_contract
        <where>
            PAY_SRC = #{paySrc} and `STATUS` = 1 and CONTRACT_TYPE =#{contractType}
        </where>
        limit 1
    </select>

</mapper>