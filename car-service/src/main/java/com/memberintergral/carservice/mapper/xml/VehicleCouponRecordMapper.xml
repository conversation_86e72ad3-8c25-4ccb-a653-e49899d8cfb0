<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.memberintergral.carservice.mapper.VehicleCouponRedordMapper">
    <resultMap id="BaseResultMap" type="com.memberintergral.carservice.domain.entity.VehicleCouponRecord">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="uid" property="uid" jdbcType="BIGINT"/>
        <result column="virtual_uid" property="virtualUid" jdbcType="BIGINT"/>
        <result column="channel" property="channel" jdbcType="VARCHAR"/>
        <result column="batch_id" property="batchId" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="batch_id" property="batchId" jdbcType="VARCHAR"/>
        <result column="device_id" property="deviceId" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="getVehicleCouponRecordByUid" resultMap="BaseResultMap">
        select *
        from `vehicle_coupon_record`
        <where>
            channel = #{channel} and batch_id = #{batchId}
            <choose>
                <when test="uid != null and uid !=0 ">
                    and uid = #{uid}
                </when>

                <when test="deviceId != null and deviceId !='' ">
                    and device_id = #{deviceId}
                </when>
            </choose>

        </where>


    </select>

    <select id="getVehicleCouponRecordByDeviceId" resultMap="BaseResultMap">
        select *
        from `vehicle_coupon_record`
        where device_id = #{deviceId}
    </select>
    <update id="updateVehicleCouponsRecordById">
        update `vehicle_coupon_record`
        set uid = #{uid},update_time=now()
        where
        id IN
        <foreach collection="vehicleCouponRecordIds" item="itemId" index="index" open="(" close=")" separator=",">
            #{itemId}
        </foreach>
    </update>
    <insert id="insert" parameterType="com.memberintergral.carservice.domain.entity.VehicleCouponRecord" useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO
        `vehicle_coupon_record`
        (`uid`,`virtual_uid`,`channel`,`batch_id`,`create_time`,`update_time`,`device_id`)
        VALUES
        (#{uid},#{virtualUid},#{channel}, #{batchId},#{createTime},#{updateTime},#{deviceId});
    </insert>

</mapper>