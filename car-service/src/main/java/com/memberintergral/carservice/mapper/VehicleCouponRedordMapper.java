package com.memberintergral.carservice.mapper;

import com.memberintergral.carservice.domain.entity.VehicleCouponRecord;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @program: vip_adv
 * @description: order_info表Mapper类
 * @author: <EMAIL>
 * @create: 2018-09-26 11:58
 **/
@Repository("vehicleCouponRedordMapper")
public interface VehicleCouponRedordMapper {

    List<VehicleCouponRecord> getVehicleCouponRecordByUid(@Param("uid") Long uid, @Param("deviceId") String deviceId, @Param("channel")String channel, @Param("batchId")String batchId);

    List<VehicleCouponRecord> getVehicleCouponRecordByDeviceId(@Param("deviceId")String deviceId);

    Long updateVehicleCouponsRecordById( @Param("uid") Long uid,  @Param("vehicleCouponRecordIds") List<Long> vehicleCouponRecordIds);

    Long insert(VehicleCouponRecord vehicleCouponRecord);

}
