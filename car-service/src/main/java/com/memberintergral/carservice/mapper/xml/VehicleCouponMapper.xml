<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.memberintergral.carservice.mapper.VehicleCouponMapper">
    <resultMap id="BaseResultMap" type="com.memberintergral.carservice.domain.entity.VehicleCoupon">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="channel" property="channel" jdbcType="VARCHAR"/>
        <result column="coupon_name" property="couponName" jdbcType="VARCHAR"/>
        <result column="order_id" property="orderId" jdbcType="BIGINT"/>
        <result column="pic_url" property="picUrl" jdbcType="VARCHAR"/>
        <result column="restricts" property="restricts" jdbcType="VARCHAR"/>
        <result column="serialKey" property="serialKey" jdbcType="VARCHAR"/>
        <result column="targets" property="targets" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="uid" property="uid" jdbcType="BIGINT"/>
        <result column="virtual_uid" property="virtualUid" jdbcType="BIGINT"/>
        <result column="used" property="used" jdbcType="INTEGER"/>
        <result column="discount_amount" property="discountAmount" jdbcType="DOUBLE"/>
        <result column="sort_id" property="sortId" jdbcType="INTEGER"/>
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="months" property="months" jdbcType="INTEGER"/>
        <result column="template_id" property="templateId" jdbcType="INTEGER"/>
        <result column="batch_id" property="batchId" jdbcType="VARCHAR"/>
        <result column="continute_type" property="continuteType" jdbcType="INTEGER"/>
        <result column="detail" property="detail" jdbcType="VARCHAR"/>
        <result column="device_id" property="deviceId" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="getVehicleCouponsByUid" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select *
        from `vehicle_coupon`
        <where>
            <choose>
                <when test="uid != null and uid !=0 ">
                    and uid = #{uid}
                </when>

                <when test="virtualUid != null and virtualUid!=0  ">
                    and virtual_uid = #{virtualUid}
                </when>
            </choose>
        </where>
        order by discount_amount desc
    </select>

    <select id="getVehicleCouponsByDeviceId" resultMap="BaseResultMap">
        select *
        from `vehicle_coupon`
        where device_id = #{deviceId}
    </select>

    <insert id="insert" parameterType="com.memberintergral.carservice.domain.entity.VehicleCoupon" useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO
        `vehicle_coupon`
        (`channel`,`coupon_name`,`pic_url`,`restricts`,`serialKey`,`targets`,`type`,`uid`,`virtual_uid`,`used`,`discount_amount`,`sort_id`,`start_time`,`end_time`,`create_time`,`update_time`,`months`,`template_id`,`batch_id`,`continute_type`,`detail`,`device_id`)
        VALUES
        (#{channel},#{couponName},#{picUrl},#{restricts},#{serialKey},#{targets},
        #{type},#{uid},#{virtualUid},#{used},#{discountAmount},#{sortId},
        #{startTime},#{endTime},#{createTime},#{updateTime},#{months},#{templateId},#{batchId},#{continuteType},#{detail},#{deviceId});
    </insert>

    <update id="updateVehicleCouponsBySerialKey">
        update `vehicle_coupon`
        set order_id = #{orderId}, used = #{used},update_time=now()
        where serialKey = #{serialKey}
    </update>
    <update id="updateVehicleCouponsById">
        update `vehicle_coupon`
        set uid = #{uid},update_time=now()
        where
        id IN
        <foreach collection="vehicleCouponIds" item="itemId" index="index" open="(" close=")" separator=",">
            #{itemId}
        </foreach>
    </update>

    <update id="updateVRVehicleCouponsById">
        update `vehicle_coupon`
        set virtual_uid = #{virtualUid},update_time=now()
        where
        id IN
        <foreach collection="vehicleCouponIds" item="itemId" index="index" open="(" close=")" separator=",">
            #{itemId}
        </foreach>
    </update>
    <select id="getVehicleCouponsBySerialKey" resultMap="BaseResultMap">
        select id,discount_amount, used ,`type`,continute_type,start_time,end_time,months,channel
        from `vehicle_coupon`
        <where>
            serialKey = #{serialKey} and used = 0
            <choose>
                <when test="uid != null and uid !=0 and virtualUid != null and virtualUid !=0">
                    and ( uid = #{uid} or virtual_uid = #{virtualUid})
                </when>
                <when test="uid != null and uid !=0 ">
                    and uid = #{uid}
                </when>
                <when test="virtualUid != null and virtualUid !=0 ">
                    and virtual_uid = #{virtualUid}
                </when>
            </choose>

        </where>

    </select>

</mapper>