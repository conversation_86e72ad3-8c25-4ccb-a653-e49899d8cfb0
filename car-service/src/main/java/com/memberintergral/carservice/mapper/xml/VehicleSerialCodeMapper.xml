<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.memberintergral.carservice.mapper.VehicleSerialCodeMapper">
    <resultMap id="BaseResultMap" type="com.memberintergral.carservice.domain.entity.VehicleSerialCode">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="createdTime" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="updateTime" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="cash" property="cash" jdbcType="DOUBLE"/>
        <result column="channel" property="channel" jdbcType="VARCHAR"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="days" property="days" jdbcType="INTEGER"/>
        <result column="expireTime" property="expireTime" jdbcType="TIMESTAMP"/>
        <result column="orderInfoId" property="orderInfoId" jdbcType="BIGINT"/>
        <result column="productTypeId" property="productTypeId" jdbcType="VARCHAR"/>
        <result column="serialKey" property="serialKey" jdbcType="VARCHAR"/>
        <result column="used" property="used" jdbcType="BIT"/>
        <result column="way" property="way" jdbcType="VARCHAR"/>


    </resultMap>
    <sql id="Base_Column_List">
        id,createdTime, updateTime, cash, channel, creator, days, expireTime,orderInfoId,productTypeId,
        serialKey,used,way
    </sql>

    <!--根据兑换码查询兑换码实体-->
    <select id="getSerialByKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        `VEHICLE_SERIAL_CODE`
        WHERE
        serialKey = #{serialKey}
    </select>

    <!--更新订单信息-->
    <update id="updateCode" parameterType="com.memberintergral.carservice.domain.entity.VehicleSerialCode">
        UPDATE
        `VEHICLE_SERIAL_CODE`
        SET
        updateTime= #{updateTime},
        cash= #{cash},
        channel= #{channel},
        creator= #{creator},
        days= #{days},
        expireTime= #{expireTime},
        orderInfoId= #{orderInfoId},
        productTypeId= #{productTypeId},
        serialKey= #{serialKey},
        used= #{used},
        way= #{way}
        WHERE
        id = #{id}
    </update>

    <!--根据兑换码列表获取实体list-->
    <select id="getSerialCodeByCodes" resultType="com.memberintergral.carservice.domain.entity.CodeOrderEntity">
        SELECT
        v.serialKey AS exCode,
        v.used AS STATUS,
        v.days AS days,
        o.PID AS uid
        FROM
        `VEHICLE_SERIAL_CODE` v
        LEFT JOIN
        `VEHICLE_ORDER` o
        ON v.orderInfoId = o.id
        WHERE
        v.SERIALKEY IN
        <foreach collection="list" item="code" open="(" close=")" separator=",">
            #{code}
        </foreach>
    </select>


    <!-- 批量插入生成的兑换码 -->
    <insert id="insertCodeBatch" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into VEHICLE_SERIAL_CODE
        (creator, createdTime, cash, days, channel, expireTime, productTypeId, serialKey,used, way)
        values
        <foreach collection="list" item="reddemCode" index="index" separator=",">
            (
            #{reddemCode.creator}, #{reddemCode.createdTime},#{reddemCode.cash},#{reddemCode.days},
            #{reddemCode.channel}, #{reddemCode.expireTime}, #{reddemCode.productTypeId}, #{reddemCode.serialKey},
            #{reddemCode.used},#{reddemCode.way}
            )
        </foreach>
    </insert>

    <!--根据id范围查询兑换码实体-->
    <select id="queryCodeInfoByIdScope" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        `VEHICLE_SERIAL_CODE`
        WHERE
        id <![CDATA[ >= ]]> #{minId}
        AND
        id <![CDATA[ <= ]]> #{maxId}
    </select>

    <select id="getSerialCodeDetailByCodes" resultType="com.memberintergral.carservice.domain.entity.SerialCodeDetail">
        SELECT
        v.SERIALKEY serialKey,
        v.PRODUCTTYPEID productTypeId,
        v.EXPIRETIME expireTime,
        v.CHANNEL channel,
        v.CREATEDTIME createdTime,
        v.CASH cash,
        v.USED used,
        v.ORDERINFOID orderId,
        o.USER_ID userId,
        o.PID pid
        FROM
        `VEHICLE_SERIAL_CODE` v
        LEFT JOIN
        `VEHICLE_ORDER` o
        ON v.orderInfoId = o.id
        WHERE
        v.SERIALKEY IN
        <foreach collection="list" item="code" open="(" close=")" separator=",">
            #{code}
        </foreach>
    </select>
</mapper>