package com.memberintergral.carservice.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.memberintergral.carservice.domain.entity.DataDataset;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-24
 */
public interface DataDatasetMapper extends BaseMapper<DataDataset> {


    /**
     * 根据code查询DataSet数据
     *
     * @param code
     * @return
     */
    List<DataDataset> getDataSetByActivityCode(@Param("code")  String code);
}
