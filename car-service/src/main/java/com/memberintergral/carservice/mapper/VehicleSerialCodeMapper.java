package com.memberintergral.carservice.mapper;

import com.memberintergral.carservice.domain.entity.CodeOrderEntity;
import com.memberintergral.carservice.domain.entity.SerialCodeDetail;
import com.memberintergral.carservice.domain.entity.VehicleSerialCode;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @program: vip_adv MASTER
 * @description: 兑换码mapper
 * @author: <EMAIL>
 * @create: 2018-10-09 17:46
 */
@Repository
public interface VehicleSerialCodeMapper {


    /**
     * 通过兑换码key查询兑换码
     *
     * @param serialKey
     * @return
     */
    VehicleSerialCode getSerialByKey(String serialKey);


    /**
     * 更新兑换码
     *
     * @param sc
     * @return
     */
    int updateCode(VehicleSerialCode sc);


    /**
     * 根据兑换码列表获取实体列表
     *
     * @param codeList 兑换码列表
     * @return java.util.List<cn.kuwo.vip1.mapper.master.VehicleSerialCodeMapper>
     * <AUTHOR>
     * @date 2019/11/4 20:34
     */
    List<CodeOrderEntity> getSerialCodeByCodes(@Param("list") List<String> codeList);


    /**
     * 批量插入兑换码实体
     *
     * @param codeList 兑换码实体列表
     * @return java.lang.Long
     * <AUTHOR>
     * @date 2020/4/23 18:50
     */
    Long insertCodeBatch(@Param("list") List<VehicleSerialCode> codeList);


    /**
     * 根据id范围查询兑换码信息
     *
     * @param minId 低id
     * @param maxId 高id
     * @return java.util.List<cn.kuwo.vip1.entity.master.VehicleSerialCode>
     * <AUTHOR>
     * @date 2020/4/24 12:06
     */
    List<VehicleSerialCode> queryCodeInfoByIdScope(@Param("minId") Long minId, @Param("maxId") Long maxId);



    /**
     * 根据兑换码列表获取实体列表
     *
     * @param codeList 兑换码列表
     * @return java.util.List<cn.kuwo.vip1.mapper.master.VehicleSerialCodeMapper>
     * <AUTHOR>
     * @date 2019/11/4 20:34
     */
    List<SerialCodeDetail> getSerialCodeDetailByCodes(@Param("list") List<String> codeList);
}
