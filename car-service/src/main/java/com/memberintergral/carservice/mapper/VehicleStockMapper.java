package com.memberintergral.carservice.mapper;

import com.memberintergral.carservice.domain.entity.VehicleStock;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @program: vip_adv
 * @description: order_info表Mapper类
 * @author: <EMAIL>
 * @create: 2018-09-26 11:58
 **/
@Repository("vehicleStockMapper")
public interface VehicleStockMapper {

    VehicleStock getVehicleStockByKey(@Param("uniqueActivityKey") String uniqueActivityKey);

    Long insert(VehicleStock vehicleStock);

    int updateVehicleStock(VehicleStock vehicleStock);

    List<VehicleStock> getAllStock();

}
