/*
 * create By <PERSON>yu
 */
package com.memberintergral.carservice.domain.DTO;

import java.util.Map;

/**
 * @program: vip_adv
 * @description: pay_order 中转类
 * @author: <EMAIL>
 * @create: 2018-09-26 19:26
 */
public class PayOrderDTO {

    private String cash;
    private String payType;
    private String autoPay;
    private String platform;
    private String src;
    private String act;
    private String clientAct;
    private String uid;
    private String userName;
    private String paySrc;
    private Map<?, ?> products;
    private String couponUniqueId;//优惠券Id
    // 增加参数，用来表示是否使用二维码原标识. true表示使用，false表示没有
    private String originalCode;
    /**
     * 用来确定是用二维码还是app支付唤起
     * qr(默认值)
     * sdk
     */
    private String invoke = "qr";

    /**
     * 架号或其他车辆相关标识
     * 可选,之后的其它类型的扩展字段,最后都放到developerPayload里面
     */
    private String vinCode;

    /**
     * 开发者相关的扩展字段,允许三方携带信息,异步通知的时候会原样返回
     * 可选,目前暂时不限定字段的大小,之后如果非常多的话就限制
     */
    private String developerPayload;


    public String getCash() {
        return cash;
    }

    public void setCash(String cash) {
        this.cash = cash;
    }

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public String getAutoPay() {
        return autoPay;
    }

    public void setAutoPay(String autoPay) {
        this.autoPay = autoPay;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getSrc() {
        return src;
    }

    public void setSrc(String src) {
        this.src = src;
    }

    public String getAct() {
        return act;
    }

    public void setAct(String act) {
        this.act = act;
    }

    public String getClientAct() {
        return clientAct;
    }

    public void setClientAct(String clientAct) {
        this.clientAct = clientAct;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPaySrc() {
        return paySrc;
    }

    public void setPaySrc(String paySrc) {
        this.paySrc = paySrc;
    }

    public Map<?, ?> getProducts() {
        return products;
    }

    public void setProducts(Map<?, ?> products) {
        this.products = products;
    }

    public String getOriginalCode() {
        return originalCode;
    }

    public void setOriginalCode(String originalCode) {
        this.originalCode = originalCode;
    }

    public String getInvoke() {
        return invoke;
    }

    public void setInvoke(String invoke) {
        this.invoke = invoke;
    }

    public String getVinCode() {
        return vinCode;
    }

    public void setVinCode(String vinCode) {
        this.vinCode = vinCode;
    }

    public String getDeveloperPayload() {
        return developerPayload;
    }

    public void setDeveloperPayload(String developerPayload) {
        this.developerPayload = developerPayload;
    }

    public String getCouponUniqueId() {
        return couponUniqueId;
    }

    public void setCouponUniqueId(String couponUniqueId) {
        this.couponUniqueId = couponUniqueId;
    }
}
