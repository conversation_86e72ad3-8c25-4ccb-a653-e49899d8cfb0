package com.memberintergral.carservice.domain.entity;


/**
 * @program: vip_adv
 * @description: 下单参数接收类
 * @author: <EMAIL>
 * @create: 2019-01-10 11:39
 */

public class OrderReqEntity extends BaseReqEntity {
    public String platVersion;
    public String jsonStr;
    public String callbackUrl;
    // 1 端内 2活动 3端外 4车载端会员中心
    public Integer fromType;

    public String deviceId;

    public String fromsrc;

    public String carModel;
    // 下单日志
    private String urlparams;

    public String elem_area;

    public String filterId;

    public String callbackUrlExt;

    public String requestId;

    // 暂时没用
    public String apiv;

    // h5传的客户端版本号 eg: ********
    public String version;

    public String getApiv() {
        return apiv;
    }

    public void setApiv(String apiv) {
        this.apiv = apiv;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getCallbackUrlExt() {
        return callbackUrlExt;
    }

    public void setCallbackUrlExt(String callbackUrlExt) {
        this.callbackUrlExt = callbackUrlExt;
    }

    public String getFilterId() {
        return filterId;
    }

    public void setFilterId(String filterId) {
        this.filterId = filterId;
    }

    public String getElem_area() {
        return elem_area;
    }

    public void setElem_area(String elem_area) {
        this.elem_area = elem_area;
    }

    public String getUrlparams() {
        return urlparams;
    }

    public void setUrlparams(String urlparams) {
        this.urlparams = urlparams;
    }


    public String getCarModel() {
        return carModel;
    }

    public void setCarModel(String carModel) {
        this.carModel = carModel;
    }

    VehicleProduct product;

    public VehicleProduct getProduct() {
        return product;
    }

    public void setProduct(VehicleProduct product) {
        this.product = product;
    }

    public String getFromsrc() {
        return fromsrc;
    }

    public void setFromsrc(String fromsrc) {
        this.fromsrc = fromsrc;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public Integer getFromType() {
        return fromType;
    }

    public void setFromType(Integer fromType) {
        this.fromType = fromType;
    }

    public String getCallbackUrl() {
        return callbackUrl;
    }

    public void setCallbackUrl(String callbackUrl) {
        this.callbackUrl = callbackUrl;
    }


    public String getPlatVersion() {
        return platVersion;
    }

    public void setPlatVersion(String platVersion) {
        this.platVersion = platVersion;
    }

    public String getJsonStr() {
        return jsonStr;
    }

    public void setJsonStr(String jsonStr) {
        this.jsonStr = jsonStr;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }
}
