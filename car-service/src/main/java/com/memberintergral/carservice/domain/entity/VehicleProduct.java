package com.memberintergral.carservice.domain.entity;



import com.memberintergral.carservice.config.enums.ProductEnum;

import java.sql.Timestamp;
import java.util.Date;

/**
 * @program: vip_adv
 * @description: vehicle_product表实体
 * @author: <EMAIL>
 * @create: 2018-09-28 18:55
 */
public class VehicleProduct {

    /**
     * 自增主键
     */
    private Long id;
    /**
     * 用户 id
     */
    private Long userId;
    /**
     * 价格,如果买的是音乐包，则为总体价格，如果是单曲或者专辑，则为单价
     */
    private Double price;
    /**
     * 产品ID
     */
    private String productId;
    /**
     * 商品小类别  vip1 vip2 vip3 vip4
     */
    private Long productTypeId;
    /**
     * 该字段和  vehicle_product 表的id字段关联
     */
    private Long orderId;
    /**
     * 购买时间
     */
    private Timestamp buyDate;
    /**
     * 和购买时间一样,物品的有效时间
     */
    private Timestamp startDate;
    /**
     * 商品结束日期
     */
    private Timestamp expireDate;
    /**
     * 天数
     */
    private Short duration;
    /**
     * 购买月份
     */
    private Short cnt;
    /**
     * 这个状态和订单状态一样
     */
    private Short status;
    /**
     * 虚拟用户的id
     */
    private Long virtualUid;
    /**
     * 虚拟账号往真实账号合并时的时间
     */
    private Date mergeDate;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public Long getProductTypeId() {
        return productTypeId;
    }

    public void setProductTypeId(Long productTypeId) {
        this.productTypeId = productTypeId;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Timestamp getBuyDate() {
        return buyDate;
    }

    public void setBuyDate(Timestamp buyDate) {
        this.buyDate = buyDate;
    }

    public Timestamp getStartDate() {
        return startDate;
    }

    public void setStartDate(Timestamp startDate) {
        this.startDate = startDate;
    }

    public Timestamp getExpireDate() {
        return expireDate;
    }

    public void setExpireDate(Timestamp expireDate) {
        this.expireDate = expireDate;
    }

    public Short getDuration() {
        return duration;
    }

    public void setDuration(Short duration) {
        this.duration = duration;
    }

    public Short getCnt() {
        return cnt;
    }

    public void setCnt(Short cnt) {
        this.cnt = cnt;
    }

    public Short getStatus() {
        return status;
    }

    public void setStatus(Short status) {
        this.status = status;
    }

    public Long getVirtualUid() {
        return virtualUid;
    }

    public void setVirtualUid(Long virtualUid) {
        this.virtualUid = virtualUid;
    }

    public Date getMergeDate() {
        return mergeDate;
    }

    public void setMergeDate(Date mergeDate) {
        this.mergeDate = mergeDate;
    }

    public ProductEnum getProductType() {
        return ProductEnum.getProductType(this.getProductTypeId().intValue());
    }

}
