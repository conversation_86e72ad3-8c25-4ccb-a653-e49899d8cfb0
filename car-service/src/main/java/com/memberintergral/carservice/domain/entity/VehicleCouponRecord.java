package com.memberintergral.carservice.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class VehicleCouponRecord implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Long uid;

    private String channel;

    /**
     * 批次Id
     */
    private String batchId;

    private Date createTime;

    private Date updateTime;

    private Long virtualUid;

    private String deviceId;


}
