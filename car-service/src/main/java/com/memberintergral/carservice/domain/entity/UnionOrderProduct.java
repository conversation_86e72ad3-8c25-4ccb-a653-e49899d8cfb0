package com.memberintergral.carservice.domain.entity;

import java.util.Date;

/**
 * @program: vip_adv
 * @description: 联合查询订单产品表
 * @author: <EMAIL>
 * @create: 2018-10-22 10:44
 */
public class UnionOrderProduct {
    private Long uid;
    private Long pid;
    private Integer payType;
    private String platForm;
    private Long orderId;
    private Long productTypeId;
    private Double cnt;
    private Integer duration;
    private Double credit;
    private Date exDate;
    private Integer subdays;
    private String src;
    private String paySrc;
    private String platVersion;
    private String productType;

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public Long getPid() {
        return pid;
    }

    public void setPid(Long pid) {
        this.pid = pid;
    }

    public Integer getPayType() {
        return payType;
    }

    public void setPayType(Integer payType) {
        this.payType = payType;
    }

    public String getPlatForm() {
        return platForm;
    }

    public void setPlatForm(String platForm) {
        this.platForm = platForm;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getProductTypeId() {
        return productTypeId;
    }

    public void setProductTypeId(Long productTypeId) {
        this.productTypeId = productTypeId;
    }

    public Double getCnt() {
        return cnt;
    }

    public void setCnt(Double cnt) {
        this.cnt = cnt;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public Double getCredit() {
        return credit;
    }

    public void setCredit(Double credit) {
        this.credit = credit;
    }

    public Date getExDate() {
        return exDate;
    }

    public void setExDate(Date exDate) {
        this.exDate = exDate;
    }

    public Integer getSubdays() {
        return subdays;
    }

    public void setSubdays(Integer subdays) {
        this.subdays = subdays;
    }

    public String getSrc() {
        return src;
    }

    public void setSrc(String src) {
        this.src = src;
    }

    public String getPaySrc() {
        return paySrc;
    }

    public void setPaySrc(String paySrc) {
        this.paySrc = paySrc;
    }

    public String getPlatVersion() {
        return platVersion;
    }

    public void setPlatVersion(String platVersion) {
        this.platVersion = platVersion;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    @Override
    public String toString() {
        return "UnionOrderProduct{" +
                "uid=" + uid +
                ", pid=" + pid +
                ", payType=" + payType +
                ", platForm='" + platForm + '\'' +
                ", orderId=" + orderId +
                ", productTypeId=" + productTypeId +
                ", cnt=" + cnt +
                ", credit=" + credit +
                ", exDate=" + exDate +
                ", subdays=" + subdays +
                ", src='" + src + '\'' +
                ", paySrc='" + paySrc + '\'' +
                ", platVersion='" + platVersion + '\'' +
                ", productType='" + productType + '\'' +
                '}';
    }
}
