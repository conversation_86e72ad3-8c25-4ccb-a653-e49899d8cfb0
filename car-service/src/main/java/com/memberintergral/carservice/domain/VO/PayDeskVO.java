package com.memberintergral.carservice.domain.VO;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PayDeskVO implements Serializable {

    private static final long serialVersionUID=1L;

    private Integer id;

    /**
     * 收银台标识
     */
    @NotNull(message = "收银台标识payDeskSign不能为空")
    private String payDeskSign;

    /**
     * 收银台名称
     */
    @NotNull(message = "收银台名称payDeskName不能为空")
    private String payDeskName;

    /**
     * 编辑人
     */
    private String editorName;

    /**
     * 展示类型
     */
    private Integer showType;

    /**
     * json文案
     */
    private String doc;


}
