package com.memberintergral.carservice.domain.entity;

import java.util.Date;

public class VipConfig {

    private Long id;

    private Date time;

    private String type;

    private String categray;

    private String upgrade;

    private Integer price;

    private Long period;

    private Long downLimit;

    private Integer downAudiobr;

    private Integer downVIDEOBR;

    private Integer listenLimit;

    private Integer listenAUDIOBR;

    private Integer listenVIDEOBR;

    private Integer videoDownLimit;

    private Integer videoListenLimit;

    private Integer resType;

    private String name;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getResType() {
        return resType;
    }

    public void setResType(Integer resType) {
        this.resType = resType;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCategray() {
        return categray;
    }

    public void setCategray(String categray) {
        this.categray = categray;
    }

    public String getUpgrade() {
        return upgrade;
    }

    public void setUpgrade(String upgrade) {
        this.upgrade = upgrade;
    }

    public Integer getPrice() {
        return price;
    }

    public void setPrice(Integer price) {
        this.price = price;
    }

    public Long getPeriod() {
        return period;
    }

    public void setPeriod(Long period) {
        this.period = period;
    }

    public Long getDownLimit() {
        return downLimit;
    }

    public void setDownLimit(Long downLimit) {
        this.downLimit = downLimit;
    }

    public Integer getDownAudiobr() {
        return downAudiobr;
    }

    public void setDownAudiobr(Integer downAudiobr) {
        this.downAudiobr = downAudiobr;
    }

    public Integer getDownVIDEOBR() {
        return downVIDEOBR;
    }

    public void setDownVIDEOBR(Integer downVIDEOBR) {
        this.downVIDEOBR = downVIDEOBR;
    }

    public Integer getListenLimit() {
        return listenLimit;
    }

    public void setListenLimit(Integer listenLimit) {
        this.listenLimit = listenLimit;
    }

    public Integer getListenAUDIOBR() {
        return listenAUDIOBR;
    }

    public void setListenAUDIOBR(Integer listenAUDIOBR) {
        this.listenAUDIOBR = listenAUDIOBR;
    }

    public Integer getListenVIDEOBR() {
        return listenVIDEOBR;
    }

    public void setListenVIDEOBR(Integer listenVIDEOBR) {
        this.listenVIDEOBR = listenVIDEOBR;
    }


    public Integer getVideoDownLimit() {
        return videoDownLimit;
    }

    public void setVideoDownLimit(Integer videoDownLimit) {
        this.videoDownLimit = videoDownLimit;
    }

    public Integer getVideoListenLimit() {
        return videoListenLimit;
    }

    public void setVideoListenLimit(Integer videoListenLimit) {
        this.videoListenLimit = videoListenLimit;
    }

    public static VipConfig createCareFreePackageVipConfig(String type, long period, String packageName) {
        VipConfig vipConfig = new VipConfig();
        vipConfig.setTime(new Date());
        vipConfig.setType(type);
        vipConfig.setPrice(0);
        vipConfig.setCategray("vip");
        vipConfig.setUpgrade("");
        vipConfig.setPeriod(period);

        vipConfig.setListenAUDIOBR(10000);
        vipConfig.setDownAudiobr(10000);

        vipConfig.setListenLimit(3);
        vipConfig.setListenVIDEOBR(10000);

        vipConfig.setDownLimit(3000l);
        vipConfig.setDownVIDEOBR(10000);

        vipConfig.setVideoDownLimit(0);
        vipConfig.setVideoListenLimit(0);


        // 畅听包固定值
        vipConfig.setResType(1);
        vipConfig.setName(packageName);

        return vipConfig;
    }

}
