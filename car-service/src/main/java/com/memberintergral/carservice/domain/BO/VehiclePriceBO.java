package com.memberintergral.carservice.domain.BO;


import com.fasterxml.jackson.annotation.JsonIgnore;

public class VehiclePriceBO {
    private Integer gearId;

    private Integer filterId;

    private Integer month;

    private String title;

    private String tag;

    private Double currentPrice;

    private Double renewPrice;

    private Double underPrice;

    private Double discountAmount;

    private Double couponAmount;

    private String subTitle;

    private Boolean selected;

    private String paySrc;

    private Boolean autoPay;

    private String wxTflg;

    private Integer upGradeId;

    private String label;

    private String src;

    private long lastTime;

    private String couponUniqueId;

    private String style;

    private String bottomTag;

    private Integer forceLogin;

    /**
     * 竖屏
     */
    private String descImgTall;


    /**
     * 宽屏
     */
    private String descImgWide;


    /**
     * 超宽屏
     */
    private String descImgLong;

    /**
     * 是否活动挡位
     */
    private String isActivity;

    /**
     * 是否活动弹窗
     */
    private String showActivityPopup;

    /**
     * 是否未登录购买
     */
    private String showQrcode;

    /**
     * 自动续费文案
     */
    private  String autoPayDesc;

    /**
     * 登录弹窗
     */
    private String showLoginPopup="0";

    /**
     * 档位左上角标签文案
     */
    private String topLeftTag;

    /**
     * 折扣信息
     */
    private String discount;

    /**
     * 二维码链接
     */
    private String qrcodeLink;

    /**
     * 支付成功弹窗图片
     */
    private String payFinishedPopupImg;

    /**
     * 二维码未登录遮罩文案
     */
    private String qrcodeUnloginText;

    /**
     * 支付成功弹窗标题
     */
    private String payFinishedPopupTitle;

    /**
     * 支付成功弹窗描述
     */
    private String payFinishedPopupDesc;

    /**
     * 支付成功弹窗提示
     */
    private String payFinishedPopupTips;

    /**
     * 活动二维码显示条件限制
     */
    private Integer showLimitType;

    /**
     * 是否已购买
     */
    private Integer hasBuy;

    /**
     * 升级价格
     */
    @JsonIgnore
    private String carToSvipDayPrice;

    /**
     * 升级价格
     */
    @JsonIgnore
    private String carToSvipMonthPrice;

    // 豪v升级价格
    @JsonIgnore
    private String vipLuxuryToSvipDayPrice;
    @JsonIgnore
    private String vipLuxuryToSvipMonthPrice;

    // 音乐包升级价格
    @JsonIgnore
    private String vipmToSvipDayPrice;
    @JsonIgnore
    private String vipmToSvipMonthPrice;
    @JsonIgnore
    private String vipmToVehicleVipDayPrice;
    @JsonIgnore
    private String vipmToVehicleVipMonthPrice;

    public void setVipLuxuryToSvipDayPrice(String vipLuxuryToSvipDayPrice) {
        this.vipLuxuryToSvipDayPrice = vipLuxuryToSvipDayPrice;
    }

    public void setVipLuxuryToSvipMonthPrice(String vipLuxuryToSvipMonthPrice) {
        this.vipLuxuryToSvipMonthPrice = vipLuxuryToSvipMonthPrice;
    }

    public String getVipLuxuryToSvipDayPrice() {
        return vipLuxuryToSvipDayPrice;
    }

    public String getVipLuxuryToSvipMonthPrice() {
        return vipLuxuryToSvipMonthPrice;
    }

    public String getVipmToSvipDayPrice() {
        return vipmToSvipDayPrice;
    }

    public void setVipmToSvipDayPrice(String vipmToSvipDayPrice) {
        this.vipmToSvipDayPrice = vipmToSvipDayPrice;
    }

    public String getVipmToSvipMonthPrice() {
        return vipmToSvipMonthPrice;
    }

    public void setVipmToSvipMonthPrice(String vipmToSvipMonthPrice) {
        this.vipmToSvipMonthPrice = vipmToSvipMonthPrice;
    }

    public String getVipmToVehicleVipDayPrice() {
        return vipmToVehicleVipDayPrice;
    }

    public void setVipmToVehicleVipDayPrice(String vipmToVehicleVipDayPrice) {
        this.vipmToVehicleVipDayPrice = vipmToVehicleVipDayPrice;
    }

    public String getVipmToVehicleVipMonthPrice() {
        return vipmToVehicleVipMonthPrice;
    }

    public void setVipmToVehicleVipMonthPrice(String vipmToVehicleVipMonthPrice) {
        this.vipmToVehicleVipMonthPrice = vipmToVehicleVipMonthPrice;
    }

    private String vipType;

    public String getVipType() {
        return vipType;
    }

    public void setVipType(String vipType) {
        this.vipType = vipType;
    }

    public String getCarToSvipDayPrice() {
        return carToSvipDayPrice;
    }

    public void setCarToSvipDayPrice(String carToSvipDayPrice) {
        this.carToSvipDayPrice = carToSvipDayPrice;
    }

    public String getCarToSvipMonthPrice() {
        return carToSvipMonthPrice;
    }

    public void setCarToSvipMonthPrice(String carToSvipMonthPrice) {
        this.carToSvipMonthPrice = carToSvipMonthPrice;
    }

    public Integer getGearId() {
        return gearId;
    }

    public void setGearId(Integer gearId) {
        this.gearId = gearId;
    }

    public String getShowLoginPopup() {
        return showLoginPopup;
    }

    public void setShowLoginPopup(String showLoginPopup) {
        this.showLoginPopup = showLoginPopup;
    }

    public String getDescImgTall() {
        return descImgTall;
    }

    public void setDescImgTall(String descImgTall) {
        this.descImgTall = descImgTall;
    }

    public String getDescImgWide() {
        return descImgWide;
    }

    public void setDescImgWide(String descImgWide) {
        this.descImgWide = descImgWide;
    }

    public String getDescImgLong() {
        return descImgLong;
    }

    public void setDescImgLong(String descImgLong) {
        this.descImgLong = descImgLong;
    }

    public String getIsActivity() {
        return isActivity;
    }

    public void setIsActivity(String isActivity) {
        this.isActivity = isActivity;
    }

    public String getShowActivityPopup() {
        return showActivityPopup;
    }

    public void setShowActivityPopup(String showActivityPopup) {
        this.showActivityPopup = showActivityPopup;
    }

    public String getShowQrcode() {
        return showQrcode;
    }

    public void setShowQrcode(String showQrcode) {
        this.showQrcode = showQrcode;
    }

    public Integer getForceLogin() {
        return forceLogin;
    }

    public void setForceLogin(Integer forceLogin) {
        this.forceLogin = forceLogin;
    }

    public String getStyle() {
        return style;
    }

    public void setStyle(String style) {
        this.style = style;
    }

    public String getBottomTag() {
        return bottomTag;
    }

    public void setBottomTag(String bottomTag) {
        this.bottomTag = bottomTag;
    }

    public String getCouponUniqueId() {
        return couponUniqueId;
    }

    public void setCouponUniqueId(String couponUniqueId) {
        this.couponUniqueId = couponUniqueId;
    }


    public long getLastTime() {
        return lastTime;
    }

    public void setLastTime(long lastTime) {
        this.lastTime = lastTime;
    }

    public String getSrc() {
        return src;
    }

    public void setSrc(String src) {
        this.src = src;
    }

    public Integer getFilterId() {
        return filterId;
    }

    public void setFilterId(Integer filterId) {
        this.filterId = filterId;
    }

    public String getDoc() {
        return doc;
    }

    public void setDoc(String doc) {
        this.doc = doc;
    }

    private String doc;


    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public Integer getUpGradeId() {
        return upGradeId;
    }

    public void setUpGradeId(Integer upGradeId) {
        this.upGradeId = upGradeId;
    }

    public String getWxTflg() {
        return wxTflg;
    }

    public void setWxTflg(String wxTflg) {
        this.wxTflg = wxTflg;
    }

    public Double getCouponAmount() {
        return couponAmount;
    }

    public void setCouponAmount(Double couponAmount) {
        this.couponAmount = couponAmount;
    }

    public Double getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(Double discountAmount) {
        this.discountAmount = discountAmount;
    }

    public Integer getMonth() {
        return month;
    }

    public void setMonth(Integer month) {
        this.month = month;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }


    public Double getUnderPrice() {
        return underPrice;
    }

    public void setUnderPrice(Double underPrice) {
        this.underPrice = underPrice;
    }

    public String getSubTitle() {
        return subTitle;
    }

    public void setSubTitle(String subTitle) {
        this.subTitle = subTitle;
    }

    public Boolean getSelected() {
        return selected;
    }

    public void setSelected(Boolean selected) {
        this.selected = selected;
    }

    public String getPaySrc() {
        return paySrc;
    }

    public void setPaySrc(String paySrc) {
        this.paySrc = paySrc;
    }

    public Boolean getAutoPay() {
        return autoPay;
    }

    public void setAutoPay(Boolean autoPay) {
        this.autoPay = autoPay;
    }


    public Double getCurrentPrice() {
        return currentPrice;
    }

    public void setCurrentPrice(Double currentPrice) {
        this.currentPrice = currentPrice;
    }

    public Double getRenewPrice() {
        return renewPrice;
    }

    public void setRenewPrice(Double renewPrice) {
        this.renewPrice = renewPrice;
    }

    public String getAutoPayDesc() {
        return autoPayDesc;
    }

    public void setAutoPayDesc(String autoPayDesc) {
        this.autoPayDesc = autoPayDesc;
    }

    public String getTopLeftTag() {
        return topLeftTag;
    }

    public void setTopLeftTag(String topLeftTag) {
        this.topLeftTag = topLeftTag;
    }

    public String getDiscount() {
        return discount;
    }

    public void setDiscount(String discount) {
        this.discount = discount;
    }


    public String getQrcodeLink() {
        return qrcodeLink;
    }

    public void setQrcodeLink(String qrcodeLink) {
        this.qrcodeLink = qrcodeLink;
    }

    public String getPayFinishedPopupImg() {
        return payFinishedPopupImg;
    }

    public void setPayFinishedPopupImg(String payFinishedPopupImg) {
        this.payFinishedPopupImg = payFinishedPopupImg;
    }

    public String getQrcodeUnloginText() {
        return qrcodeUnloginText;
    }

    public void setQrcodeUnloginText(String qrcodeUnloginText) {
        this.qrcodeUnloginText = qrcodeUnloginText;
    }

    public String getPayFinishedPopupTitle() {
        return payFinishedPopupTitle;
    }

    public void setPayFinishedPopupTitle(String payFinishedPopupTitle) {
        this.payFinishedPopupTitle = payFinishedPopupTitle;
    }

    public String getPayFinishedPopupDesc() {
        return payFinishedPopupDesc;
    }

    public void setPayFinishedPopupDesc(String payFinishedPopupDesc) {
        this.payFinishedPopupDesc = payFinishedPopupDesc;
    }

    public String getPayFinishedPopupTips() {
        return payFinishedPopupTips;
    }

    public void setPayFinishedPopupTips(String payFinishedPopupTips) {
        this.payFinishedPopupTips = payFinishedPopupTips;
    }

    public Integer getHasBuy() {
        return hasBuy;
    }

    public void setHasBuy(Integer hasBuy) {
        this.hasBuy = hasBuy;
    }

    public Integer getShowLimitType() {
        return showLimitType;
    }

    public void setShowLimitType(Integer showLimitType) {
        this.showLimitType = showLimitType;
    }
}
