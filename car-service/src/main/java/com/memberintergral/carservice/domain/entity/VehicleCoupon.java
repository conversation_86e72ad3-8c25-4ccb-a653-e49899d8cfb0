package com.memberintergral.carservice.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class VehicleCoupon implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String channel;

    /**
     * 优惠券名称
     */
    private String couponName;

    /**
     * 订单id
     */
    private Long orderId;

    private String picUrl;

    private String restricts;

    @TableField("serialKey")
    private String serialKey;

    /**
     * 发放目标人群 1.不限人群 2购买过音乐包的用户
     */
    private String targets;

    /**
     * 优惠券 会员类型 vip_17车载 vip_34 超级会员
     */
    private Integer type;

    private Long uid;

    private int used;

    private Double discountAmount;

    private Integer sortId;

    private Date startTime;

    private Date endTime;

    private Date createTime;

    /**
     * 车载切
     */
    private Date updateTime;

    /**
     * 月份数
     */
    private Integer months;

    /**
     * 优惠券模板Id
     */
    private Integer templateId;

    /**
     * 批次
     */
    private String batchId;

    /**
     * 0 都支持 1支持连续 2支持非连续
     */
    private Integer continuteType;

    private String detail;
    private String deviceId;
    private Long virtualUid;


}
