package com.memberintergral.carservice.domain.entity;

import java.util.Date;

/**
 * @program: vip_adv
 * @description: vehicle_serial_code表实体
 * @author: <EMAIL>
 * @create: 2018-09-28 18:55
 */
public class VehicleSerialCode {

    /**
     * 主键
     */
    private Long id;
    /**
     * 创建时间
     */
    private Date createdTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 金额
     */
    private Double cash;
    /**
     * 渠道
     */
    private String channel;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 兑换码有效期
     */
    private Integer days;
    /**
     * 到期时间
     */
    private Date expireTime;
    /**
     * 订单id
     */
    private Long orderInfoId;
    /**
     * 产品类型id
     */
    private String productTypeId;
    /**
     * 兑换码
     */
    private String serialKey;
    /**
     * 是否已使用
     */
    private Boolean used;
    /**
     * 用途
     */
    private String way;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Double getCash() {
        return cash;
    }

    public void setCash(Double cash) {
        this.cash = cash;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Integer getDays() {
        return days;
    }

    public void setDays(Integer days) {
        this.days = days;
    }

    public Date getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

    public Long getOrderInfoId() {
        return orderInfoId;
    }

    public void setOrderInfoId(Long orderInfoId) {
        this.orderInfoId = orderInfoId;
    }

    public String getProductTypeId() {
        return productTypeId;
    }

    public void setProductTypeId(String productTypeId) {
        this.productTypeId = productTypeId;
    }

    public String getSerialKey() {
        return serialKey;
    }

    public void setSerialKey(String serialKey) {
        this.serialKey = serialKey;
    }

    public Boolean getUsed() {
        return used;
    }

    public void setUsed(Boolean used) {
        this.used = used;
    }

    public String getWay() {
        return way;
    }

    public void setWay(String way) {
        this.way = way;
    }

    @Override
    public String toString() {
        return "VehicleSerialCode{" +
                "id=" + id +
                ", createdTime=" + createdTime +
                ", updateTime=" + updateTime +
                ", cash=" + cash +
                ", channel='" + channel + '\'' +
                ", creator='" + creator + '\'' +
                ", days=" + days +
                ", expireTime=" + expireTime +
                ", orderInfoId=" + orderInfoId +
                ", productTypeId='" + productTypeId + '\'' +
                ", serialKey='" + serialKey + '\'' +
                ", used=" + used +
                ", way='" + way + '\'' +
                '}';
    }
}