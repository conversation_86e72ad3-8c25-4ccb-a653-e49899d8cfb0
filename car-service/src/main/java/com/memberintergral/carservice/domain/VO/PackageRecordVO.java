package com.memberintergral.carservice.domain.VO;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PackageRecordVO implements Serializable {


    private Integer id;
    @NotBlank(message = "packageKey不能为空")
    private String packageKey;
    @NotBlank(message = "packageValue不能为空")
    private String packageValue;

    private String name;

    private String type;

    @JsonIgnore
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date updateTime;


}
