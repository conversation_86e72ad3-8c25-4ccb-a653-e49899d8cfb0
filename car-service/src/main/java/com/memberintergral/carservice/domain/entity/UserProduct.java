package com.memberintergral.carservice.domain.entity;

import java.util.Date;

/**
 * @program: vip_adv
 * @description: 用户产品表
 * @author: <EMAIL>
 * @create: 2019-03-05 18:36
 */
public class UserProduct {
    private Long id;
    private Long userId;
    private Double price;
    private String productId;
    private Long productTypeId;
    private Long orderId;
    private Date buyDate;
    private Date startDate;
    private Date expireDate;
    private Short duration;
    private Short cnt;
    private Byte status;
    private String bitrate;
    private Integer isPresell;
    private String fansKey;
    private Long virtualUid;
    private Date mergeDate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public Long getProductTypeId() {
        return productTypeId;
    }

    public void setProductTypeId(Long productTypeId) {
        this.productTypeId = productTypeId;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Date getBuyDate() {
        return buyDate;
    }

    public void setBuyDate(Date buyDate) {
        this.buyDate = buyDate;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getExpireDate() {
        return expireDate;
    }

    public void setExpireDate(Date expireDate) {
        this.expireDate = expireDate;
    }

    public Short getDuration() {
        return duration;
    }

    public void setDuration(Short duration) {
        this.duration = duration;
    }

    public Short getCnt() {
        return cnt;
    }

    public void setCnt(Short cnt) {
        this.cnt = cnt;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public String getBitrate() {
        return bitrate;
    }

    public void setBitrate(String bitrate) {
        this.bitrate = bitrate;
    }

    public Integer getIsPresell() {
        return isPresell;
    }

    public void setIsPresell(Integer isPresell) {
        this.isPresell = isPresell;
    }

    public String getFansKey() {
        return fansKey;
    }

    public void setFansKey(String fansKey) {
        this.fansKey = fansKey;
    }

    public Long getVirtualUid() {
        return virtualUid;
    }

    public void setVirtualUid(Long virtualUid) {
        this.virtualUid = virtualUid;
    }

    public Date getMergeDate() {
        return mergeDate;
    }

    public void setMergeDate(Date mergeDate) {
        this.mergeDate = mergeDate;
    }

}
