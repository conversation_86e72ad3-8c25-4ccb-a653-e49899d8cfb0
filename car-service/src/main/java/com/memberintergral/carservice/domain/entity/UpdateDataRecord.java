package com.memberintergral.carservice.domain.entity;

public class UpdateDataRecord extends BaseTable {

    private String dataTableName;//更新的数据行所在的表
    private Long dataId;//更新的数据行的id
    private String dataColumnNames;//更新的哪些列，中间用逗号隔开
    private String dataColumnSourceValues;//更新列的原始值
    private String description;//更新描述


    public String getDataTableName() {
        return dataTableName;
    }

    public void setDataTableName(String dataTableName) {
        this.dataTableName = dataTableName;
    }

    public String getDataColumnNames() {
        return dataColumnNames;
    }

    public void setDataColumnNames(String dataColumnNames) {
        this.dataColumnNames = dataColumnNames;
    }

    public String getDataColumnSourceValues() {
        return dataColumnSourceValues;
    }

    public void setDataColumnSourceValues(String dataColumnSourceValues) {
        this.dataColumnSourceValues = dataColumnSourceValues;
    }

    public Long getDataId() {
        return dataId;
    }

    public void setDataId(Long dataId) {
        this.dataId = dataId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }


}
