package com.memberintergral.carservice.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SCHEME_CONFIG")
public class SchemeConfig implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "ID", type = IdType.AUTO)
    private Long id;

    /**
     * 唯一标识码
     */
    @TableField("CODE")
    private String code;

    /**
     * 配置分类
     */
    @TableField("CATEGORY")
    private String category;

    /**
     * 配置信息
     */
    @TableField("RESOURCE")
    private String resource;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("UPDATE_TIME")
    private Date updateTime;

    /**
     * 月份
     */
    @TableField("EXT1")
    private String ext1;

    /**
     * 预留扩展字段2
     */
    @TableField("EXT2")
    private String ext2;

    /**
     * 预留扩展字段3
     */
    @TableField("EXT3")
    private String ext3;


}
