package com.memberintergral.carservice.domain.entity;

/**
 * @program: vip_adv
 * @description: 访问实体类基类
 * @author: <EMAIL>
 * @create: 2019-01-21 14:35
 */
public class BaseReqEntity {
    /**
     * 用户uId
     */
    public String uid;
    /**
     * 用户sessionId
     */
    public String sid;
    /**
     * 虚拟用户id
     */
    public String virtualUid;
    /**
     * 虚拟用户sessionId
     */
    public String virtualSid;

    /**
     * 活动标识
     */
    public String src;

    public String getSrc() {
        return src;
    }

    public void setSrc(String src) {
        this.src = src;
    }


    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getSid() {
        return sid;
    }

    public void setSid(String sid) {
        this.sid = sid;
    }

    public String getVirtualUid() {
        return virtualUid;
    }

    public void setVirtualUid(String virtualUid) {
        this.virtualUid = virtualUid;
    }

    public String getVirtualSid() {
        return virtualSid;
    }

    public void setVirtualSid(String virtualSid) {
        this.virtualSid = virtualSid;
    }
}
