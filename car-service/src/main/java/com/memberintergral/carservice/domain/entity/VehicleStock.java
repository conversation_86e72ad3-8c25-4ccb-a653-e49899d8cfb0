package com.memberintergral.carservice.domain.entity;

import java.util.Date;

public class VehicleStock {

    private Long id;
    private String uniqueActivityKey;
    private int totalStock;
    private int consumeStock;
    private Date createTime;
    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUniqueActivityKey() {
        return uniqueActivityKey;
    }

    public void setUniqueActivityKey(String uniqueActivityKey) {
        this.uniqueActivityKey = uniqueActivityKey;
    }

    public int getTotalStock() {
        return totalStock;
    }

    public void setTotalStock(int totalStock) {
        this.totalStock = totalStock;
    }

    public int getConsumeStock() {
        return consumeStock;
    }

    public void setConsumeStock(int consumeStock) {
        this.consumeStock = consumeStock;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
