package com.memberintergral.carservice.domain.entity;

import java.util.Date;

/**
 * @program: vip_adv
 * @description: 自动续费表
 * @author: <EMAIL>
 * @create: 2018-10-09 12:04
 */
public class AutoRenewalInfoVehicle {

    /**
     * id
     */
    private Long id;
    /**
     * 创建时间
     */
    private Date createdTime;
    /**
     * 原续费订单id
     */
    private Long orgOrderId;

    /**
     * 原续费订单id
     */
    private Long orgBuyOrderId;

    /**
     * 新生成，进行支付的订单	(index加索引)
     */
    private Long orderId;
    /**
     * 用户id (index加索引)
     */
    private Long userId;
    /**
     * 返回值 (李双的返回值) 返回code
     */
    private String respCode;
    /**
     * 返回值 描述
     */
    private String respDesc;
    /**
     * 到期天数(maturity)
     */
    private Short mtrtDays;
    /**
     * 操作信息(给该笔订单进行的操作,比如免费续费4天，解约等动作)
     */
    private String operInfo;
    /**
     * 完成时间
     */
    private Date completeTime;
    /**
     * 虚拟用户的id
     */
    private Long virtualUid;
    /**
     * 产品 id (用于传值)
     */
    private Long productTypeId;
    /**
     * 这个是签约的 id (用于传值)
     */
    private Long pid;

    private String orgPaySrc;
    private String orgPlatVersion;

    public String getOrgPlatVersion() {
        return orgPlatVersion;
    }

    public void setOrgPlatVersion(String orgPlatVersion) {
        this.orgPlatVersion = orgPlatVersion;
    }

    public String getOrgPaySrc() {
        return orgPaySrc;
    }

    public void setOrgPaySrc(String orgPaySrc) {
        this.orgPaySrc = orgPaySrc;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getOrgOrderId() {
        return orgOrderId;
    }

    public void setOrgOrderId(Long orgOrderId) {
        this.orgOrderId = orgOrderId;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getRespCode() {
        return respCode;
    }

    public void setRespCode(String respCode) {
        this.respCode = respCode;
    }

    public String getRespDesc() {
        return respDesc;
    }

    public void setRespDesc(String respDesc) {
        this.respDesc = respDesc;
    }

    public Short getMtrtDays() {
        return mtrtDays;
    }

    public void setMtrtDays(Short mtrtDays) {
        this.mtrtDays = mtrtDays;
    }

    public String getOperInfo() {
        return operInfo;
    }

    public void setOperInfo(String operInfo) {
        this.operInfo = operInfo;
    }

    public Date getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(Date completeTime) {
        this.completeTime = completeTime;
    }

    public Long getVirtualUid() {
        return virtualUid;
    }

    public void setVirtualUid(Long virtualUid) {
        this.virtualUid = virtualUid;
    }

    public Long getProductTypeId() {
        return productTypeId;
    }

    public void setProductTypeId(Long productTypeId) {
        this.productTypeId = productTypeId;
    }

    public Long getPid() {
        return pid;
    }

    public void setPid(Long pid) {
        this.pid = pid;
    }

    public Date getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    public Long getOrgBuyOrderId() {
        return orgBuyOrderId;
    }

    public void setOrgBuyOrderId(Long orgBuyOrderId) {
        this.orgBuyOrderId = orgBuyOrderId;
    }
}
