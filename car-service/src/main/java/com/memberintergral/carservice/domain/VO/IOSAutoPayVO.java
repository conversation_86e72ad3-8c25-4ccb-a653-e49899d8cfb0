package com.memberintergral.carservice.domain.VO;

import cn.hutool.json.JSONUtil;
import com.memberintergral.carservice.util.MD5;
import com.google.common.base.Joiner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;

@Component
public class IOSAutoPayVO {
    private static final Logger logger = LoggerFactory.getLogger(IOSAutoPayVO.class);
    /**
     * 订单
     */
    private String customer_id;

    /**
     *时间戳 秒
     */
    private String expirationDate;

    private String orderId;

    private String originOrderId;

    private String payType;

    private String productId;

    private String sign;

    private String cash;

    private String type;

    private String userId;

    private String src;

    private String service;

    private String time;


    public boolean checkValidParam(){
        Map<String,Object> map=new HashMap<>();
        map.put("expirationDate",expirationDate);
        map.put("productId",productId);
        map.put("payType",payType);
        map.put("service",service);
        map.put("userId",userId);
        map.put("cash",cash);
        map.put("customer_id",customer_id);
        map.put("time",time);
        map.put("orderId",orderId);
        map.put("originOrderId",originOrderId);
        map.put("src",src);
        map.put("type",type);
        String extSign="";
        try{
            extSign= generateNotifyUrl(map,"ios_vehicle_autopay");
            logger.info("checkValidParam sign={} extSign={} map={}",sign,extSign, JSONUtil.toJsonStr(map));
        }catch (Exception e){
            logger.error("checkValidParam sign has error!",e);
        }
        return extSign.equals(sign);
    }

    public static String generateNotifyUrl(Map<String,Object> params,String privateKey) throws UnsupportedEncodingException {
        TreeMap<String,Object> treeMap = new TreeMap<>();
        if (params!=null&&params.size()>0){
            for (Map.Entry<String,Object> entry:params.entrySet()){
                treeMap.put(entry.getKey(),entry.getValue());
            }
        }
        String waitSignData =  packageData(treeMap,false);
        MD5 md5 = new MD5();
        String sign = md5.getMD5ofStr(waitSignData+privateKey);
        return sign;
    }
    public static String packageData(TreeMap<String, Object> params, boolean urlEncode) throws UnsupportedEncodingException {
        List<String> temp = new LinkedList<>();
        Iterator<String> iterator = params.keySet().iterator();
        while (iterator.hasNext()){
            String key = iterator.next();
            String value = urlEncode? URLEncoder.encode(String.valueOf(params.get(key)),"utf-8"): String.valueOf(params.get(key));
            temp.add(key+"="+value);
        }
        return Joiner.on("&").join(temp);
    }

    public String getCustomer_id() {
        return customer_id;
    }

    public void setCustomer_id(String customer_id) {
        this.customer_id = customer_id;
    }

    public String getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(String expirationDate) {
        this.expirationDate = expirationDate;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getOriginOrderId() {
        return originOrderId;
    }

    public void setOriginOrderId(String originOrderId) {
        this.originOrderId = originOrderId;
    }

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getCash() {
        return cash;
    }

    public void setCash(String cash) {
        this.cash = cash;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getSrc() {
        return src;
    }

    public void setSrc(String src) {
        this.src = src;
    }

    public String getService() {
        return service;
    }

    public void setService(String service) {
        this.service = service;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }
}
