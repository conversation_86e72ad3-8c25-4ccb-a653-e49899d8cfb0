package com.memberintergral.carservice.domain.entity;

import java.util.Date;

/**
 * 批量兑换码
 *
 * <AUTHOR>
 */
public class BatchExchageCode {
    //自增主键id
    private Long id;
    //兑换码有效期起始时间
    private Date validBegin;
    //兑换码有效期结束时间
    private Date validEnd;
    //渠道名称
    private String channel;
    //本次生成的数量
    private int count;
    //生成兑换码的类型
    private int type;
    //是否可以进行兑换
    private int isValid;
    //兑换码类型名称
    private String exchangeName;
    //要兑换商品的有效时长
    private int exchangedProductValidDays;
    private Date createTime;
    //标记
    private String mark;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getValidBegin() {
        return validBegin;
    }

    public void setValidBegin(Date validBegin) {
        this.validBegin = validBegin;
    }

    public Date getValidEnd() {
        return validEnd;
    }

    public void setValidEnd(Date validEnd) {
        this.validEnd = validEnd;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getIsValid() {
        return isValid;
    }

    public void setIsValid(int isValid) {
        this.isValid = isValid;
    }

    public String getExchangeName() {
        return exchangeName;
    }

    public void setExchangeName(String exchangeName) {
        this.exchangeName = exchangeName;
    }

    public int getExchangedProductValidDays() {
        return exchangedProductValidDays;
    }

    public void setExchangedProductValidDays(int exchangedProductValidDays) {
        this.exchangedProductValidDays = exchangedProductValidDays;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getMark() {
        return mark;
    }

    public void setMark(String mark) {
        this.mark = mark;
    }


}
