spring:
  profiles:
    active: prod
  application:
    name: carService
  main:
    allow-circular-references: true

management:
  endpoints:
    web:
      exposure:
        include: "prometheus"
      base-path: "/actuator"
  metrics:
    web:
      server:
        request:
          autotime:
            percentiles-histogram: true
    distribution:
      slo:
        http:
          server:
            requests: PT0.005S, PT0.010S, PT0.025S, PT0.05S, PT0.075S, PT0.1S, PT0.25S, PT0.5S, PT0.75S, PT1S, PT2.5S, PT5S, PT7.5S, PT10S
      minimum-expected-value:
        http:
          server:
            requests:  PT0.005S
      maximum-expected-value:
        http:
          server:
            requests:  PT0.005S