spring:
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
      config:
        server-addr: localhost:8848
        group: DEFAULT_GROUP
    sentinel:
      transport:
        dashboard: localhost:8081
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          url: **********************************************************************************************************************
          username: root
          password: root
          driverClassName: com.mysql.cj.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
      druid:
        initial-size: 5
        minIdle: 5
        maxActive: 20
        maxWait: 60000
        timeBetweenEvictionRunsMillis: 60000
        minEvictableIdleTimeMillis: 60000
        validationQuery: SELECT 1
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        filters: stat,wall
        connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
        logSlowSql: true
redis:
  config:
    REDIS_SERVER_MASTER: 127.0.0.1:6379:apple1413
    REDIS_SERVER_SLAVE: 127.0.0.1:6379:apple1413
    REDIS_SERVER_EXFORSTATS: 127.0.0.1:6379:apple1413
    REDIS_SERVER_BUSINESS: 127.0.0.1:6379:apple1413
    SSDB_SERVER_ADDRESS: 127.0.0.1:8888
    SSDB_FENHUI_SERVER_ADDRESS: ********:8891
    SSDB_SHANHU_SERVER_ADDRESS: **********:9221:1987@wbzhyjn_REDIS:3
    d1:
      address: redis://localhost:6379
      password: apple1413
      selectDb: 2
    d2:
      address: redis://localhost:6379
      password: apple1413
      selectDb: 2
      userStringCodec: true
    d3:
      address: redis://localhost:6379
      password: apple1413
      selectDb: 0
      userStringCodec: true
dubbo:
  application:
    name: carService
  protocol:
    name: dubbo
    port: -1
  registries:
    local_1:
      address: nacos://127.0.0.1:8848?namespace=a62ae846-2e12-424a-9d71-bf58f12accf1
      group: local
  scan:
    base-packages: com.memberintergral.carservice.service


server:
  port: 8446
mybatis-plus:
  # 自定义xml文件路径
  mapper-locations: classpath:com/memberintergral/carservice/mapper/xml/*.xml
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
vip:
  domain: http://testvip.kuwo.cn
  server:
    domain: http://testvip.kuwo.cn
    boxOutPayUrl: https://h5app.kuwo.cn/m/carmembercash/index.html
    awardUrl: https://h5app.kuwo.cn/m/activitybenefits/index.html

validate:
  login:
    url : http://loginserver.kuwo.cn/u.s?type=validate&uid=%s&sid=%s&req_enc=utf8&res_enc=utf8
check:
  virtual:
    user:
      login:
        url: http://loginserver.kuwo.cn/u.s?type=virtual_valid&uid=%s&sid=%s&req_enc=utf8&res_enc=utf8

kuwo:
  monitor:
    enabled: true
    sentinel:
      address: **********:8848
      namespace: 6f61394b-0c40-42af-bb2c-72944d8439c3
      groupId: dev
      dataId: vipconf-flow-rules

dress:
  domain: http://testvip.kuwo.cn
nacos:
  config:

pay:
  server:
    domain: pay.kuwo.cn/pay_GAME
  wx:
    callback:
      url: http://vip1.kuwo.cn/vip/pay/wxweb/vehicle/callback

qrcode:
  image:
    path: /home/<USER>/apache/htdocs/star/

addticketorder:
  sequence:
    begin: 30000000001
album:
  price:
    check:
      url: http://musicpay.kuwo.cn/music.pay
carmusic:
  md5key: EavDqCpAkAvb5Rpn
  notify:
    url: http://wapi.kuwo.cn/openapi/v1/car/mini/pay/vehicle/recvPayStatus
  sign:
    url: http://car-vip.kuwo.cn/vehicle/signNotice
  unsign:
    url: http://car-vip.kuwo.cn/vehicle/relieveNotice

filepath:
  html:
    digitalarea: /home/<USER>/.resourse/html/digitalArea/
    digitalareaTemp: /home/<USER>/.resourse/html/digitalArea/temp/
    fans:
      albumTemplate: /home/<USER>/.resourse/html/fans/albumTeamplate/
      albumTemplate.temp: /home/<USER>/.resourse/html/fans/albumTeamplate/temp/
    vip:
      activityTemplate: /home/<USER>/.resourse/html/vip/activityTeamplate/
      activityTemplate.temp: /home/<USER>/.resourse/html/vip/activityTeamplate/temp/
  separate: /
image:
  access:
    path: https://pay.kuwo.cn/star/
jx:
  server:
    domain: jx.kuwo.cn
kgfx:
  server:
    domain: kwtofx.kuwo.cn
kwlogin:
  server:
    domain: i.kuwo.cn
kwpush:
  server:
    domain: wmanager.kuwo.cn
livebooking:
  validate:
    login:
      url: http://loginserver.kuwo.cn/u.s?type=validate&uid=%s&sid=%s&req_enc=utf8&res_enc=utf8

returnpath:
  html:
    digitalarea: /vip_html/digitalArea/
    digitalareaTemp: /vip_html/digitalArea/temp/
    fans:
      albumTemplate: /vip_html/fans/albumTeamplate/
      albumTemplate.temp: /vip_html/fans/albumTeamplate/temp/
    vip:
      activityTemplate: /vip_html/vip/activityTeamplate/
      activityTemplate.temp: /vip_html/vip/activityTeamplate/temp/

usergrade:
  server:
    domain: https://integralapi.kuwo.cn

order:
  server:
    domain: http://10:251:7:141:8000
NOTIFY_RECOMMEND_URL:  https://testvip.kuwo.cn/commercia/vip/recommend/refresh
sentinel:
  nacos:
    namespace: df1616de-b65e-44b6-ab0e-debdc7cca419
    groupId: local
    vip-rule-dataId: vipService-flow-rules