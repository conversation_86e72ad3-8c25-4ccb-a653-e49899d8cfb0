spring:
  cloud:
    nacos:
      discovery:
        server-addr: ************:8848
      config:
        server-addr: ************:8848
        group: DEFAULT_GROUP
    sentinel:
      transport:
        dashboard: *************:8080
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          url: jdbc:mysql://************:3340/VIP_USER?useUnicode=true&autoReconnect=true&characterEncoding=utf-8&serverTimezone=Asia/Shanghai&allowMultiQueries=true
          username: pay_vip_write
          password: vip^_^_dr521
          driverClassName: com.mysql.cj.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
        vipconf:
          url: jdbc:mysql://*************:3340/vip_conf?useUnicode=true&autoReconnect=true&characterEncoding=utf-8&allowMultiQueries=true
          username: vip_conf_read
          password: vip^_^_dr521
          driverClassName: com.mysql.cj.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
        abRead:
          url: *******************************************************************************************************************************************************
          username: vipadmin_read
          password: vip^_^_dr521
          driverClassName: com.mysql.cj.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
      druid:
        initial-size: 5
        minIdle: 5
        maxActive: 20
        maxWait: 60000
        timeBetweenEvictionRunsMillis: 60000
        minEvictableIdleTimeMillis: 60000
        validationQuery: SELECT 1
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        filters: stat,wall
        connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
        logSlowSql: true

redis:
  config:
    REDIS_SERVER_MASTER: ***********:6380:2019yyFF$%
    REDIS_SERVER_SLAVE: ***********:6380:2019yyFF$%
    REDIS_SERVER_EXFORSTATS: ***********:6380:2019yyFF$%
    REDIS_SERVER_BUSINESS: ***********:6380:2019yyFF$%
    REDIS_SERVER_LOWPRICE: ***********:6380:2019yyFF$%
    REDIS_CLOUD_STR: ***********:6380:2019yyFF$%
    REDIS_CLOUD_INDICATOR: ************:6379:SJtO$3jJlxurlnS@
NOTIFY_RECOMMEND_URL: https://vip1.kuwo.cn/commercia/vip/recommend/refresh
dubbo:
  application:
    name: car-service
  protocol:
    name: dubbo
    port: -1
  registries:
    p_1:
      address: nacos://************:8848?namespace=4566ca54-8e14-4749-a4c5-4ffc028d6377
      group: prod

  scan:
    base-packages: com.memberintergral.carservice.service
  consumer:
    filter: tracing
  cloud:
    subscribed-services: carService-provider

server:
  port: 8446
mybatis-plus:
  # 自定义xml文件路径
  mapper-locations: classpath:com/memberintergral/carservice/mapper/xml/*.xml
vip:
  server:
    domain: vip1.kuwo.cn
    boxOutPayUrl: https://h5app.kuwo.cn/m/carmembercash/index.html
    awardUrl: https://h5app.kuwo.cn/m/activitybenefits/index.html
validate:
  login:
    url : http://loginserver.kuwo.cn/u.s?type=validate&uid=%s&sid=%s&req_enc=utf8&res_enc=utf8
check:
  virtual:
    user:
      login:
        url: http://loginserver.kuwo.cn/u.s?type=virtual_valid&uid=%s&sid=%s&req_enc=utf8&res_enc=utf8

kuwo:
  monitor:
    enabled: true
    sentinel:
      address: ************:8848
      namespace: df1616de-b65e-44b6-ab0e-debdc7cca419
      groupId: prod
      dataId: carservice-flow-rules

dress:
  domain: http://vip1.kuwo.cn
nacos:
  config:
    namespace: 1fac387a-1569-45fb-9d13-6b5e4e969381
    vipadvnamespace: 05064b77-3712-442e-98c9-62efe827fd41
pay:
  server:
    domain: pay.kuwo.cn/pay
  wx:
    callback:
      url: http://vip1.kuwo.cn/vip/pay/wxweb/vehicle/callback

qrcode:
  image:
    path: /home/<USER>/apache/htdocs/star/

addticketorder:
  sequence:
    begin: 30000000001
album:
  price:
    check:
      url: http://musicpay.kuwo.cn/music.pay
carmusic:
  md5key: EavDqCpAkAvb5Rpn
  notify:
    url: http://wapi.kuwo.cn/openapi/v1/car/mini/pay/vehicle/recvPayStatus
  sign:
    url: http://car-vip.kuwo.cn/vehicle/signNotice
  unsign:
    url: http://car-vip.kuwo.cn/vehicle/relieveNotice

filepath:
  html:
    digitalarea: /home/<USER>/.resourse/html/digitalArea/
    digitalareaTemp: /home/<USER>/.resourse/html/digitalArea/temp/
    fans:
      albumTemplate: /home/<USER>/.resourse/html/fans/albumTeamplate/
      albumTemplate.temp: /home/<USER>/.resourse/html/fans/albumTeamplate/temp/
    vip:
      activityTemplate: /home/<USER>/.resourse/html/vip/activityTeamplate/
      activityTemplate.temp: /home/<USER>/.resourse/html/vip/activityTeamplate/temp/
  separate: /
image:
  access:
    path: https://pay.kuwo.cn/star/
jx:
  server:
    domain: jx.kuwo.cn
kgfx:
  server:
    domain: kwtofx.kuwo.cn
kwlogin:
  server:
    domain: i.kuwo.cn
kwpush:
  server:
    domain: wmanager.kuwo.cn
livebooking:
  validate:
    login:
      url: http://loginserver.kuwo.cn/u.s?type=validate&uid=%s&sid=%s&req_enc=utf8&res_enc=utf8

returnpath:
  html:
    digitalarea: /vip_html/digitalArea/
    digitalareaTemp: /vip_html/digitalArea/temp/
    fans:
      albumTemplate: /vip_html/fans/albumTeamplate/
      albumTemplate.temp: /vip_html/fans/albumTeamplate/temp/
    vip:
      activityTemplate: /vip_html/vip/activityTeamplate/
      activityTemplate.temp: /vip_html/vip/activityTeamplate/temp/

usergrade:
  server:
    domain: https://integralapi.kuwo.cn

#下单中间页
order:
  server:
    domain: https://h5app.kuwo.cn/apps/vipforcarplayv2/interService.html

sentinel:
  nacos:
    namespace: df1616de-b65e-44b6-ab0e-debdc7cca419
    groupId: prod
    vip-rule-dataId: carService-flow-rules
    vip-Degrade-dataId: carService-degrade-rules
