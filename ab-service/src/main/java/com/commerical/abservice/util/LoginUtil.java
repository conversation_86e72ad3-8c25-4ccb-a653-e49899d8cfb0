package com.commerical.abservice.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;
@Slf4j
public class LoginUtil {
    public static boolean isVirtualUidLogin(String uid,String virtualUid){
        if((StringUtils.isBlank(uid)||MyNumberUtils.toLONG(uid)<=0)
                &&(StringUtils.isNotBlank(virtualUid)&&MyNumberUtils.toLONG(virtualUid)>0&&virtualUid.length()>=10)){
            return true;}
        return false;
    }

    public static boolean isUidLogin(String uid,String virtualUid){
        if((StringUtils.isNotBlank(uid)&&MyNumberUtils.toLONG(uid)>0&&uid.length()>1)
                &&(StringUtils.isNotBlank(virtualUid)&&MyNumberUtils.toLONG(virtualUid)>0&&virtualUid.length()>=10)){
            return true;}
        return false;
    }

    public static boolean isValidUid(String uid){
        if(StringUtils.isNotBlank(uid)&&MyNumberUtils.toLONG(uid)>0){
            return true;}
        return false;
    }


    public static boolean checkGuideLoginRule(Integer fromType,String source,String channel){
        try{
            if(fromType!=4){
                return false;
            }
            if(StringUtils.isBlank(source)){
                return false;
            }
            source=source.toLowerCase();
            if(source.indexOf("_sdk_")>0){
                return false;
            }
            String vers=extractNumbers(source);
            if(StringUtils.isBlank(vers)||vers.contains("8550")||vers.startsWith("6")){
                return false;
            }
            if(StringUtils.isBlank(channel)){
                return false;
            }
            if(StringUtils.equals(channel,"yingmo")&&vers.startsWith("48")){
                return false;
            }
            return true;
        }catch (Exception e){
            log.error("checkGuideLoginRule has error!",e);
        }
        return false;
    }
    public static String extractNumbers(String input) {
        // 使用正则表达式匹配所有数字（包括点号）
        Pattern pattern = Pattern.compile("\\d+(\\.\\d+)*");
        Matcher matcher = pattern.matcher(input);

        if (matcher.find()) {
            // 移除点号并拼接数字
            String numbersWithDots = matcher.group();
            return numbersWithDots.replaceAll("\\.", "");
        } else {
            return null; // 如果没有找到数字，返回null或其他默认值
        }
    }

}
