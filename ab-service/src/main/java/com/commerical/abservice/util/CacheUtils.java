package com.commerical.abservice.util;


import com.commerical.abservice.enity.Gear;
import com.commerical.abservice.service.GearService;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
@Slf4j
@Component
public class CacheUtils {
    public static List<String> payDesklist= Arrays.asList("membercenter_cash","doudi_viplist_doc");

    @Value("${vip.domain}")
    private String vipDomain;


    @Autowired
    private GearService gearService;



    public final LoadingCache<Integer, Gear> getGearCache = CacheBuilder.newBuilder()
            .expireAfterWrite(15, TimeUnit.MINUTES)
            .maximumSize(1500)
            .build(new CacheLoader<Integer, Gear>() {
                @Override
                public Gear load(Integer gearId) throws Exception {
                    Gear gear=gearService.selectGearById(gearId);
                    return gear;
                }
            });


    private List<Integer> parseIdsFromKey(String key) {
        // 假设key格式为"1,2,3,..."
        String[] idStrings = key.split(",");
        return Arrays.stream(idStrings)
                .map(Integer::parseInt)
                .collect(Collectors.toList());
    }
}
