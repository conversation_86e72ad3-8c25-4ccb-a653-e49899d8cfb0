package com.commerical.abservice.rule.source;

import com.commerical.abservice.http.DataSetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Desc: 基于数据源集的加载
 * @date 2022-10-09 14:56:53
 */
@Component
public class DataSetSource implements RuleSource{

    @Autowired
    private DataSetService dataSetService;


    /**
     * 数据集生成的应该都是数组
     * @param params
     * @return
     */
    @Override
    public List loadRule(Map params) {
        String code = (String) params.get("code");
        int page = 1;
        int pageSize = 100;
        List allRules = new ArrayList();
        do {
            try {
                Optional optional = dataSetService.getDataSetByCode(code, page, pageSize);
                if (!optional.isPresent()){
                    break;
                }
                ArrayList objects = (ArrayList) optional.get();
                if (objects.isEmpty()){
                    break;
                }
                allRules.addAll(objects);
                ++page;
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }while (true);
        return allRules;
    }

    @Override
    public String getSourceType() {
        return RuleSourceType.DATA_SET.getRuleSourceName();
    }
}
