package com.commerical.abservice.rule.load;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.commerical.abservice.enity.Rule;
import com.commerical.abserviceapi.req.AbRequest;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @Desc: 基于ruleType load
 * @date 2022-10-12 10:11:29
 */
public class RuleTypeLoad {

    private static Map<String, RulesIterator> ruleMap = new ConcurrentHashMap<>();

    public static List<? extends Rule> getRuleTypeSource(AbRequest abRequest){
        String ruleType=abRequest.getRuleType();
        if (!ruleMap.containsKey(ruleType)){
            return new ArrayList<>();
        }
        List<? extends Rule> rules = ruleMap.get(ruleType).loadRules(abRequest);
        if (CollectionUtils.isEmpty(rules)){
            rules = new ArrayList<>();
        }
        return rules;
    }

    /**
     * 加载rule
     * @param ruleType
     * @param rulesIterator
     */
    public static void  loadRule(String ruleType, RulesIterator rulesIterator){
        ruleMap.put(ruleType, rulesIterator);
    }

}
