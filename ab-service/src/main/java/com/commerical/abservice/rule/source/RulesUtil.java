package com.commerical.abservice.rule.source;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @Desc:
 * @date 2022-10-09 15:21:56
 */
public class RulesUtil {

    private static Map<String, RuleSource> rules = new ConcurrentHashMap<>();


    public static void register(String key, RuleSource ruleSource){
        rules.put(key,ruleSource);
    }


    public static RuleSource getRules(String key){
        return rules.get(key);
    }
    public static RuleSource getRules(RuleSourceType ruleSourceType){
        return rules.get(ruleSourceType.getRuleSourceName());
    }



}
