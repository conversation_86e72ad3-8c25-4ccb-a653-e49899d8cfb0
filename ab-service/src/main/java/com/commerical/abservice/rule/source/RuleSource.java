package com.commerical.abservice.rule.source;

import org.springframework.beans.factory.InitializingBean;

import java.util.Map;

/**
 * 规则来源
 * 1. 数据库
 * 2. http接口
 * 3. redis xxx
 */
public interface RuleSource extends InitializingBean {

    /**
     * 加载数据返回指定类型的值
     * @param params
     * @return
     * @param <T>
     */
    <T> T loadRule(Map params);


    String getSourceType();


    default void register(){
        RulesUtil.register(getSourceType(), this);
    }

    @Override
    default void afterPropertiesSet() throws Exception {
        register();
    };

}
