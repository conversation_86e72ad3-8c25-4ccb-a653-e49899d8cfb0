package com.commerical.abservice.rule.source;

import com.commerical.abservice.enity.RecallRule;
import com.commerical.abservice.service.RecallRuleService;
import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Desc: 基于数据源集的加载
 * @date 2022-10-09 14:56:53
 */
@Component
@Slf4j
public class RecallDataSetSource implements RuleSource {

    @Autowired
    private RecallRuleService recallRuleService;

    /**
     * 数据集生成的应该都是数组
     *
     * @param params
     * @return
     */
    @Override
    public List<RecallRule> loadRule(Map params) {
        String codes = String.valueOf(params.get("codes"));
        if (StringUtils.isBlank(codes)) {
            return null;
        }
        List<String> list = Splitter.on(",").splitToList(codes);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        RecallRule query = new RecallRule();
        query.setCodes(list);
        List<RecallRule> recallRules = recallRuleService.selectByQuery(query);
        if (CollectionUtils.isEmpty(recallRules)) {
            return null;
        }
        return recallRules;
    }

    @Override
    public String getSourceType() {
        return RuleSourceType.RECALL_DATA_SET.getRuleSourceName();
    }

    @Override
    public void register() {
        RulesUtil.register(getSourceType(), this);
    }

}
