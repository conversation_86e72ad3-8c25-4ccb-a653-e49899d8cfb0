package com.commerical.abservice.rule.load;

import com.commerical.abservice.cache.PcAbRuleCache;
import com.commerical.abservice.nacos.PackageNacos;
import com.commerical.abserviceapi.cenum.RuleType;
import com.commerical.abservice.enity.Rule;
import com.commerical.abserviceapi.req.AbRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @Desc: pc获取rule数据
 * @date 2022-10-12 10:32:41
 */
@Component
@Slf4j
public class PcRuleIterator implements RulesIterator{

    @Autowired
    private PcAbRuleCache pcAbRuleCache;

    @Autowired
    private PackageNacos packageNacos;

    @Override
    public List<? extends Rule> loadRules() {
        return pcAbRuleCache.getValue("1");
    }

    @Override
    public List<? extends Rule> loadRules(AbRequest abRequest) {
          String gearId= abRequest.getGearId();
          String userId= abRequest.getUserId();
          String payDeskSign= abRequest.getPayDeskSign();
           if(isAddStandFilters(userId,payDeskSign)){
             return pcAbRuleCache.getValue("VIP_STAND_CONF_"+gearId);
           }
        return pcAbRuleCache.getValue("VIP_CONF_"+gearId);
    }

    /**
     * 添加独立策略
     */
    public boolean isAddStandFilters(String userId,String payDeskSign){
        try{
            if((StringUtils.isNotBlank(userId)&&!userId.equals("0"))&&(StringUtils.equals(payDeskSign,"limitpop")||StringUtils.equals(payDeskSign,"membercenter_cash"))){
                List<String> standUids=packageNacos.getPackageConfig().getStandUids();
                if(!CollectionUtils.isEmpty(standUids)){
                    for(String uid:standUids){
                        if(userId.endsWith(uid)){
                            return true;
                        }
                    }
                }
            }
        }catch (Exception e){
            log.error("isAddStandFilters has error!userId={}",userId,e);
        }
        return false;
    }

    @Override
    public String getRuleType() {
        return RuleType.PC_RULE_TYPE.getRuleName();
    }


}
