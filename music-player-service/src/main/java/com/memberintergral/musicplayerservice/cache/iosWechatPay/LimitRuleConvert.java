package com.memberintergral.musicplayerservice.cache.iosWechatPay;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 * @Desc:
 * @date 2022-11-18 09:54:20
 */
public class LimitRuleConvert {

    private static ConcurrentHashMap<String, LimitRule> ruleConverts = new ConcurrentHashMap<>();

    private static volatile AtomicBoolean init = new AtomicBoolean(false);

    public static void register(String key, LimitRule limitRule){
        ruleConverts.put(key, limitRule);
    }

    public static LimitRule getLimitRuleConvert(String key){
        if (!init.get()){
            init();
        }
        return ruleConverts.get(key);
    }

    public static void  init(){
        if (init.compareAndSet(false, true)){
            new WeekLimitRule().register();
            new PeriodLimitRule().register();
            new FixTimeLimitRule().register();

        }
    }

}
