package com.memberintergral.musicplayerservice.cache.dressCenter;

import com.google.common.cache.Cache;
import com.kuwo.commercialization.common.cache.RedisTTlEventType;
import com.kuwo.commercialization.common.cache.TTLCache;
import com.kuwo.commercialization.common.cache.TTLEvent;
import com.commerical.musicplayerservice.enity.PubDressCenterPic;
import com.memberintergral.musicplayerservice.mapper.vipConf.PubDressCenterPicMapper;
import com.memberintergral.musicplayerservice.util.DressCenterDetailUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Desc: 桌面图标cache
 * @date 2023-04-12 18:22:49
 */
@Component
@Slf4j
public class DeskIconCache extends TTLCache<Long, PubDressCenterPic> {

    public static final String myChangeKey = "deskIconCache";

    @Autowired
    private PubDressCenterPicMapper pubDressCenterPicMapper;

    public DeskIconCache(@Qualifier("redissonActClient") RedissonClient redissonActClient) {
        super(redissonActClient, myChangeKey, 12, TimeUnit.HOURS);
    }


    @Override
    protected PubDressCenterPic loadFromDb(Long key) {
        return pubDressCenterPicMapper.selectByPrimaryKey(key);
    }

    @Override
    protected void initCache(Cache<Long, PubDressCenterPic> cache) {
        List<PubDressCenterPic> allList = pubDressCenterPicMapper.getAllList();
        if (allList!=null && allList.size()>0){
            allList.forEach(k-> {
                k.setPic(DressCenterDetailUtil.imgUrlConvert(k.getPic()));
                getCache().put(k.getId(),k);
            });
        }
        log.info("[init deskIcon cache finish!]");

    }

    @Override
    protected void changeCacheValue(TTLEvent event) {
        if (RedisTTlEventType.RELOAD_CACHE == event.getEventType()&& myChangeKey.equals(event.getTtlChangeKey())){
            innerClearCache();
        }
    }

    @Override
    protected void innerClearCache() {
        getCache().invalidateAll();
        initCache(getCache());
    }

}
