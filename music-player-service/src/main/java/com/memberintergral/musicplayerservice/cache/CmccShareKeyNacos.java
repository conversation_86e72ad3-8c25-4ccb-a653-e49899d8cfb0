package com.memberintergral.musicplayerservice.cache;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * ios 播放器限免 从nacos获取数据
 */
@Component
public class CmccShareKeyNacos extends NacosRepository {


    private static String groupId = "common_conf";
    private static String dataId = "cmcc_share_key";
    private static long readTimeout = 1000l * 10;

    public CmccShareKeyNacos(ConfigService configService) throws NacosException {
        super(configService, groupId, dataId, readTimeout);

    }

    @Override
    protected Object convert(String configData) {
        List<Long> cache = JSONArray.parseArray(configData, Long.class);
        if (CollectionUtils.isEmpty(cache)) {
            return Lists.newArrayList();
        }
        return cache;
    }

    public List<Long> getShareKeys(){
        return  (List<Long>) this.data;
    }

    public Boolean addList(Long shareKey) throws NacosException {
        if (Objects.isNull(shareKey) || ((List<Long>) this.data).contains(shareKey)) {
            return Boolean.TRUE;
        }
        return addJsonArrayConfig(Lists.newArrayList(shareKey));
    }

}
