package com.memberintergral.musicplayerservice.cache.dressCenter;

import com.commerical.musicplayerservice.enity.VipTheme;
import com.google.common.cache.Cache;
import com.kuwo.commercialization.common.cache.RedisTTlEventType;
import com.kuwo.commercialization.common.cache.TTLCache;
import com.kuwo.commercialization.common.cache.TTLEvent;
import com.memberintergral.musicplayerservice.mapper.musicPlayer.VipThemeMapper;
import com.memberintergral.musicplayerservice.util.DressCenterDetailUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Desc: 皮肤缓存,由于皮肤列表总共几千条，直接存储到本地内存里即可
 * @date 2023-04-11 17:28:59
 */
@Component
@Slf4j
public class SkinCache extends TTLCache<String, List<VipTheme>> {

    public static final String myChangeKey = "skinCache";

    private static Map<String,VipTheme> specialVersionMap = new ConcurrentHashMap<String,VipTheme>();
    private static Map<String,VipTheme> vipThemeMap = new ConcurrentHashMap<String,VipTheme>();

    private VipThemeMapper vipThemeMapper;

    public SkinCache(@Qualifier("redissonActClient") RedissonClient redissonActClient, VipThemeMapper vipThemeMapper) {
        super(redissonActClient, myChangeKey,1, TimeUnit.HOURS);
        this.vipThemeMapper = vipThemeMapper;
    }

    @Override
    protected List<VipTheme> loadFromDb(String key) {
        List<VipTheme> all = new ArrayList<>();
        // 加载到本地cache里
        long start = 0l;
        int size = 500;
        List<VipTheme> vipThemes = null;
        do {
            vipThemes = vipThemeMapper.loadVipThemeByPage(start, size);
            if (vipThemes!=null&& vipThemes.size()>0){
                start = vipThemes.get(vipThemes.size()-1).getId();
                //转换下
                for (VipTheme theme: vipThemes){
                    theme.setCoverPic(DressCenterDetailUtil.imgUrlConvert(theme.getCoverPic()));
                    theme.setResourcePath(DressCenterDetailUtil.imgUrlConvert(theme.getResourcePath()));
                }
                all.addAll(vipThemes);
            }
        }while (vipThemes!=null && vipThemes.size()>0);

        // 构建列表
        all.stream().forEach(k->{
            String buildKey = buildSearchVersionKey(k.getSortName(), k.getPlatform(), k.getVersion());
            specialVersionMap.put(buildKey, k);
            vipThemeMap.put(String.valueOf(k.getId()), k);
        });
        return all;
    }

    @Override
    protected void initCache(Cache<String, List<VipTheme>> cache) {
        loadFromDb("1111");
        log.info("[load skin cache finished!]");
    }


    /**
     * 构建 key
     * @param sortName
     * @param plat
     * @param clientVersion
     * @return
     */
    private String buildSearchVersionKey(String sortName,String plat, String clientVersion ){
        StringBuilder builder = new StringBuilder();
        builder.append(sortName);
        builder.append("@@split@@");
        builder.append(plat);
        builder.append("@@split@@");
        builder.append(clientVersion);
        return builder.toString();
    }

    /**
     * 根据参数进行适配当前可用对主题皮肤
     * @param curSkinId
     * @param plat
     * @param clientVersion
     * @return
     */
    public VipTheme getAdaptTheme(String curSkinId, String plat, String clientVersion){
        // 这块儿主要对作用就是用来加载数据(更新数据)，可用搞成异步加载感觉
        getValue("");
        VipTheme specialTheme = null;
        VipTheme vipTheme = vipThemeMap.get(curSkinId);
        if (vipTheme!=null){
            String sortName = vipTheme.getSortName();
            String searchKey = buildSearchVersionKey(sortName, plat, clientVersion);
            specialTheme = specialVersionMap.get(searchKey);
            if (specialTheme == null){
                // 默认匹配对（张伟那边对数据导过来）
                String defaultMatchKey1 = buildSearchVersionKey(sortName, plat, "all");
                specialTheme = specialVersionMap.get(defaultMatchKey1);
                if (specialTheme == null){
                    String defaultMatchKey2 = buildSearchVersionKey(sortName, "all", "all");
                    specialTheme = specialVersionMap.get(defaultMatchKey2);
                }
            }
        }
        return specialTheme;
    }

    @Override
    protected void changeCacheValue(TTLEvent event) {
        if (RedisTTlEventType.RELOAD_CACHE == event.getEventType()&& myChangeKey.equals(event.getTtlChangeKey())){
            innerClearCache();
        }
    }

    @Override
    protected void innerClearCache() {
        getCache().invalidateAll();
        initCache(getCache());
    }
}
