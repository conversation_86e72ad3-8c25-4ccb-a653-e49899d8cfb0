package com.memberintergral.musicplayerservice.cache;

import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.memberintergral.musicplayerservice.cache
 * @date:2023/2/24
 */

@Component
public class MaxUIdTwoDayBeforeNacos extends NacosRepository {

    private static String groupId = "common_conf";
    private static String dataId = "max_uid_two_day_before";
    private static long readTimeout = 1000l * 10;
    private static long defaultUserId = 594362441L;

    public MaxUIdTwoDayBeforeNacos(ConfigService configService) throws NacosException {
        super(configService, groupId, dataId, readTimeout);
    }

    @Override
    protected Object convert(String configData) {
        Long userId = NumberUtils.toLong(configData);
        return userId;
    }

    public Long getMaxUIdTwoDayBefore(){
        if (Objects.isNull(this.data)) {
            return defaultUserId;
        }
        return NumberUtils.toLong(String.valueOf(this.data));
    }

    public Boolean update(Long userId) throws NacosException {
        if (Objects.isNull(userId)) {
            return overwriteConfig("");
        }
        return overwriteConfig(String.valueOf(userId));
    }
}
