package com.memberintergral.musicplayerservice.cache.audioConfig;

import com.google.common.cache.Cache;
import com.kuwo.commercialization.common.cache.RedisTTlEventType;
import com.kuwo.commercialization.common.cache.TTLCache;
import com.kuwo.commercialization.common.cache.TTLEvent;
import com.memberintergral.musicplayerservice.entity.AudioRecommendDTO;
import com.memberintergral.musicplayerservice.mapper.vipConf.AudioRecommendMapper;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 * @Desc:  识别连接设备推音质
 */
@Component
public class AudioRecommendCache extends TTLCache<String, List<AudioRecommendDTO>> {

    public static final String myChangeKey = "AudioRecommendConfigCache";

    @Autowired
    private AudioRecommendMapper audioRecommendMapper;

    private List<AudioRecommendDTO> recommendConfigs = new ArrayList<>(1000);

    public AudioRecommendCache(@Qualifier("redissonActClient") RedissonClient redissonActClient, AudioRecommendMapper recommendMapper) {
        super(redissonActClient, myChangeKey, 30, TimeUnit.MINUTES);
        this.audioRecommendMapper = recommendMapper;
    }

    @Override
    protected List<AudioRecommendDTO> loadFromDb(String key) {
        AtomicLong id = new AtomicLong();
        int pageSize = 10;
        recommendConfigs.clear();
        List<AudioRecommendDTO> recommends = null;
        do {
            recommends =  audioRecommendMapper.getAudioRecommendConfig(id.get(), pageSize);
            recommends = Optional.of(recommends).orElse(new ArrayList<>());
            recommends.forEach(k-> {recommendConfigs.add(k); id.set(k.getId());});
        } while (recommends.size() > 0);
        return recommendConfigs;
    }

    @Override
    protected void initCache(Cache<String, List<AudioRecommendDTO>> cache) {
        loadFromDb(null);
    }



    @Override
    protected void changeCacheValue(TTLEvent event) {
        if (RedisTTlEventType.RELOAD_CACHE == event.getEventType() && myChangeKey.equals(event.getTtlChangeKey())){
            clearCache();
        }
    }

    private void clearCache() {
        getCache().invalidateAll();
        initCache(getCache());
    }
}
