package com.memberintergral.musicplayerservice.cache;

import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.memberintergral.musicplayerservice.util.recallStrategy.common.RedisKey;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.memberintergral.musicplayerservice.cache
 * @date:2022/12/23
 */

@Component
public class RecallPopNacos extends NacosRepository {


    private static String groupId = "common_conf";
    private static String dataId = "recall_pop";
    private static long readTimeout = 1000l * 10;

    private Gson gson;

    public RecallPopNacos(ConfigService configService) throws NacosException {
        super(configService, groupId, dataId, readTimeout);
    }

    @Override
    protected Object convert(String configData) {

        if (this.gson == null){
            this.gson = new Gson();
        }
        Map<String, Double> cache = gson.fromJson(configData, Map.class);

        if (cache == null) {
            cache = Maps.newHashMap();
        }

        return cache;
    }


    /**
     * 判断是否需要展示
     * @return
     */
    public Integer getNacosData(String key){
        Map<String, Double> map = (Map<String, Double>) this.data;
        if (map != null && map.containsKey(key)) {
            Double aDouble = map.get(key);
            if (aDouble != null) {
                return aDouble.intValue();
            }
        }
        return RedisKey.NOT_ENABLED_RECALL_RECALLSTRATEGY;
    }

}
