package com.memberintergral.musicplayerservice.cache.iosWechatPay;

import com.google.gson.internal.LinkedTreeMap;
import com.kuwo.commercialization.common.utill.DateUtil;
import com.memberintergral.musicplayerservice.util.recallStrategy.common.RedisKey;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Desc:
 * @date 2022-11-17 20:23:38
 */
@Data
public class PeriodLimitRule  implements LimitRule{

    private String type;

    private int duration;

    private int nums;

    private int interval;

    private List<Time> openTimes;

    @Override
    public String getLimitType() {
        return "period";
    }

    @Override
    public LimitRule parseToObj(LinkedTreeMap enity) {
        PeriodLimitRule periodLimitRule = new PeriodLimitRule();
        if (enity.containsKey("type")){
            periodLimitRule.setType(String.valueOf(enity.get("type")));
        }
        if (enity.containsKey("duration")){
            periodLimitRule.setDuration((int) Double.parseDouble(String.valueOf(enity.get("duration"))));
        }
        if (enity.containsKey("nums")){
            periodLimitRule.setNums((int) Double.parseDouble(String.valueOf(enity.get("nums"))));
        }
        if (enity.containsKey("interval")){
            periodLimitRule.setInterval((int) Double.parseDouble(String.valueOf(enity.get("interval"))));
        }
        if (enity.containsKey("open_times")){
            ArrayList _open_time_list = (ArrayList) enity.get("open_times");
            ArrayList<Time> open_times = new ArrayList<>();
            if (!CollectionUtils.isEmpty(_open_time_list)){
                for (Object o: _open_time_list){
                    LinkedTreeMap timeMap = (LinkedTreeMap) o;
                    Time time = new Time();
                    if (timeMap.containsKey("start")){
                        time.setStart(String.valueOf(timeMap.get("start")));
                    }
                    if (timeMap.containsKey("end")){
                        time.setEnd(String.valueOf(timeMap.get("end")));
                    }
                    open_times.add(time);
                }
            }
            periodLimitRule.setOpenTimes(open_times);
        }

        return periodLimitRule;
    }

    @Override
    public boolean validateShouldDisplay(LimitRuleContext limitRuleContext) {
        boolean matched = true;
        String province = limitRuleContext.getProvince();
        String city = limitRuleContext.getCity();
        RedissonClient redissonClient = limitRuleContext.getRedissonClient();
        // 控制一下展示的频率
        int current = 0;
        int max = 0;
        long last_access = 0l;
        long next_access = 0l;

        RMapCache<Object, Object> mapCache = redissonClient.getMapCache(RedisKey.APPLE_IOS_WECHAT_PAY_PERIOD_KEY);
        if (matched && StringUtils.isNotBlank(province) && mapCache.containsKey(province)){
            String info = (String) mapCache.get(province);
            String[] s = info.split("_");
            current = Integer.parseInt(s[0]);
            max = Integer.parseInt(s[1]);
            last_access = Long.parseLong(s[2]);
            next_access = Long.parseLong(s[3]);
            // 针对修改的地方做个限制
            if (nums> max){
                max = nums;
            }
            if (current>= max){
                return false;
            }
            if (System.currentTimeMillis() - last_access <= 1000l*24*60*60*interval){
                return false;
            }
        }
        if (matched && StringUtils.isNotBlank(city) && mapCache.containsKey(city)){
            String info = (String) mapCache.get(city);
            String[] s = info.split("_");
            current = Integer.parseInt(s[0]);
            max = Integer.parseInt(s[1]);
            last_access = Long.parseLong(s[2]);
            next_access = Long.parseLong(s[3]);
            if (nums> max){
                max = nums;
            }
            if (current>= max){
                return false;
            }
            if (System.currentTimeMillis() - last_access <= 1000l*24*60*60*interval){
                return false;
            }
        }
        Calendar date = Calendar.getInstance();
        date.setTime(new Date());
        if (matched && !CollectionUtils.isEmpty(openTimes)){
            matched = openTimes.stream().anyMatch(time -> DateUtil.validateInHourTime(date,time.getStart(), time.getEnd(),"HH:mm:ss"));
        }
        if (matched){
            max = nums;
            current ++;
            long now = System.currentTimeMillis();
            long nextAccessTime = now+ 1000l*24*60*60*interval;
            String msg = "" +current+"_"+max+"_"+ now + "_"+nextAccessTime;
            if (StringUtils.isNotBlank(province)){
                mapCache.put(province,msg,duration, TimeUnit.DAYS);
            }
            if (StringUtils.isNotBlank(city)){
                mapCache.put(city,msg,duration, TimeUnit.DAYS);
            }
        }
        return matched;
    }
}
