package com.memberintergral.musicplayerservice.cache.dressCenter;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import com.memberintergral.musicplayerservice.cache.NacosRepository;
import org.springframework.stereotype.Component;

/**
 * 从nacos获取数据
 */
@Component
public class TabListDataNacos extends NacosRepository {


    private static String dataId = "dress_center_tab_list";
    private static long readTimeout = 1000l * 10;
    private static String groupId = "common_conf";

    private JSONArray defaultData;

    public TabListDataNacos(ConfigService configService) throws NacosException {
        super(configService, groupId, dataId, readTimeout);

    }

    @Override
    protected Object convert(String configData) {
        defaultData = JSONArray.parseArray(configData);
        return defaultData;
    }
    public JSONArray getTabListDefaultData(){
        return defaultData;
    }

}
