package com.memberintergral.musicplayerservice.cache;

import com.google.common.cache.Cache;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.kuwo.commercialization.common.cache.TTLCache;
import com.kuwo.commercialization.common.http.VipDomainHttpRequest;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Desc: 支付挽留cache
 * @date 2024-07-25 21:16:07
 */
@Component
public class VipCenterPayNewRetentionConfigCache extends TTLCache<String, Map> {

    private VipDomainHttpRequest request;

    private static Gson gson = new Gson();

    public static final String myChangeKey = "VipCenterPayNewRetentionConfigCache";

    public static final String defaultReturn = "{\"btnList\":[{\"switch\":\"0\",\"btnText\":\"\",\"btnUrl\":\"\",\"starTagIds\":\"\"}]}";

    public static final Map defaultNap = gson.fromJson(defaultReturn, Map.class);


    public VipCenterPayNewRetentionConfigCache(@Qualifier("redissonNewVipTransferClient") RedissonClient redissonNewVipTransferClient, VipDomainHttpRequest request) {
        super(redissonNewVipTransferClient, myChangeKey,30, TimeUnit.SECONDS);
        this.request = request;

    }

    @Override
    protected Map loadFromDb(String key) {
        try {
            String detainment4vipCenter = (String) request.getDataSetItem(Lists.newArrayList("detainment4vipCenterNew")).get("detainment4vipCenterNew");
            return gson.fromJson(detainment4vipCenter, Map.class);
        } catch (IOException e) {
           return  defaultNap;
        }
    }

    @Override
    protected void initCache(Cache<String, Map> cache) {
        loadFromDb("");
    }


    /**
     * 获取会员挽留模式cache
     * @param isIosAuditModel
     * @return
     */
    public Map getPayPageRetentionConfig(boolean isIosAuditModel) {
        if (isIosAuditModel) {
            return defaultNap;
        }
        return getValue("");
    }

}
