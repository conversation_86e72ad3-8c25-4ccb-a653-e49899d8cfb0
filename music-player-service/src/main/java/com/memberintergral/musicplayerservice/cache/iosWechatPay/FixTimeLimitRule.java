package com.memberintergral.musicplayerservice.cache.iosWechatPay;

import com.google.gson.internal.LinkedTreeMap;
import com.kuwo.commercialization.common.utill.DateUtil;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 基于固定时间范围的limit rule
 */
@Data
public class FixTimeLimitRule implements LimitRule{

    private String type;

    private List<Time> openDates;

    private List<Time> excludeDates;



    @Override
    public String getLimitType() {
        return "fixTime";
    }

    @Override
    public LimitRule parseToObj(LinkedTreeMap enity) {
        FixTimeLimitRule fixTimeLimitRule = new FixTimeLimitRule();
        if (enity.containsKey("type")){
            fixTimeLimitRule.setType(String.valueOf(enity.get("type")));
        }
        if (enity.containsKey("openDates")){
            ArrayList _open_dates_list = (ArrayList) enity.get("openDates");
            ArrayList<Time> open_dates = new ArrayList<>();
            if (!CollectionUtils.isEmpty(_open_dates_list)){
                for (Object o: _open_dates_list){
                    LinkedTreeMap timeMap = (LinkedTreeMap) o;
                    Time time = new Time();
                    if (timeMap.containsKey("start")){
                        time.setStart(String.valueOf(timeMap.get("start")));
                    }
                    if (timeMap.containsKey("end")){
                        time.setEnd(String.valueOf(timeMap.get("end")));
                    }
                    open_dates.add(time);
                }
            }
            fixTimeLimitRule.setOpenDates(open_dates);
        }
        if (enity.containsKey("excludeDates")){
            ArrayList _exclude_dates_list = (ArrayList) enity.get("excludeDates");
            ArrayList<Time> exclude_dates = new ArrayList<>();
            if (!CollectionUtils.isEmpty(_exclude_dates_list)){
                for (Object o: _exclude_dates_list){
                    LinkedTreeMap timeMap = (LinkedTreeMap) o;
                    Time time = new Time();
                    if (timeMap.containsKey("start")){
                        time.setStart(String.valueOf(timeMap.get("start")));
                    }
                    if (timeMap.containsKey("end")){
                        time.setEnd(String.valueOf(timeMap.get("end")));
                    }
                    exclude_dates.add(time);
                }
            }
            fixTimeLimitRule.setExcludeDates(exclude_dates);
        }
        return fixTimeLimitRule;
    }

    @Override
    public boolean validateShouldDisplay(LimitRuleContext limitRuleContext) {
        boolean matched = true;
        if (!CollectionUtils.isEmpty(openDates)){
            matched = openDates.stream().anyMatch(time -> DateUtil.between(new Date(),DateUtil.parseDate(time.getStart(),DateUtil.STANDARD_DAY_HOUR_FORMAT), DateUtil.parseDate(time.getEnd(),DateUtil.STANDARD_DAY_HOUR_FORMAT)));
        }
        if (matched && !CollectionUtils.isEmpty(excludeDates)){
            matched = matched && !excludeDates.stream().anyMatch(time -> DateUtil.between(new Date(),DateUtil.parseDate(time.getStart(),DateUtil.STANDARD_DAY_HOUR_FORMAT), DateUtil.parseDate(time.getEnd(),DateUtil.STANDARD_DAY_HOUR_FORMAT)));
        }
        return matched;
    }
}
