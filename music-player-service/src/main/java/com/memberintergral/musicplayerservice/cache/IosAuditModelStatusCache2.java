package com.memberintergral.musicplayerservice.cache;

import com.google.common.cache.Cache;
import com.kuwo.commercialization.common.cache.TTLCache;
import com.kuwo.commercialization.common.http.EcomRemoteRequest;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.StringJoiner;
import java.util.concurrent.TimeUnit;

/**
 * 判断当前版本是否是审核模式的cache
 */
@Component
public class IosAuditModelStatusCache2 extends TTLCache<String, String> {

    public static final String myChangeKey = "iosAuditModelStatusConfig2";

    public static final String SPLIT_FLAG = "@@split@@";


    private EcomRemoteRequest ecomRemoteRequest;

    public IosAuditModelStatusCache2(@Qualifier("redissonNewVipTransferClient") RedissonClient redissonNewVipTransferClient, EcomRemoteRequest ecomRemoteRequest) {
        super(redissonNewVipTransferClient, myChangeKey, 10, TimeUnit.MINUTES);
        this.ecomRemoteRequest = ecomRemoteRequest;
    }

    @Override
    protected String loadFromDb(String key) {
        String vers= "";
        String appUid= "";
        String src= "";
        String uid= "";
        try {
            String[] split = key.split(SPLIT_FLAG);
            vers = split[0];
            appUid = split[1];
            src = split[2];
            uid = split[3];
            return  ecomRemoteRequest.getIosAuditConfig(vers, appUid, src, uid);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    @Override
    protected void initCache(Cache<String, String> cache) {

    }

    /**
     * 生成key
     * @return
     */
    public static String makeKey(){
        StringJoiner stringJoiner = new StringJoiner(SPLIT_FLAG);
        stringJoiner.add("111111");
        stringJoiner.add("222222");
        stringJoiner.add("musicPlayService");
        stringJoiner.add("111111");
        return stringJoiner.toString();
    }

    /**
     * 判断当前是否是审核模式
     * @return
     */
    public boolean isAuditModel(String src){
        String targetValue = getValue(makeKey());
        return src.equals(targetValue);
    }


}
