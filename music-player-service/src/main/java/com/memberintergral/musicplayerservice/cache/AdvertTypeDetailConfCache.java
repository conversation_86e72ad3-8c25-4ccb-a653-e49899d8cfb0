package com.memberintergral.musicplayerservice.cache;
import com.google.common.cache.Cache;
import com.kuwo.commercialization.common.cache.RedisTTlEventType;
import com.kuwo.commercialization.common.cache.TTLCache;
import com.kuwo.commercialization.common.cache.TTLEvent;
import com.memberintergral.musicplayerservice.entity.AdvertTypeDetailConf;
import com.memberintergral.musicplayerservice.mapper.musicPlayer.AdvertTypeDetailConfMapper;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Desc: 配置缓存
 */
@Component
@Slf4j
public class AdvertTypeDetailConfCache extends TTLCache<String, List<AdvertTypeDetailConf>> {

    public static final String myChangeKey = "advertTypeDetailConfCache";

    private static Map<String,AdvertTypeDetailConf> specialVersionMap = new ConcurrentHashMap<String,AdvertTypeDetailConf>();
    private AdvertTypeDetailConfMapper advertTypeDetailConfMapper;

    public AdvertTypeDetailConfCache(@Qualifier("redissonActClient") RedissonClient redissonActClient, AdvertTypeDetailConfMapper advertTypeDetailConfMapper) {
        super(redissonActClient, myChangeKey,1, TimeUnit.HOURS);
        this.advertTypeDetailConfMapper = advertTypeDetailConfMapper;
    }

    @Override
    protected List<AdvertTypeDetailConf> loadFromDb(String key) {
        AdvertTypeDetailConf search=new AdvertTypeDetailConf();
        search.setAwardType(-1);
        search.setUserType(-1);
        search.setConfType(11);
        List<AdvertTypeDetailConf>objs=advertTypeDetailConfMapper.selectByCondition(search);
        if(objs==null||objs.size()==0){
            objs=new ArrayList<AdvertTypeDetailConf>();
        }
        // 构建列表
        objs.stream().forEach(k->{
            String buildKey = buildSearchVersionKey(String.valueOf(k.getAwardType()), String.valueOf(k.getUserType()), String.valueOf(k.getConfType()));
            specialVersionMap.put(buildKey, k);
        });
        return objs;
    }

    @Override
    protected void initCache(Cache<String, List<AdvertTypeDetailConf>> cache) {
        loadFromDb("1111");
        log.info("[load skin cache finished!]");
    }

    public AdvertTypeDetailConf getAdvertTypeDetailConf(String awardType,String userType,String confType){
        getValue("");
        AdvertTypeDetailConf obj=null;
        String defaultMatchKey = buildSearchVersionKey(awardType, userType, confType);
        if(specialVersionMap.get(defaultMatchKey)!=null){
            obj=specialVersionMap.get(defaultMatchKey);
        }
        return obj;
    }

    /**
     * 构建 key
     * @return
     */
    private String buildSearchVersionKey(String awardType,String userType, String confType ){
        StringBuilder builder = new StringBuilder();
        builder.append(awardType);
        builder.append("@@split@@");
        builder.append(userType);
        builder.append("@@split@@");
        builder.append(confType);
        return builder.toString();
    }


    @Override
    protected void changeCacheValue(TTLEvent event) {
        if (RedisTTlEventType.RELOAD_CACHE == event.getEventType()&& myChangeKey.equals(event.getTtlChangeKey())){
            innerClearCache();
        }
    }

    @Override
    protected void innerClearCache() {
        getCache().invalidateAll();
        initCache(getCache());
    }
}
