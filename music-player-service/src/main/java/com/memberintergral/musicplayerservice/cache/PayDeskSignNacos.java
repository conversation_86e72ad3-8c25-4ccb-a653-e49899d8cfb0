package com.memberintergral.musicplayerservice.cache;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import com.google.common.collect.Lists;
import com.memberintergral.musicplayerservice.entity.dto.PayDeskSignDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * ios 播放器限免 从nacos获取数据
 */
@Component
public class PayDeskSignNacos extends NacosRepository {


    private static String groupId = "common_conf";
    private static String dataId = "pay_desk_sign";
    private static long readTimeout = 1000l * 10;

    private Map<String, List<PayDeskSignDTO>> payDeskSignMap;

    public PayDeskSignNacos(ConfigService configService) throws NacosException {
        super(configService, groupId, dataId, readTimeout);

    }

    @Override
    protected Object convert(String configData) {
        List<PayDeskSignDTO> cache = JSONObject.parseArray(configData, PayDeskSignDTO.class);
        if (CollectionUtils.isEmpty(cache)) {
            return Lists.newArrayList();
        }
        payDeskSignMap = cache.stream().peek(e -> {
            if (StringUtils.isBlank(e.getUpdateTime())) {
                e.setUpdateTime(getDateString());
            }
        }).sorted(Comparator.comparing(PayDeskSignDTO::getUpdateTime)).collect(Collectors.groupingBy(PayDeskSignDTO::getCode));
        return cache;
    }

    public List<PayDeskSignDTO> getListByCode(String code){
        if(MapUtils.isEmpty(payDeskSignMap) || this.data == null){
            return Lists.newArrayList();
        }else{
            if (StringUtils.isBlank(code)) {
                return (List<PayDeskSignDTO>)this.data;
            }
            List<PayDeskSignDTO> PayDeskSignDTOS = payDeskSignMap.get(code);
            if (CollectionUtils.isEmpty(PayDeskSignDTOS)) {
                return Lists.newArrayList();
            }
            return PayDeskSignDTOS;
        }
    }

    public Boolean addList(List<PayDeskSignDTO> PayDeskSignDTOS) throws NacosException {
        if (CollectionUtils.isEmpty(PayDeskSignDTOS)) {
            return Boolean.TRUE;
        }
        String dateString = getDateString();
        PayDeskSignDTOS.stream().filter(e -> StringUtils.isBlank(e.getUpdateTime())).forEach(e -> e.setUpdateTime(dateString));
        return addJsonArrayConfig(PayDeskSignDTOS);
    }

    private String getDateString(){
        String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";
        String sTime = DateFormatUtils.format(new Date(), DATE_FORMAT);
        return sTime;
    }

}
