package com.memberintergral.musicplayerservice.cache.dressCenter;

import com.commerical.musicplayerservice.enity.PlayerStyleDTO;
import com.google.common.cache.Cache;
import com.kuwo.commercialization.common.cache.RedisTTlEventType;
import com.kuwo.commercialization.common.cache.TTLCache;
import com.kuwo.commercialization.common.cache.TTLEvent;
import com.memberintergral.musicplayerservice.mapper.musicPlayer.PlayerStyleMapper;
import com.memberintergral.musicplayerservice.service.PlayerStyleService;
import com.memberintergral.musicplayerservice.util.DressCenterDetailUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Desc: 播放器样式相关cache
 * @date 2023-04-12 15:08:06
 */
@Component
@Slf4j
public class PlayerStyleCache  extends TTLCache<String, PlayerStyleDTO> {

    @Autowired
    private PlayerStyleMapper playerStyleMapper;

    @Autowired
    private PlayerStyleService playerStyleService;

    private static final String SPLIT = "@@split@@";

    public static final String myChangeKey = "playerStyleCache";
    public PlayerStyleCache(@Qualifier("redissonActClient") RedissonClient redissonActClient) {
        super(redissonActClient, myChangeKey,1, TimeUnit.HOURS);
    }

    @Override
    protected PlayerStyleDTO loadFromDb(String key) {
        String[] myKey = key.split(SPLIT);
        return playerStyleService.getByCodeAndPlat(myKey[0],myKey[1]);
    }

    public static String buildSearchKey(String styleCode, String plat){
       return styleCode+SPLIT+plat;
    }




    @Override
    protected void initCache(Cache<String, PlayerStyleDTO> cache) {
        List<PlayerStyleDTO> playerStyleDTOS = playerStyleMapper.selectAllPlayerStyles();
        if (playerStyleDTOS!=null && playerStyleDTOS.size()>0){
           playerStyleDTOS.forEach(k-> {
               k.setPreviewImgUrl(DressCenterDetailUtil.imgUrlConvert(k.getPreviewImgUrl()));
               getCache().put(buildSearchKey(k.getStyleCode(),k.getPlatform()),k);
           });
        }
        log.info("[init playerStyle cache finish!]");
    }

    @Override
    protected void changeCacheValue(TTLEvent event) {
        if (RedisTTlEventType.RELOAD_CACHE == event.getEventType()&& myChangeKey.equals(event.getTtlChangeKey())){
            innerClearCache();
        }
    }

    @Override
    protected void innerClearCache() {
        getCache().invalidateAll();
        initCache(getCache());
    }
}
