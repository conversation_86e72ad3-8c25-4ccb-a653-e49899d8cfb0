package com.memberintergral.musicplayerservice.cache;
import com.google.common.cache.Cache;
import com.kuwo.commercialization.common.cache.RedisTTlEventType;
import com.kuwo.commercialization.common.cache.TTLCache;
import com.kuwo.commercialization.common.cache.TTLEvent;
import com.memberintergral.musicplayerservice.entity.PlayerClientTypeDTO;
import com.memberintergral.musicplayerservice.mapper.musicPlayer.PlayerClientTypeMapper;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class PlayerClientTypeCache extends TTLCache<String, List<PlayerClientTypeDTO>> {

    public static final String myChangeKey = "playerClientType";
    @Autowired
    private PlayerClientTypeMapper playerClientTypeMapper;

    private List<PlayerClientTypeDTO> playerClientTypeMappers = new ArrayList<>();

    private static Map<String, PlayerClientTypeDTO> clientTypeMap = new ConcurrentHashMap<>();

    public PlayerClientTypeCache(@Qualifier("redissonActClient") RedissonClient redissonActClient, PlayerClientTypeMapper playerClientTypeMapper) {
        super(redissonActClient, myChangeKey);
        this.playerClientTypeMapper = playerClientTypeMapper;
    }

    @Override
    protected List<PlayerClientTypeDTO> loadFromDb(String key) {
        playerClientTypeMappers.clear();
        clientTypeMap.clear();
        List<PlayerClientTypeDTO> objs = playerClientTypeMapper.selectAll();
        if(objs!=null&&objs.size()>0){
            for(PlayerClientTypeDTO obj:objs){
                if(obj.getClientTypeId()!=null){
                    clientTypeMap.put(String.valueOf(obj.getClientTypeId()),obj);
                }
            }
        }
        return objs;
    }

    @Override
    protected void initCache(Cache<String, List<PlayerClientTypeDTO>> cache) {
        loadFromDb(null);
    }

    public Integer getApivLimit(String clientTypeCode){
        Integer apivLimit=0;
        if(StringUtils.isNotEmpty(clientTypeCode)&&clientTypeMap.get(clientTypeCode)!=null
            &&clientTypeMap.get(clientTypeCode).getApivLimit()!=null){
            apivLimit=clientTypeMap.get(clientTypeCode).getApivLimit();
        }
        return apivLimit;
    }


    @Override
    protected void changeCacheValue(TTLEvent event) {
        if (RedisTTlEventType.RELOAD_CACHE == event.getEventType() && myChangeKey.equals(event.getTtlChangeKey())){
            clearCache();
        }
    }

    private void clearCache() {
        getCache().invalidateAll();
        initCache(getCache());
    }
}
