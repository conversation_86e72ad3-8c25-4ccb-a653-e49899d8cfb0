package com.memberintergral.musicplayerservice.cache;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import com.memberintergral.musicplayerservice.entity.dto.LowPriceDTO;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 从nacos获取数据
 */
@Component
public class LowPriceNacos extends NacosRepository {


    private static String groupId = "common_conf";
    private static String dataId = "low_price_vip_conf";
    private static long readTimeout = 1000l * 10;

    private List<LowPriceDTO> lowPriceDTO;

    public LowPriceNacos(ConfigService configService) throws NacosException {
        super(configService, groupId, dataId, readTimeout);

    }

    @Override
    protected Object convert(String configData) {
        lowPriceDTO = JSONArray.parseArray(configData, LowPriceDTO.class);
        return lowPriceDTO;
    }

    /**
     * 判断是否需要展示
     * @return
     */
    public List<LowPriceDTO> getLowPriceDTO(){
        return lowPriceDTO;
    }

}
