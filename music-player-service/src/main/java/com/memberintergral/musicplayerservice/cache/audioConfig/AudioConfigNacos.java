package com.memberintergral.musicplayerservice.cache.audioConfig;

import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.memberintergral.musicplayerservice.cache.NacosRepository;
import com.memberintergral.musicplayerservice.entity.dto.UserBlankDTO;
import com.memberintergral.musicplayerservice.util.recallStrategy.common.RedisKey;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 兜底数据 开关
 */
@Component
public class AudioConfigNacos extends NacosRepository {

    private static String groupId = "common_conf";
    private static String dataId = "device_switch";
    private static long readTimeout = 1000l * 10;

    public final static String DOUDI_SWITCH_KEY_AR = "doudiSwtichAndroid";
    public final static String DOUDI_SWITCH_KEY_IOS = "doudiSwtichIOS";

    private Gson gson;

    private Map<String, List<UserBlankDTO>> userBlankMap;

    public AudioConfigNacos(ConfigService configService) throws NacosException {
        super(configService, groupId, dataId, readTimeout);

    }

    @Override
    protected Object convert(String configData) {
        if (this.gson == null){
            this.gson = new Gson();
        }
        Map<String, Integer> cache = gson.fromJson(configData, Map.class);

        if (cache == null) {
            cache = Maps.newHashMap();
        }

        return cache;
    }


    /**
     * 判断是否需要展示
     * @return
     */
    public Integer getNacosData(String key) {
        Map<String, Integer> map = (Map<String, Integer>) this.data;
        if (map != null && map.containsKey(key)) {
            Integer val = map.get(key);
            if (val != null) {
                return val;
            }
        }
        return RedisKey.INT_YES_FLAG;
    }

}
