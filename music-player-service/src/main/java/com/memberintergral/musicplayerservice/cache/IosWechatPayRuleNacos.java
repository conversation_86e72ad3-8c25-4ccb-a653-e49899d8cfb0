package com.memberintergral.musicplayerservice.cache;

import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import com.google.gson.Gson;
import com.google.gson.internal.LinkedTreeMap;
import com.memberintergral.musicplayerservice.cache.iosWechatPay.IosWechatPayRule;
import com.memberintergral.musicplayerservice.cache.iosWechatPay.LimitRule;
import com.memberintergral.musicplayerservice.cache.iosWechatPay.LimitRuleContext;
import com.memberintergral.musicplayerservice.cache.iosWechatPay.LimitRuleConvert;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * ios 微信支付开关 从nacos获取数据
 */
@Component
public class IosWechatPayRuleNacos extends NacosRepository {


    private static String groupId = "switch_config";
    private static String dataId = "ios_wechat_pay";
    private static long readTimeout = 1000l * 10;

    private Gson gson;

    private RedissonClient redissonNewVipTransferClient;

    public IosWechatPayRuleNacos(ConfigService configService, @Qualifier("redissonNewVipTransferClient") RedissonClient redissonNewVipTransferClient) throws NacosException {
        super(configService, groupId, dataId, readTimeout);
        this.redissonNewVipTransferClient = redissonNewVipTransferClient;

    }

    @Override
    protected Object convert(String configData) {
        List<IosWechatPayRule> iosWechatPayRules = new ArrayList<>();
        if (this.gson == null){
            this.gson = new Gson();
        }
        Map cache = gson.fromJson(configData, Map.class);
        if (cache.containsKey("rules")){
            ArrayList rules = (ArrayList) cache.get("rules");
            if (!CollectionUtils.isEmpty(rules)){
                for (Object rule: rules){
                    LinkedTreeMap _rule = (LinkedTreeMap) rule;
                    IosWechatPayRule iosWechatPayRule = new IosWechatPayRule();
                    if (_rule.containsKey("tag")){
                        iosWechatPayRule.setTag(String.valueOf(_rule.get("tag")));
                    }
                    if (_rule.containsKey("displayStatus")){
                        iosWechatPayRule.setDisplayStatus((int) Double.parseDouble(String.valueOf(_rule.get("displayStatus"))));
                    }
                    if(_rule.containsKey("citys")){
                        ArrayList _citys = (ArrayList) _rule.get("citys");
                        if (!CollectionUtils.isEmpty(_citys)){
                            List<String> citys = new ArrayList<>();
                            for (Object c: _citys){
                                citys.add(String.valueOf(c));
                            }
                            iosWechatPayRule.setCitys(citys);
                        }
                    }
                    if(_rule.containsKey("provinces")){
                        ArrayList _provinces = (ArrayList) _rule.get("provinces");
                        if (!CollectionUtils.isEmpty(_provinces)){
                            List<String> provinces = new ArrayList<>();
                            for (Object c: _provinces){
                                provinces.add(String.valueOf(c));
                            }
                            iosWechatPayRule.setProvinces(provinces);
                        }
                    }
                    if(_rule.containsKey("excludeCities")){
                        ArrayList _excludeCitys = (ArrayList) _rule.get("excludeCities");
                        if (!CollectionUtils.isEmpty(_excludeCitys)){
                            List<String> citys = new ArrayList<>();
                            for (Object c: _excludeCitys){
                                citys.add(String.valueOf(c));
                            }
                            iosWechatPayRule.setExcludeCities(citys);
                        }
                    }
                    if (_rule.containsKey("limits")){
                       ArrayList _limits = (ArrayList) _rule.get("limits");
                       List<LimitRule> limits = new ArrayList<>();
                       if (!CollectionUtils.isEmpty(_limits)){
                            for (Object _limit: _limits){
                                LinkedTreeMap t_limit = (LinkedTreeMap) _limit;
                                String type = String.valueOf(t_limit.get("type"));
                                LimitRule limitRuleConvert = LimitRuleConvert.getLimitRuleConvert(type);
                                if (limitRuleConvert!=null){
                                    limits.add(limitRuleConvert.parseToObj(t_limit));
                                }
                            }
                       }
                       iosWechatPayRule.setLimits(limits);
                    }
                    iosWechatPayRules.add(iosWechatPayRule);
                }
            }
        }
        return iosWechatPayRules;
    }


    /**
     * 判断是否需要展示
     * @return
     */
    public boolean matchDisplayWechatPay(String province, String city){
        LimitRuleContext context = new LimitRuleContext();
        context.setCity(city);
        context.setProvince(province);
        context.setRedissonClient(redissonNewVipTransferClient);
        boolean match = false;
        List<IosWechatPayRule> iosWechatPayRules = (List<IosWechatPayRule>) this.data;
        if (!CollectionUtils.isEmpty(iosWechatPayRules)){
            for (IosWechatPayRule rule: iosWechatPayRules){
                if (rule.matchRule(context)){
                    match = true;
                    break;
                }
            }
        }
        return match;
    }

}
