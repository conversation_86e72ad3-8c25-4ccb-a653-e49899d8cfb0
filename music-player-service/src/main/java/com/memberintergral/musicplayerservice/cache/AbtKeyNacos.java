package com.memberintergral.musicplayerservice.cache;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import com.google.common.collect.Lists;
import com.memberintergral.musicplayerservice.entity.dto.AbtKeyDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * ios 播放器限免 从nacos获取数据
 */
@Component
public class AbtKeyNacos extends NacosRepository {


    private static String groupId = "common_conf";
    private static String dataId = "abt_key";
    private static long readTimeout = 1000l * 10;

    private Map<String, List<AbtKeyDTO>> abtKeyMap;

    public AbtKeyNacos(ConfigService configService) throws NacosException {
        super(configService, groupId, dataId, readTimeout);

    }

    @Override
    protected Object convert(String configData) {
        List<AbtKeyDTO> cache = JSONObject.parseArray(configData, AbtKeyDTO.class);
        if (CollectionUtils.isEmpty(cache)) {
            return Lists.newArrayList();
        }
        abtKeyMap = cache.stream().sorted(Comparator.comparing(AbtKeyDTO::getUpdateTime)).collect(Collectors.groupingBy(AbtKeyDTO::getCode));
        return cache;
    }

    public List<AbtKeyDTO> getListByCode(String code){
        if(MapUtils.isEmpty(abtKeyMap) || this.data == null){
            return Lists.newArrayList();
        }else{
            if (StringUtils.isBlank(code)) {
                return (List<AbtKeyDTO>)this.data;
            }
            List<AbtKeyDTO> abtKeyDTOS = abtKeyMap.get(code);
            if (CollectionUtils.isEmpty(abtKeyDTOS)) {
                return Lists.newArrayList();
            }
            return abtKeyDTOS;
        }
    }

    public Boolean addList(List<AbtKeyDTO> abtKeyDTOS) throws NacosException {
        if (CollectionUtils.isEmpty(abtKeyDTOS)) {
            return Boolean.TRUE;
        }
        String dateString = getDateString();
        abtKeyDTOS.stream().filter(e -> StringUtils.isBlank(e.getUpdateTime())).forEach(e -> e.setUpdateTime(dateString));
        return addJsonArrayConfig(abtKeyDTOS);
    }

    private String getDateString(){
        String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";
        String sTime = DateFormatUtils.format(new Date(), DATE_FORMAT);
        return sTime;
    }

}
