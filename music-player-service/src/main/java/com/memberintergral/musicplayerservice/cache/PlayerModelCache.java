package com.memberintergral.musicplayerservice.cache;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.kuwo.commercialization.common.cache.RedisTTlEventType;
import com.kuwo.commercialization.common.cache.TTLCache;
import com.kuwo.commercialization.common.cache.TTLEvent;
import com.memberintergral.musicplayerservice.entity.PlayerModelDTO;
import com.memberintergral.musicplayerservice.entity.query.PlayerModelQuery;
import com.memberintergral.musicplayerservice.mapper.musicPlayer.PlayerModelMapper;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Desc:
 * @date 2023-11-08 17:52:54
 */
@Component
public class PlayerModelCache extends TTLCache<String, List<PlayerModelDTO>> {

    public static final String myChangeKey = "PlayerModelCache";

    private PlayerModelMapper playerModelMapper;

    public PlayerModelCache(@Qualifier("redissonActClient") RedissonClient redissonActClient, PlayerModelMapper playerModelMapper) {
        super(redissonActClient, myChangeKey,1, TimeUnit.DAYS);
        this.playerModelMapper = playerModelMapper;
    }

    @Override
    protected void configure(CacheBuilder<String, List<PlayerModelDTO>> builder) {
        builder.maximumSize(500);
    }

    @Override
    protected List<PlayerModelDTO> loadFromDb(String key) {
        PlayerModelQuery playerModelQuery = new PlayerModelQuery();
        playerModelQuery.setModelCode(key);
        return playerModelMapper.selectByQuery(playerModelQuery);
    }

    @Override
    protected void initCache(Cache<String, List<PlayerModelDTO>> cache) {

    }

    @Override
    protected void changeCacheValue(TTLEvent event) {
        if (RedisTTlEventType.RELOAD_CACHE == event.getEventType() && myChangeKey.equals(event.getTtlChangeKey())){
            clearCache();
        }
    }

    private void clearCache() {
        getCache().invalidateAll();
        initCache(getCache());
    }

}
