package com.memberintergral.musicplayerservice.cache.audioConfig;

import com.google.common.cache.Cache;
import com.kuwo.commercialization.common.cache.RedisTTlEventType;
import com.kuwo.commercialization.common.cache.TTLCache;
import com.kuwo.commercialization.common.cache.TTLEvent;
import com.memberintergral.musicplayerservice.entity.AudioDeviceDTO;
import com.memberintergral.musicplayerservice.mapper.vipConf.AudioDeviceMapper;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 * @Desc:  识别连接设备推音质
 */
@Component
public class AudioDeviceCache extends TTLCache<String, List<AudioDeviceDTO>> {

    public static final String myChangeKey = "AudioDeviceCache";

    @Autowired
    private AudioDeviceMapper audioDeviceMapper;

    private List<AudioDeviceDTO> recommendConfigs = new ArrayList<>(1000);

    public AudioDeviceCache(@Qualifier("redissonActClient") RedissonClient redissonActClient, AudioDeviceMapper audioDeviceMapper) {
        super(redissonActClient, myChangeKey, 15, TimeUnit.MINUTES);
        this.audioDeviceMapper = audioDeviceMapper;
    }

    @Override
    protected List<AudioDeviceDTO> loadFromDb(String key) {
        AtomicLong id = new AtomicLong();
        int pageSize = 10;
        recommendConfigs.clear();
        List<AudioDeviceDTO> recommends = null;
        do {
            recommends =  audioDeviceMapper.getAudioDeviceGtIdPage(id.get(), pageSize);
            recommends = Optional.of(recommends).orElse(new ArrayList<>());
            recommends.forEach(k-> {recommendConfigs.add(k); id.set(k.getId());});
        } while (recommends.size() > 0);
        return recommendConfigs;
    }

    @Override
    protected void initCache(Cache<String, List<AudioDeviceDTO>> cache) {
        loadFromDb(null);
    }


    @Override
    protected void changeCacheValue(TTLEvent event) {
        if (RedisTTlEventType.RELOAD_CACHE == event.getEventType() && myChangeKey.equals(event.getTtlChangeKey())){
            clearCache();
        }
    }

    private void clearCache() {
        getCache().invalidateAll();
        initCache(getCache());
    }
}
