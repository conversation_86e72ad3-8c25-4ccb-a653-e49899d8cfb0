package com.memberintergral.musicplayerservice.cache.iosWechatPay;

import com.google.gson.internal.LinkedTreeMap;
import com.kuwo.commercialization.common.utill.DateUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Desc:
 * @date 2022-11-17 20:23:54
 */
@Data
@Slf4j
public class WeekLimitRule implements LimitRule{

    private String type;

    private List<String> weeks;

    private List<Time> openTimes;


    @Override
    public String getLimitType() {
        return "week";
    }

    @Override
    public LimitRule parseToObj(LinkedTreeMap enity) {
        WeekLimitRule weekLimitRule = new WeekLimitRule();
        if (enity.containsKey("type")){
            weekLimitRule.setType(String.valueOf(enity.get("type")));
        }
        if (enity.containsKey("weeks")){
            ArrayList _weeks = (ArrayList) enity.get("weeks");
            ArrayList<String> weeks = new ArrayList<>();
            if (!CollectionUtils.isEmpty(_weeks)){
                for (Object o:_weeks){
                    weeks.add(String.valueOf(o));
                }
            }
            weekLimitRule.setWeeks(weeks);
        }
        if (enity.containsKey("open_times")){
            ArrayList _open_time_list = (ArrayList) enity.get("open_times");
            ArrayList<Time> open_times = new ArrayList<>();
            if (!CollectionUtils.isEmpty(_open_time_list)){
                for (Object o: _open_time_list){
                    LinkedTreeMap timeMap = (LinkedTreeMap) o;
                    Time time = new Time();
                    if (timeMap.containsKey("start")){
                        time.setStart(String.valueOf(timeMap.get("start")));
                    }
                    if (timeMap.containsKey("end")){
                        time.setEnd(String.valueOf(timeMap.get("end")));
                    }
                    open_times.add(time);
                }
            }
            weekLimitRule.setOpenTimes(open_times);
        }
        return weekLimitRule;
    }

    @Override
    public boolean validateShouldDisplay(LimitRuleContext limitRuleContext) {
        boolean matched = true;
        Calendar date = Calendar.getInstance();
        date.setTime(new Date());
        int week = date.get(Calendar.DAY_OF_WEEK);
        if (week == 1){
            week = 7;
        }else {
            week = week -1;
        }
        if (!CollectionUtils.isEmpty(weeks)){
            int finalWeek = week;
            matched = weeks.stream().anyMatch(k->Integer.valueOf(k).intValue()== finalWeek);
        }
        if (matched && !CollectionUtils.isEmpty(openTimes)){
            matched = openTimes.stream().anyMatch(time -> DateUtil.validateInHourTime(date,time.getStart(), time.getEnd(),"HH:mm:ss"));
        }
        log.info("week matchd ->{}", matched);
        return matched;
    }
}
