package com.memberintergral.musicplayerservice.cache;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import com.memberintergral.musicplayerservice.entity.dto.MusicPlayerDTO;
import org.springframework.stereotype.Component;


/**
 * 从nacos获取数据
 */
@Component
public class MusicPlayerNacos extends NacosRepository {


    private static String groupId = "common_conf";
    private static String dataId = "musicplayer_conf";
    private static long readTimeout = 1000l * 10;

    private MusicPlayerDTO musicPlayerDTO;

    public MusicPlayerNacos(ConfigService configService) throws NacosException {
        super(configService, groupId, dataId, readTimeout);

    }

    @Override
    protected Object convert(String configData) {
        musicPlayerDTO = JSONObject.parseObject(configData, MusicPlayerDTO.class);
        return musicPlayerDTO;
    }

    public MusicPlayerDTO getMusicPlayerDTO() {
        return musicPlayerDTO;
    }

}
