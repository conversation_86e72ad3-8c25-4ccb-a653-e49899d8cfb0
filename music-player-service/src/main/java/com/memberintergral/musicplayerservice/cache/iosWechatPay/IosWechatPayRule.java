package com.memberintergral.musicplayerservice.cache.iosWechatPay;

import lombok.Data;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @Desc: ios 微信支付相关rule
 * @date 2022-11-17 20:17:57
 */
@Data
@Slf4j
@ToString
public class IosWechatPayRule {

    private String tag;

    private List<String> citys;

    private List<String> provinces;

    // 排除城市
    private List<String> excludeCities;

    private Integer displayStatus = 1;


    private List<LimitRule> limits;


    /**
     * 针对没有匹配对城市或省份直接返回SUCCESS ,匹配对城市要仔细进行校验
     * @return
     */
    public boolean matchRule(LimitRuleContext limitRuleContext){
        log.info("当前请求参数 -> {}", limitRuleContext);
        String province = limitRuleContext.getProvince();
        String city = limitRuleContext.getCity();
        boolean matched = true;
        if (!CollectionUtils.isEmpty(citys)){
            matched = citys.stream().anyMatch(k-> k.contains(city.replaceAll("市", "")));
        }
        log.info("城市命中 ->{}", matched);
        if (!CollectionUtils.isEmpty(provinces)){
            matched = matched || provinces.stream().anyMatch(k-> k.contains(province.replaceAll("省", "")));
        }
        log.info("省份命中 ->{}", matched);
        if (matched && !CollectionUtils.isEmpty(excludeCities)){
            matched = matched && !excludeCities.stream().anyMatch(k-> k.contains(city.replaceAll("市", "")));
        }
        log.info("排除城市 ->{}", matched);
        if (matched && displayStatus ==0){
            return false;
        }
        if (matched && !CollectionUtils.isEmpty(limits)){
            matched = limits.stream().allMatch(k->k.validateShouldDisplay(limitRuleContext));
        }
        log.info("当前规则 -> {}, 命中结果 -->{}", this,matched);
        return matched;
    }

}
