package com.memberintergral.musicplayerservice.cache;

import com.google.common.cache.Cache;
import com.kuwo.commercialization.common.cache.RedisTTlEventType;
import com.kuwo.commercialization.common.cache.TTLCache;
import com.kuwo.commercialization.common.cache.TTLEvent;
import com.memberintergral.musicplayerservice.entity.RecommendConfigNew;
import com.memberintergral.musicplayerservice.mapper.musicPlayer.RecommendConfigMapper;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 * @Desc:  播放推荐v2版本config cache
 * @date 2022-08-24 20:14:40
 */
@Component
public class PlayerRecommendConfigV2Cache extends TTLCache<String, List<RecommendConfigNew>> {

    public static final String myChangeKey = "playerRecommendConfigV2";

    @Autowired
    private RecommendConfigMapper recommendConfigMapper;

    private List<RecommendConfigNew> recommendConfigs = new ArrayList<>(1000);

    public PlayerRecommendConfigV2Cache(@Qualifier("redissonActClient") RedissonClient redissonActClient, RecommendConfigMapper recommendConfigMapper) {
        super(redissonActClient, myChangeKey);
        this.recommendConfigMapper = recommendConfigMapper;
    }

    @Override
    protected List<RecommendConfigNew> loadFromDb(String key) {
        AtomicLong id = new AtomicLong();
        int pageSize = 10;
        recommendConfigs.clear();
        List<RecommendConfigNew> recommends = null;
        do {
            recommends =  recommendConfigMapper.getNewRecommendConfig(id.get(), pageSize);
            recommends = Optional.of(recommends).orElse(new ArrayList<>());
            recommends.forEach(k-> {recommendConfigs.add(k); id.set(k.getId());});
        }while (recommends.size() > 0);
        return recommendConfigs;
    }

    @Override
    protected void initCache(Cache<String, List<RecommendConfigNew>> cache) {
        loadFromDb(null);
    }



    @Override
    protected void changeCacheValue(TTLEvent event) {
        if (RedisTTlEventType.RELOAD_CACHE == event.getEventType() && myChangeKey.equals(event.getTtlChangeKey())){
            clearCache();
        }
    }

    private void clearCache() {
        getCache().invalidateAll();
        initCache(getCache());
    }
}
