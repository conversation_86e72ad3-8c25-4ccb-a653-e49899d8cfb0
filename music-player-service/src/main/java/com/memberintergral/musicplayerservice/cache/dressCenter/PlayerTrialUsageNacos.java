package com.memberintergral.musicplayerservice.cache.dressCenter;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import com.memberintergral.musicplayerservice.cache.NacosRepository;
import com.memberintergral.musicplayerservice.resp.TrialInfo;
import org.springframework.stereotype.Component;

/**
 * 从nacos获取数据
 */
@Component
public class PlayerTrialUsageNacos extends NacosRepository {


    private static String dataId = "player_trial_usage";
    private static long readTimeout = 1000l * 10;
    private static String groupId = "common_conf";

    private TrialInfo trialInfo;

    public PlayerTrialUsageNacos(ConfigService configService) throws NacosException {
        super(configService, groupId, dataId, readTimeout);

    }

    @Override
    protected Object convert(String configData) {
        trialInfo = JSONObject.parseObject(configData, TrialInfo.class);
        return trialInfo;
    }
    public TrialInfo getTrialInfoDefaultData(){
        return trialInfo;
    }

}
