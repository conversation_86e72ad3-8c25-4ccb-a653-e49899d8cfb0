package com.memberintergral.musicplayerservice.cache;

import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import com.google.gson.Gson;
import com.memberintergral.musicplayerservice.resp.PlayerTrialRes;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import java.util.Map;

/**
 * ios 播放器限免 从nacos获取数据
 */
@Component
public class PlayerTrialNacos extends NacosRepository {


    private static String groupId = "common_conf";
    private static String dataId = "player_trial";
    private static long readTimeout = 1000l * 10;

    private Gson gson;

    private RedissonClient redissonActClient;

    public PlayerTrialNacos(ConfigService configService, @Qualifier("redissonActClient") RedissonClient redissonActClient) throws NacosException {
        super(configService, groupId, dataId, readTimeout);
        this.redissonActClient = redissonActClient;

    }

    @Override
    protected Object convert(String configData) {
        /*{
		    "periodDays":3,
		    "startTime":"2022-12-03 00:00:00",
		    "endTime":"2023-02-03 00:00:00"
	    }*/

        if (this.gson == null){
            this.gson = new Gson();
        }
        Map cache = gson.fromJson(configData, Map.class);

        PlayerTrialRes  playerTrialRes = new PlayerTrialRes();
        if (cache.containsKey("periodDays")){
            playerTrialRes.setPeriodDays(Double.parseDouble(String.valueOf(cache.get("periodDays"))));
        }
        if (cache.containsKey("periodDays")){
            playerTrialRes.setStartTime(String.valueOf(cache.get("startTime")));
        }
        if (cache.containsKey("periodDays")){
            playerTrialRes.setEndTime(String.valueOf(cache.get("endTime")));
        }
        return playerTrialRes;
    }


    /**
     * 判断是否需要展示
     * @return
     */
    public PlayerTrialRes getPlayerTrialData(){
        PlayerTrialRes playerTrialRes = (PlayerTrialRes) this.data;
        if(playerTrialRes!=null&&playerTrialRes.getPeriodDays()!=null&&
                StringUtils.isNotEmpty(playerTrialRes.getStartTime())&&
                        StringUtils.isNotEmpty(playerTrialRes.getEndTime())){
            return playerTrialRes;
        }else{
            return null;
        }
    }

}
