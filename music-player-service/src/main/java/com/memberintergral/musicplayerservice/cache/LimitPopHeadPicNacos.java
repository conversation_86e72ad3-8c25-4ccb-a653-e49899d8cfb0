package com.memberintergral.musicplayerservice.cache;

import cn.hutool.core.convert.Convert;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.stream.Collectors;

@Getter
@Component
@Slf4j
public class LimitPopHeadPicNacos extends NacosRepository {
    private final static String DATE_ID = "limit_pop_head_pic.json";
    private final static String GROUP_ID = "DEFAULT_GROUP";
    private final static long TIME_OUT = 3000L;

    private Set<String> whiteSet;

    public LimitPopHeadPicNacos(ConfigService configService) throws NacosException {
        super(configService, GROUP_ID, DATE_ID, TIME_OUT);
    }

    @Override
    protected Object convert(String configData) {
        if (StringUtils.isBlank(configData) && !JSONUtil.isTypeJSON(configData)) {
            log.warn("config data is blank or config data is not a json object => {}", configData);
            return false;
        }

        JSONArray objects = JSONUtil.parseArray(configData);
        this.whiteSet = objects.stream().map(Convert::toStr).collect(Collectors.toSet());

        return true;
    }

}

