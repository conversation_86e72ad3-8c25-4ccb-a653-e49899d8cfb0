package com.memberintergral.musicplayerservice.cache.dressCenter;

import com.google.common.cache.Cache;
import com.kuwo.commercialization.common.cache.RedisTTlEventType;
import com.kuwo.commercialization.common.cache.TTLCache;
import com.kuwo.commercialization.common.cache.TTLEvent;
import com.commerical.musicplayerservice.enity.VipHanger;
import com.memberintergral.musicplayerservice.mapper.musicPlayer.VipHangerMapper;
import com.memberintergral.musicplayerservice.util.DressCenterDetailUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Desc: 挂件cache
 * @date 2023-04-12 17:15:18
 */
@Component
@Slf4j
public class VipHangerCache extends TTLCache<String, VipHanger> {

    public static final String myChangeKey = "hangerCache";

    @Autowired
    private VipHangerMapper vipHangerMapper;
    public VipHangerCache(@Qualifier("redissonActClient") RedissonClient redissonActClient) {
        super(redissonActClient, myChangeKey,2, TimeUnit.HOURS);
    }

    @Override
    protected VipHanger loadFromDb(String key) {
        return vipHangerMapper.getHangersById(Long.valueOf(key));
    }

    @Override
    protected void initCache(Cache<String, VipHanger> cache) {
        List<VipHanger> hangers = vipHangerMapper.getAllList();
        if (hangers!=null && hangers.size()>0){
            for (VipHanger hanger:hangers){
                hanger.setHPreviewImage(DressCenterDetailUtil.imgUrlConvert(hanger.getHPreviewImage()));
                hanger.setZipurl(DressCenterDetailUtil.imgUrlConvert(hanger.getZipurl()));
                getCache().put(String.valueOf(hanger.getId()),hanger);
            }
        }
        log.info("[load hanger cache finished!]");
    }


    @Override
    protected void innerClearCache() {
        getCache().invalidateAll();
        initCache(getCache());
    }

    @Override
    protected void changeCacheValue(TTLEvent event) {
        if (RedisTTlEventType.RELOAD_CACHE == event.getEventType()&& myChangeKey.equals(event.getTtlChangeKey())){
            innerClearCache();
        }
    }
}
