package com.memberintergral.musicplayerservice.cache;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import com.google.common.collect.Lists;
import com.memberintergral.musicplayerservice.entity.dto.FromSrcTitleDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 从nacos获取数据
 */
@Component
public class FromSrcTitleNacos extends NacosRepository {


    private static String groupId = "common_conf";
    private static String dataId = "fromsrc_title_vip_conf";
    private static long readTimeout = 1000l * 10;

    private Map<String, List<FromSrcTitleDTO>> fromSrcTitleMap;

    public FromSrcTitleNacos(ConfigService configService) throws NacosException {
        super(configService, groupId, dataId, readTimeout);

    }

    @Override
    protected Object convert(String configData) {
        List<FromSrcTitleDTO> cache = JSONObject.parseArray(configData, FromSrcTitleDTO.class);
        if (CollectionUtils.isEmpty(cache)) {
            return Lists.newArrayList();
        }
        fromSrcTitleMap = cache.stream().sorted(Comparator.comparing(FromSrcTitleDTO::getFromSrc)).collect(Collectors.groupingBy(FromSrcTitleDTO::getFromSrc));
        return cache;
    }

    public List<FromSrcTitleDTO> getListByCode(String code){
        if(MapUtils.isEmpty(fromSrcTitleMap) || this.data == null){
            return Lists.newArrayList();
        }else{
            if (StringUtils.isBlank(code)) {
                return (List<FromSrcTitleDTO>)this.data;
            }
            List<FromSrcTitleDTO> userBlankDTOS = fromSrcTitleMap.get(code);
            if (CollectionUtils.isEmpty(userBlankDTOS)) {
                return Lists.newArrayList();
            }
            return userBlankDTOS;
        }
    }

    public Boolean addList(List<FromSrcTitleDTO> fromSrcTitles) throws NacosException {
        if (CollectionUtils.isEmpty(fromSrcTitles)) {
            return Boolean.TRUE;
        }
        List<FromSrcTitleDTO> collect = fromSrcTitles.stream().filter(e -> StringUtils.isNotBlank(e.getFromSrc())).filter(e -> StringUtils.isNotBlank(e.getTitle())).collect(Collectors.toList());
        return addJsonArrayConfig(collect);
    }

    public Boolean delete(FromSrcTitleDTO fromSrcTitle) throws NacosException {
        if (Objects.isNull(fromSrcTitle) || StringUtils.isBlank(fromSrcTitle.getFromSrc())) {
            return Boolean.TRUE;
        }
        List<FromSrcTitleDTO> fromSrcTitleDTOS;
        if (this.data == null) {
            fromSrcTitleDTOS = Lists.newArrayList();
        } else {
            fromSrcTitleDTOS = (List<FromSrcTitleDTO>)this.data;
        }
        List<FromSrcTitleDTO> collect = fromSrcTitleDTOS.stream().filter(e -> !fromSrcTitle.getFromSrc().equals(e.getFromSrc())).collect(Collectors.toList());
        return overwriteConfig(JSONObject.toJSONString(collect));
    }

}
