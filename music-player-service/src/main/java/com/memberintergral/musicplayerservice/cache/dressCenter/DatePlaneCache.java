package com.memberintergral.musicplayerservice.cache.dressCenter;

import com.google.common.cache.Cache;
import com.kuwo.commercialization.common.cache.RedisTTlEventType;
import com.kuwo.commercialization.common.cache.TTLCache;
import com.kuwo.commercialization.common.cache.TTLEvent;
import com.commerical.musicplayerservice.enity.NameplateDetail;
import com.memberintergral.musicplayerservice.mapper.musicPlayer.NameplateDetailMapper;
import com.memberintergral.musicplayerservice.util.DressCenterDetailUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Desc: 铭牌本地cache
 * @date 2023-04-12 17:49:27
 */
@Component
@Slf4j
public class DatePlaneCache extends TTLCache<String, NameplateDetail> {

    @Autowired
    private NameplateDetailMapper nameplateDetailMapper;
    public static final String myChangeKey = "datePlanCache";
    public DatePlaneCache(@Qualifier("redissonActClient") RedissonClient redissonActClient) {
        super(redissonActClient, myChangeKey,2, TimeUnit.HOURS);
    }

    @Override
    protected NameplateDetail loadFromDb(String key) {
        return nameplateDetailMapper.selectByPrimaryKey(Long.valueOf(key));
    }

    @Override
    protected void initCache(Cache<String, NameplateDetail> cache) {
        List<NameplateDetail> allList = nameplateDetailMapper.getAllList();
        if (allList!=null && allList.size()>0){
            allList.forEach(k->{
                k.setPicUrl(DressCenterDetailUtil.imgUrlConvert(k.getPicUrl()));
                getCache().put(String.valueOf(k.getId()),k);
            });
        }
        log.info("[init datePlane cache finish!]");
    }

    @Override
    protected void changeCacheValue(TTLEvent event) {
        if (RedisTTlEventType.RELOAD_CACHE == event.getEventType()&& myChangeKey.equals(event.getTtlChangeKey())){
            innerClearCache();
        }
    }

    @Override
    protected void innerClearCache() {
        getCache().invalidateAll();
        initCache(getCache());
    }
}
