package com.memberintergral.musicplayerservice.cache;

import com.google.common.cache.Cache;
import com.google.common.collect.Lists;
import com.kuwo.commercialization.common.cache.RedisTTlEventType;
import com.kuwo.commercialization.common.cache.TTLCache;
import com.kuwo.commercialization.common.cache.TTLEvent;
import com.kuwo.commercialization.common.http.VipDomainHttpRequest;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class InterfaceTransferConfCache extends TTLCache<String, List<String>> {

    public static final String myChangeKey = "interfaceTransferConfCache";
    private static Map<String,String> confMap = new ConcurrentHashMap<String,String>();
    private VipDomainHttpRequest vipDomainHttpRequest;

    public InterfaceTransferConfCache(@Qualifier("redissonNewVipTransferClient") RedissonClient redissonNewVipTransferClient, VipDomainHttpRequest vipDomainHttpRequest) {
        super(redissonNewVipTransferClient, myChangeKey,1, TimeUnit.MINUTES);
        this.vipDomainHttpRequest = vipDomainHttpRequest;
    }

    @Override
    protected List<String> loadFromDb(String key) {
        List<String>objs=new ArrayList<String>();
        try{
            Map dataSetItem = vipDomainHttpRequest.getDataSetItem(Lists.newArrayList( "ui_dress_gray_transfer"));
            if(dataSetItem!=null&&dataSetItem.size()>0){
                for (Object mapKey : dataSetItem.keySet()) {
                    confMap.put(String.valueOf(mapKey), String.valueOf(dataSetItem.get(String.valueOf(mapKey))));
                }
            }
            return objs;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    protected void initCache(Cache<String, List<String>> cache) {
        loadFromDb("1111");
        log.info("[load cache finished!]");
    }

    public String getTransferConf(String dateItemCode){
        getValue("");
        String obj="";
        String defaultMatchKey = buildSearchVersionKey(dateItemCode);
        if(confMap.get(defaultMatchKey)!=null){
            obj=confMap.get(defaultMatchKey);
        }
        return obj;
    }

    /**
     * 构建 key
     * @return
     */
    private String buildSearchVersionKey(String dateItemCode){
        StringBuilder builder = new StringBuilder();
        builder.append(dateItemCode);
        return builder.toString();
    }


    @Override
    protected void changeCacheValue(TTLEvent event) {
        if (RedisTTlEventType.RELOAD_CACHE == event.getEventType()&& myChangeKey.equals(event.getTtlChangeKey())){
            innerClearCache();
        }
    }

    @Override
    protected void innerClearCache() {
        getCache().invalidateAll();
        initCache(getCache());
    }
}
