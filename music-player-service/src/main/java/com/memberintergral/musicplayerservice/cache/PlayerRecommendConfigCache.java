package com.memberintergral.musicplayerservice.cache;

import com.google.common.cache.Cache;
import com.kuwo.commercialization.common.cache.RedisTTlEventType;
import com.kuwo.commercialization.common.cache.TTLCache;
import com.kuwo.commercialization.common.cache.TTLEvent;
import com.memberintergral.musicplayerservice.entity.RecommendConfig;
import com.memberintergral.musicplayerservice.mapper.musicPlayer.RecommendConfigMapper;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Desc:  播放推荐config cache
 * @date 2022-08-24 20:14:40
 */
@Component
public class PlayerRecommendConfigCache extends TTLCache<String, List<RecommendConfig>> {

    public static final String myChangeKey = "playerRecommendConfig";

    @Autowired
    private RecommendConfigMapper recommendConfigMapper;

    private List<RecommendConfig> recommendConfigs = new ArrayList<>();

    public PlayerRecommendConfigCache(@Qualifier("redissonActClient") RedissonClient redissonActClient, RecommendConfigMapper recommendConfigMapper) {
        super(redissonActClient, myChangeKey);
        this.recommendConfigMapper = recommendConfigMapper;
    }

    @Override
    protected List<RecommendConfig> loadFromDb(String key) {
        int page = 1;
        int pageSize = 10;
        recommendConfigs.clear();
        List<RecommendConfig> recommends = null;
        do {
            recommends =  recommendConfigMapper.loadAvailableConfigByPage((page-1)*pageSize, pageSize);
            Optional.of(recommends).orElse(new ArrayList<>()).stream().forEach(e-> recommendConfigs.add(e));
            ++page;
        }while (recommends!=null && recommends.size() > 0);
        return recommendConfigs;
    }

    @Override
    protected void initCache(Cache<String, List<RecommendConfig>> cache) {
        loadFromDb(null);
    }



    @Override
    protected void changeCacheValue(TTLEvent event) {
        if (RedisTTlEventType.RELOAD_CACHE == event.getEventType() && myChangeKey.equals(event.getTtlChangeKey())){
            clearCache();
        }
    }

    private void clearCache() {
        getCache().invalidateAll();
        initCache(getCache());
    }
}
