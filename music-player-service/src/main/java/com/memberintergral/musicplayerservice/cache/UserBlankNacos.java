package com.memberintergral.musicplayerservice.cache;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.memberintergral.musicplayerservice.entity.dto.UserBlankDTO;
import com.memberintergral.musicplayerservice.resp.PlayerTrialRes;
import org.apache.commons.collections.comparators.ComparableComparator;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * ios 播放器限免 从nacos获取数据
 */
@Component
public class UserBlankNacos extends NacosRepository {


    private static String groupId = "common_conf";
    private static String dataId = "user_blank";
    private static long readTimeout = 1000l * 10;

    private Map<String, List<UserBlankDTO>> userBlankMap;

    public UserBlankNacos(ConfigService configService) throws NacosException {
        super(configService, groupId, dataId, readTimeout);

    }

    @Override
    protected Object convert(String configData) {
        List<UserBlankDTO> cache = JSONObject.parseArray(configData, UserBlankDTO.class);
        if (CollectionUtils.isEmpty(cache)) {
            return Lists.newArrayList();
        }
        userBlankMap = cache.stream().sorted(Comparator.comparing(UserBlankDTO::getUpdateTime)).collect(Collectors.groupingBy(UserBlankDTO::getCode));
        return cache;
    }

    public List<UserBlankDTO> getListByCode(String code){
        if(MapUtils.isEmpty(userBlankMap) || this.data == null){
            return Lists.newArrayList();
        }else{
            if (StringUtils.isBlank(code)) {
                return (List<UserBlankDTO>)this.data;
            }
            List<UserBlankDTO> userBlankDTOS = userBlankMap.get(code);
            if (CollectionUtils.isEmpty(userBlankDTOS)) {
                return Lists.newArrayList();
            }
            return userBlankDTOS;
        }
    }

    public Boolean addList(List<UserBlankDTO> userBlankDTOS) throws NacosException {
        if (CollectionUtils.isEmpty(userBlankDTOS)) {
            return Boolean.TRUE;
        }
        String dateString = getDateString();
        userBlankDTOS.stream().filter(e -> StringUtils.isBlank(e.getUpdateTime())).forEach(e -> e.setUpdateTime(dateString));
        return addJsonArrayConfig(userBlankDTOS);
    }

    private String getDateString(){
        String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";
        String sTime = DateFormatUtils.format(new Date(), DATE_FORMAT);
        return sTime;
    }

}
