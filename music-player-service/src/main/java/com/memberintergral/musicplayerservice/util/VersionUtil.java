package com.memberintergral.musicplayerservice.util;

import org.apache.commons.lang3.StringUtils;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.memberintergral.musicplayerservice.util
 * @date:2022/12/26
 */
public class VersionUtil {

    /**
     * 判断版本号大于等于 某个版本
     * @param channel
     * @param targetVersion
     * @return
     */
    public static boolean versionLargeThan(String channel, int targetVersion){
        if(StringUtils.isNotEmpty(channel) && channel.split("_").length==4){
            String[] channelArr = channel.split("_");
            String channelName=channelArr[3];
            int version=MyNumberUtils.toInt(channelArr[2].replace(".",""));
            return version>=targetVersion;
        }
        return false;
    }

}
