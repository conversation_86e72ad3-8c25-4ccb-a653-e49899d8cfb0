package com.memberintergral.musicplayerservice.util.recallStrategy;

import com.memberintergral.musicplayerservice.entity.UserUnsign;
import com.memberintergral.musicplayerservice.entity.dto.UserBlankDTO;
import com.memberintergral.musicplayerservice.mapper.musicPlayer.UserUnsignMapper;
import com.memberintergral.musicplayerservice.req.RecallRequest;
import com.memberintergral.musicplayerservice.service.UserBlankService;
import com.memberintergral.musicplayerservice.util.recallStrategy.common.RecallPopUrl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.memberintergral.musicplayerservice.util.recallStrategy
 * 自动续费解约
 * 用户微信/支付宝/苹果支付解约后，打开客户端，弹出一次；（每次解约弹出一次）
 * @date:2022/9/5
 */
@Service
@Slf4j
@Order(4)
public class UnsignRecall extends AbstractRecallStrategy {

    @Autowired
    private UserUnsignMapper userUnsignMapper;

    @Autowired
    private UserBlankService userBlankService;

    @Override
    public Result deploy(RecallRequest request) {
        if (request.getUserIdStr().equals("224734578")) {
            return new Result("https://h5app.kuwo.cn/pay/modal/retrieve.html");
        }

        Long userId = request.getUserIdLong();

        if (order != null && order < 0) {
            UserBlankDTO userBlankDTO = new UserBlankDTO();
            userBlankDTO.setUserId(userId);
            userBlankDTO.setCode("playerPop");
            if (!userBlankService.isExist(userBlankDTO)) {
                return next(request);
            }
        }

        UserUnsign userUnsign = userUnsignMapper.getRecordById(userId);
        //判断是否可以弹弹窗
        if (userUnsign != null && userUnsign.getLastPopWindowTime().before(userUnsign.getUnsignTime())){
            userUnsignMapper.updateLastPopWindowTime(userId);
            log.trace("RECALL>>>"+"\t"+"UNSIGN_RECALL"+"\t"+userId);
            return new Result("https://h5app.kuwo.cn/pay/modal/retrieve.html");
        }
        return next(request);
    }

}
