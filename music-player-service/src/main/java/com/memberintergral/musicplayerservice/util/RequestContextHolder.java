package com.memberintergral.musicplayerservice.util;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 请求上下文 请求上下文相关
 */
public class RequestContextHolder {

    private static final ThreadLocal<RequestHolder> hodler =  new ThreadLocal();


    public static RequestHolder getHolder(){
        return hodler.get();
    }


    public static void setHolder(RequestHolder requestHolder){
        hodler.set(requestHolder);
    }


    public static void clean(){
        hodler.remove();
    }


    public static class RequestHolder{

        private HttpServletRequest request;

        private HttpServletResponse response;

        private Map<String, Object> attach = new HashMap<>();

        private String userIp;

        private String traceId;

        public RequestHolder(HttpServletRequest request, HttpServletResponse response) {
            this.request = request;
            this.response = response;
            if (request!=null){
                this.userIp = getUserIp(request);
            }
            this.traceId = UUID.randomUUID().toString();
        }

        public HttpServletRequest getRequest() {
            return request;
        }

        public void setRequest(HttpServletRequest request) {
            this.request = request;
        }

        public HttpServletResponse getResponse() {
            return response;
        }

        public void setResponse(HttpServletResponse response) {
            this.response = response;
        }

        public Map<String, Object> getAttach() {
            return attach;
        }

        public void setAttach(Map<String, Object> attach) {
            this.attach = attach;
        }

        public Object getAttachment(String key){
            if (attach.containsKey(key)){
                return attach.get(key);
            }
            return null;
        }

        public void addAttachment(String key, Object value){
            attach.put(key, value);
        }

        public String getUserIp() {
            return userIp;
        }

        private String getUserIp(HttpServletRequest request)
        {
//			用户IP
            String ip = request.getHeader("x-forwarded-for");
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("Proxy-Client-IP");
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("WL-Proxy-Client-IP");
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getRemoteAddr();
            }
            ip = ip == null ? "" : ip;
            int sp = ip.indexOf(",");//如果运用了多级反向代理则取第一个逗号前面的ip
            if(sp!=-1)ip=ip.substring(0, sp);

            return ip;
        }

        public String getTraceId() {
            return traceId;
        }
    }

}
