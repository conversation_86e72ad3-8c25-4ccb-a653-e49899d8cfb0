package com.memberintergral.musicplayerservice.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kuwo.commercialization.common.cenum.RedisConstant;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * @Desc:redis操作工具类
 * @date 2022-03-23 10:10:13
 */
@Component
public class RedisUtil {

    private Logger logger = LoggerFactory.getLogger(RedisUtil.class);

    @Autowired
    private RedissonClient redissonClient;

    /*public void setMapObject(String token, Object object,String mapName,Long seconds) {
        RMapCache<String, Object> map = redissonClient.getMapCache(mapName);
        map.put(token, object,seconds, TimeUnit.SECONDS);
    }

    public Object getMapObject(String key, String mapName) {
        RMapCache<String, Object> map = redissonClient.getMapCache(mapName);
        return map.get(key);

    }*/

    public RMapCache<String, Object> getMap(String mapName) {
        RMapCache<String, Object> map = redissonClient.getMapCache(mapName);
        return map;

    }

    //操作 String
    public void setString(String key,String value,Long seconds) {
        RBucket<Object> bucket = redissonClient.getBucket(key);
        bucket.set(value,seconds,TimeUnit.SECONDS);
    }

    public String getString(String key) {
        RBucket<String> bucket = redissonClient.getBucket(key);
        return bucket.get();
    }

    public void delBucket(String key) {
        RBucket<Object> bucket = redissonClient.getBucket(key);
        bucket.delete();
    }

    //操作List
    /*public <T>void setList(String key, List<T> brandCodes,Long seconds) {
        RList<T> list = redissonClient.getList(key);
        brandCodes.forEach( s -> {
            list.add(s);
        });
        list.expire(seconds,TimeUnit.SECONDS);
    }

    public <T>List<T> getList(String key) {
        RList<T> list = redissonClient.getList(key);
        return list.readAll();
    }*/

    /**
     * 将对象存入缓存
     */
    public void setObject(String key, Object obj, long overTime) {
        try {
            final String value = new ObjectMapper().writeValueAsString(obj);
            setString(key,value,overTime);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public Object getObject(final String key, @SuppressWarnings("rawtypes") Class clazz) {
        final Class classCopy = clazz;
        String value = getString(key);
        try {
            if (!StringUtils.isEmpty(value)) {
                return new ObjectMapper().readValue(value, classCopy);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public Boolean getBit(String key, Long offset) {
        RBitSet bitSet = redissonClient.getBitSet(key);
        return bitSet.get(offset);
    }

    public Boolean setBit(String key, Long offset, Boolean flag) {
        RBitSet bitSet = redissonClient.getBitSet(key);
        return bitSet.set(offset, flag);
    }

    public void increment(String key) {
        RLongAdder longAdder = redissonClient.getLongAdder(key);
        longAdder.increment();

    }

    /*public <T>void addSet(String key, Set<T> brandCodes) {
        RSet<T> set = redissonClient.getSet(key);
        brandCodes.forEach( s -> {
            set.add(s);
        });
    }

    public <T>void addSet(String key, T s) {
        RSet<T> set = redissonClient.getSet(key);
        set.add(s);
    }

    public <T>void removeSet(String key, Set<T> brandCodes) {
        RSet<T> set = redissonClient.getSet(key);
        brandCodes.forEach( s -> {
            set.remove(s);
        });
    }

    public <T>void removeSet(String key, T s) {
        RSet<T> set = redissonClient.getSet(key);
        set.remove(s);
    }

    public int getSetSize(String key) {
        RSet<Object> set = redissonClient.getSet(key);
        return set.size();
    }

    *//**
     * object类型的get
     *//*
    public <T> T getTObject(String key) {
        RBucket<T> bucket = redissonClient.getBucket(key);
        return bucket.get();
    }


    *//**
     * object类型的set
     *//*
    public <T> void setTObject(String key, T value, Long seconds) {
        RBucket<T> bucket = redissonClient.getBucket(key);
        bucket.set(value, seconds, TimeUnit.SECONDS);
    }*/
    public <T>void setListJsonStr(String key, List<T> objs,Long seconds) {
        setString(key, JSON.toJSONString(objs), seconds);

    }

    public <T>List<T> getListJsonStr(String key,Class<T> clazz) {
        String value=getString(key);
        List<T> objs = null;
        if(StringUtils.isNotEmpty(value)){
            objs = JSONArray.parseArray(value,clazz);
        }
        return objs;
    }

}
