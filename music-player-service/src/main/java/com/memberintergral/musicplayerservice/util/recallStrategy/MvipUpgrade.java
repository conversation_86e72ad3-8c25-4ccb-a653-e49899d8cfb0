package com.memberintergral.musicplayerservice.util.recallStrategy;

import com.memberintergral.musicplayerservice.entity.dto.UserBlankDTO;
import com.memberintergral.musicplayerservice.req.RecallRequest;
import com.memberintergral.musicplayerservice.service.UserBlankService;
import com.memberintergral.musicplayerservice.util.recallStrategy.common.PropertiesValue;
import com.memberintergral.musicplayerservice.util.recallStrategy.common.RedisKey;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.memberintergral.musicplayerservice.util.recallStrategy
 * @date:2022/9/5
 */
@Service
@Slf4j
@Order(100)
public class MvipUpgrade extends AbstractRecallStrategy{

    @Autowired
    private UserBlankService userBlankService;

    private static final String TEST_POP_PREFIX = "https://h5app.kuwo.cn/pay/uat_vipresident/vipmUpgradePopup.html?type=006";

    private static final String POP_PREFIX = "https://h5app.kuwo.cn/pay/vipresident/vipmUpgradePopup.html?type=006";

    @Override
    public Result deploy(RecallRequest request) {
        try {
            Map<String, String> userSamInfoCache = request.getUserSamInfoCache();
            Long svipExpire = NumberUtils.toLong(userSamInfoCache.get("svipExpire"));
            Long vipLuxuryExpire = NumberUtils.toLong(userSamInfoCache.get("vipLuxuryExpire"));
            Long vipmExpire = NumberUtils.toLong(userSamInfoCache.get("vipmExpire"));

            Long now = request.getNow().getTime();
            String userId = request.getUserId();

            if (order != null && order < 0) {
                List<UserBlankDTO> mVipUpgradeEndwith = userBlankService.list("mVipUpgrade_endwith");
                if (StringUtils.isBlank(userId) || CollectionUtils.isEmpty(mVipUpgradeEndwith)
                        || !mVipUpgradeEndwith.stream().map(UserBlankDTO::getUserId).map(String::valueOf).anyMatch(userId::endsWith)) {
                    return next(request);
                }
            }

            if (!"android".equals(request.getPlatform())
                    //是豪V
                    || (vipLuxuryExpire != null && vipLuxuryExpire >= now)
                    //是超会
                    || (svipExpire != null && svipExpire >= now)
                    //不是音乐包
                    || (vipmExpire == null || vipmExpire < now)) {
                return next(request);
            }

            String mvipUpgradeUserCacheKey= RedisKey.analysisKey(RedisKey.MVIP_UPGRADE_USER_CACHE_KEY, userId);
            if (redisNewVipTransferUtil.getObject(mvipUpgradeUserCacheKey, Integer.class)!=null) {
                return next(request);
            }

            String url;
            if (PropertiesValue.SERVER_MODEL.equals("prod")) {
                url = POP_PREFIX;
            } else {
                url = TEST_POP_PREFIX;
            }
            redisNewVipTransferUtil.setObject(mvipUpgradeUserCacheKey, 1, RedisKey.ONE_DAY_EXPIRE);
            log.info("ActivityRecall-deploy, statistics userId:{} isMvipAutoPay:{}", userId, userSamInfoCache.get("vipmAutoPayUser"));
            return new Result(url);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return next(request);
    }

}
