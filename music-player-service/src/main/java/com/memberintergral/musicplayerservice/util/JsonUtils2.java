package com.memberintergral.musicplayerservice.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.memberintergral.musicplayerservice.util
 * @date:2023/9/19
 */
public class JsonUtils2 {

    public static Object toJson(Object obj) {
        String base = ConvertUtils.toStr(obj);
        if (isjsonObject(base)) {
            JSONObject json = JSONObject.parseObject(base);
            for (String key : json.keySet()) {
                json.put(key, toJson(json.get(key)));
            }
            return json;
        }
        if (isjsonArray(base)) {
            JSONArray json = JSONObject.parseArray(base);
            for (int i = 0; i < json.size(); i++) {
                json.set(i, toJson(json.get(i)));
            }
            return json;
        }
        return obj;
    }

    public static boolean isjsonObject(String str) {
        try {
            if (StringUtils.isBlank(str)) {
                return Boolean.FALSE;
            }
            JSONObject jsonStr = JSONObject.parseObject(str);
            return Boolean.TRUE;
        } catch (Exception e) {
            return Boolean.FALSE;
        }
    }

    public static boolean isjsonArray(String str) {
        try {
            if (StringUtils.isBlank(str)) {
                return Boolean.FALSE;
            }
            JSONArray jsonStr = JSONObject.parseArray(str);
            return Boolean.TRUE;
        } catch (Exception e) {
            return Boolean.FALSE;
        }
    }

}
