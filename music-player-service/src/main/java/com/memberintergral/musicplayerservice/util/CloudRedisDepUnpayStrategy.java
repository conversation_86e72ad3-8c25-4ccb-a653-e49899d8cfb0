package com.memberintergral.musicplayerservice.util;

import com.alibaba.fastjson.JSONObject;
import com.kuwo.commercialization.common.utill.MD5;
import com.memberintergral.musicplayerservice.entity.dto.DataDatasetDTO;
import com.memberintergral.musicplayerservice.entity.dto.DataItemDTO;
import com.memberintergral.musicplayerservice.entity.query.DataDatasetQuery;
import com.memberintergral.musicplayerservice.entity.query.DataItemQuery;
import com.memberintergral.musicplayerservice.mapper.stasticsRead.DataItemMapper;
import com.memberintergral.musicplayerservice.mapper.stasticsRead.DataSetMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.memberintergral.recallService.util
 * @date:2023/12/5
 */

@Slf4j
@Service
public class CloudRedisDepUnpayStrategy {

    public static final String RECALL_REDIS_UNPAY_STRATEGY = "RECALL_REDIS_UNPAY_STRATEGY";

    private volatile Strategy strategy = null;

    @Autowired
    private DataSetMapper dataSetMapper;

    @Autowired
    private DataItemMapper dataItemMapper;

    public Boolean loadConfig() {
        DataDatasetQuery datasetQuery = new DataDatasetQuery();
        datasetQuery.setCode("vip_dev_ops_config");
        datasetQuery.setStatus(1);
        List<DataDatasetDTO> dataDatasetDTOS = dataSetMapper.selectByQuery(datasetQuery);
        if (CollectionUtils.isEmpty(dataDatasetDTOS)) {
            return false;
        }
        Integer dataSetId = dataDatasetDTOS.stream().findFirst().orElseGet(DataDatasetDTO::new).getId();

        DataItemQuery dataItemQuery = new DataItemQuery();
        dataItemQuery.setCode(RECALL_REDIS_UNPAY_STRATEGY);
        dataItemQuery.setStatus(1);
        dataItemQuery.setMid(dataSetId);
        List<DataItemDTO> dataItemDTOS = dataItemMapper.selectByQuery(dataItemQuery);
        String content = dataItemDTOS.stream().findFirst().orElseGet(DataItemDTO::new).getContent();
        if (StringUtils.isBlank(content)) {
            return false;
        }
        JSONObject jsonObject = JSONObject.parseObject(content);
        Strategy temp = new Strategy();
        if (jsonObject.containsKey("uid")) {
            temp.setEndsWithUid(String.valueOf(jsonObject.get("uid")));
        }
        if (jsonObject.containsKey("graySwitch")) {
            temp.setGraySwitch("1".equals(String.valueOf(jsonObject.get("graySwitch"))));
        }
        strategy = temp;
        return true;

    }

    /**
     * 是否使用新redis策略
     *
     * @param redisKey
     * @return
     */
    public boolean useNewStrategy(String redisKey) {
        Strategy strategyInner = strategy;
        if (strategyInner == null || !strategyInner.isGraySwitch()) {
            return false;
        }
        Integer md5EndValue = md5EndValue(redisKey);
        if (StringUtils.isNotBlank(strategyInner.getEndsWithUid())) {
            String[] uids = strategyInner.getEndsWithUid().split(",");
            for (String uid : uids) {
                if (StringUtils.isNumeric(uid) && md5EndValue.equals(Integer.parseInt(uid))) {
                    return true;
                }
            }
        }
        return false;
    }

    public class Strategy {
        private String endsWithUid;

        private boolean graySwitch = false;

        public String getEndsWithUid() {
            return endsWithUid;
        }

        public void setEndsWithUid(String endsWithUid) {
            this.endsWithUid = endsWithUid;
        }

        public boolean isGraySwitch() {
            return graySwitch;
        }

        public void setGraySwitch(boolean graySwitch) {
            this.graySwitch = graySwitch;
        }
    }

    public Integer md5EndValue(String key) {
        Integer end = null;
        String encryption = key;
        do {
            String md5ofStr = MD5.getMD5ofStr(encryption);
            if (StringUtils.isNotBlank(md5ofStr)) {
                String reverse = StringUtils.reverse(md5ofStr);
                for (int i = 0; i < reverse.length(); i++) {
                    String value = String.valueOf(reverse.charAt(i));
                    if (StringUtils.isNumeric(value)) {
                        end = Integer.parseInt(value);
                        break;
                    }
                }
            }
            encryption = md5ofStr;
        } while (end == null);
        return end;
    }

}
