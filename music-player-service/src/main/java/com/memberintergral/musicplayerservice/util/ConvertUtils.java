package com.memberintergral.musicplayerservice.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.nio.ByteBuffer;
import java.nio.charset.Charset;
import java.text.NumberFormat;
import java.util.Date;
import java.util.Set;

/**
 * 类型转换器
 * @author: <EMAIL>
 * @version: v1.0
 * @description: cn.kuwo.added.util
 * @date:2022/11/10
 */
public class ConvertUtils {
    /**
     * 转换为字符串<br>
     * 如果给定的值为null，或者转换失败，返回默认值<br>
     * 转换失败不会报错
     *
     * @param value        被转换的值
     * @param defaultValue 转换错误时的默认值
     * @return 结果
     */
    public static String toStr(Object value, String defaultValue) {
        try {
            if (null == value) {
                return defaultValue;
            }
            if (value instanceof String) {
                return (String) value;
            }
            return value.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 转换为字符串<br>
     * 如果给定的值为<code>null</code>，或者转换失败，返回默认值<code>null</code><br>
     * 转换失败不会报错
     *
     * @param value 被转换的值
     * @return 结果
     */
    public static String toStr(Object value) {
        try {
            return toStr(value, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 转换为字符<br>
     * 如果给定的值为null，或者转换失败，返回默认值<br>
     * 转换失败不会报错
     *
     * @param value        被转换的值
     * @param defaultValue 转换错误时的默认值
     * @return 结果
     */
    public static Character toChar(Object value, Character defaultValue) {
        try {
            if (null == value) {
                return defaultValue;
            }
            if (value instanceof Character) {
                return (Character) value;
            }

            final String valueStr = toStr(value, null);
            return StringUtils.isEmpty(valueStr) ? defaultValue : valueStr.charAt(0);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 转换为字符<br>
     * 如果给定的值为<code>null</code>，或者转换失败，返回默认值<code>null</code><br>
     * 转换失败不会报错
     *
     * @param value 被转换的值
     * @return 结果
     */
    public static Character toChar(Object value) {
        try {
            return toChar(value, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 转换为byte<br>
     * 如果给定的值为<code>null</code>，或者转换失败，返回默认值<br>
     * 转换失败不会报错
     *
     * @param value        被转换的值
     * @param defaultValue 转换错误时的默认值
     * @return 结果
     */
    public static Byte toByte(Object value, Byte defaultValue) {
        try {
            if (value == null) {
                return defaultValue;
            }
            if (value instanceof Byte) {
                return (Byte) value;
            }
            if (value instanceof Number) {
                return ((Number) value).byteValue();
            }
            final String valueStr = toStr(value, null);
            if (StringUtils.isEmpty(valueStr)) {
                return defaultValue;
            }
            try {
                return Byte.parseByte(valueStr);
            } catch (Exception e) {
                return defaultValue;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 转换为byte<br>
     * 如果给定的值为<code>null</code>，或者转换失败，返回默认值<code>null</code><br>
     * 转换失败不会报错
     *
     * @param value 被转换的值
     * @return 结果
     */
    public static Byte toByte(Object value) {
        try {
            return toByte(value, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 转换为Short<br>
     * 如果给定的值为<code>null</code>，或者转换失败，返回默认值<br>
     * 转换失败不会报错
     *
     * @param value        被转换的值
     * @param defaultValue 转换错误时的默认值
     * @return 结果
     */
    public static Short toShort(Object value, Short defaultValue) {
        try {
            if (value == null) {
                return defaultValue;
            }
            if (value instanceof Short) {
                return (Short) value;
            }
            if (value instanceof Number) {
                return ((Number) value).shortValue();
            }
            final String valueStr = toStr(value, null);
            if (StringUtils.isBlank(valueStr)) {
                return defaultValue;
            }
            try {
                return Short.parseShort(valueStr.trim());
            } catch (Exception e) {
                return defaultValue;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 转换为Short<br>
     * 如果给定的值为<code>null</code>，或者转换失败，返回默认值<code>null</code><br>
     * 转换失败不会报错
     *
     * @param value 被转换的值
     * @return 结果
     */
    public static Short toShort(Object value) {
        try {
            return toShort(value, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 转换为Number<br>
     * 如果给定的值为空，或者转换失败，返回默认值<br>
     * 转换失败不会报错
     *
     * @param value        被转换的值
     * @param defaultValue 转换错误时的默认值
     * @return 结果
     */
    public static Number toNumber(Object value, Number defaultValue) {
        try {
            if (value == null) {
                return defaultValue;
            }
            if (value instanceof Number) {
                return (Number) value;
            }
            final String valueStr = toStr(value, null);
            if (StringUtils.isEmpty(valueStr)) {
                return defaultValue;
            }
            try {
                return NumberFormat.getInstance().parse(valueStr);
            } catch (Exception e) {
                return defaultValue;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 转换为Number<br>
     * 如果给定的值为空，或者转换失败，返回默认值<code>null</code><br>
     * 转换失败不会报错
     *
     * @param value 被转换的值
     * @return 结果
     */
    public static Number toNumber(Object value) {
        try {
            return toNumber(value, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 转换为int<br>
     * 如果给定的值为空，或者转换失败，返回默认值<br>
     * 转换失败不会报错
     *
     * @param value        被转换的值
     * @param defaultValue 转换错误时的默认值
     * @return 结果
     */
    public static Integer toInt(Object value, Integer defaultValue) {
        try {
            if (value == null) {
                return defaultValue;
            }
            if (value instanceof Integer) {
                return (Integer) value;
            }
            if (value instanceof Number) {
                return ((Number) value).intValue();
            }
            final String valueStr = toStr(value, null);
            if (StringUtils.isEmpty(valueStr)) {
                return defaultValue;
            }
            try {
                return Integer.parseInt(valueStr.trim());
            } catch (Exception e) {
                return defaultValue;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 转换为int<br>
     * 如果给定的值为<code>null</code>，或者转换失败，返回默认值<code>null</code><br>
     * 转换失败不会报错
     *
     * @param value 被转换的值
     * @return 结果
     */
    public static Integer toInt(Object value) {
        try {
            return toInt(value, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 转换为Integer数组<br>
     *
     * @param str 被转换的值
     * @return 结果
     */
    public static Integer[] toIntArray(String str) {
        try {
            return toIntArray(",", str);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 转换为Long数组<br>
     *
     * @param str 被转换的值
     * @return 结果
     */
    public static Long[] toLongArray(String str) {
        try {
            return toLongArray(",", str);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 转换为Integer数组<br>
     *
     * @param split 分隔符
     * @param split 被转换的值
     * @return 结果
     */
    public static Integer[] toIntArray(String split, String str) {
        try {
            if (StringUtils.isEmpty(str)) {
                return new Integer[]{};
            }
            String[] arr = str.split(split);
            final Integer[] ints = new Integer[arr.length];
            for (int i = 0; i < arr.length; i++) {
                final Integer v = toInt(arr[i], 0);
                ints[i] = v;
            }
            return ints;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 转换为Long数组<br>
     *
     * @param split 分隔符
     * @param str   被转换的值
     * @return 结果
     */
    public static Long[] toLongArray(String split, String str) {
        try {
            if (StringUtils.isEmpty(str)) {
                return new Long[]{};
            }
            String[] arr = str.split(split);
            final Long[] longs = new Long[arr.length];
            for (int i = 0; i < arr.length; i++) {
                final Long v = toLong(arr[i], null);
                longs[i] = v;
            }
            return longs;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 转换为String数组<br>
     *
     * @param str 被转换的值
     * @return 结果
     */
    public static String[] toStrArray(String str) {
        try {
            return toStrArray(",", str);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 转换为String数组<br>
     *
     * @param split 分隔符
     * @param split 被转换的值
     * @return 结果
     */
    public static String[] toStrArray(String split, String str) {
        try {
            return str.split(split);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 转换为long<br>
     * 如果给定的值为空，或者转换失败，返回默认值<br>
     * 转换失败不会报错
     *
     * @param value        被转换的值
     * @param defaultValue 转换错误时的默认值
     * @return 结果
     */
    public static Long toLong(Object value, Long defaultValue) {
        try {
            if (value == null) {
                return defaultValue;
            }
            if (value instanceof Long) {
                return (Long) value;
            }
            if (value instanceof Number) {
                return ((Number) value).longValue();
            }
            if (value instanceof Date) {
                return ((Date) value).getTime();
            }
            final String valueStr = toStr(value, null);
            if (StringUtils.isEmpty(valueStr)) {
                return defaultValue;
            }
            try {
                // 支持科学计数法
                return new BigDecimal(valueStr.trim()).longValue();
            } catch (Exception e) {
                return defaultValue;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 转换为long<br>
     * 如果给定的值为<code>null</code>，或者转换失败，返回默认值<code>null</code><br>
     * 转换失败不会报错
     *
     * @param value 被转换的值
     * @return 结果
     */
    public static Long toLong(Object value) {
        try {
            return toLong(value, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 转换为double<br>
     * 如果给定的值为空，或者转换失败，返回默认值<br>
     * 转换失败不会报错
     *
     * @param value        被转换的值
     * @param defaultValue 转换错误时的默认值
     * @return 结果
     */
    public static Double toDouble(Object value, Double defaultValue) {
        try {
            if (value == null) {
                return defaultValue;
            }
            if (value instanceof Double) {
                return (Double) value;
            }
            if (value instanceof Number) {
                return ((Number) value).doubleValue();
            }
            final String valueStr = toStr(value, null);
            if (StringUtils.isEmpty(valueStr)) {
                return defaultValue;
            }
            try {
                // 支持科学计数法
                return new BigDecimal(valueStr.trim()).doubleValue();
            } catch (Exception e) {
                return defaultValue;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 转换为double<br>
     * 如果给定的值为空，或者转换失败，返回默认值<code>null</code><br>
     * 转换失败不会报错
     *
     * @param value 被转换的值
     * @return 结果
     */
    public static Double toDouble(Object value) {
        try {
            return toDouble(value, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 转换为Float<br>
     * 如果给定的值为空，或者转换失败，返回默认值<br>
     * 转换失败不会报错
     *
     * @param value        被转换的值
     * @param defaultValue 转换错误时的默认值
     * @return 结果
     */
    public static Float toFloat(Object value, Float defaultValue) {
        try {
            if (value == null) {
                return defaultValue;
            }
            if (value instanceof Float) {
                return (Float) value;
            }
            if (value instanceof Number) {
                return ((Number) value).floatValue();
            }
            final String valueStr = toStr(value, null);
            if (StringUtils.isEmpty(valueStr)) {
                return defaultValue;
            }
            try {
                return Float.parseFloat(valueStr.trim());
            } catch (Exception e) {
                return defaultValue;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 转换为Float<br>
     * 如果给定的值为空，或者转换失败，返回默认值<code>null</code><br>
     * 转换失败不会报错
     *
     * @param value 被转换的值
     * @return 结果
     */
    public static Float toFloat(Object value) {
        try {
            return toFloat(value, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 转换为boolean<br>
     * String支持的值为：true、false、yes、ok、no，1,0 如果给定的值为空，或者转换失败，返回默认值<br>
     * 转换失败不会报错
     *
     * @param value        被转换的值
     * @param defaultValue 转换错误时的默认值
     * @return 结果
     */
    public static Boolean toBool(Object value, Boolean defaultValue) {
        try {
            if (value == null) {
                return defaultValue;
            }
            if (value instanceof Boolean) {
                return (Boolean) value;
            }
            String valueStr = toStr(value, null);
            if (StringUtils.isEmpty(valueStr)) {
                return defaultValue;
            }
            valueStr = valueStr.trim().toLowerCase();
            switch (valueStr) {
                case "true":
                    return true;
                case "false":
                    return false;
                case "yes":
                    return true;
                case "ok":
                    return true;
                case "no":
                    return false;
                case "1":
                    return true;
                case "0":
                    return false;
                default:
                    return defaultValue;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 转换为boolean<br>
     * 如果给定的值为空，或者转换失败，返回默认值<code>null</code><br>
     * 转换失败不会报错
     *
     * @param value 被转换的值
     * @return 结果
     */
    public static Boolean toBool(Object value) {
        try {
            return toBool(value, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 转换为Enum对象<br>
     * 如果给定的值为空，或者转换失败，返回默认值<br>
     *
     * @param clazz        Enum的Class
     * @param value        值
     * @param defaultValue 默认值
     * @return Enum
     */
    public static <E extends Enum<E>> E toEnum(Class<E> clazz, Object value, E defaultValue) {
        try {
            if (value == null) {
                return defaultValue;
            }
            if (clazz.isAssignableFrom(value.getClass())) {
                @SuppressWarnings("unchecked")
                E myE = (E) value;
                return myE;
            }
            final String valueStr = toStr(value, null);
            if (StringUtils.isEmpty(valueStr)) {
                return defaultValue;
            }
            try {
                return Enum.valueOf(clazz, valueStr);
            } catch (Exception e) {
                return defaultValue;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 转换为Enum对象<br>
     * 如果给定的值为空，或者转换失败，返回默认值<code>null</code><br>
     *
     * @param clazz Enum的Class
     * @param value 值
     * @return Enum
     */
    public static <E extends Enum<E>> E toEnum(Class<E> clazz, Object value) {
        try {
            return toEnum(clazz, value, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 转换为BigInteger<br>
     * 如果给定的值为空，或者转换失败，返回默认值<br>
     * 转换失败不会报错
     *
     * @param value        被转换的值
     * @param defaultValue 转换错误时的默认值
     * @return 结果
     */
    public static BigInteger toBigInteger(Object value, BigInteger defaultValue) {
        try {
            if (value == null) {
                return defaultValue;
            }
            if (value instanceof BigInteger) {
                return (BigInteger) value;
            }
            if (value instanceof Long) {
                return BigInteger.valueOf((Long) value);
            }
            final String valueStr = toStr(value, null);
            if (StringUtils.isEmpty(valueStr)) {
                return defaultValue;
            }
            try {
                return new BigInteger(valueStr);
            } catch (Exception e) {
                return defaultValue;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 转换为BigInteger<br>
     * 如果给定的值为空，或者转换失败，返回默认值<code>null</code><br>
     * 转换失败不会报错
     *
     * @param value 被转换的值
     * @return 结果
     */
    public static BigInteger toBigInteger(Object value) {
        try {
            return toBigInteger(value, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 转换为BigDecimal<br>
     * 如果给定的值为空，或者转换失败，返回默认值<br>
     * 转换失败不会报错
     *
     * @param value        被转换的值
     * @param defaultValue 转换错误时的默认值
     * @return 结果
     */
    public static BigDecimal toBigDecimal(Object value, BigDecimal defaultValue) {
        try {
            if (value == null) {
                return defaultValue;
            }
            if (value instanceof BigDecimal) {
                return (BigDecimal) value;
            }
            if (value instanceof Long) {
                return new BigDecimal((Long) value);
            }
            if (value instanceof Double) {
                return new BigDecimal((Double) value);
            }
            if (value instanceof Integer) {
                return new BigDecimal((Integer) value);
            }
            final String valueStr = toStr(value, null);
            if (StringUtils.isEmpty(valueStr)) {
                return defaultValue;
            }
            try {
                return new BigDecimal(valueStr);
            } catch (Exception e) {
                return defaultValue;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 转换为BigDecimal<br>
     * 如果给定的值为空，或者转换失败，返回默认值<br>
     * 转换失败不会报错
     *
     * @param value 被转换的值
     * @return 结果
     */
    public static BigDecimal toBigDecimal(Object value) {
        try {
            return toBigDecimal(value, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static Object toJson(Object obj){
        String base = toStr(obj);
        if (isjsonObject(base)) {
            JSONObject json = JSONObject.parseObject(base);
            for (String key : json.keySet()) {
                Object value = toJson(json.get(key));
                if (value == null) {
                    continue;
                }
                json.put(key, value);
            }
            return json;
        }
        if (isjsonArray(base)) {
            JSONArray json = JSONObject.parseArray(base);
            for (int i = 0; i < json.size(); i++) {
                Object element = toJson(json.get(i));
                if (element == null) {
                    continue;
                }
                json.set(i, element);
            }
            return json;
        }
        if ("\"\"".equals(obj)) {
            return null;
        }
        return obj;
    }

    public static boolean isjsonObject(String str) {
        try {
            if (StringUtils.isBlank(str)) {
                return Boolean.FALSE;
            }
            JSONObject jsonStr = JSONObject.parseObject(str);
            return Boolean.TRUE;
        } catch (Exception e) {
            return Boolean.FALSE;
        }
    }

    public static boolean isjsonArray(String str) {
        try {
            if (StringUtils.isBlank(str)) {
                return Boolean.FALSE;
            }
            JSONArray jsonStr = JSONObject.parseArray(str);
            return Boolean.TRUE;
        } catch (Exception e) {
            return Boolean.FALSE;
        }
    }

    /**
     * 将对象转为字符串<br>
     * 1、Byte数组和ByteBuffer会被转换为对应字符串的数组 2、对象数组会调用Arrays.toString方法
     *
     * @param obj 对象
     * @return 字符串
     */
    public static String utf8Str(Object obj) {
        try {
            return str(obj, "UTF-8");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 将对象转为字符串<br>
     * 1、Byte数组和ByteBuffer会被转换为对应字符串的数组 2、对象数组会调用Arrays.toString方法
     *
     * @param obj         对象
     * @param charsetName 字符集
     * @return 字符串
     */
    public static String str(Object obj, String charsetName) {
        try {
            return str(obj, Charset.forName(charsetName));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 将对象转为字符串<br>
     * 1、Byte数组和ByteBuffer会被转换为对应字符串的数组 2、对象数组会调用Arrays.toString方法
     *
     * @param obj     对象
     * @param charset 字符集
     * @return 字符串
     */
    public static String str(Object obj, Charset charset) {
        try {
            if (null == obj) {
                return null;
            }

            if (obj instanceof String) {
                return (String) obj;
            } else if (obj instanceof byte[]) {
                return str((byte[]) obj, charset);
            } else if (obj instanceof Byte[]) {
                byte[] bytes = ArrayUtils.toPrimitive((Byte[]) obj);
                return str(bytes, charset);
            } else if (obj instanceof ByteBuffer) {
                return str((ByteBuffer) obj, charset);
            }
            return obj.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 将byte数组转为字符串
     *
     * @param bytes   byte数组
     * @param charset 字符集
     * @return 字符串
     */
    public static String str(byte[] bytes, String charset) {
        try {
            return str(bytes, StringUtils.isEmpty(charset) ? Charset.defaultCharset() : Charset.forName(charset));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 解码字节码
     *
     * @param data    字符串
     * @param charset 字符集，如果此字段为空，则解码的结果取决于平台
     * @return 解码后的字符串
     */
    public static String str(byte[] data, Charset charset) {
        try {
            if (data == null) {
                return null;
            }

            if (null == charset) {
                return new String(data);
            }
            return new String(data, charset);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 将编码的byteBuffer数据转换为字符串
     *
     * @param data    数据
     * @param charset 字符集，如果为空使用当前系统字符集
     * @return 字符串
     */
    public static String str(ByteBuffer data, String charset) {
        try {
            if (data == null) {
                return null;
            }

            return str(data, Charset.forName(charset));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 将编码的byteBuffer数据转换为字符串
     *
     * @param data    数据
     * @param charset 字符集，如果为空使用当前系统字符集
     * @return 字符串
     */
    public static String str(ByteBuffer data, Charset charset) {
        try {
            if (null == charset) {
                charset = Charset.defaultCharset();
            }
            return charset.decode(data).toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    // ----------------------------------------------------------------------- 全角半角转换

    /**
     * 半角转全角
     *
     * @param input String.
     * @return 全角字符串.
     */
    public static String toSBC(String input) {
        try {
            return toSBC(input, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 半角转全角
     *
     * @param input         String
     * @param notConvertSet 不替换的字符集合
     * @return 全角字符串.
     */
    public static String toSBC(String input, Set<Character> notConvertSet) {
        try {
            char c[] = input.toCharArray();
            for (int i = 0; i < c.length; i++) {
                if (null != notConvertSet && notConvertSet.contains(c[i])) {
                    // 跳过不替换的字符
                    continue;
                }

                if (c[i] == ' ') {
                    c[i] = '\u3000';
                } else if (c[i] < '\177') {
                    c[i] = (char) (c[i] + 65248);

                }
            }
            return new String(c);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 全角转半角
     *
     * @param input String.
     * @return 半角字符串
     */
    public static String toDBC(String input) {
        try {
            return toDBC(input, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 替换全角为半角
     *
     * @param text          文本
     * @param notConvertSet 不替换的字符集合
     * @return 替换后的字符
     */
    public static String toDBC(String text, Set<Character> notConvertSet) {
        try {
            char c[] = text.toCharArray();
            for (int i = 0; i < c.length; i++) {
                if (null != notConvertSet && notConvertSet.contains(c[i])) {
                    // 跳过不替换的字符
                    continue;
                }

                if (c[i] == '\u3000') {
                    c[i] = ' ';
                } else if (c[i] > '\uFF00' && c[i] < '\uFF5F') {
                    c[i] = (char) (c[i] - 65248);
                }
            }
            String returnString = new String(c);

            return returnString;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 数字金额大写转换 先写个完整的然后将如零拾替换成零
     *
     * @param n 数字
     * @return 中文大写数字
     */
    public static String digitUppercase(double n) {
        try {
            String[] fraction = {"角", "分"};
            String[] digit = {"零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖"};
            String[][] unit = {{"元", "万", "亿"}, {"", "拾", "佰", "仟"}};

            String head = n < 0 ? "负" : "";
            n = Math.abs(n);

            String s = "";
            for (int i = 0; i < fraction.length; i++) {
                s += (digit[(int) (Math.floor(n * 10 * Math.pow(10, i)) % 10)] + fraction[i]).replaceAll("(零.)+", "");
            }
            if (s.length() < 1) {
                s = "整";
            }
            int integerPart = (int) Math.floor(n);

            for (int i = 0; i < unit[0].length && integerPart > 0; i++) {
                String p = "";
                for (int j = 0; j < unit[1].length && n > 0; j++) {
                    p = digit[integerPart % 10] + unit[1][j] + p;
                    integerPart = integerPart / 10;
                }
                s = p.replaceAll("(零.)*零$", "").replaceAll("^$", "零") + unit[0][i] + s;
            }
            return head + s.replaceAll("(零.)*零元", "元").replaceFirst("(零.)+", "").replaceAll("(零.)+", "零").replaceAll("^整$", "零元整");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}

