package com.memberintergral.musicplayerservice.util;

/**
 * <AUTHOR>
 * @Desc: 临期策略工具类
 * @date 2024-04-07 10:49:44
 */
public class LinQiStrategyUtil {

    public static boolean checkLux(VipInfo vipInfo, int offsetDay){
        long now = System.currentTimeMillis();
        vipInfo.setNow(now);
        vipInfo.build();
        if (vipInfo.getLuxVipOffset() > 0  && vipInfo.getLuxVipOffset()<=offsetDay && vipInfo.luxAutoPayUser == 0){
            return true;
        }
        return false;
    }


    /**
     * 校验当前用户是否临期
     * @param vipInfo
     * @return
     */
    public static LinQiResult check(VipInfo vipInfo){
        long now = System.currentTimeMillis();
        vipInfo.setNow(now);
        vipInfo.build();
        LinQiResult linQiResult = new LinQiResult();
        linQiResult.setLuxLinQiDays(vipInfo.getLuxVipOffset());
        linQiResult.setSvipLinQiDays(vipInfo.getSvipOffset());
        linQiResult.setCheZaiLinQiDays(vipInfo.getCheZaiOffset());
        linQiResult.setVipmLinQiDays(vipInfo.getVipmOffset());
        if (vipInfo.getVipmOffset() > 0  && vipInfo.getVipmOffset()<=7 && vipInfo.vipmAutoPayUser == 0){
            linQiResult.setIsLinQiUser((byte) (linQiResult.getIsLinQiUser() | LinQiResult.VIPM_LIN_QI_USER));
        }
        if (vipInfo.getLuxVipOffset() > 0  && vipInfo.getLuxVipOffset()<=7 && vipInfo.luxAutoPayUser == 0){
            linQiResult.setIsLinQiUser((byte) (linQiResult.getIsLinQiUser() | LinQiResult.LUX_LIN_QI_USER));
        }
        if (vipInfo.getCheZaiOffset() > 0  && vipInfo.getCheZaiOffset()<=7 && vipInfo.cheZaiAutoPayUser == 0){
            linQiResult.setIsLinQiUser((byte) (linQiResult.getIsLinQiUser() | LinQiResult.CHEZAI_LIN_QI_USER));
        }
        if (vipInfo.getSvipOffset() > 0  && vipInfo.getSvipOffset()<=7 && vipInfo.svipAutoPayUser == 0){
            linQiResult.setIsLinQiUser((byte) (linQiResult.getIsLinQiUser() | LinQiResult.SVIP_LIN_QI_USER));
        }
        return linQiResult;
    }


    /**
     * vip 信息
     */
    public static class VipInfo{
        private Long vipmExpireTime = 0L;
        private Long luxVipExpireTime = 0L;
        private Long cheZaiExpireTime = 0L;
        private Long svipExpireTime = 0L;
        public int luxAutoPayUser = 0;
        public int vipmAutoPayUser = 0;
        public int svipAutoPayUser = 0;
        public int cheZaiAutoPayUser = 0;

        private long now = 0L;

        public final Long ONE_DAY = 24 * 60 * 60 * 1000L;


        private int vipmOffset;
        private int luxVipOffset;
        private int cheZaiOffset;
        private int svipOffset;


        public void setNow(long now){
            this.now = now;
        }

        public void setVipmExpireTime(Long vipmExpireTime) {
            this.vipmExpireTime = vipmExpireTime;
        }

        public void setLuxVipExpireTime(Long luxVipExpireTime) {
            this.luxVipExpireTime = luxVipExpireTime;
        }

        public void setCheZaiExpireTime(Long cheZaiExpireTime) {
            this.cheZaiExpireTime = cheZaiExpireTime;
        }

        public void setSvipExpireTime(Long svipExpireTime) {
            this.svipExpireTime = svipExpireTime;
        }
        public void build(){
            long diffVipmTime = vipmExpireTime - now;
            long diffLuxVip = luxVipExpireTime - now;
            long diffCheZaiTime = cheZaiExpireTime - now;
            long diffSvipTime = svipExpireTime - now;

            vipmOffset = offset(diffVipmTime);
            luxVipOffset = offset(diffLuxVip);
            cheZaiOffset = offset(diffCheZaiTime);
            svipOffset = offset(diffSvipTime);
        }

        private int offset(long diffTime){
            if (diffTime<=0){
                return (int) (diffTime / ONE_DAY);
            }
            if (diffTime % ONE_DAY == 0 ){
                return (int) (diffTime / ONE_DAY);
            }
            return (int) (diffTime / ONE_DAY + 1);
        }
        public int getVipmOffset() {
            return vipmOffset;
        }

        public int getLuxVipOffset() {
            return luxVipOffset;
        }

        public int getCheZaiOffset() {
            return cheZaiOffset;
        }

        public int getSvipOffset() {
            return svipOffset;
        }
    }



    public static class LinQiResult{

        /**
         * 是否临期用户
         * 0 非临期
         * 1 音乐包临期   1<<0
         * 2 豪华vip临期 1 <<1
         * 3 车载临期    1<<2
         * 4 超会临期    1<<3
         *
         */
        private byte isLinQiUser = 0;

        // 音乐包临期天数
        private int vipmLinQiDays;
        // 豪v临期天数
        private int luxLinQiDays;
        // 超会临期天数
        private int svipLinQiDays;
        // 车载临期天数
        private int cheZaiLinQiDays;

        public static final int BASE_OFFSET = 0;

        // 固定的状态
        public static final byte VIPM_LIN_QI_USER = 1 << BASE_OFFSET;
        public static final byte LUX_LIN_QI_USER = 1<< BASE_OFFSET+1;
        public static final byte CHEZAI_LIN_QI_USER = 1<<BASE_OFFSET+2;
        public static final byte SVIP_LIN_QI_USER = 1<< BASE_OFFSET+3;

        /**
         *  多个状态可以通过  vipmLinQiUser | luxLinQiUser 这种形式进行设置
         * @param isLinQiUser
         */
        public void setIsLinQiUser(byte isLinQiUser) {
            this.isLinQiUser = isLinQiUser;
        }


        public boolean ctl(int n){
            return (isLinQiUser >> n & 1) == 1;
        }

        public int getVipmLinQiDays() {
            return vipmLinQiDays;
        }

        public void setVipmLinQiDays(int vipmLinQiDays) {
            this.vipmLinQiDays = vipmLinQiDays;
        }

        public int getLuxLinQiDays() {
            return luxLinQiDays;
        }

        public void setLuxLinQiDays(int luxLinQiDays) {
            this.luxLinQiDays = luxLinQiDays;
        }

        public int getSvipLinQiDays() {
            return svipLinQiDays;
        }

        public void setSvipLinQiDays(int svipLinQiDays) {
            this.svipLinQiDays = svipLinQiDays;
        }

        public int getCheZaiLinQiDays() {
            return cheZaiLinQiDays;
        }

        public void setCheZaiLinQiDays(int cheZaiLinQiDays) {
            this.cheZaiLinQiDays = cheZaiLinQiDays;
        }

        public byte getIsLinQiUser() {
            return isLinQiUser;
        }

        public boolean isChezaiLinQi(){
            return ctl(BASE_OFFSET + 2);
        }

        public boolean isSvipLinQi(){
            return ctl(BASE_OFFSET + 3);
        }

        public boolean isVipmLinQi(){
            return ctl(BASE_OFFSET);
        }

        public boolean isLuxVIpLinQi(){
            return ctl(BASE_OFFSET + 1);
        }


    }

    public static void main(String[] args) {
        System.out.println((byte)1<<0);
        System.out.println((byte)1<<1);
        System.out.println((byte)1<<2);
        System.out.println((byte)1<<3);
        System.out.println((byte)1<<3 | (byte)1<<2);
        LinQiResult linQiResult = new LinQiResult();
        linQiResult.setIsLinQiUser((byte) (LinQiResult.VIPM_LIN_QI_USER | LinQiResult.CHEZAI_LIN_QI_USER | LinQiResult.SVIP_LIN_QI_USER));
        System.out.println(linQiResult.isLinQiUser);
        System.out.println( linQiResult.ctl(LinQiResult.BASE_OFFSET+1));
    }


}
