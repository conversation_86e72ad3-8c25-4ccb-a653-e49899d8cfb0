package com.memberintergral.musicplayerservice.util.recallStrategy;

import com.google.common.base.Ticker;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.kuwo.commercialization.common.utill.DateUtil;
import com.memberintergral.musicplayerservice.cache.PlayerTrialNacos;
import com.memberintergral.musicplayerservice.entity.PlayerTrialInfo;
import com.memberintergral.musicplayerservice.entity.dto.DataDatasetDTO;
import com.memberintergral.musicplayerservice.entity.dto.DataItemDTO;
import com.memberintergral.musicplayerservice.entity.dto.UserBlankDTO;
import com.memberintergral.musicplayerservice.entity.query.DataDatasetQuery;
import com.memberintergral.musicplayerservice.entity.query.DataItemQuery;
import com.memberintergral.musicplayerservice.mapper.shard.PlayerTrialMapper;
import com.memberintergral.musicplayerservice.req.RecallRequest;
import com.memberintergral.musicplayerservice.resp.PlayerTrialRes;
import com.memberintergral.musicplayerservice.service.DataDatasetService;
import com.memberintergral.musicplayerservice.service.DataItemService;
import com.memberintergral.musicplayerservice.service.UserBlankService;
import com.memberintergral.musicplayerservice.util.recallStrategy.common.PropertiesValue;
import com.memberintergral.musicplayerservice.util.recallStrategy.common.RedisKey;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.memberintergral.musicplayerservice.util.recallStrategy
 * @date:2022/12/8
 */
@Service
@Order(14)
@Slf4j
public class PlayerRecall extends AbstractRecallStrategy{

    private static final String TEST_POP_PREFIX = "https://h5app.kuwo.cn/pay/uat_commonPop/index.html?code=%s";

    private static final String POP_PREFIX = "https://h5app.kuwo.cn/pay/commonPop/index.html?code=%s";

    private LoadingCache<String,List<String>> cache;

    //最低支持版本
    private static final Integer MIN_ANDROID_VERSION = 10341;
    private static final Integer MIN_IOS_VERSION = 10410;

    @Autowired
    private PlayerTrialNacos playerTrialNacos;

    private static final String DATA_SET_KEY = "commonPopSettings";

    @Autowired
    private PlayerTrialMapper playerTrialMapper;

    @Autowired
    private DataDatasetService dataDatasetService;

    @Autowired
    private DataItemService dataItemService;

    @Autowired
    private UserBlankService userBlankService;

    @Override
    public Result deploy(RecallRequest request) {
        Date now = request.getNow();
        String userId = request.getUserId();
        String source = request.getSource();
        String platform = request.getPlatform();
        Long userIdLong = request.getUserIdLong();

        if (order != null && order < 0) {
            UserBlankDTO userBlankDTO = new UserBlankDTO();
            userBlankDTO.setUserId(userIdLong);
            userBlankDTO.setCode("playerPop");
            if (!userBlankService.isExist(userBlankDTO)) {
                return next(request);
            }
        }

        //判断版本的
        if (!versionLargeThan(source, platform)) {
            log.info("PlayerRecall deploy, {} app version is low", userId);
            return next(request);
        }

        String playerFreeUserCacheKey=RedisKey.analysisKey(RedisKey.PLAYER_PREE_USER_CACHE_KEY, userId);
        if (redisNewVipTransferUtil.getObject(playerFreeUserCacheKey, Integer.class)!=null) {
            return next(request);
        }

        Map<String, String> userSamInfoCache = request.getUserSamInfoCache();
        String svipExpireStr = userSamInfoCache.get("svipExpire");
        String vipLuxuryExpireStr = userSamInfoCache.get("vipLuxuryExpire");
        String experienceExpireStr = userSamInfoCache.get("experienceExpire");

        Long svipExpire = StringUtils.isBlank(svipExpireStr)?0L:Long.parseLong(svipExpireStr);
        Long vipLuxuryExpire = StringUtils.isBlank(vipLuxuryExpireStr)?0L:Long.parseLong(vipLuxuryExpireStr);
        Long experienceExpire = StringUtils.isBlank(experienceExpireStr)?0L:Long.parseLong(experienceExpireStr);

        //用户身份获取-超会、豪V、体验会员 不弹
        if (svipExpire > now.getTime() || vipLuxuryExpire > now.getTime() || experienceExpire > now.getTime()) {
            return next(request);
        }

        PlayerTrialRes playerTrialRes = playerTrialNacos.getPlayerTrialData();
        if (StringUtils.isBlank(playerTrialRes.getStartTime()) || StringUtils.isBlank(playerTrialRes.getEndTime())) {
            return next(request);
        }

        Date startTime = DateUtil.parseDate(playerTrialRes.getStartTime(), DateUtil.STANDARD_DAY_HOUR_FORMAT);
        Date endTime = DateUtil.parseDate(playerTrialRes.getEndTime(), DateUtil.STANDARD_DAY_HOUR_FORMAT);
        if (now.before(startTime) || now.after(endTime)) {
            return next(request);
        }

        PlayerTrialInfo playerTrialInfo =  playerTrialMapper.getUserTrialInfo(Long.parseLong(userId));
        if (playerTrialInfo!=null) {
            return next(request);
        }

        load();

        List<String> popList = null;
        try {
            popList = cache.get(DATA_SET_KEY);
        } catch (ExecutionException e) {
            e.printStackTrace();
        }
        if (CollectionUtils.isEmpty(popList)) {
            return next(request);
        }
        int index = new Random().nextInt(popList.size());
        String url = popList.get(index);

        if (PropertiesValue.SERVER_MODEL.equals("prod")) {
            url = String.format(POP_PREFIX, url);
        } else {
            url = String.format(TEST_POP_PREFIX, url);
        }

        redisNewVipTransferUtil.setObject(playerFreeUserCacheKey, 1, RedisKey.ONE_DAY_EXPIRE);
        redisNewVipTransferUtil.increment(RedisKey.PLAYER_PREE_POP_COUNT_KEY);

        return new Result(url);
    }

    private void load(){
        if (cache != null) {
            return;
        }
        cache = CacheBuilder.newBuilder()
                .concurrencyLevel(10)
                .refreshAfterWrite(10L, TimeUnit.SECONDS)
                .ticker(Ticker.systemTicker())
                .maximumSize(50)
                .build(
                        new CacheLoader<String, List<String>>() {
                            @Override
                            public List<String> load(String s) {
                                DataDatasetQuery dataDatasetQuery = new DataDatasetQuery();
                                dataDatasetQuery.setCode(s);
                                List<DataDatasetDTO> dataDatasetDTOS = dataDatasetService.selectByQuery(dataDatasetQuery);
                                if (CollectionUtils.isEmpty(dataDatasetDTOS)) {
                                    return Lists.newArrayList();
                                }
                                Integer id = dataDatasetDTOS.stream().findFirst().get().getId();
                                DataItemQuery dataItemQuery = new DataItemQuery();
                                dataItemQuery.setMid(id);
                                List<DataItemDTO> dataItemDTOS = dataItemService.selectByQuery(dataItemQuery);
                                if (CollectionUtils.isEmpty(dataDatasetDTOS)) {
                                    return Lists.newArrayList();
                                }
                                List<String> collect = dataItemDTOS.stream().map(DataItemDTO::getCode).collect(Collectors.toList());
                                return collect;
                            }
                        }
                );
    }

    /**
     * 判断版本号大于等于 某个版本
     * @param channel
     * @return
     */
    public static boolean versionLargeThan(String channel, String platform){
        if(StringUtils.isNotEmpty(channel) && channel.split("_").length==4){
            String[] channelArr = channel.split("_");
            int version= NumberUtils.toInt(channelArr[2].replace(".",""));
            int targetVersion = "android".equals(platform) ? MIN_ANDROID_VERSION : MIN_IOS_VERSION;
            return version>=targetVersion;
        }
        return false;
    }
}
