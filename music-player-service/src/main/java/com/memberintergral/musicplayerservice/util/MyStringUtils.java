package com.memberintergral.musicplayerservice.util;

import org.apache.commons.lang3.StringUtils;

public class MyStringUtils {

	public static String formatStrNull(String str) {
		if(StringUtils.isEmpty(str)){
			return "";
		}else{
			return str;
		}
	}

	public static String truncateString(String input, int maxLength) {
		if (input == null || input.length() <= maxLength) {
			return input; // 长度不足或刚好，直接返回
		}

		// 保留前 maxLength 个字符，剩余 替换为 "..."
		String prefix = input.substring(0, maxLength);
		return prefix + "...";
	}
	
}
