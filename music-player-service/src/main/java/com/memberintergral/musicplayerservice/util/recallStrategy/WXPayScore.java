package com.memberintergral.musicplayerservice.util.recallStrategy;

import com.memberintergral.musicplayerservice.entity.WxPayScoreOrderDetail;
import com.memberintergral.musicplayerservice.mapper.musicPlayer.WxPayScoreOrderDetailMapper;
import com.memberintergral.musicplayerservice.req.RecallRequest;
import com.memberintergral.musicplayerservice.util.recallStrategy.common.RecallPopUrl;
import com.memberintergral.musicplayerservice.util.recallStrategy.common.RedisKey;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.memberintergral.musicplayerservice.util.RecallStrategy
 * 微信先享后付,使用过的用户, 先享会员过期时候进行弹窗
 * 1.判断当前用户是否开过先享后付
 * 2.设置期限 1天1弹
 * @date:2022/9/2
 */
//@Service
@Order(1)
@Deprecated
public class WXPayScore extends AbstractRecallStrategy {

    @Autowired
    private WxPayScoreOrderDetailMapper wxPayScoreOrderDetailMapper;

    @Override
    public Result deploy(RecallRequest request) {
        if (request.getUserIdStr().equals("224734575")) {
            return new Result(RecallPopUrl.WX_PAY_SCORE_POP_WINDOW_URL);
        }
        String redisKey = RedisKey.analysisKey(RedisKey.WX_PAY_SCORE_POP_WINDOW_KEY, request.getUserIdStr());
        String wxPayScorePopWindowValue = redisNewVipTransferUtil.getString(redisKey);
        if (StringUtils.isBlank(wxPayScorePopWindowValue)) {
            WxPayScoreOrderDetail query = new WxPayScoreOrderDetail();
            query.setUserId(request.getUserIdLong());
            WxPayScoreOrderDetail wxPayScoreOrderDetail = wxPayScoreOrderDetailMapper.selectOneByQuery(query);
            if (Integer.parseInt(request.getRecallVersion()) >= 2 && wxPayScoreOrderDetail != null && wxPayScoreOrderDetail.getServerEndTime().before(request.getNow())) {
                redisNewVipTransferUtil.setString(redisKey, RedisKey.STR_YES_FLAG, RedisKey.ONE_DAY_EXPIRE);
                return new Result(RecallPopUrl.WX_PAY_SCORE_POP_WINDOW_URL);
            }
        }
        return next(request);
    }
}
