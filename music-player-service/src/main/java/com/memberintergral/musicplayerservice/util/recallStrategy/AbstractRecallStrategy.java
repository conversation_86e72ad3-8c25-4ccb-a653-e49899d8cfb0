package com.memberintergral.musicplayerservice.util.recallStrategy;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.kuwo.commercialization.common.utill.SpringAwareUtil;
import com.memberintergral.musicplayerservice.cache.RecallPopNacos;
import com.memberintergral.musicplayerservice.req.RecallRequest;
import com.memberintergral.musicplayerservice.util.FrameTimer;
import com.memberintergral.musicplayerservice.util.recallStrategy.common.RedisKey;
import com.memberintergral.musicplayerservice.util.redis.RedisNewVipTransferUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.AnnotationAwareOrderComparator;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.ToIntFunction;
import java.util.stream.Collectors;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.memberintergral.musicplayerservice.util.RecallStrategy
 * @date:2022/9/2
 */
@Slf4j
public abstract class AbstractRecallStrategy {

    @Autowired
    protected RedisNewVipTransferUtil redisNewVipTransferUtil;

    protected AbstractRecallStrategy strategy;

    protected Integer order;

    private static ThreadLocal<Map<String, Long>> spendTimeMapThreadLocal = new InheritableThreadLocal<>();

    public static AbstractRecallStrategy chain() {
        FrameTimer watch = FrameTimer.getInstance();
        Map<String, AbstractRecallStrategy> beansOfType = SpringAwareUtil.getApplicationContext().getBeansOfType(AbstractRecallStrategy.class);
        log.info("method:{}, search strategy bean, take time {}", "chain", watch.getSplitTime0());
        if (MapUtils.isEmpty(beansOfType)) {
            return null;
        }
        //去除掉 废弃的
        Iterator<String> iterator = beansOfType.keySet().iterator();
        while (iterator.hasNext()) {
            if (Objects.nonNull(AnnotationUtils.findAnnotation(beansOfType.get(iterator.next()).getClass(), Deprecated.class))) {
                iterator.remove();
            }
        }

        RecallPopNacos recallPopNacos = SpringAwareUtil.getApplicationContext().getBean(RecallPopNacos.class);

        if (recallPopNacos != null) {
            beansOfType.keySet().stream().forEach(e -> {
                        Integer value = recallPopNacos.getNacosData(e);
                        if (Objects.nonNull(value)) {
                            beansOfType.get(e).setOrder(value);
                        }
                    }
            );
        }

        //先使用配置文件的顺序进行排序
        //order 数值部分是优先级，负号标识开启白名单
        ToIntFunction<? super AbstractRecallStrategy> keyExtractor = e -> Math.abs(e.getOrder());
        List<AbstractRecallStrategy> list = beansOfType.values().stream().filter(Objects::nonNull).filter(e -> Objects.nonNull(e.getOrder())).filter(e -> !e.getOrder().equals(RedisKey.NOT_ENABLED_RECALL_RECALLSTRATEGY)).sorted(Comparator.comparingInt(keyExtractor).reversed()).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(list)) {
            //配置文件中不存在 则使用注解Order的值进行排序
            list = Lists.newArrayList(beansOfType.values());
            AnnotationAwareOrderComparator.sort(list);
            Collections.reverse(list);
            log.info("method:{}, @Order strategy bean, take time {}", "chain", watch.getSplitTime0());
        }

        AbstractRecallStrategy first = list.stream().peek(e -> e.setNextApproval(null)).reduce((a, b) -> {
            b.setNextApproval(a);
            return b;
        }).orElse(null);
        //此处很必要，避免循环调用
        list.stream().findFirst().orElse(new AbstractRecallStrategy() {
            @Override
            public Result deploy(RecallRequest request) {
                return null;
            }
        }).setNextApproval(null);
        log.info("method:{}, chain strategy bean, take time {}", "chain", watch.getSplitTime0());
        return first;
    }

    public void setNextApproval(AbstractRecallStrategy strategy) {
        this.strategy = strategy;
    }

    public Result next(RecallRequest request) {
        if (Objects.isNull(request.getWatch())) {
            request.setWatch(FrameTimer.getInstance());
            spendTimeMapThreadLocal.set(Maps.newLinkedHashMap());
        }
        spendTimeMapThreadLocal.get().put(this.getClass().getSimpleName(), request.getWatch().getSplitTime0());
        if (this.strategy == null) {
            return new Result(null);
        }
        Result result = this.strategy.aroundDeploy(request);

        return result;
    }

    public Result aroundDeploy(RecallRequest request) {
        Result deploy = deploy(request);
        if (StringUtils.isBlank(request.getHitStrategy()) && deploy != null && StringUtils.isNotBlank(deploy.getUrl())) {
            request.setHitStrategy(this.getClass().getName());
        }
        return deploy;
    }

    public abstract Result deploy(RecallRequest request);

    public static boolean vaild(RecallRequest request) {
        if (StringUtils.isBlank(request.getPlatform())) {
            request.setPlatform("");
        }
        if (StringUtils.isBlank(request.getRecallVersion())) {
            request.setRecallVersion("0");
        }
        if (StringUtils.isBlank(request.getVersion())) {
            request.setVersion("0");
        }
        if (StringUtils.isBlank(request.getUserId()) || !StringUtils.isNumeric(request.getUserId())) {
            request.setUserId("0");
        }
        request.setUserIdStr(request.getUserId());
        request.setUserIdLong(Long.parseLong(request.getUserId()));
        return Boolean.FALSE;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public String getSpendTimes() {
        Map<String, Long> map = spendTimeMapThreadLocal.get();
        spendTimeMapThreadLocal.remove();
        if (Objects.isNull(map)) {
            return "";
        }
        return JSONObject.toJSONString(map);
    }

}
