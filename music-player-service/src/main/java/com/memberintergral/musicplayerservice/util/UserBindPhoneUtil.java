package com.memberintergral.musicplayerservice.util;

import com.alibaba.fastjson.JSONObject;
import com.kuwo.commercialization.common.http.OkHttpUtil;
import com.memberintergral.musicplayerservice.util.recallStrategy.common.KuwoDES;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.regex.Pattern;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.memberintergral.musicplayerservice.util.recallStrategy
 * @date:2022/9/5
 */
@Slf4j
public class UserBindPhoneUtil {

    private static String regex = "^1\\d{10}$";
    
    public static String getPhoneByUid(String uid) {
        try {
            String url = "http://ip.i.kuwo.cn/US_NEW/kuwo/getBindInfo?f=commerce";
            StringBuffer sb = new StringBuffer();
            sb.append("uid=").append(uid);
            sb.append("&sx=12345678");
            sb.append("&dev_id=dev_id");
            sb.append("&sid=123");
            sb.append("&dev_name=dev_name");
            sb.append("&src=src");
            sb.append("&from=from");
            sb.append("&devType=devType");
            sb.append("&devResolution=devResolution");
            sb.append("&version=version");

            String q = KuwoDES.getEncryptStrToStr(sb.toString(), "&A$h4sD7");
            String res = OkHttpUtil.getForString(url + "&q=" + q);
            String data = null;
            data = KuwoDES.getDesJSONFromParam(res, "12345678");
            JSONObject jsonObj = JSONObject.parseObject(data);
            log.trace("getUidByPhone url:" + url + "&q=" + q + ",return :" + res);
            jsonObj.getString("phone");
            if (jsonObj != null && jsonObj.getString("hasBind") != null && "1".equals(jsonObj.getString("hasBind"))) {
                return jsonObj.getString("mobile");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
    
    public static String getUidByPhone(String phone) {
        if(!Pattern.matches(regex, phone)){
            return null;
        }
        try {
            String url = "http://ip.i.kuwo.cn/US_NEW/kuwo/get_mobile_user?f=commerce";
            StringBuffer sb = new StringBuffer();
            sb.append("mobile=").append(phone);
            sb.append("&sx=12345678");
            sb.append("&dev_id=dev_id");
            sb.append("&sid=123");
            sb.append("&dev_name=dev_name");
            sb.append("&src=src");
            sb.append("&from=from");
            sb.append("&devType=devType");
            sb.append("&devResolution=devResolution");
            sb.append("&version=version");

            String q = KuwoDES.getEncryptStrToStr(sb.toString(), "&A$h4sD7");
            String res = OkHttpUtil.getForString(url + "&q=" + q);
            String data = null;
            data = KuwoDES.getDesJSONFromParam(res, "12345678");
            JSONObject jsonObj = JSONObject.parseObject(data);
            log.info("getUidByPhone url:" + url + "&q=" + q + ",return :" + res);
            if (jsonObj != null && "200".equals(jsonObj.getString("status")) && "succ".equals(jsonObj.getString("result")) && "ok".equals(jsonObj.getString("msg"))) {
                return jsonObj.getString("uid");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
