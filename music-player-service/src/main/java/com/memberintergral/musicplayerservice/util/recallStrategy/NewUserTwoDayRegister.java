package com.memberintergral.musicplayerservice.util.recallStrategy;

import com.memberintergral.musicplayerservice.cache.MaxUIdTwoDayBeforeNacos;
import com.memberintergral.musicplayerservice.req.RecallRequest;
import com.memberintergral.musicplayerservice.util.recallStrategy.common.PropertiesValue;
import com.memberintergral.musicplayerservice.util.recallStrategy.common.RedisKey;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.memberintergral.musicplayerservice.util.recallStrategy
 * @date:2023/2/24
 */
@Deprecated
@Service
@Slf4j
@Order(100)
//海宁 两天内注册非会员用户弹窗逻辑
public class NewUserTwoDayRegister extends AbstractRecallStrategy{

    @Resource(name = "maxUIdTwoDayBeforeNacos")
    private MaxUIdTwoDayBeforeNacos maxUIdTwoDayBeforeNacos;


    private static final String TEST_POP_PREFIX = "http://h5app.kuwo.cn/m/uat_recallpop/index.html";

    private static final String POP_PREFIX = "http://h5app.kuwo.cn/m/recallpop/index.html";

    @Override
    public Result deploy(RecallRequest request) {
        Long userIdLong = request.getUserIdLong();
        Long maxUIdTwoDayBefore = maxUIdTwoDayBeforeNacos.getMaxUIdTwoDayBefore();
        if (Objects.isNull(maxUIdTwoDayBefore) || maxUIdTwoDayBefore > userIdLong) {
            return next(request);
        }
        Long now = request.getNow().getTime();
        Map<String, String> userSamInfoCache = request.getUserSamInfoCache();
        Long svipExpire = NumberUtils.toLong(userSamInfoCache.get("svipExpire"));
        Long vipLuxuryExpire = NumberUtils.toLong(userSamInfoCache.get("vipLuxuryExpire"));
        Long vipmExpire = NumberUtils.toLong(userSamInfoCache.get("vipmExpire"));
        if (svipExpire > now || vipLuxuryExpire > now || vipmExpire > now) {
            return next(request);
        }

        String newUserTwoDayRegisterCacheKey= RedisKey.analysisKey(RedisKey.NEW_USER_TWO_DAY_REGISTER_CACHE_KEY, String.valueOf(userIdLong));
        if (redisNewVipTransferUtil.getObject(newUserTwoDayRegisterCacheKey, Integer.class)!=null) {
            return next(request);
        }

        String url;
        if (PropertiesValue.SERVER_MODEL.equals("prod")) {
            url = POP_PREFIX;
        } else {
            url = TEST_POP_PREFIX;
        }
        redisNewVipTransferUtil.setObject(newUserTwoDayRegisterCacheKey, 1, RedisKey.ONE_DAY_EXPIRE);
        return new Result(url);
    }

}
