package com.memberintergral.musicplayerservice.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.executor.statement.RoutingStatementHandler;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.logging.jdbc.PreparedStatementLogger;
import org.apache.ibatis.logging.jdbc.StatementLogger;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Plugin;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.SystemMetaObject;
import org.apache.ibatis.session.ResultHandler;
import org.apache.shardingsphere.driver.jdbc.core.statement.ShardingSpherePreparedStatement;
import org.springframework.stereotype.Component;

import java.lang.reflect.Proxy;
import java.sql.Statement;
import java.util.Properties;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.memberintergral.musicplayerservice.util
 * @date:2022/9/26
 */
@Component
@Intercepts({ @Signature(method = "query", type = StatementHandler.class, args = {Statement.class, ResultHandler.class}) })
@Slf4j
public class MybatisInterceptor implements Interceptor {

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        FrameTimer frameTimer = FrameTimer.getInstance();
        Long startTime = frameTimer.getSplitTime0();
        Object proceed = invocation.proceed();
        Long endTime = frameTimer.getSplitTime0();
        frameTimer.stop0();
        printSql(endTime - startTime, invocation);
        return proceed;
    }

    public void printSql(Long takeTime, Invocation invocation){
        if (takeTime > 100) {
            //获取查询sql
            String sql = parseSql(invocation);
            //打印日志信息
            log.warn("requestId:{}, method:{}, sql is : {}, take time is {} ms", FrameTimer.getUUID(), "intercept", sql, takeTime);
        }
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
        // TODO Auto-generated method stub
    }

    /**
     * 获得真正的处理对象,可能多层代理.
     */
    public String parseSql(Invocation invocation) {
        String sql="";
        Statement statement=(Statement) invocation.getArgs()[0];
        if(Proxy.isProxyClass(statement.getClass())){
            MetaObject metaObject= SystemMetaObject.forObject(statement);
            Object h=metaObject.getValue("h");
            if(h instanceof StatementLogger){
                RoutingStatementHandler rsh=(RoutingStatementHandler) invocation.getTarget();
                sql=rsh.getBoundSql().getSql();
            }else {
                PreparedStatementLogger psl=(PreparedStatementLogger) h;
                sql=psl.getPreparedStatement().toString();
            }
        } else if (statement instanceof ShardingSpherePreparedStatement){
            MetaObject metaObject= SystemMetaObject.forObject(statement);
            Object originSql = metaObject.getValue("sql");
            sql = String.valueOf(originSql);
        } else{
            sql=statement.toString();
        }
        if (StringUtils.isNotBlank(sql)) {
            sql = sql.replace("\n", " ").replace("\t", "");
        }
        return sql;
    }

}
