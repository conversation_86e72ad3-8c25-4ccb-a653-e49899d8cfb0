package com.memberintergral.musicplayerservice.util;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.memberintergral.musicplayerservice.req.FreeModelRequest;
import com.memberintergral.musicplayerservice.util.redis.RedisNewVipTransferUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class VerCheckUtil {

    @Autowired
    private RedisNewVipTransferUtil redisNewVipTransferUtil;

    /**
     * IOS版本checkUrl 高山虎提供
     */
    @Value("${http.ecom.url}")
    private String CHECK_DOMAIN;
    /**
     * 查看是否为审核版本
     *
     * @return
     */
    public Boolean isIOSCheckVers(FreeModelRequest freeModelRequest){
        if(StringUtils.isBlank(freeModelRequest.getSource())){
            log.error("VerCheckUtil isIOSCheckVers source is empty!");
            return false;
        }
        String iosCheckVersion="VerCheckUtil:iosCheckVersion";
        if(redisNewVipTransferUtil.getString(iosCheckVersion)!=null){
            log.info("VerCheckUtil isIOSCheckVers redis check vers={}!",redisNewVipTransferUtil.getString(iosCheckVersion));
            return redisNewVipTransferUtil.getString(iosCheckVersion).equals(freeModelRequest.getSource());
        }
        String vers= freeModelRequest.parseSourceVerStr(freeModelRequest.getSource());
        String tempUrl = String.format(CHECK_DOMAIN+"/EcomResourceServer/getIOSIsHideAd.do?plat=ip&apiversion=38&ver=%s&appuid=%s&src=%s&isVip=0&uid=%s", vers, freeModelRequest.getAppUid(), freeModelRequest.getSource(), freeModelRequest.getUid());
        String result= HttpUtil.get(tempUrl);
        if(StringUtils.isBlank(result)){
            log.error("VerCheckUtil isIOSCheckVers CHECK_URL http result is empty!");
            return false;
        }
        JSONObject resutlObject=JSONUtil.parseObj(result);
        if(resutlObject.containsKey("examineApp")){
            JSONObject checkVersionObject=resutlObject.getJSONObject("examineApp");
            if(checkVersionObject.containsKey("name")){
                String checkVersion=checkVersionObject.getStr("name");
                log.info("VerCheckUtil isIOSCheckVers http check vers={}!",checkVersion);
                redisNewVipTransferUtil.setString(iosCheckVersion,checkVersion,60*60L);
                if(checkVersion.equals(freeModelRequest.getSource())){
                    return true;
                }
            }
        }
        return false;
    }
}
