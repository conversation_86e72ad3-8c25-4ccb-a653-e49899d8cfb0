package com.memberintergral.musicplayerservice.util.recallStrategy;

import com.memberintergral.musicplayerservice.entity.dto.UserBlankDTO;
import com.memberintergral.musicplayerservice.req.RecallRequest;
import com.memberintergral.musicplayerservice.service.UserBlankService;
import com.memberintergral.musicplayerservice.util.TimeUtils;
import com.memberintergral.musicplayerservice.util.recallStrategy.common.RedisKey;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.memberintergral.musicplayerservice.util.recallStrategy
 * @date:2022/9/6
 */
@Service
@Slf4j
@Order(12)
public class Vip7User extends AbstractRecallStrategy{

    @Autowired
    private UserBlankService userBlankService;

    @Override
    public Result deploy(RecallRequest request) {
        //志云七天会员弹框
        try {
            String userId = request.getUserId();
            Long userIdLong = request.getUserIdLong();

            if (order != null && order < 0) {
                UserBlankDTO userBlankDTO = new UserBlankDTO();
                userBlankDTO.setUserId(userIdLong);
                userBlankDTO.setCode("playerPop");
                if (!userBlankService.isExist(userBlankDTO)) {
                    return next(request);
                }
            }

            String recallVersion = request.getRecallVersion();
            Long experienceExpire=Long.valueOf(request.getUserSamInfoCache().get("experienceExpire"));
            if(experienceExpire>0&&"1".equals(recallVersion)&&userIdLong>0){
                Date expireDate=new Date(experienceExpire);
                int dateSub= TimeUtils.daysBetween(request.getNow(), expireDate);
                String vip7CacheKey=RedisKey.analysisKey(RedisKey.VIP7_USER_CACHE_KEY, userId);
                if (dateSub <= 3 && redisNewVipTransferUtil.getObject(vip7CacheKey, Integer.class)==null) {
                    redisNewVipTransferUtil.setObject(vip7CacheKey, 1, RedisKey.ONE_YEAT_EXPIRE);
                    return new Result("https://vip1.kuwo.cn/vip/vue/activity/newUserGiftRecall/index.html?");
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return next(request);
    }
}
