package com.memberintergral.musicplayerservice.util.recallStrategy;

import com.memberintergral.musicplayerservice.req.RecallRequest;
import com.memberintergral.musicplayerservice.util.TimeUtils;
import com.memberintergral.musicplayerservice.util.recallStrategy.common.BoxFunctionType;
import com.memberintergral.musicplayerservice.util.recallStrategy.common.BoxSurfaceType;
import com.memberintergral.musicplayerservice.util.recallStrategy.common.NormalBox;
import com.memberintergral.musicplayerservice.util.recallStrategy.common.RedisKey;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;

import java.text.MessageFormat;
import java.util.Date;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.memberintergral.musicplayerservice.util.recallStrategy
 * @date:2022/9/5
 */
//@Service
@Slf4j
@Order(9)
@Deprecated
public class IosNormalBox extends AbstractRecallStrategy {


    @Override
    public Result deploy(RecallRequest request) {
        try {
            String platform = request.getPlatform();
            Integer isAutoPayUser = request.getIsAutoPayUser();
            if ("ios".equals(platform) && isAutoPayUser == 0 && false) {
                String deviceTail = request.getDeviceTail();
                Long vipmExpire = Long.valueOf(request.getUserSamInfoCache().get("vipmExpire"));
                Long vipLuxuryExpire = Long.valueOf(request.getUserSamInfoCache().get("vipLuxuryExpire"));
                Long userId = request.getUserIdLong();
                int dateSub = 0;
                Date vipmExpireDate = new Date(vipmExpire);
                Date vipLuxuryExpireDate = new Date(vipLuxuryExpire);
                dateSub = vipmExpire > vipLuxuryExpire ? TimeUtils.daysBetween(request.getNow(), vipmExpireDate) : TimeUtils.daysBetween(request.getNow(), vipLuxuryExpireDate);
                dateSub = dateSub <= -31 ? -31 : dateSub;

                if (!vipmExpire.equals(0L) && vipmExpire > vipLuxuryExpire) {
                    String cacheKey = MessageFormat.format(RedisKey.BOX_CONFIG, platform, BoxSurfaceType.normal.getType(), BoxFunctionType.vipRecall.getType(), dateSub);
                    NormalBox normalBox = (NormalBox) redisNewVipTransferUtil.getObject(cacheKey, NormalBox.class);
                    if (normalBox != null) {
                        if (dateSub > 0) {
                            if (getUserRecallBeforeConfigRate(userId)) {
                                addUserRecallBeforeConfigRate(userId);
                                return new Result(normalBox.getBottomRedirectUrl());
                            }
                        } else {
                            if (getUserRecallAfterConfigRate(userId)) {
                                //map.put("vipRecallNormalBox", normalBox);
                                log.debug("op=getRecallWindow&platform=" + platform + "&userId=" + userId);
                                addUserRecallAfterConfigRate(userId);
                            }
                        }

                    }
                }

                if (!vipLuxuryExpire.equals(0L) && vipLuxuryExpire >= vipmExpire) {
                    String cacheKey = "boxConfig_" + platform + "_" + BoxSurfaceType.normal.getType() + "_" + BoxFunctionType.vipRecall.getType() + "_" + deviceTail + "_" + dateSub;
                    NormalBox normalBox = (NormalBox) redisNewVipTransferUtil.getObject(cacheKey, NormalBox.class);
                    if (normalBox != null) {
                        if (dateSub > 0) {
                            if (getUserRecallBeforeConfigRate(userId)) {
                                //map.put("vipRecallNormalBox", normalBox);
                                addUserRecallBeforeConfigRate(userId);
                            }

                        } else {
                            if (getUserRecallAfterConfigRate(userId)) {
                                //map.put("vipRecallNormalBox", normalBox);
                                addUserRecallAfterConfigRate(userId);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {

        }
        return next(request);
    }

    //查看过期前是否已经枷锁  true
    private boolean getUserRecallBeforeConfigRate(Long userId) throws Exception {
        boolean result = true;
        String userRecallConfigBeforeKey = MessageFormat.format(RedisKey.USER_RECALL_CONFIG_BEFORE, userId);
        Date now = new Date();
        Date lastRequestDate = (Date) redisNewVipTransferUtil.getObject(userRecallConfigBeforeKey, Date.class);
        if (lastRequestDate != null) {
            if (TimeUtils.daysBetween(lastRequestDate, now) < 2) {
                result = false;
            }
        }
        return result;
    }

    //过期前三天的限制枷锁
    private void addUserRecallBeforeConfigRate(Long userId) throws Exception {
        String userRecallConfigBeforeKey = "userRecallConfigBefore" + userId;
        Date now = new Date();
        redisNewVipTransferUtil.setObject(userRecallConfigBeforeKey, now, RedisKey.DAY_OF_8_EXPIRE);
    }

    //过期后验证距离上次弹窗是否超过七天,弹窗次数是否超过三次  //true可以弹 false不可以
    private boolean getUserRecallAfterConfigRate(Long userId) throws Exception {
        boolean result = true;
        String userRecallConfigAfterKey = RedisKey.analysisKey(RedisKey.USER_RECALL_CONFIG_AFTER, userId);
        Date now = new Date();
        Date lastRequestDate = (Date) redisNewVipTransferUtil.getObject(userRecallConfigAfterKey, Date.class);
        if (lastRequestDate != null) {
            if (TimeUtils.daysBetween(lastRequestDate, now) < 2) {
                result = false;
            }
        }
        return result;
    }

    //过期后增加七天弹窗的限制
    private void addUserRecallAfterConfigRate(Long userId) throws Exception {
        String userRecallConfigAfterKey = RedisKey.analysisKey(RedisKey.USER_RECALL_CONFIG_AFTER, userId);
        String userRecallConfigAfterRateKey = RedisKey.analysisKey(RedisKey.USER_RECALL_CONFIG_AFTER_RATE, userId);
        Date now = new Date();
        redisNewVipTransferUtil.setObject(userRecallConfigAfterKey, now, RedisKey.DAY_OF_8_EXPIRE);
        redisNewVipTransferUtil.increment(userRecallConfigAfterRateKey);
    }
}
