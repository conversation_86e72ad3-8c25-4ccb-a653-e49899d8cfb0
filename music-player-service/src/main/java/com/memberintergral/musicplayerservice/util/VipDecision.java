/*
 * create By <PERSON>
 */
package com.memberintergral.musicplayerservice.util;

import com.memberintergral.musicplayerservice.util.enums.ProductType;

import java.util.List;

/**
 * vip决策
 * 
 * 
 * <AUTHOR>
 * @date 2015-8-4
 * @version 1.0
 */
public interface VipDecision {
	
	/**
	 * 当前vip是否能 “被” 升级
	 */
	boolean canUpgraded();
	/**
	 * 获取折扣点
	 *
	 * @return -1代表无此策略
	 * <AUTHOR>
	 */
	double getDiscount();
	/**
	 * 获取特殊月份定价策略
	 *
	 * @param month
	 * @return -1代表无此策略
	 * <AUTHOR>
	 */
	double getSpecial(int month);
	
	/**
	 * 获取自动续费的单价
	 *
	 * @param autopay
	 * @return
	 * <AUTHOR>
	 */
	long	getAutoPayPrice ();
	
	/**
	 * 是否是音乐包类型
	 *
	 * @return
	 * <AUTHOR>
	 */
	boolean isVipm ();
	
	/**
	 * 获取“子产品”
	 *
	 * @return
	 * <AUTHOR>
	 */
	List<ProductType> subProducts ();
	
	
	public double getSpecial(int month,String platForm) ;
}
