package com.memberintergral.musicplayerservice.feign;

import com.kuwo.commercialization.common.resp.BasicResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "indicator-service")
public interface IndicatorFeign {


    @RequestMapping("/freeModel/jd")
    BasicResponse isFreeModelUser(@RequestParam("userId")String userId,
                                  @RequestParam("q36")String q36,
                                  @RequestParam("notrace")String notrace,
                                  @RequestParam("deviceId")String deviceId,
                                  @RequestParam("platfrom")String platfrom,
                                  @RequestParam("source")String source);

    @RequestMapping("/ios/subscript/sign")
    BasicResponse getIosSignStatus(@RequestParam("userId")String userId,
                                   @RequestParam("deviceId")String deviceId,
                                   @RequestParam("source")String source);

}
