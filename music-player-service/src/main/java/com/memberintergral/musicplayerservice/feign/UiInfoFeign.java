package com.memberintergral.musicplayerservice.feign;

import com.kuwo.commercialization.common.resp.BasicResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.memberintergral.musicplayerservice.feign
 * @date:2024/7/26
 */

@FeignClient(value = "uiService")
public interface UiInfoFeign {

    @GetMapping("/ui/all")
    BasicResponse getAllInfo(@RequestParam("userId") String userId);

    @GetMapping("/ui/simplev")
    BasicResponse getSimpleV(@RequestParam("userId") String userId);

}
