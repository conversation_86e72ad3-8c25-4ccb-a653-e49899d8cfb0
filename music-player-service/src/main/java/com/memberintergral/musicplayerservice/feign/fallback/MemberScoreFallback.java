package com.memberintergral.musicplayerservice.feign.fallback;

import com.kuwo.commercialization.common.resp.BasicResponse;
import com.memberintergral.musicplayerservice.feign.MemberScoreFeign;
import org.springframework.stereotype.Component;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.memberintergral.musicplayerservice.feign.fallback
 * @date:2023/2/18
 */
@Component
public class MemberScoreFallback implements MemberScoreFeign {

    @Override
    public BasicResponse getUserScore(String userId, String sessionId) {
        return null;
    }

    @Override
    public BasicResponse getUserScoreNoSessionId(String userId, String vers) {
        return null;
    }

    @Override
    public BasicResponse getMemberRank() {
        return null;
    }

}
