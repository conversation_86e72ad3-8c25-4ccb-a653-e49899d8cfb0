package com.memberintergral.musicplayerservice.feign;

import com.kuwo.commercialization.common.message.MessageModel;
import feign.Request;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.memberintergral.musicplayerservice.feign
 * @date:2023/2/17
 */
@FeignClient(value = "vipConf")
public interface FilterFeign {

    @GetMapping("/filter/getPriceGearInfo")
    MessageModel getPriceGearInfo(@RequestParam("userId") String userId, @RequestParam("virtualUserId") String virtualUserId,
                                  @RequestParam("deviceId") String deviceId, @RequestParam("payDeskSign") String payDeskSign,
                                  @RequestParam("platform") String platform, @RequestParam("vers") String vers);

    @GetMapping("/price/validate/uid/add")
    MessageModel validateUidSrcAdd(@RequestParam("userId")String userId, @RequestParam("srcList")String srcList, Request.Options options);
    
}
