package com.memberintergral.musicplayerservice.feign;

import com.kuwo.commercialization.common.resp.BasicResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.memberintergral.musicplayerservice.feign
 * @date:2023/2/17
 */
@FeignClient(value = "intergralService")
public interface MemberScoreFeign {

    @GetMapping("/basic/info")
    BasicResponse getUserScore(@RequestParam("userId") String userId, @RequestParam("sessionId") String sessionId);

    @GetMapping("/basic/noauth/info")
    BasicResponse getUserScoreNoSessionId(@RequestParam("userId") String userId, @RequestParam("vers") String vers);

    @GetMapping("/basic/rank/list")
    BasicResponse getMemberRank();

}
