package com.memberintergral.musicplayerservice.filter;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kuwo.commercialization.common.message.SystemCodeErrorConstant;
import com.kuwo.commercialization.common.resp.BasicResponse;
import com.memberintergral.musicplayerservice.annotation.EncryptionAnnotation;
import com.memberintergral.musicplayerservice.config.exception.ServiceException;
import com.memberintergral.musicplayerservice.req.TimeDataRequest;
import com.memberintergral.musicplayerservice.util.AESUtil;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

/**
 * @description: responseBody自动加密
 */
@ControllerAdvice
public class EncryptResponseBodyAdvice implements ResponseBodyAdvice<BasicResponse> {

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        // 如果有EncryptionAnnotation注解，则处理
        if (returnType.hasMethodAnnotation(EncryptionAnnotation.class)) {
            return true;
        }

        return false;
    }

    @SneakyThrows
    @Override
    public BasicResponse beforeBodyWrite(BasicResponse body, MethodParameter returnType, MediaType selectedContentType, Class<? extends HttpMessageConverter<?>> selectedConverterType, ServerHttpRequest request, ServerHttpResponse response) {

        // 真实数据
        Integer code = body.getCode();
        Object data = body.getData();

        // 如果data为空，直接返回
        if (data == null) {
            return body;
        }

        // 如果是实体，并且继承了RequestTimeData，则放入时间戳
        if (data instanceof TimeDataRequest) {
            ((TimeDataRequest)data).setCurrentTimeMillis(System.currentTimeMillis());
        }
        String dataText = JSONUtil.toJsonStr(data);

        // 如果data为空，直接返回
        if (StringUtils.isBlank(dataText)) {
            return body;
        }

        // 如果位数小于16，报错
        if (code == 200 && dataText.length() < 16) {
            throw new ServiceException(SystemCodeErrorConstant.ENCRYPT_FAIL_5000001);
        }

        String mode = returnType.getAnnotatedElement().getAnnotation(EncryptionAnnotation.class).mode();
        String padding = returnType.getAnnotatedElement().getAnnotation(EncryptionAnnotation.class).padding();
        String key = returnType.getAnnotatedElement().getAnnotation(EncryptionAnnotation.class).key();
        String iv = returnType.getAnnotatedElement().getAnnotation(EncryptionAnnotation.class).iv();
        
        String encryptText = AESUtil.encryptHex(mode, padding, key, iv, dataText);

        body.setData(encryptText);
        return body;
    }
}
