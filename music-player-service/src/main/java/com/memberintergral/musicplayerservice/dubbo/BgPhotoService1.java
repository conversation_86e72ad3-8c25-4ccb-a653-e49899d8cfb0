package com.memberintergral.musicplayerservice.dubbo;

import com.commerical.musicplayerservice.enity.BgPhoto;
import com.memberintergral.musicplayerservice.mapper.musicPlayer.BgPhotoMapper;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@DubboService
public class BgPhotoService1 implements com.commerical.musicplayerservice.service.BgPhotoService1{
	private Logger logger = LoggerFactory.getLogger(BgPhotoService1.class);
	@Autowired
	private BgPhotoMapper bgPhotoMapper;


	@Override
	public BgPhoto getById(Long id) {
		return bgPhotoMapper.selectByPrimaryKey(id);
	}

	@Override
	public List<BgPhoto> getAll() {
		return bgPhotoMapper.selectAll();
	}
}
