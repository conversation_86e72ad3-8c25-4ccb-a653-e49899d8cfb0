package com.memberintergral.musicplayerservice.dubbo;

import com.commerical.musicplayerservice.enity.NameplateDetail;
import com.memberintergral.musicplayerservice.mapper.musicPlayer.NameplateDetailMapper;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@DubboService
public class NameplateService1 implements com.commerical.musicplayerservice.service.NameplateService1{
	private Logger logger = LoggerFactory.getLogger(NameplateService1.class);
	@Autowired
	private NameplateDetailMapper nameplateDetailMapper;


	@Override
	public NameplateDetail getById(Long id) {

		return nameplateDetailMapper.selectByPrimaryKey(id);
	}

	@Override
	public List<NameplateDetail> getAll() {

		return nameplateDetailMapper.getAllList();
	}
}
