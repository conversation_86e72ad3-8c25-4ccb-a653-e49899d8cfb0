package com.memberintergral.musicplayerservice.dubbo;

import com.commerical.musicplayerservice.enity.VipHanger;
import com.memberintergral.musicplayerservice.mapper.musicPlayer.VipHangerMapper;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@DubboService
public class VipHangerService1 implements com.commerical.musicplayerservice.service.VipHangerService1{
	private Logger logger = LoggerFactory.getLogger(VipHangerService1.class);
	@Autowired
	private VipHangerMapper vipHangerMapper;


	@Override
	public VipHanger getById(Long id) {

		return vipHangerMapper.getHangersById(id);
	}

	@Override
	public List<VipHanger> getAll() {

		return vipHangerMapper.getAllList();
	}
}
