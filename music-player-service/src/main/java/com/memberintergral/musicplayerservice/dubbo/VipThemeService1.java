package com.memberintergral.musicplayerservice.dubbo;

import com.commerical.musicplayerservice.enity.VipTheme;
import com.memberintergral.musicplayerservice.mapper.musicPlayer.VipThemeMapper;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@DubboService
public class VipThemeService1 implements com.commerical.musicplayerservice.service.VipThemeService1 {
	private Logger logger = LoggerFactory.getLogger(VipThemeService1.class);
	@Autowired
	private VipThemeMapper vipThemeMapper;


	@Override
	public VipTheme getById(Long id) {
		return vipThemeMapper.selectByPrimaryKey(id);
	}

	@Override
	public List<String> getAllSortNames() {
		return vipThemeMapper.selectAllSortNames();
	}
}
