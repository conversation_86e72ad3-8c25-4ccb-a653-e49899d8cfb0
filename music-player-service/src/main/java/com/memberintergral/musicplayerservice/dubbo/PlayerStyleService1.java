package com.memberintergral.musicplayerservice.dubbo;

import com.commerical.musicplayerservice.enity.PlayerStyleDTO;
import com.memberintergral.musicplayerservice.entity.query.PlayerStyleQuery;
import com.memberintergral.musicplayerservice.mapper.musicPlayer.PlayerStyleMapper;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@DubboService
public class PlayerStyleService1 implements com.commerical.musicplayerservice.service.PlayerStyleService1 {

    @Autowired
    private PlayerStyleMapper playerStyleMapper;

    @Override
    public PlayerStyleDTO getByCodeAndPlat(String styleCode, String platform) {
        PlayerStyleQuery styleQuery=new PlayerStyleQuery();
        styleQuery.setStyleCode(styleCode);
        styleQuery.setPlatform(platform);
        List<PlayerStyleDTO> styleLists = playerStyleMapper.selectByQuery(styleQuery);
        if(styleLists==null||styleLists.size()==0){
            return null;
        }
        return styleLists.get(0);
    }

    @Override
    public List<PlayerStyleDTO> getAllPlayerStyles() {

        return playerStyleMapper.selectAllPlayerStyles();
    }
}
