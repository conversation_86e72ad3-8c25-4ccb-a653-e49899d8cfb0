package com.memberintergral.musicplayerservice.dubbo;

import com.commerical.musicplayerservice.enity.PubDressCenterPic;
import com.memberintergral.musicplayerservice.mapper.vipConf.PubDressCenterPicMapper;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@DubboService
public class AppPicService1 implements com.commerical.musicplayerservice.service.AppPicService1 {
	private Logger logger = LoggerFactory.getLogger(AppPicService1.class);
	@Autowired
	private PubDressCenterPicMapper pubDressCenterPicMapper;


	@Override
	public PubDressCenterPic getById(Long id) {

		return pubDressCenterPicMapper.selectByPrimaryKey(id);
	}

	@Override
	public List<PubDressCenterPic> getAll() {
//		return pubDressCenterPicMapper.getAllListShow();
		return pubDressCenterPicMapper.getAllList();
	}
}
