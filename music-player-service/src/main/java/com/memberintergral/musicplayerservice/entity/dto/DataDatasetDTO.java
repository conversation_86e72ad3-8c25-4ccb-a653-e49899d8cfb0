package com.memberintergral.musicplayerservice.entity.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
* <AUTHOR>
* @version V1.0
* <p>Description: DataDataset DTO</p>
* @date 2022-12-26 15:21:33
*/
@Getter
@Setter
public class DataDatasetDTO {

    /**
    * 
    */
    private Integer id;

    /**
    * 数据的唯一标识
    */
    private String code;

    /**
    * 数据集类型
    */
    private Integer type;

    /**
    * 数据模型ID
    */
    private Integer mid;

    /**
    * 数据集名称
    */
    private String title;

    /**
    * 备注信息
    */
    private String info;

    /**
    * 状态1 在线，0不在
    */
    private Integer status;

    /**
    * 自定义数据JSON
    */
    private String custom;

    /**
    * 预留扩展字段1
    */
    private String ext1;

    /**
    * 预留扩展字段2
    */
    private String ext2;

    /**
    * 预留扩展字段3
    */
    private String ext3;

    /**
     * 记录创建时间
     */
    protected Date createTime;
    /**
     * 记录更新时间
     */
    protected Date updateTime;

}
