package com.memberintergral.musicplayerservice.entity;

import lombok.*;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AudioRecommendPushDTO implements Serializable {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 类型：1安卓-主推音质类型 2IOS-主推音质类型  3续费勾选
     */
    private Integer type;

    /**
     * value: type = 1,2 value为 1至臻母带 2至臻全景声 3至臻2.0
     * value: type = 3 value为 1续费勾选 0不续费勾选
     */
    private Integer value;
}
