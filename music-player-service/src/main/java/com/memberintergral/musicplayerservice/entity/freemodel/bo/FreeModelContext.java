package com.memberintergral.musicplayerservice.entity.freemodel.bo;

import com.memberintergral.musicplayerservice.req.FreeModelRequest;
import com.memberintergral.musicplayerservice.resp.FreeModelResult;
import lombok.Data;

import java.util.Map;

/**
 * 做任务返回接口数据
 */
@Data
public class FreeModelContext {

    /**
     * 激励视频条件
     */
    IncentiveVideoConditionBO incentiveVideoConditionBO;

    /**
     * 任务记录
     */
    TaskRecordBO taskRecordBO;

    FreeModelTextBO freeModelTextBO;

    Map<String,String> extraMap;

    FreeModelResult freeModelResult;

    String abt;
}
