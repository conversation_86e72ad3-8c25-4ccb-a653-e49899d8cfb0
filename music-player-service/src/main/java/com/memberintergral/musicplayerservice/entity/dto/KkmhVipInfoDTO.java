package com.memberintergral.musicplayerservice.entity.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
* <AUTHOR>
* @version V1.0
* <p>Description: KkmhVipInfo DTO</p>
* @date 2022-08-25 17:54:21
*/
@Getter
@Setter
public class KkmhVipInfoDTO {

    /**
    * 
    */
    private Long id;

    /**
    * 产品id
    */
    private String productId;

    /**
    * 会员周期
    */
    private Integer duration;

    /**
    * 订单id
    */
    private Long orderId;

    /**
    * 兑换第三方会员的手机号
    */
    private String phone;

    /**
    * 兑换（领取）时间
    */
    private java.util.Date redeemTime;

    /**
    * 是否兑换（领取） 0:未兑换  1:已兑换
    */
    private Integer status;

    /**
    * 用户id
    */
    private Long userId;

    /**
    * 第三方返回的vip信息
    */
    private String vipInfo;

    /**
     * 记录创建时间
     */
    protected Date createTime;
}
