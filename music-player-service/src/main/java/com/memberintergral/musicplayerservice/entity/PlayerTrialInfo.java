package com.memberintergral.musicplayerservice.entity;

import lombok.Builder;
import lombok.Data;

import java.util.Date;


/**
 * 播放器试用信息表
 */
@Data
@Builder
public class PlayerTrialInfo {

    private Long id;

    private Long userId;

    private Long deviceId;

    /**
     * 试用类型
     *  0 模式
     *  1 动效
     */
    private Integer typeCode;


    private String styleCode;

    private String modelCode;

    /**
     * 限免id
     */
    private String restrictId;


    private Date startTime;

    private Date endTime;

    private Date createTime;

    private Date updateTime;


    private String ext1;


    private String ext2;

    /**
     * 平台信息
     * 1: 安卓
     * 2： ios
     * 3: 安卓+ ios
     *
     */
    private String platInfo;


}
