package com.memberintergral.musicplayerservice.entity;

import java.util.Date;

public class UserHanger {
    private Long id;

    private Date createTime;

    private Date endTime;

    private String fromSrc;

    private Long hCategoryId;

    private Long hangerId;

    private Date startTime;

    private Integer status;

    private Date updateTime;

    private Long userId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getFromSrc() {
        return fromSrc;
    }

    public void setFromSrc(String fromSrc) {
        this.fromSrc = fromSrc == null ? null : fromSrc.trim();
    }

    public Long gethCategoryId() {
        return hCategoryId;
    }

    public void sethCategoryId(Long hCategoryId) {
        this.hCategoryId = hCategoryId;
    }

    public Long getHangerId() {
        return hangerId;
    }

    public void setHangerId(Long hangerId) {
        this.hangerId = hangerId;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }
}