package com.memberintergral.musicplayerservice.entity;

import com.google.gson.Gson;
import com.google.gson.internal.LinkedTreeMap;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Desc: 推荐相关配置
 * @date 2022-08-24 18:29:57
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RecommendConfig {

    private Long id;

    /**
     * 进入播放器页面次数 1 第一次  2 第二次
     */
    private Integer entryTimes;

    /**
     * 推荐目标用户类型
     * 1 所有用户
     * 2 已使用模式用户
     * 3 已使用动效用户
     * 4 未使用模式用户
     * 5 未使用动效用户
     * 6 模式/动效均为使用用户
     *
     */
    private Integer rcUserType;

    /**
     * 推荐模式code, 最后推荐等是这个code
     */
    private String rcModelCode;

    /**
     * 推荐的播放器模式code, 这个是默认展示等播放器
     */
    private String rcStyleCode;


    /**
     * 推荐时长 单位小时
     */
    private Long trialDuration;


    /**
     * 推荐开始时间
     */
    private Date rcStTime;

    /**
     * 推荐结束时间
     */
    private Date rcEndTime;

    private String content;

    private Date createTime;

    private Date updateTime;

    /**
     *  0 未删除 1 已删除
     */
    private Integer deleteStatus;

    private String ext1;

    private String ext2;


    private Integer sort;

    /**
     * 转换 成返回客户端的promt
     * @return
     */
    public  RecFloatLayerPrompt getLayerPrompt(){
        Gson gson = new Gson();
        RecFloatLayerPrompt.RecFloatLayerPromptBuilder builder = RecFloatLayerPrompt.builder();
        Map contentMap = gson.fromJson(content, Map.class);
        if (contentMap.containsKey("rc_settingSuccess")){
            LinkedTreeMap rc_settingSuccess = (LinkedTreeMap) contentMap.get("rc_settingSuccess");
            RecFloatLayerPrompt.Tip setSuccess = RecFloatLayerPrompt.Tip.builder()
                    .svipIconUrl(String.valueOf(rc_settingSuccess.get("svipFloatIcon")))
                    .luxIconUrl(String.valueOf(rc_settingSuccess.get("luxFloatIcon")))
                    .text(String.valueOf(rc_settingSuccess.get("floatText")))
                    .build();
            builder.setSuccess(setSuccess);
        }
        if (contentMap.containsKey("rc_trialSuccess")){
            LinkedTreeMap rc_trialSuccess = (LinkedTreeMap) contentMap.get("rc_trialSuccess");
            RecFloatLayerPrompt.Tip trialSuccess = RecFloatLayerPrompt.Tip.builder()
                    .svipIconUrl(String.valueOf(rc_trialSuccess.get("svipFloatIcon")))
                    .luxIconUrl(String.valueOf(rc_trialSuccess.get("luxFloatIcon")))
                    .text(String.valueOf(rc_trialSuccess.get("floatText")))
                    .build();
            builder.trialSuccess(trialSuccess);
        }
        return builder.build();
    }
}
