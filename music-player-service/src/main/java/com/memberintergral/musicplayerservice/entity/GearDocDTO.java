package com.memberintergral.musicplayerservice.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.memberintergral.musicplayerservice.entity.query
 * @date:2023/8/10
 */
@NoArgsConstructor
@Data
public class GearDocDTO {
    @JsonProperty("oPrice")
    private String oPrice;
    @JsonProperty("maxNum")
    private String maxNum;
    @JsonProperty("limitPriceAddPay")
    private String limitPriceAddPay;
    @JsonProperty("btnText")
    private String btnText;
    @JsonProperty("topTag")
    private String topTag;
    @JsonProperty("bottomTag")
    private String bottomTag;
    @JsonProperty("title")
    private String title;
    @JsonProperty("subTitle")
    private String subTitle;
    @JsonProperty("tip")
    private String tip;
    @JsonProperty("autoPayDesc")
    private String autoPayDesc;
}
