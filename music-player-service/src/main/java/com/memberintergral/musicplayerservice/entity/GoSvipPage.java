package com.memberintergral.musicplayerservice.entity;

import lombok.Data;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.memberintergral.musicplayerservice.entity
 * @date:2022/9/5
 */
@Data
public class GoSvipPage {

    private Long id;

    private Long userId;

    /**
     * history_buy_credit 历史购买金额
     * buy_digest_album  购买数专次数
     * buy_month_3_cnt 购买季卡次数
     * buy_month_12_cnt 购买年卡次数
     * user_vip_days 用户再期天数
     */
    // 类别
    private String category;

    // 类别值
    private String categoryValues;


    private Integer enterSvipPage;

}
