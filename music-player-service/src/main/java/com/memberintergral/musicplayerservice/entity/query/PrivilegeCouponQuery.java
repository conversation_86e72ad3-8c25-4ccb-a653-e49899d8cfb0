package com.memberintergral.musicplayerservice.entity.query;

import java.util.List;

public class PrivilegeCouponQuery {
    private Long userId;
    
    private String channel;
    
    private List<String> businessCodes;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public List<String> getBusinessCodes() {
        return businessCodes;
    }

    public void setBusinessCodes(List<String> businessCodes) {
        this.businessCodes = businessCodes;
    }
}