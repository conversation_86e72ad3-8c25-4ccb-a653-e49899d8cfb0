package com.memberintergral.musicplayerservice.entity;

import lombok.Data;

import java.util.Date;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.memberintergral.musicplayerservice.entity
 * @date:2022/9/6
 */
@Data
public class UserActiveInterceptData {

    private Integer id;

    private Long userId;
    
    private Integer aliveNum;

    private Integer payPageNum;

    private Integer fluidNum;

    private Date createTime;

    private Date updateTime;

}
