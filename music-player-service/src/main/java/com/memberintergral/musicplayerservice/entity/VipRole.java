package com.memberintergral.musicplayerservice.entity;

import lombok.Getter;

/**
 * 全局限免用户角色
 */
@Getter
public enum VipRole {
    UN_KNOWN(-1, "未知身份"),
    ON_VIPM(1, "在期音乐包"),
    ON_LUXVIP(2, "在期豪华vip"),
    ON_VEHICLE_VIP(3, "在期车载vip"),
    ON_SVIP(4, "在期超会"),
    NO_MEMBER_FIRST_OPEN(5, "非会员首开"),
    NO_MEMBER_FIRST_BACK_FLOW_HIGH(10, "非会员回流高潜"),
    LINQI_VIPM(6, "临期音乐包"),
    LINQI_LUXVIP(7, "临期豪华vip"),
    LINQI_VEHICLE_VIP(8, "临期车载"),
    LINQI_SVIP(9, "临期超会"),
    NO_MEMBER_FIRST_BACK_FLOW_LOW(11, "非会员回流低潜"),
    ;
    private int roleId;
    private String description;

    VipRole(int roleId, String description) {
        this.roleId = roleId;
        this.description = description;
    }
}
