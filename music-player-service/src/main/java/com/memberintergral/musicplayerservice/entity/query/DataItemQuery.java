package com.memberintergral.musicplayerservice.entity.query;

import lombok.Getter;
import lombok.Setter;

/**
* <AUTHOR>
* @version V1.0
* <p>Description: DataItem query</p>
* @date 2022-12-26 15:21:33
*/
@Getter
@Setter
public class DataItemQuery {

    /**
    * 
    */
    private Integer id;

    /**
    * 数据的唯一标识
    */
    private String code;

    /**
    * 数据集ID
    */
    private Integer mid;

    /**
    * 数据项名称
    */
    private String title;

    /**
    * 备注信息
    */
    private String info;

    /**
    * 排序字段
    */
    private Integer sort;

    /**
    * 数据项内容
    */
    private String content;

    /**
    * 
    */
    private java.util.Date updateTime;

    /**
    * 
    */
    private java.util.Date createTime;

    /**
    * 状态1 在线，0不在
    */
    private Integer status;

    /**
    * 预留扩展字段1
    */
    private String ext1;

    /**
    * 预留扩展字段2
    */
    private String ext2;

    /**
    * 预留扩展字段3
    */
    private String ext3;

}
