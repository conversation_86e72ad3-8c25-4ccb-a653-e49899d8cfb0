package com.memberintergral.musicplayerservice.entity.dto;

import lombok.Data;

/**
 * 装扮中心 参数
 */
@Data
public class DressCenterDTO {

    /**
     * 播放器API版本
     */
    private String apiv;

    /**
     * 播放器API版本
     */
    private String playerApiv;

    /**
     * 背景API版本
     */
    private String bgPhotoApiv;
    /**
     * 资源分类id
     */
    private String resourceId;
    /**
     * 用户id
     */
    private String uid;
    /**
     * 设备id
     */
    private String deviceId;
    /**
     *  客户端版本
     */
    private String source;
    /**
     * 平台
     */
    private Integer platform;
    /**
     * 皮肤大版本
     */
    private String themeVersions;

    /**
     * 一级tab code
     */
    private String topTabCode;

    /**
     * 二级tab code
     */
    private String twoTabCode;

    /**
     *  pubType 1 预览 2正式
     */
    private Integer pubType;

    /**
     * 具体资源ID
     */
    private String detailId;
    /**
     * 具体资源code
     */
    private String detailCode;
    /**
     * 用户登录sessionid
     */
    private String sid;
    /**
     * 装扮中心的公共api版本_旧，用户体系判断版本使用
     */
    private String apiversion;
    /**
     * top/two
     */
    private String tabDataType;

    /**
     * 装扮中心的公共api版本_新(1 活动限定)
     */
    private Integer dressCenterApiv;
}
