package com.memberintergral.musicplayerservice.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 装扮中心顶部导航栏配置
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DressCenterToptab implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 描述
     */
    private String descDetail;

    /**
     * 图片url
     */
    private String icon;

    /**
     * 排序
     */
    private Integer sort;

    private String activeIcon;

    /**
     * new  标
     */
    private String newIcon;

    /**
     * 1.个性皮肤
     */
    private Integer type;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 编辑人
     */
    private String editorName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 1下线 0上线
     */
    private Integer isDelete;

    /**
     * 唯一code
     */
    private String code;

    /**
     * 接口url
     */
    @JsonIgnore
    private String url;

    /**
     * 平台 1 IOS 2 ar 3 ios&ar
     */
    private Integer platform;

    /**
     * 是否 发布 0 已修改未发布 1 已发布
     */
    private Integer isPub;

}
