package com.memberintergral.musicplayerservice.entity.dto;

import com.memberintergral.musicplayerservice.util.enums.ProductType;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version V1.0
 * <p>Description: UserProduct DTO</p>
 * @date 2022-08-26 12:24:36
 */
@Getter
@Setter
public class UserProductDTO {

    /**
     *
     */
    private Long id;

    /**
     *
     */
    private Long userId;

    /**
     *
     */
    private Double price;

    /**
     *
     */
    private String productId;

    /**
     *
     */
    private Long productTypeId;

    /**
     *
     */
    private Long orderId;

    /**
     *
     */
    private java.util.Date buyDate;

    /**
     *
     */
    private java.util.Date startDate;

    /**
     *
     */
    private java.util.Date expireDate;

    /**
     *
     */
    private Integer duration;

    /**
     *
     */
    private Integer cnt;

    /**
     *
     */
    private Integer status;

    /**
     *
     */
    private String bitrate;

    /**
     *
     */
    private Integer isPresell;

    /**
     *
     */
    private String fansKey;

    /**
     *
     */
    private Long virtualUid;

    /**
     *
     */
    private java.util.Date mergeDate;

    public ProductType getProductType() {
        return ProductType.getProductType(this.getProductTypeId().intValue());
    }

}
