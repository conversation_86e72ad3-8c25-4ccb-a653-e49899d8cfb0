package com.memberintergral.musicplayerservice.entity.dto;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Getter
@Setter
@Data
public class AudioConfigApiDTO {

    @NotBlank(message = "参数错误")
    private String uid;

    @NotBlank(message = "参数错误")
    private String sid;

    @NotBlank(message = "参数错误")
    private String platform;

    @NotBlank(message = "参数错误")
    private String name;

    @NotBlank(message = "参数错误")
    private String playQuality;

    @NotNull(message = "参数错误")
    @NotEmpty(message = "参数错误")
    private List<String> qualityList;

}
