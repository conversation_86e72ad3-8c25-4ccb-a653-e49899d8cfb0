package com.memberintergral.musicplayerservice.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 装扮中心二级tab
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DressCenterTwotab implements Serializable,Comparable<DressCenterTwotab>{

    private static final long serialVersionUID=1L;

    @JsonIgnore
    private Long id;

    /**
     * 资源key
     */

    private Long resourceId;

    /**
     * 一级tabid
     */
    @JsonIgnore
    private String typeKey;

    /**
     * 二级tabcode
     */
    @JsonIgnore
    private String toptabCode;

    /**
     * 是否显示自选色按钮
     */
    private Integer showSkinAutoColor;

    /**
     * 是否显示自定义图按钮
     */
    private Integer showSkinAutoPic;

    /**
     * 是否显示自定义背景按钮
     */
    private Integer showBgAutoPic;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 创建人
     */
    @JsonIgnore
    private String createName;

    /**
     * 编辑人
     */
    @JsonIgnore
    private String editorName;

    /**
     * 创建时间
     */
    @JsonIgnore
    private Date createTime;

    /**
     * 修改时间
     */
    @JsonIgnore
    private Date updateTime;

    /**
     * 0上线/1下线
     */
    @JsonIgnore
    private Integer isDelete;

    /**
     * 唯一code
     */
    @JsonProperty("twoTabCode")
    private String code;

    /**
     * 名称
     */
    private String name;

    @Override
    public int compareTo(DressCenterTwotab o) {
        if(o.getSort()==null||this.getSort()==null){
            return o.getSort()==null?-1:1;
        }
        return this.getSort().compareTo(o.getSort());
    }

}
