package com.memberintergral.musicplayerservice.entity.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.memberintergral.musicplayerservice.entity.dto
 * @date:2022/12/28
 */

@Getter
@Setter
@EqualsAndHashCode
public class DataItemCouponContentExcelDTO {

    @ExcelProperty("福利名称")
    private String coupon_name;

    @ExcelProperty("状态")
    private String status;

    @ExcelProperty("图片链接")
    private String img_url;

    @ExcelProperty("跳转链接")
    private String get_url;

    @ExcelProperty("口令")
    private String watchword;

    @ExcelProperty("底图链接")
    private String watchword_bg;

    @ExcelProperty("排序")
    private String coupon_order;

    @ExcelProperty("分组")
    private String group;

    @ExcelProperty("用户领取身份")
    private String user_type;

    @ExcelProperty("活动限定关联的src")
    private String src;

    @ExcelProperty("活动限定关联的活动页地址")
    private String activity_address;

    @ExcelProperty("埋点id")
    private String log_id;

    @ExcelProperty("类型")
    private String type;

}
