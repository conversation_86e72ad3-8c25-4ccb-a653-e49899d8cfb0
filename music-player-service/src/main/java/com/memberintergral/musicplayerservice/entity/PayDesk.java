package com.memberintergral.musicplayerservice.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-27
 */
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
public class PayDesk implements Serializable {

    private static final long serialVersionUID=1L;

    private Integer id;

    /**
     * 收银台标识
     */
    private String payDeskSign;

    /**
     * 收银台名称
     */
    private String payDeskName;

    /**
     * 编辑人
     */
    private String editorName;

    /**
     * 1删除 0不删除
     */
    private Integer isDelete;

    /**
     * json文案
     */
    private String doc;

    /**
     * 展示类型
     */
    private Integer showType;

    /**
     * 创建时间
     */
    @JsonIgnore
    private Date createTime;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date updateTime;


}
