package com.memberintergral.musicplayerservice.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SongSheetSkin implements Serializable {
    //主键
    private Long id;
    //标记code
    private Integer code;
    //模板名称
    private String name;
    //付费类型 0、免费 1、豪华VIP 2、超级会员
    private Integer paymentType;
    //拦截类型 0、拉起h5  1、拉起弹窗'
    private Integer interceptType;
    //拦截url地址
    private String interceptUrl;
    //封面
    private String coverUrl;
    //图标
    private String iconUrl;
    //资源信息,json串
    private String resourceInfo;
    //前几张图片
    private Integer firstFewNum;
    //pag图层
    private String pagLayer;
    //内容明细，json串
    private String content;
    //目前用作api版本
    private Integer dataVersion;
    //顺序数值
    private Integer showOrder;
    //创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createTime;
    //更新时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date updateTime;
    //0 未删除 1 已删除
    private Integer deleteStatus;

}