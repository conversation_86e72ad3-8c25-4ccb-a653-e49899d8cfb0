package com.memberintergral.musicplayerservice.entity.dto;

import lombok.Data;

import java.util.Objects;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.memberintergral.musicplayerservice.entity.dto
 * @date:2022/12/28
 */
@Data
public class AbtKeyDTO {

    private String code;

    private String abtKey;

    private String updateTime;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AbtKeyDTO that = (AbtKeyDTO) o;
        return Objects.equals(code, that.code) && Objects.equals(abtKey, that.abtKey);
    }

    @Override
    public int hashCode() {
        return Objects.hash(code, abtKey);
    }
}
