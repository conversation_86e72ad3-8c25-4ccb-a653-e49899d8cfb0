package com.memberintergral.musicplayerservice.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @version V1.0
* <p>Description: SongLimitPop DTO</p>
* @date 2024-05-23 21:28:40
*/
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SongLimitPopDTO {

    /**
    * 
    */
    private Long id;

    /**
    * 类型:1歌曲2歌手3歌单4专辑
    */
    private String type;

    /**
     * 名称
     */
    private String name;

    /**
    * 资源id
    */
    private Long sourceId;

    /**
     * 资源id
     */
    private List<Long> sourceIds;

    /**
    * 标题
    */
    private String title;

    /**
    * 背景图链接
    */
    private String coverUrl;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否逻辑删除，1表示删除，0表示未删除
     */
    protected Integer deleted = 0;
    /**
     * 上下线状态
     */
    private Integer status;
    /**
     * 分组标识
     */
    private String groupId;
    /**
     * 分组标识列表
     */
    private List<String> groupIds;
    /**
     * 更新人中文名
     */
    private String updateCnName;
    /**
     * 更新人英文名
     */
    private String updateEnName;
    /**
     * 名称
     */
    private String ext;
    /**
     * 第几页
     */
    private Integer pageIndex = 1;
    /**
     * 每页多少条
     */
    private Integer pageSize = 10;
    /**
     * 分页取多少条
     */
    private Integer limit = 10;
    /**
     * 偏移量
     */
    private Integer offset = 0;
    /**
     * 开始时间
     */
    protected Date startTime;
    /**
     * 结束时间
     */
    protected Date endTime;
    /**
     * 排序字段
     */
    protected String orderBy;
    /**
     * 排序类型
     */
    protected String orderType = "desc";

}
