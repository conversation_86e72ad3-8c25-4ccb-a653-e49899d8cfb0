package com.memberintergral.musicplayerservice.entity.dto;

import com.memberintergral.musicplayerservice.entity.DressCenterTwotab;
import com.memberintergral.musicplayerservice.resp.dresscenter.TipInfoRes;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 装扮中心二级tab
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DressCenterTabDTO implements Serializable {

    private static final long serialVersionUID=1L;


    /**
     * 一级tab id
     */
    private Long id;

    /**
     * 一级tab name
     */
    private String name;

    /**
     * 一级tab code
     */
    private String topCode;

    /**
     * 一级 tab  desc
     */
    private String desc;

    /**
     * 一级 tab  icon
     */
    private String icon;

    /**
     * 一级 tab  activeIcon
     */
    private String activeIcon;

    /**
     * 一级 tab  type
     */
    private Integer type;

    /**
     * 一级 tab  newIcon
     */
    private String newIcon;

    /**
     * 二级tab
     */
    private List<?extends Object> child;

    private TipInfoRes tipInfo;

}
