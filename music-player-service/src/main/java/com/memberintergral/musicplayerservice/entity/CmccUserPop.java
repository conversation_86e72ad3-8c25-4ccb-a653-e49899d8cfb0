package com.memberintergral.musicplayerservice.entity;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.memberintergral.musicplayerservice.entity
 * @date:2023/2/13
 */
@Data
public class CmccUserPop {

    private Long id;

    private Long shareKey;

    private Long userId;

    private List<Long> userIds;

    private String phone;

    private List<Long> phones;

    private String ext;

    private String ext1;

    private Integer deleted;

    private Date updateTime;

    private Date createTime;

}
