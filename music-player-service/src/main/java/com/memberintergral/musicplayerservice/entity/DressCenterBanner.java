package com.memberintergral.musicplayerservice.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 装扮中心 banner配置
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DressCenterBanner implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * banner 名称
     */
    private String name;

    /**
     * 描述
     */
    private String descDetail;

    /**
     * pic图
     */
    private String pic;

    /**
     * 排序
     */
    @JsonIgnore
    private Integer sort;

    /**
     * 配置 JSON
     */
    private String router;

    /**
     * 0上线 1下线
     */
    @JsonIgnore
    private Integer isDelete;

    /**
     * 编辑人
     */
    @JsonIgnore
    private String editorName;

    /**
     * 创建人
     */
    @JsonIgnore
    private String createName;

    /**
     * 创建时间
     */
    @JsonIgnore
    private Date createTime;

    /**
     * 修改时间
     */
    @JsonIgnore
    private Date updateTime;

    /**
     * code
     */
    @JsonIgnore
    private String code;

    /**
     * 平台 1 IOS 2 ar 3 ios&ar
     */
    private Integer platform;

    /**
     * color
     */
    private String color;

    /**
     * 业务类型
     */
    @JsonIgnore
    private String businessType;
}
