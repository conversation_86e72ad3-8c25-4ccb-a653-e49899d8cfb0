package com.memberintergral.musicplayerservice.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.memberintergral.musicplayerservice.entity
 * @date:2023/8/10
 */
@NoArgsConstructor
@Data
public class FilterDocDTO {

    @JsonProperty("src")
    private String src;
    @JsonProperty("pid")
    private String pid;
    @JsonProperty("exitPopupDuration")
    private String exitPopupDuration;
    @JsonProperty("exitPopupCountdown")
    private String exitPopupCountdown;
    @JsonProperty("exitPopupTime")
    private String exitPopupTime;
    @JsonProperty("isAddPayShow")
    private String isAddPayShow;
    @JsonProperty("style")
    private String style;
    @JsonProperty("descImg")
    private String descImg;
    @JsonProperty("btnText")
    private String btnText;
    @JsonProperty("topTag")
    private String topTag;
    @JsonProperty("bottomTag")
    private String bottomTag;
    @JsonProperty("title")
    private String title;
    @JsonProperty("subTitle")
    private String subTitle;
    @JsonProperty("tip")
    private String tip;
    @JsonProperty("orderType")
    private String orderType;
    @JsonProperty("autoPayDesc")
    private String autoPayDesc;
    @JsonProperty("autoPayBtnShow")
    private String autoPayBtnShow;
    @JsonProperty("discounts_pid")
    private String discountsPid;
    @JsonProperty("isIosDiscounts")
    private String isIosDiscounts;
}
