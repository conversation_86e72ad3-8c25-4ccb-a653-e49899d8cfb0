package com.memberintergral.musicplayerservice.entity.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * <p>Description: UnionVipInfo DTO</p>
 * @date 2022-08-25 17:54:21
 */
@Getter
@Setter
public class UnionVipInfoDTO {

    /**
     *
     */
    private Long id;

    /**
     * 联合会员类型
     */
    private String type;

    /**
     * 产品id
     */
    private String productId;

    /**
     * 充值价格
     */
    private Long price;

    /**
     * 会员周期
     */
    private Integer duration;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 兑换联合会员的手机号
     */
    private String phone;

    /**
     * 兑换（领取）时间
     */
    private java.util.Date redeemTime;

    /**
     * 是否兑换（领取） 0:未兑换  1:已兑换
     */
    private Integer status;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 联合会员返回的vip信息
     */
    private String vipInfo;

    /**
     * 预留扩展字段1
     */
    private String ext1;

    /**
     * 预留扩展字段2
     */
    private String ext2;

    /**
     * 预留扩展字段3
     */
    private String ext3;

    /**
     * 记录创建时间
     */
    protected Date createTime;
}
