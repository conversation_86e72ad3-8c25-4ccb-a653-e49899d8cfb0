package com.memberintergral.musicplayerservice.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.memberintergral.recallService.entity
 * @date:2023/8/10
 */
@NoArgsConstructor
@Data
public class UnPayFilterDTO {
    @JsonProperty("title")
    private String title;
    @JsonProperty("subTitle")
    private String subTitle;
    @JsonProperty("btnText")
    private String btnText;
    @JsonProperty("oPrice")
    private BigDecimal oPrice;
    @JsonProperty("autoPay")
    private Integer autoPay;
    @JsonProperty("month")
    private Integer month;
    @JsonProperty("vipType")
    private String vipType;
    @JsonProperty("vipTypeId")
    private Integer vipTypeId;
    @JsonProperty("lastTime")
    private Long lastTime;
    @JsonProperty("price")
    private BigDecimal price;
    @JsonProperty("autoPayPrice")
    private BigDecimal autoPayPrice;
    @JsonProperty("descImg")
    private String descImg;
    @JsonProperty("src")
    private String src;
    @JsonProperty("pid")
    private String pid;
    @JsonProperty("discountsPid")
    private String discountsPid;
    @JsonProperty("isIosDiscounts")
    private Boolean isIosDiscounts;
    @JsonProperty("orderType")
    private Integer orderType;
    @JsonProperty("btnEvent")
    private Integer btnEvent;
    @JsonProperty("payTypeList")
    private List<FilterPayWayDTO.PayWay> payTypeList;
    @JsonProperty("curPayType")
    private FilterPayWayDTO.PayWay curPayType;
    @JsonProperty("paySrc")
    private Integer paySrc;
    @JsonProperty("funsCustomerId")
    private String funsCustomerId;
    @JsonProperty("act")
    private String act;
    @JsonProperty("wxTflag")
    private String wxTflag;
    @JsonProperty("platform")
    private String platform;
    @JsonProperty("autoPayDesc")
    private String autoPayDesc;
    @JsonProperty("unpayordertitle")
    private String unpayordertitle;
    @JsonProperty("isNewUser")
    private Boolean isNewUser;
    @JsonProperty("filterFirstPay")
    private String filterFirstPay;
}
