/*
 * create By <PERSON>
 */
package com.memberintergral.musicplayerservice.entity;
import java.io.Serializable;
import java.text.ParseException;
import java.util.Date;

import com.memberintergral.musicplayerservice.service.VipThemeService;
import com.memberintergral.musicplayerservice.util.TimeUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 主体皮肤
 */

public class VipTheme implements Serializable {
	private Logger logger = LoggerFactory.getLogger(VipTheme.class);
	private Long id;
	private Long themeCategoryId;
	private int order=0;
	private int recommandOrder;
	private int topOrder;
	private String titlePic;//名称
	private String name;//名称
	private String title;//题目
	private String type;//主题类型
	private String description;
	private String coverPic;//封面
	private Long size;//主题包大小 - ANDROID
	private String corner;//角标
	private Long indexValue;//排序值 ＋ＩＮＤＥＸ
	private String resourcePath;//资源包地址-ANDROID
	private String resourcePath1;//资源包地址-IOS
	private Long size1;//主题包大小- IOS
	private Date showTime;//准备显示的时间
	private String platform;//支持的平台 ar - ios - all ＋ＩＮＤＥＸ
	private Boolean isDel = false;//是否已被删除 上线下线 ＋ＩＮＤＥＸ
	private  String version;//版本号
	private  String sortName;//系列名
	private  Long singerId;//系列名
	private  String color;//色值
	private  Integer vipTagNum;//系列名
	
	private  String redirectUrl;//跳转地址
	

	private String createTimeStr;
	private String showTimeStr;
	private String isDelStr;
	private String typeStr;
	private Integer isNew;
	private String redirectUrlStr;
	
	//跳转H5页面标题
	private String pageTitle;

	private Date createdTime = new Date();

	private Date updateTime = new Date();

	public Integer getIsNew(){
		try {
			Date now = new Date();
			int createdTime= TimeUtils.daysBetween(this.getCreatedTime(),now);
			if(createdTime<=31){
				return 1;
			}else{
				return 0;
			}
		} catch (Exception e) {
			logger.info("VipTheme convert failed id="+id);
		}
		return 0;
	}

	public void setIsNew(Integer isNew) throws ParseException {
		this.isNew=isNew;
	}

	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getTitle() {
		return title;
	}
	
	public Long getSingerId() {
		return singerId;
	}
	public void setSingerId(Long singerId) {
		this.singerId = singerId;
	}
	public int getOrder() {
		return order;
	}
	public void setOrder(int order) {
		this.order = order;
	}
	public int getTopOrder() {
		return topOrder;
	}
	public void setTopOrder(int topOrder) {
		this.topOrder = topOrder;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	public String getCoverPic() {
		return coverPic;
	}
	public void setCoverPic(String coverPic) {
		this.coverPic = coverPic;
	}
	public Long getSize() {
		return size;
	}
	public void setSize(Long size) {
		this.size = size;
	}
	public String getCorner() {
		return corner;
	}
	public void setCorner(String corner) {
		this.corner = corner;
	}
	public Long getIndexValue() {
		return indexValue;
	}
	public void setIndexValue(Long indexValue) {
		this.indexValue = indexValue;
	}
	public String getResourcePath() {
		return resourcePath;
	}
	public void setResourcePath(String resourcePath) {
		this.resourcePath = resourcePath;
	}
	public Boolean getIsDel() {
		return isDel;
	}
	
	
	public Long getThemeCategoryId() {
		return themeCategoryId;
	}
	public void setThemeCategoryId(Long themeCategoryId) {
		this.themeCategoryId = themeCategoryId;
	}
	public void setIsDel(Boolean isDel) {
		this.isDel = isDel;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	public String getCreateTimeStr() {
		this.createTimeStr = DateFormatUtils.format(this.getCreatedTime(), "yyyy-MM-dd HH:mm");
		return createTimeStr;
	}
	public void setCreateTimeStr(String createTimeStr) {
		this.createTimeStr = createTimeStr;
	}
	public String getIsDelStr() {
		if(this.getIsDel()) isDelStr = "已下线";
		else {
			if(this.getShowTime().before(new Date())) isDelStr = "已上线";
			else isDelStr = "待上线";
		}
		return isDelStr;
	}
	public void setIsDelStr(String isDelStr) {
		this.isDelStr = isDelStr;
	}
	public String getResourcePath1() {
		return resourcePath1;
	}
	public void setResourcePath1(String resourcePath1) {
		this.resourcePath1 = resourcePath1;
	}
	public Long getSize1() {
		return size1;
	}
	public void setSize1(Long size1) {
		this.size1 = size1;
	}
	public String getPlatform() {
		return platform;
	}
	public void setPlatform(String platform) {
		this.platform = platform;
	}
	public Date getShowTime() {
		return showTime;
	}
	public void setShowTime(Date showTime) {
		this.showTime = showTime;
	}
	public String getShowTimeStr() {
		this.showTimeStr = DateFormatUtils.format(this.getShowTime(), "yyyy-MM-dd HH:mm");
		return showTimeStr;
	}
	public void setShowTimeStr(String showTimeStr) {
		this.showTimeStr = showTimeStr;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getTypeStr() {
		if(StringUtils.isNotEmpty(this.getType())){
			if("free".equalsIgnoreCase(this.getType())) this.typeStr = "免费";
			else if("vip_1".equalsIgnoreCase(this.getType())) this.typeStr = "标准音乐包";
			else if("vip_2".equalsIgnoreCase(this.getType())) this.typeStr = "豪华音乐包";
			else if("song_1".equalsIgnoreCase(this.getType())) this.typeStr = "歌曲";
			else if("album_1".equalsIgnoreCase(this.getType())) this.typeStr = "专辑";
			else if("vip_7".equalsIgnoreCase(this.getType())) this.typeStr = "豪华VIP";
			else if("vip_4".equalsIgnoreCase(this.getType())) this.typeStr = "VIP会员";
			else if("vip_8".equalsIgnoreCase(this.getType())) this.typeStr = "签到VIP";
			else if("vip_19".equalsIgnoreCase(this.getType())) this.typeStr = "豪华VIP自动续费";
			else if("vip_33".equalsIgnoreCase(this.getType())) this.typeStr = "豪华VIP年卡";
			else if("vip_34".equalsIgnoreCase(this.getType())) this.typeStr = "超级会员";
		}
		return typeStr;
	}
	public void setTypeStr(String typeStr) {
		this.typeStr = typeStr;
	}
	public String getVersion() {
		return version;
	}
	public void setVersion(String version) {
		this.version = version;
	}
	public String getSortName() {
		return sortName;
	}
	public void setSortName(String sortName) {
		this.sortName = sortName;
	}
	public String getColor() {
		return color;
	}
	public void setColor(String color) {
		this.color = color;
	}
	public String getRedirectUrl() {
		return redirectUrl;
	}
	public void setRedirectUrl(String redirectUrl) {
		this.redirectUrl = redirectUrl;
	}
	
	public String getRedirectUrlStr() {
		if(redirectUrlStr == null) {
			redirectUrlStr = "";
		}
		return redirectUrlStr;
	}
	
	public void setRedirectUrlStr(String redirectUrlStr) {
		//只有vip_8签到vip返回redirectUrlStr跳转链接
		if(StringUtils.isNotEmpty(this.getType())){
			if("vip_8".equalsIgnoreCase(this.getRedirectUrl())) {
				//TODO
				//this.redirectUrlStr = Config.GET_VIP_FOR_STAR;
			} else {
				this.redirectUrlStr = "";
			}
		}
	}
	
	
	public String getPageTitle() {
		this.pageTitle = "";
		if("vip_1".equals(this.redirectUrl)) {	
			this.pageTitle = "音乐包";
		}

		if("vip_7".equals(this.redirectUrl)) {
			this.pageTitle = "豪华vip";
		}

		if("vip_8".equals(this.redirectUrl)) {
			this.pageTitle = "积分签到";
		}
		
		return pageTitle;
	}
	public void setPageTitle(String pageTitle) {
		this.pageTitle = pageTitle;
	}
	public void setRedirectUrlStrNullStr() {
		this.redirectUrlStr = "";
	}
	public Integer getVipTagNum() {
		return vipTagNum;
	}
	public void setVipTagNum(Integer vipTagNum) {
		this.vipTagNum = vipTagNum;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public int getRecommandOrder() {
		return recommandOrder;
	}

	public void setRecommandOrder(int recommandOrder) {
		this.recommandOrder = recommandOrder;
	}

	public String getTitlePic() {
		return titlePic;
	}

	public void setTitlePic(String titlePic) {
		this.titlePic = titlePic;
	}

	public Boolean getDel() {
		return isDel;
	}

	public void setDel(Boolean del) {
		isDel = del;
	}

	public Date getCreatedTime() {
		return createdTime;
	}

	public void setCreatedTime(Date createdTime) {
		this.createdTime = createdTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
}
