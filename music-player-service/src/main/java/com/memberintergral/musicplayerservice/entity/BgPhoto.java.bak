/*
 * create By <PERSON>
 */
package com.memberintergral.musicplayerservice.entity;
import java.io.Serializable;
import java.util.Date;
public class BgPhoto implements Serializable {
	
	private Long id;
	private Date time;
	private String name;
	private Integer isFree;  //是否限免   1 是 0 否
	private Integer sex;     //0女   1 男
	private String categrayId;
	private Integer vipTagNum;
	private Date freeTimeStart;
	private Date freeTimeEnd;
	private Integer order;
	private String zipUrl;
	private String picUrl;
	private Integer isShow;  //1显示 0 不显示
	private String categoryName;
	private String previewPic;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Date getTime() {
		return time;
	}

	public void setTime(Date time) {
		this.time = time;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}



	public String getCategrayId() {
		return categrayId;
	}

	public void setCategrayId(String categrayId) {
		this.categrayId = categrayId;
	}

	public Integer getVipTagNum() {
		return vipTagNum;
	}

	public void setVipTagNum(Integer vipTagNum) {
		this.vipTagNum = vipTagNum;
	}

	public Date getFreeTimeStart() {
		return freeTimeStart;
	}

	public void setFreeTimeStart(Date freeTimeStart) {
		this.freeTimeStart = freeTimeStart;
	}

	public Date getFreeTimeEnd() {
		return freeTimeEnd;
	}

	public void setFreeTimeEnd(Date freeTimeEnd) {
		this.freeTimeEnd = freeTimeEnd;
	}

	public String getZipUrl() {
		return zipUrl;
	}

	public void setZipUrl(String zipUrl) {
		this.zipUrl = zipUrl;
	}

	public String getPicUrl() {
		return picUrl;
	}

	public void setPicUrl(String picUrl) {
		this.picUrl = picUrl;
	}

	public String getCategoryName() {
		return categoryName;
	}

	public void setCategoryName(String categoryName) {
		this.categoryName = categoryName;
	}

	public String getPreviewPic() {
		return previewPic;
	}

	public void setPreviewPic(String previewPic) {
		this.previewPic = previewPic;
	}

	public Integer getIsFree() {
		return isFree;
	}

	public void setIsFree(Integer isFree) {
		this.isFree = isFree;
	}

	public Integer getSex() {
		return sex;
	}

	public void setSex(Integer sex) {
		this.sex = sex;
	}

	public Integer getOrder() {
		return order;
	}

	public void setOrder(Integer order) {
		this.order = order;
	}

	public Integer getIsShow() {
		return isShow;
	}

	public void setIsShow(Integer isShow) {
		this.isShow = isShow;
	}
}
