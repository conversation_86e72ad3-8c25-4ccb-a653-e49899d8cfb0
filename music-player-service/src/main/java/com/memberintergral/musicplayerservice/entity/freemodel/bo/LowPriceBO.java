package com.memberintergral.musicplayerservice.entity.freemodel.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Builder
@Data
public class LowPriceBO {

    /**
     * 是否符合低价策略
     */
    @JsonProperty("isLowPrice")
    private boolean isLowPrice;

    /**
     * 文案
     */
    private String title;

    /**
     * 跳转链接
     */
    private String redirectUrl;


}
