package com.memberintergral.musicplayerservice.entity.dto;

import lombok.Data;

import java.util.Objects;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.memberintergral.musicplayerservice.entity.dto
 * @date:2022/12/28
 */
@Data
public class PayDeskSignDTO {

    private String code;

    private String sign;

    private String updateTime;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PayDeskSignDTO that = (PayDeskSignDTO) o;
        return Objects.equals(code, that.code) && Objects.equals(sign, that.sign);
    }

    @Override
    public int hashCode() {
        return Objects.hash(code, sign);
    }
}
