package com.memberintergral.musicplayerservice.entity;

import java.util.Date;

public class PlayerClientTypeDTO {
    /**
     * 主键
     */
    private Long id;
    /**
     * 客户端type的id
     */
    private Integer clientTypeId;
    /**
     * 客户端type名称
     */
    private String clientTypeName;
    /**
     * 服务端开始支持的apiv
     */
    private Integer apivLimit;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 0 未删除 1 已删除
     */
    private Integer deleteStatus;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getClientTypeId() {
        return clientTypeId;
    }

    public void setClientTypeId(Integer clientTypeId) {
        this.clientTypeId = clientTypeId;
    }

    public String getClientTypeName() {
        return clientTypeName;
    }

    public void setClientTypeName(String clientTypeName) {
        this.clientTypeName = clientTypeName == null ? null : clientTypeName.trim();
    }

    public Integer getApivLimit() {
        return apivLimit;
    }

    public void setApivLimit(Integer apivLimit) {
        this.apivLimit = apivLimit;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getDeleteStatus() {
        return deleteStatus;
    }

    public void setDeleteStatus(Integer deleteStatus) {
        this.deleteStatus = deleteStatus;
    }
}