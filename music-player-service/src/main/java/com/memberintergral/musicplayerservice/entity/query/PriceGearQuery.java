package com.memberintergral.musicplayerservice.entity.query;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PriceGearQuery {

//    /**
//     * 规则类型
//     */
//    private String ruleType;

    /**
     * userId
     */
    private String userId;

    /**
     * sid
     */
    private String sid;

    /**
     * virtualUserId
     */
    private String virtualUserId;

    /**
     * virtualSid
     */
    private String virtualSid;


//    /**
//     * gearId 档位id
//     */
//    private String gearId;

    /**
     * deviceId
     */
    //@NotNull(message = "收银台deviceId不能为空")
    private String deviceId;

    /**
     * 收银台标识
     */
    @NotNull(message = "收银台标识不能为空")
    private String payDeskSign;

    /**
     * 平台
     */
    @NotNull(message = "收银台platform不能为空")
    private String platform;

    /**
     * 分类时传入
     */
    private String vipType;

    private String vers;

    /**
     * ios source
     */
    private String source;

    /**
     * ios firstPay
     */
    private Boolean firstPay;

    /**
     * 来源
     */
    private String fromsrc;

}
