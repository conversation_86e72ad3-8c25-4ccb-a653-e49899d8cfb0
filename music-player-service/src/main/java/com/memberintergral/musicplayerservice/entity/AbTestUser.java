package com.memberintergral.musicplayerservice.entity;

import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.memberintergral.musicplayerservice.entity
 * @date:2022/9/5
 */
@Data
public class AbTestUser {

    private Long id;

    private Long userId;

    /**
     * 1 模型人群 2 随机人群 3.酷我模型人群 4 白名单人群
     * model_mc   model_random   model_kw
     *
     */
    // 类别
    private Integer userType;

}
