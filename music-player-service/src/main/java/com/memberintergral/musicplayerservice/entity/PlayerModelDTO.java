package com.memberintergral.musicplayerservice.entity;


import java.io.Serializable;
import java.util.Date;

/**
* <AUTHOR>
* @version V1.0
* <p>Description: PlayerModel DTO</p>
* @date 2022-08-22 13:41:54
*/
public class PlayerModelDTO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 模式名称
     */
    private String modelName;

    /**
     * 产品定义的模式code
     */
    private String modelCode;

    /**
     * 模式说明
     */
    private String modelDesc;

    /**
     * 播放器类型code, 0、模式 1、动效
     */
    private String typeCode;

    /**
     * 0 未删除 1 已删除
     */
    private Integer deleteStatus;

    /**
     * 拓展字段1
     */
    private String ext1;

    /**
     * 拓展字段2
     */
    private String ext2;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 播放器样式数量
     */
    private Integer styleCount;

    /**
     * 排序字段
     */
    private Integer sort;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public String getModelCode() {
        return modelCode;
    }

    public void setModelCode(String modelCode) {
        this.modelCode = modelCode;
    }

    public String getModelDesc() {
        return modelDesc;
    }

    public void setModelDesc(String modelDesc) {
        this.modelDesc = modelDesc;
    }

    public String getTypeCode() {
        return typeCode;
    }

    public void setTypeCode(String typeCode) {
        this.typeCode = typeCode;
    }

    public Integer getDeleteStatus() {
        return deleteStatus;
    }

    public void setDeleteStatus(Integer deleteStatus) {
        this.deleteStatus = deleteStatus;
    }

    public String getExt1() {
        return ext1;
    }

    public void setExt1(String ext1) {
        this.ext1 = ext1;
    }

    public String getExt2() {
        return ext2;
    }

    public void setExt2(String ext2) {
        this.ext2 = ext2;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getStyleCount() {
        return styleCount;
    }

    public void setStyleCount(Integer styleCount) {
        this.styleCount = styleCount;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }
}
