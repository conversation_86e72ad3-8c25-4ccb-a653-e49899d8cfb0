package com.memberintergral.musicplayerservice.entity;

import lombok.*;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AudioDeviceDTO implements Serializable {


    /**
     * 主键ID
     */
    private Integer id;

    /**
     * audio_recommend_config 主键id
     */
    private Integer pid;

    /**
     * 设备名
     */
    private String name;

    /**
     * 耳机图片
     */
    private String pic;

    /**
     * 音质排名
     */
    private String audioSort;
}
