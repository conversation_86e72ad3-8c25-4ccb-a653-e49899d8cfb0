package com.memberintergral.musicplayerservice.entity.query;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * <p>Description: OrderInfo query</p>
 * @date 2022-08-26 12:24:36
 */
@Getter
@Setter
public class OrderInfoQuery {

    /**
     *
     */
    private Long id;

    /**
     *
     */
    private Long userId;

    /**
     *
     */
    private Long pid;

    /**
     *
     */
    private Double credit;

    /**
     *
     */
    private java.util.Date time;

    /**
     *
     */
    private java.util.Date payDate;

    /**
     *
     */
    private Integer payType;

    /**
     *
     */
    private Integer status;

    /**
     *
     */
    private String act;

    /**
     *
     */
    private String platform;

    /**
     *
     */
    private String src;

    /**
     *
     */
    private String clientAct;

    /**
     *
     */
    private String autoPay;

    /**
     *
     */
    private Integer type;

    /**
     *
     */
    private Integer sourceType;

    /**
     *
     */
    private Integer paySrc;

    /**
     *
     */
    private Integer shareFalg;

    /**
     *
     */
    private Integer falsed;

    /**
     *
     */
    private String thirdOrderid;

    /**
     *
     */
    private Integer isHide;

    /**
     *
     */
    private String fansCustomerId;

    /**
     *
     */
    private Long virtualUid;

    /**
     *
     */
    private java.util.Date mergeDate;

    /**
     *
     */
    private String thirdunion;

    /**
     *
     */
    private List<String> srcs;

}
