package com.memberintergral.musicplayerservice.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.memberintergral.musicplayerservice.entity
 * @date:2023/7/25
 */
@NoArgsConstructor
@Data
public class RenewalHoldResp {
    @JsonProperty("ida")
    private List<String> ida;
    @JsonProperty("id")
    private String id;
    @JsonProperty("title")
    private String title;
    @JsonProperty("subTitle")
    private String subTitle;
    @JsonProperty("topTag")
    private String topTag;
    @JsonProperty("bottomTag")
    private String bottomTag;
    @JsonProperty("tip")
    private String tip;
    @JsonProperty("btnText")
    private String btnText;
    @JsonProperty("oPrice")
    private Double oPrice;
    @JsonProperty("maxNum")
    private Integer maxNum;
    @JsonProperty("limitPriceAddPay")
    private Integer limitPriceAddPay;
    @JsonProperty("autoPay")
    private Integer autoPay;
    @JsonProperty("month")
    private Integer month;
    @JsonProperty("vipType")
    private String vipType;
    @JsonProperty("vipTypeId")
    private Integer vipTypeId;
    @JsonProperty("vipTypeName")
    private Object vipTypeName;
    @JsonProperty("autoPayChecked")
    private Boolean autoPayChecked;
    @JsonProperty("lastTime")
    private Integer lastTime;
    @JsonProperty("price")
    private Double price;
    @JsonProperty("autoPayPrice")
    private Double autoPayPrice;
    @JsonProperty("autoPayBtnShow")
    private Boolean autoPayBtnShow;
    @JsonProperty("descImg")
    private String descImg;
    @JsonProperty("style")
    private String style;
    @JsonProperty("src")
    private String src;
    @JsonProperty("orderType")
    private Integer orderType;
    @JsonProperty("btnEvent")
    private Integer btnEvent;
    @JsonProperty("isAddPayShow")
    private Boolean isAddPayShow;
    @JsonProperty("addedPrice")
    private Integer addedPrice;
    @JsonProperty("isFirstRenewal")
    private Object isFirstRenewal;
    @JsonProperty("exitPopupDuration")
    private String exitPopupDuration;
    @JsonProperty("exitPopupCountdown")
    private String exitPopupCountdown;
    @JsonProperty("exitPopupTime")
    private String exitPopupTime;
    @JsonProperty("payTypeList")
    private List<PayTypeListDTO> payTypeList;
    @JsonProperty("curPayType")
    private PayTypeListDTO curPayType;

    @NoArgsConstructor
    @Data
    public static class PayTypeListDTO {
        @JsonProperty("id")
        private String id;
        @JsonProperty("btnType")
        private String btnType;
        @JsonProperty("name")
        private String name;
        @JsonProperty("icon")
        private String icon;
        @JsonProperty("tip")
        private String tip;
        @JsonProperty("goodsCode")
        private String goodsCode;
        @JsonProperty("checked")
        private Integer checked;
    }
}
