package com.memberintergral.musicplayerservice.entity.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version V1.0
 * <p>Description: OrderInfo DTO</p>
 * @date 2022-08-26 12:24:36
 */
@Getter
@Setter
public class OrderInfoDTO {

    /**
     *
     */
    private Long id;

    /**
     *
     */
    private Long userId;

    /**
     *
     */
    private Long pid;

    /**
     *
     */
    private Double credit;

    /**
     *
     */
    private java.util.Date time;

    /**
     *
     */
    private java.util.Date payDate;

    /**
     *
     */
    private Integer payType;

    /**
     *
     */
    private Integer status;

    /**
     *
     */
    private String act;

    /**
     *
     */
    private String platform;

    /**
     *
     */
    private String src;

    /**
     *
     */
    private String clientAct;

    /**
     *
     */
    private String autoPay;

    /**
     *
     */
    private Integer type;

    /**
     *
     */
    private Integer sourceType;

    /**
     *
     */
    private Integer paySrc;

    /**
     *
     */
    private Integer shareFalg;

    /**
     *
     */
    private Integer falsed;

    /**
     *
     */
    private String thirdOrderid;

    /**
     *
     */
    private Integer isHide;

    /**
     *
     */
    private String fansCustomerId;

    /**
     *
     */
    private Long virtualUid;

    /**
     *
     */
    private java.util.Date mergeDate;

    /**
     *
     */
    private String thirdunion;

}
