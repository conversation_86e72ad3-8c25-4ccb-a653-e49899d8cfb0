package com.memberintergral.musicplayerservice.entity.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.memberintergral.musicplayerservice.entity.dto
 * @date:2022/12/28
 */

@Getter
@Setter
@EqualsAndHashCode
public class DataItemVipCardContentExcelDTO {

    @ExcelProperty("order")
    private Integer order;

    @ExcelProperty("status")
    private String status;

    @ExcelProperty("remark")
    private String remark;

    @ExcelProperty("group")
    private String group;

    @ExcelProperty("vipcard_name")
    private String vipcard_name;

    @ExcelProperty("type")
    private String type;

    @ExcelProperty("id")
    private String id;

    @ExcelProperty("platform")
    private String platform;

    @ExcelProperty("pid")
    private String pid;

    @ExcelProperty("src")
    private String src;

    @ExcelProperty("sale_price")
    private String sale_price;

    @ExcelProperty("origin_price")
    private String origin_price;

    @ExcelProperty("discount")
    private String discount;

    @ExcelProperty("top_left")
    private String top_left;

    @ExcelProperty("bottom_left")
    private String bottom_left;

    @ExcelProperty("top_right")
    private String top_right;

    @ExcelProperty("bottom_right")
    private String bottom_right;

    @ExcelProperty("bottom")
    private String bottom;

    @ExcelProperty("img_url")
    private String img_url ;

    @ExcelProperty("user_type")
    private String user_type;

    @ExcelProperty("user_status")
    private String user_status;

    @ExcelProperty("month")
    private String month;

    @ExcelProperty("auto_pay")
    private String auto_pay;

    @ExcelProperty("ext")
    private String ext;

}
