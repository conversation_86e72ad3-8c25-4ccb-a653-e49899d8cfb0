package com.memberintergral.musicplayerservice.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.memberintergral.musicplayerservice.entity
 * @date:2023/8/10
 */
@NoArgsConstructor
@Data
public class FilterPayWayDTO {

    @JsonProperty("style")
    private String style;
    @JsonProperty("list")
    private List<PayWay> list;

    @NoArgsConstructor
    @Data
    public static class PayWay {
        @JsonProperty("id")
        private String id;
        @JsonProperty("btnType")
        private String btnType;
        @JsonProperty("name")
        private String name;
        @JsonProperty("icon")
        private String icon;
        @JsonProperty("tip")
        private String tip;
        @JsonProperty("goodsCode")
        private String goodsCode;
        @JsonProperty("checked")
        private Integer checked;
    }
}
