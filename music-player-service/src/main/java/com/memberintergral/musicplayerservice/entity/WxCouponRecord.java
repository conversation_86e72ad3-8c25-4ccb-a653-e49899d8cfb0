package com.memberintergral.musicplayerservice.entity;

import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 * @Desc: 微信卡券记录部分
 * @date 2024-06-28 18:21:44
 */
@Data
@ToString
public class WxCouponRecord {

    private Long id;


    // 用户id
    private Long userId;

    // 微信批次id
    private String wxSockId;

    // 发放的卡券id
    private String couponId;

    // 创建时间
    private Date createTime;

    // 发放时间
    private Date sendTime;

    // 使用时间
    private Date useTime;

    // 微信模版id
    private String planId;

    // 自动续费优惠tag
    private String goodsTag;


}
