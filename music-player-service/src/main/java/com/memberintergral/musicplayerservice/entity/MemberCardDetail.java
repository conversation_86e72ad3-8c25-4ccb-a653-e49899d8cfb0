package com.memberintergral.musicplayerservice.entity;

import java.util.Date;

/**
 * <AUTHOR>
 * @Desc:
 * @date 2023-03-02 11:02:55
 */
public class MemberCardDetail {

    private Long id;

    private Date buyDate;

    private int duration;

    private int isUsed;

    private String orderId;

    private String productTypeId;

    private Long receiverId;

    private int status;

    private Long userId;

    private String vipCardOrderId;

    private Date vipCardGetDate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getBuyDate() {
        return buyDate;
    }

    public void setBuyDate(Date buyDate) {
        this.buyDate = buyDate;
    }

    public int getDuration() {
        return duration;
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }

    public int getIsUsed() {
        return isUsed;
    }

    public void setIsUsed(int isUsed) {
        this.isUsed = isUsed;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getProductTypeId() {
        return productTypeId;
    }

    public void setProductTypeId(String productTypeId) {
        this.productTypeId = productTypeId;
    }

    public Long getReceiverId() {
        return receiverId;
    }

    public void setReceiverId(Long receiverId) {
        this.receiverId = receiverId;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getVipCardOrderId() {
        return vipCardOrderId;
    }

    public void setVipCardOrderId(String vipCardOrderId) {
        this.vipCardOrderId = vipCardOrderId;
    }

    public Date getVipCardGetDate() {
        return vipCardGetDate;
    }

    public void setVipCardGetDate(Date vipCardGetDate) {
        this.vipCardGetDate = vipCardGetDate;
    }
}
