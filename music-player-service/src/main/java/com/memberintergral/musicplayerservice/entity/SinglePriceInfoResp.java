package com.memberintergral.musicplayerservice.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.memberintergral.musicplayerservice.entity
 * @date:2023/7/25
 */
@NoArgsConstructor
@Data
public class SinglePriceInfoResp {

    @JsonProperty("code")
    private Integer code;
    @JsonProperty("data")
    private DataDTO data;
    @JsonProperty("desc")
    private String desc;

    @NoArgsConstructor
    @Data
    public static class DataDTO {
        @JsonProperty("data")
        private List<DataDTO.DataDTO2> data;

        @NoArgsConstructor
        @Data
        public static class DataDTO2 {
            @JsonProperty("moreLink")
            private String moreLink;
            @JsonProperty("moreText")
            private String moreText;
            @JsonProperty("name")
            private String name;
            @JsonProperty("icon")
            private String icon;
            @JsonProperty("id")
            private Integer id;
            @JsonProperty("type")
            private String type;
            @JsonProperty("list")
            private List<DataDTO.DataDTO2.ListDTO> list;
            @JsonProperty("desc")
            private String desc;

            @NoArgsConstructor
            @Data
            public static class ListDTO {
                @JsonProperty("autoPay")
                private Integer autoPay;
                @JsonProperty("abData")
                private List<DataDTO.DataDTO2.ListDTO.AbDataDTO> abData;
                @JsonProperty("gearData")
                private DataDTO.DataDTO2.ListDTO.GearDataDTO gearData;
                @JsonProperty("gearName")
                private String gearName;
                @JsonProperty("platform")
                private String platform;
                @JsonProperty("extend")
                private String extend;
                @JsonProperty("vipData")
                private DataDTO.DataDTO2.ListDTO.VipDataDTO vipData;
                @JsonProperty("act")
                private String act;
                @JsonProperty("payAdd")
                private List<?> payAdd;
                @JsonProperty("freeAdd")
                private List<?> freeAdd;
                @JsonProperty("doc")
                private DataDTO.DataDTO2.ListDTO.DocDTO doc;
                @JsonProperty("id")
                private Integer id;
                @JsonProperty("orderRank")
                private Integer orderRank;

                @NoArgsConstructor
                @Data
                public static class GearDataDTO {
                    @JsonProperty("unit")
                    private String unit;
                    @JsonProperty("month")
                    private Integer month;
                    @JsonProperty("name")
                    private String name;
                }

                @NoArgsConstructor
                @Data
                public static class VipDataDTO {
                    @JsonProperty("name")
                    private String name;
                    @JsonProperty("id")
                    private Integer id;
                    @JsonProperty("type")
                    private String type;
                }

                @NoArgsConstructor
                @Data
                public static class DocDTO {
                    @JsonProperty("limitPriceAddPay")
                    private String limitPriceAddPay;
                    @JsonProperty("btnText")
                    private String btnText;
                    @JsonProperty("subTitle")
                    private String subTitle;
                    @JsonProperty("autoPayDesc")
                    private String autoPayDesc;
                    @JsonProperty("topTag")
                    private String topTag;
                    @JsonProperty("bottomTag")
                    private String bottomTag;
                    @JsonProperty("tip")
                    private String tip;
                    @JsonProperty("oPrice")
                    private String oPrice;
                    @JsonProperty("maxNum")
                    private String maxNum;
                    @JsonProperty("title")
                    private String title;
                }

                @NoArgsConstructor
                @Data
                public static class AbDataDTO {
                    @JsonProperty("filterId")
                    private Integer filterId;
                    @JsonProperty("extend")
                    private DataDTO.DataDTO2.ListDTO.AbDataDTO.ExtendDTO extend;
                    @JsonProperty("renewalPrice")
                    private Double renewalPrice;
                    @JsonProperty("filterFromsrc")
                    private String filterFromsrc;
                    @JsonProperty("price")
                    private Double price;
                    @JsonProperty("isFirstRenewal")
                    private Boolean isFirstRenewal;
                    @JsonProperty("doc")
                    private DataDTO.DataDTO2.ListDTO.AbDataDTO.DocDTO doc;
                    @JsonProperty("payWay")
                    private DataDTO.DataDTO2.ListDTO.AbDataDTO.PayWayDTO payWay;
                    @JsonProperty("orderRank")
                    private Integer orderRank;

                    @NoArgsConstructor
                    @Data
                    public static class ExtendDTO {
                    }

                    @NoArgsConstructor
                    @Data
                    public static class DocDTO {
                        @JsonProperty("orderType")
                        private String orderType;
                        @JsonProperty("btnText")
                        private String btnText;
                        @JsonProperty("src")
                        private String src;
                        @JsonProperty("isSrcInCode")
                        private String isSrcInCode;
                        @JsonProperty("autoPayBtnShow")
                        private Integer autoPayBtnShow;
                        @JsonProperty("topTag")
                        private String topTag;
                        @JsonProperty("bottomTag")
                        private String bottomTag;
                        @JsonProperty("exitPopupDuration")
                        private String exitPopupDuration;
                        @JsonProperty("title")
                        private String title;
                        @JsonProperty("subTitle")
                        private String subTitle;
                        @JsonProperty("autoPayDesc")
                        private String autoPayDesc;
                        @JsonProperty("isAddPayShow")
                        private String isAddPayShow;
                        @JsonProperty("exitPopupTime")
                        private String exitPopupTime;
                        @JsonProperty("descImg")
                        private String descImg;
                        @JsonProperty("style")
                        private String style;
                        @JsonProperty("tip")
                        private String tip;
                        @JsonProperty("exitPopupCountdown")
                        private String exitPopupCountdown;
                    }

                    @NoArgsConstructor
                    @Data
                    public static class PayWayDTO {
                        @JsonProperty("style")
                        private String style;
                        @JsonProperty("list")
                        private List<DataDTO.DataDTO2.ListDTO.AbDataDTO.PayWayDTO.ListDTO2> list;

                        @NoArgsConstructor
                        @Data
                        public static class ListDTO2 {
                            @JsonProperty("name")
                            private String name;
                            @JsonProperty("icon")
                            private String icon;
                            @JsonProperty("checked")
                            private Integer checked;
                            @JsonProperty("tip")
                            private String tip;
                            @JsonProperty("id")
                            private String id;
                            @JsonProperty("goodsCode")
                            private String goodsCode;
                            @JsonProperty("btnType")
                            private String btnType;
                        }
                    }
                }
            }
        }
    }
}
