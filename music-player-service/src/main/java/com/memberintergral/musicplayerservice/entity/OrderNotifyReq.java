package com.memberintergral.musicplayerservice.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.memberintergral.musicplayerservice.entity
 * @date:2023/8/11
 */

@NoArgsConstructor
@Data
public class OrderNotifyReq {

     @JsonProperty("uid")
     private String uid;
     @JsonProperty("src")
     private String src;
     @JsonProperty("source")
     private String source;
     @JsonProperty("type")
     private String type;
     @JsonProperty("payType")
     private String payType;
     @JsonProperty("platform")
     private String platform;
     @JsonProperty("paySrc")
     private String paySrc;
     @JsonProperty("funsCustomerId")
     private String funsCustomerId;
     @JsonProperty("act")
     private String act;
     @JsonProperty("wxTflag")
     private String wxTflag;
     @JsonProperty("detail")
     private String detail;
    
}
