package com.memberintergral.musicplayerservice.entity;

import lombok.*;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AudioRecommendDTO implements Serializable {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 类型：1至臻母带 2至臻全景声 3至臻2.0
     */
    private Integer type;

    /**
     * 平台：ar安卓 ios苹果
     */
    private String platform;

    /**
     * 主标题
     */
    private String title;

    /**
     * 副标题
     */
    private String subtitle;

    /**
     * 耳机图片
     */
    private String pic;

    /**
     * 音质描述图片
     */
    private String audioDescPic;

    /**
     * 按钮文案
     */
    private String btnDesc;

    /**
     * 音质排名
     */
    private String audioSort;

    private String typeName;

    private String typeLabel;
}
