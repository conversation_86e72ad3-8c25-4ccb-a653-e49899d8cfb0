package com.memberintergral.musicplayerservice.entity;

import com.kuwo.commercialization.common.utill.DateUtil;
import com.kuwo.commercialization.common.vo.UiInfo;
import lombok.*;
import org.apache.commons.lang.StringUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.function.Predicate;

/**
 * @Desc: 推荐相关配置_new
 * @date 2023-08-02 11:43:57
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
public class RecommendConfigNew {
    /**
     * 主键
     */
    private Long id;
    /**
     * 开始推荐时间
     */
    private Date rcStTime;
    /**
     * 结束推荐时间
     */
    private Date rcEndTime;
    /**
     * 排序字段，优先级
     */
    private Integer sort;
    /**
     * 推荐目标用户,多个英文逗号分隔 (1豪华vip，2超级vip，3非豪华和超级VIP，4全部)
     */
    private String rcUserType;
    /**
     * 推荐模式类型
     */
    private String rcModelCode;
    /**
     * 推荐的播放器模式code
     */
    private String rcStyleCode;
    /**
     * 试用时长
     */
    private Long trialDuration;
    /**
     * 用户当前皮肤类型 (1 未改过（默认）,2全部)
     */
    private Integer userCurrentType;
    /**
     * 皮肤推荐样式
     */
    private Integer rcPattern;//0自动使用 1弹窗引导(老版本默认是0，自动使用)
    /**
     * 内容明细，json串
     */
    private String content;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 0 未删除 1 已删除
     */
    private Integer deleteStatus;
    /**
     * 拓展字段1
     */
    private String ext1;
    /**
     * 拓展字段2
     */
    private String ext2;


    /**
     * 规则过滤部分逻辑
     * @param uiInfo
     * @return
     */
    public static Predicate<RecommendConfigNew> filterRule(UiInfo uiInfo){

        return k -> {
            long svipExpire = uiInfo.getSvipExpire();
            long vipLuxuryExpire = uiInfo.getVipLuxuryExpire();
            boolean histRule = false;
            if (DateUtil.between(new Date(), k.getRcStTime(), k.getRcEndTime())){
                histRule = true;
            }
            if (histRule && StringUtils.isNotEmpty(k.getRcUserType())){
                long now = System.currentTimeMillis();
                histRule = Arrays.stream(k.getRcUserType().split(",")).anyMatch(s -> {
                    // 会员信息匹配
                    if ("4".equals(s)){
                        return true;
                    }
                    if ("1".equals(s) && vipLuxuryExpire > now && svipExpire < now){
                        return true;
                    }
                    if ("2".equals(s) && svipExpire > now){
                        return true;
                    }
                    if ("3".equals(s) && (vipLuxuryExpire < now && svipExpire < now)){
                        return true;
                    }
                    return false;
                });
            }
            return histRule;
        };

    }

}