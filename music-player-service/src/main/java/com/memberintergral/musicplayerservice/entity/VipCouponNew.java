package com.memberintergral.musicplayerservice.entity;

import com.memberintergral.musicplayerservice.util.recallStrategy.common.CouponTargets;
import com.memberintergral.musicplayerservice.util.recallStrategy.common.CouponType;
import lombok.Data;

import java.util.Date;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.memberintergral.musicplayerservice.entity
 * @date:2022/9/6
 */
@Data
public class VipCouponNew {

    private String serialKey;//代表代金券的序列码 ＋ＩＮＤＥＸ

    private String name;//代金券名称

    private String picUrl;//代金券"头像"

    private CouponType type;//代金券类型

    private Double worth;//代金券额度
    
    private Date startTime;//有效期开始时间
    
    private Date endTime;//有效期结束时间

    private CouponTargets targets;//发放人群

    private String restricts;//使用范围

    private Boolean used = false;

    private String channel;//渠道

    private Long orderId = -1L;//订单ID ＋ＩＮＤＥＸ   HASH

    private Long uid = -1L;//用户ID ＋ＩＮＤＥＸ

    private Long sortId;

    private Long id;

    private String exp1;//exchangeCodeKey;

    private Date createdTime;

    private Date updateTime;

}
