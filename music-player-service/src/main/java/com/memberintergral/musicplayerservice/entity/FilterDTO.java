package com.memberintergral.musicplayerservice.entity;

import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-04
 */
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
public class FilterDTO implements Serializable {

    private static final long serialVersionUID=1L;

    private Integer id;

    /**
     * 策略名
     */
    private String filterName;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 续费价格
     */
    private BigDecimal renewalPrice;

    private String doc;

    /**
     * 档位id
     */
    private Integer gearId;

    private Integer isDelete;

    /**
     * 城市包
     */
    private String filterCityPackageId;

    /**
     * device id 匹配
     */
    private String filterDevice;

    /**
     * 是否为首开
     */
    private String filterFirstPay;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 剩余在期时间
     */
    private String filterPeriodLeft;

    /**
     * 是否首次续期
     */
    private String filterFirstRenew;

    /**
     * 过期时间
     */
    private String filterOverDue;

    /**
     * user_id尾号
     */
    private String filterUser;

    /**
     * virtual_user尾号
     */
    private String filterVitrualUser;

    /**
     * 人群包
     */
    private String filterPersonPackageId;

    private String payWay;

    private String editorName;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 排序
     */
    private Integer orderRank;

    /**
     * 创建时间
     */
    private Date endTime;

    /**
     * 第三方
     */
    private String unionId;

    /**
     * 第三方
     */
    private String threeMemberId;


    /**
     * 其它条件 json 结构
     */
    private String otherConditions;

    /**
     * fromsrc
     */
    private String filterFromsrc;

    /**
     * extend
     */
    private String extend;

    /**
     * 是否会员
     */
    private Integer filterIsMember;


    private Gson gson = new Gson();

    /**
     * 收银台标识
     */
    private String payDeskSign;

    /**
     * 挡位信息
     */
    private String gearDoc;

    /**
     * 是否连包
     */
    private Integer autoPay;

    private Integer gearType;

    private String vipType;

    private String src;

    private String platform;
    /**
     * 转换conditions
     * @return
     */
    public Map parseOtherConditions(){
        if (StringUtils.isBlank(otherConditions)){
            return new HashMap();
        }
        try {
           return gson.fromJson(otherConditions, Map.class);
        } catch (JsonSyntaxException e) {
            return new HashMap();
        }
    }
}
