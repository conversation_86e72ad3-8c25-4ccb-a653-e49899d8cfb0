package com.memberintergral.musicplayerservice.entity.query;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * <p>Description: PlayerStyle query</p>
 * @date 2022-08-22 13:41:54
 */

public class PlayerStyleQuery {

    /**
     * 主键
     */
    private Long id;

    /**
     * 播放器样式名称
     */
    private String styleName;

    /**
     * 播放器样式Code
     */
    private String styleCode;

    /**
     * 付费类型 0、免费 1、豪华VIP 2、超级会员   3、活动限定    4、用户体系等级
     */
    private Integer paymentType;

    /**
     * 用户体系等级数值
     */
    private Integer gradeNum;

    /**
     * src列表,逗号分隔
     */
    private String rightCodes;

    /**
     * 跳转链接
     */
    private String jumpUrl;

    /**
     * 播放器描述
     */
    private String playerDesc;

    /**
     * 播放器类型code, 0、模式 1、动效
     */
    private String typeCode;

    /**
     * 播放器模式code
     */
    private String modelCode;

    /**
     * 播放器模式code
     */
    private List<String> modelCodes;

    /**
     * 允许的嵌套类型code，多个逗号分隔
     */
    private String allowNestingCodes;

    /**
     * 默认嵌套类型codes
     */
    private String defAllowNestingCode;

    /**
     * 预览图地址
     */
    private String previewImgUrl;

    /**
     * 内容明细，json串
     */
    private String content;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 0 未删除 1 已删除
     */
    private Integer deleteStatus;

    /**
     * 拓展字段1
     */
    private String ext1;

    /**
     * 拓展字段2
     */
    private String ext2;

    /**
     * 第几页
     */
    protected Integer pageNum = 1;
    /**
     * 每页多少条
     */
    protected Integer pageSize = 10;

    /**
     * 分页取多少条
     */
    protected Integer limit = 10;
    /**
     * 偏移量
     */
    protected Integer offset = 0;

    /**
     * 排序字段
     */
    protected String orderBy;

    /**
     * 排序类型
     */
    protected String orderType = "desc";

    /**
     * 排序字段
     */
    private Integer sort;

    /**
     * 嵌套值(模式下存在)
     */
    private String nestingCode;

    /**
     * 平台
     * @return
     */
    private String platform;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getStyleName() {
        return styleName;
    }

    public void setStyleName(String styleName) {
        this.styleName = styleName;
    }

    public String getStyleCode() {
        return styleCode;
    }

    public void setStyleCode(String styleCode) {
        this.styleCode = styleCode;
    }

    public Integer getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(Integer paymentType) {
        this.paymentType = paymentType;
    }

    public String getPlayerDesc() {
        return playerDesc;
    }

    public void setPlayerDesc(String playerDesc) {
        this.playerDesc = playerDesc;
    }

    public String getTypeCode() {
        return typeCode;
    }

    public void setTypeCode(String typeCode) {
        this.typeCode = typeCode;
    }

    public String getModelCode() {
        return modelCode;
    }

    public void setModelCode(String modelCode) {
        this.modelCode = modelCode;
    }

    public String getAllowNestingCodes() {
        return allowNestingCodes;
    }

    public void setAllowNestingCodes(String allowNestingCodes) {
        this.allowNestingCodes = allowNestingCodes;
    }

    public String getDefAllowNestingCode() {
        return defAllowNestingCode;
    }

    public void setDefAllowNestingCode(String defAllowNestingCode) {
        this.defAllowNestingCode = defAllowNestingCode;
    }

    public String getPreviewImgUrl() {
        return previewImgUrl;
    }

    public void setPreviewImgUrl(String previewImgUrl) {
        this.previewImgUrl = previewImgUrl;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getDeleteStatus() {
        return deleteStatus;
    }

    public void setDeleteStatus(Integer deleteStatus) {
        this.deleteStatus = deleteStatus;
    }

    public String getExt1() {
        return ext1;
    }

    public void setExt1(String ext1) {
        this.ext1 = ext1;
    }

    public String getExt2() {
        return ext2;
    }

    public void setExt2(String ext2) {
        this.ext2 = ext2;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public List<String> getModelCodes() {
        return modelCodes;
    }

    public void setModelCodes(List<String> modelCodes) {
        this.modelCodes = modelCodes;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getNestingCode() {
        return nestingCode;
    }

    public void setNestingCode(String nestingCode) {
        this.nestingCode = nestingCode;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public Integer getGradeNum() {
        return gradeNum;
    }

    public void setGradeNum(Integer gradeNum) {
        this.gradeNum = gradeNum;
    }

    public String getRightCodes() {
        return rightCodes;
    }

    public void setRightCodes(String rightCodes) {
        this.rightCodes = rightCodes;
    }

    public String getJumpUrl() {
        return jumpUrl;
    }

    public void setJumpUrl(String jumpUrl) {
        this.jumpUrl = jumpUrl;
    }
}
