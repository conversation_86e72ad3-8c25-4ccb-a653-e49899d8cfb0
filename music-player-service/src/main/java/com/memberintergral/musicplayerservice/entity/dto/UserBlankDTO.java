package com.memberintergral.musicplayerservice.entity.dto;

import lombok.Data;

import java.util.Date;
import java.util.Objects;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.memberintergral.musicplayerservice.entity.dto
 * @date:2022/12/28
 */
@Data
public class UserBlankDTO {

    private String code;

    private Long userId;

    private String updateTime;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserBlankDTO that = (UserBlankDTO) o;
        return Objects.equals(code, that.code) && Objects.equals(userId, that.userId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(code, userId);
    }
}
