package com.memberintergral.musicplayerservice.entity;

import lombok.Data;

import java.util.Date;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.memberintergral.musicplayerservice.entity
 * @date:2022/9/5
 */
@Data
public class SchemeConfig {

    private Long id; //自增主键id
    private String code; //唯一标识码
    private String category; //配置分类
    private String resource; //配置信息
    private Date createTime;
    private Date updateTime;
    private String ext1;
    private String ext2;
    private String ext3;

}
