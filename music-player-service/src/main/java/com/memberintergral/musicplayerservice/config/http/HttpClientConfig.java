package com.memberintergral.musicplayerservice.config.http;

import com.kuwo.commercialization.common.http.EcomRemoteRequest;
import com.kuwo.commercialization.common.http.VipAdvRemoteRequest;
import com.kuwo.commercialization.common.http.VipDomainHttpRequest;
import com.kuwo.commercialization.common.utill.SSLSocketClient;
import okhttp3.OkHttpClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Desc:
 * @date 2021-07-07 16:01:10
 */
@Configuration
public class HttpClientConfig {

    @Value("${http.ecom.url}")
    private String ecomRootUrl;

    @Value("${vip.domain}")
    private String vipDomainUrl;

    @Bean
    public OkHttpClient buildOkHttpClient(){
        OkHttpClient client = new OkHttpClient().newBuilder()
                .connectTimeout(3, TimeUnit.SECONDS)
                .readTimeout(3, TimeUnit.SECONDS)
                .writeTimeout(3, TimeUnit.SECONDS)
                .sslSocketFactory(SSLSocketClient.getSSLSocketFactory(), SSLSocketClient.getX509TrustManager())
                .hostnameVerifier(SSLSocketClient.getHostnameVerifier())
                .build();
        return client;
    }

    @Bean
    public EcomRemoteRequest buildEcomRemoteRequest(){
        EcomRemoteRequest ecomRemoteRequest = new EcomRemoteRequest(ecomRootUrl, buildOkHttpClient());
        return ecomRemoteRequest;
    }

    @Bean
    public VipDomainHttpRequest buildVipDomainHttpRequest(){
        VipDomainHttpRequest vipDomainHttpRequest = new VipDomainHttpRequest(vipDomainUrl, buildOkHttpClient());
        return vipDomainHttpRequest;
    }

    @Bean
    public VipAdvRemoteRequest buildVipAdvRemoteRequest(){
        VipAdvRemoteRequest vipAdvRemoteRequest = new VipAdvRemoteRequest(vipDomainUrl, buildOkHttpClient());
        return vipAdvRemoteRequest;
    }

}
