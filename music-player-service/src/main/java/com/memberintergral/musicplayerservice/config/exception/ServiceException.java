package com.memberintergral.musicplayerservice.config.exception;

import com.kuwo.commercialization.common.message.SystemCodeErrorConstant;

/**
 * @program: vip_Conf
 * @description: 异常类
 * @author: <EMAIL>
 * @create: 2022-10-31 13:42
 */
public class ServiceException extends Exception {

    private static final long serialVersionUID = 2512091844686503453L;

    private SystemCodeErrorConstant resultCode = SystemCodeErrorConstant.FAIL;

    public ServiceException(SystemCodeErrorConstant resultCode, Throwable cause){
        super(resultCode.getMessage(),cause);
    }

    public ServiceException(){
        super();
        resultCode = SystemCodeErrorConstant.FAIL;
    }

    public ServiceException(String message, Throwable cause){
        super(message,cause);
    }

    public ServiceException(String message) {
        super(message);
    }

    public ServiceException(Throwable cause) {
        super(cause);
    }

    public ServiceException(Throwable cause, SystemCodeErrorConstant resultCode) {
        super(cause);
        this.resultCode = resultCode;
    }

    public ServiceException(SystemCodeErrorConstant resultCode) {	//根据不同描述返回
        super();
        this.resultCode = resultCode;
    }

    public SystemCodeErrorConstant getResultCode() {
        return resultCode;
    }

    public void setResultCode(SystemCodeErrorConstant resultCode) {
        this.resultCode = resultCode;
    }


}
