package com.memberintergral.musicplayerservice.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.memberintergral.musicplayerservice.config
 * @date:2022/8/30
 */
@Configuration
@Data
public class ProductTypeConfig {

    @Value("vip.promotion.exe.vip1.one.auto")
    private String vipPromotionExeVip1OneAuto;
    @Value("vip.promotion.exe.vip1.six")
    private String vipPromotionExeVip1Six;
    @Value("vip.promotion.exe.vip1.three")
    private String vipPromotionExeVip1Three;
    @Value("vip.promotion.exe.vip1.twelve")
    private String vipPromotionExeVip1Twelve;
    @Value("vip.promotion.exe.vip2.one")
    private String vipPromotionExeVip2One;
    @Value("vip.promotion.exe.vip2.six")
    private String vipPromotionExeVip2Six;
    @Value("vip.promotion.exe.vip2.three")
    private String vipPromotionExeVip2Three;
    @Value("vip.promotion.exe.vip2.twelve")
    private String vipPromotionExeVip2Twelve;
    @Value("vip.promotion.exe.vip4.six")
    private String vipPromotionExeVip4Six;
    @Value("vip.promotion.exe.vip4.twelve")
    private String vipPromotionExeVip4Twelve;
    @Value("vip.promotion.vip1.discount")
    private String vipPromotionVip1Discount;
    @Value("vip.promotion.vip2.discount")
    private String vipPromotionVip2Discount;
    @Value("vip.promotion.vip3.discount")
    private String vipPromotionVip3Discount;
    @Value("vip.promotion.vip4.discount")
    private String vipPromotionVip4Discount;
    @Value("vip_luxury.promotion.exe.vip1.one")
    private String vip_luxuryPromotionExeVip1One;
    @Value("vip_luxury.promotion.exe.vip1.one.ios")
    private String vip_luxuryPromotionExeVip1OneIos;
    @Value("vip_luxury.promotion.exe.vip1.six.ios")
    private String vip_luxuryPromotionExeVip1SixIos;
    @Value("vip_luxury.promotion.exe.vip1.three.ios")
    private String vip_luxuryPromotionExeVip1ThreeIos;
    @Value("vip_luxury.promotion.exe.vip1.twelve.ios")
    private String vip_luxuryPromotionExeVip1TwelveIos;

}
