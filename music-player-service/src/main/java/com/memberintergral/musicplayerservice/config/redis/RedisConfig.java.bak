package com.memberintergral.musicplayerservice.config.redis;

import org.apache.logging.log4j.util.Strings;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.redisson.config.SingleServerConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;

@Configuration
@EnableConfigurationProperties(RedisProperties.class)
public class RedisConfig {
    private static Logger logger = LoggerFactory.getLogger(RedisConfig.class);

    @Autowired
    private RedisProperties redisProperties;

    @Bean(destroyMethod = "shutdown",name = "redissonClient")
    RedissonClient redisson() throws IOException {
        logger.info("redission client -> init ");
        Config config = new Config();
        SingleServerConfig singleServerConfig = config.useSingleServer().setAddress(redisProperties.getAddress());
        if (Strings.isNotEmpty(redisProperties.getPassword())){
            singleServerConfig.setPassword(redisProperties.getPassword());
        }
        if (redisProperties.getSelectDb()!=null){
            singleServerConfig.setDatabase(redisProperties.getSelectDb());
        }
        return Redisson.create(config);
    }

}
