package com.memberintergral.musicplayerservice.config.redis;

import org.springframework.boot.context.properties.ConfigurationProperties;

@ConfigurationProperties(prefix = "redis.config", ignoreUnknownFields = true)
public class RedisProperties {

    private String address;

    private String password;

    private Integer selectDb;

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Integer getSelectDb() {
        return selectDb;
    }

    public void setSelectDb(Integer selectDb) {
        this.selectDb = selectDb;
    }
}
