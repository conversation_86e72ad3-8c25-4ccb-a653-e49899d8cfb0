//package com.memberintergral.musicplayerservice.config.uniqId;
//
//import com.baidu.fsg.uid.UidGenerator;
//import org.springframework.beans.BeansException;
//import org.springframework.context.ApplicationContext;
//import org.springframework.context.ApplicationContextAware;
//import org.springframework.stereotype.Component;
//
//import java.util.Map;
//import java.util.concurrent.ConcurrentHashMap;
//
//@Component
//public class IdGenerator implements ApplicationContextAware {
//
//    private static Map<String, UidGenerator> uidGeneratorMap = new ConcurrentHashMap<>();
//
//    private ApplicationContext applicationContext;
//
//    /**
//     * 创建uid Generator
//     * @param businessTag
//     * @return
//     */
//    public UidGenerator getUidGenerator(String businessTag){
//        if (uidGeneratorMap.containsKey(businessTag)){
//            return uidGeneratorMap.get(businessTag);
//        }
//        UidGenerator uidGenerator = applicationContext.getBean(UidGenerator.class);
//        uidGeneratorMap.put(businessTag, uidGenerator);
//        return uidGenerator;
//    }
//
//
//    public long getId(String businessTag){
//       return getUidGenerator(businessTag).getUID();
//    }
//
//
//    @Override
//    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
//        this.applicationContext = applicationContext;
//    }
//
//
//    public void shutdown() throws Exception {
//        for (UidGenerator uidGenerator: uidGeneratorMap.values()){
//            if (uidGenerator instanceof MyUidGenerator){
//                MyUidGenerator generator = (MyUidGenerator) uidGenerator;
//                generator.destroy();
//            }
//        }
//    }
//
//}
