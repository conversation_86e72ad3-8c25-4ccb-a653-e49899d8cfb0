//package com.memberintergral.musicplayerservice.config.sentinel;
//
//import com.alibaba.csp.sentinel.slots.block.flow.FlowRule;
//import com.google.gson.*;
//
//import java.lang.reflect.Type;
//
//public class FlowRuleDeserialize implements JsonDeserializer<FlowRule> {
//    @Override
//    public FlowRule deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
//        JsonObject jsonObj = json.getAsJsonObject();
//        FlowRule flowRule = new FlowRule();
//        if (jsonObj.has("grade")){
//            flowRule.setGrade(jsonObj.get("grade").getAsInt());
//        }
//        if (jsonObj.has("count")){
//            flowRule.setCount(jsonObj.get("count").getAsDouble());
//        }
//        if (jsonObj.has("strategy")){
//            flowRule.setStrategy(jsonObj.get("strategy").getAsInt());
//        }
//        if (jsonObj.has("controlBehavior")){
//            flowRule.setControlBehavior(jsonObj.get("controlBehavior").getAsInt());
//        }
//        if (jsonObj.has("warmUpPeriodSec")){
//            flowRule.setWarmUpPeriodSec(jsonObj.get("warmUpPeriodSec").getAsInt());
//        }
//        if (jsonObj.has("maxQueueingTimeMs")){
//            flowRule.setMaxQueueingTimeMs(jsonObj.get("maxQueueingTimeMs").getAsInt());
//        }
//        if (jsonObj.has("clusterMode")){
//            flowRule.setClusterMode(jsonObj.get("clusterMode").getAsBoolean());
//        }
//        if (jsonObj.has("resource")){
//            flowRule.setResource(jsonObj.get("resource").getAsString());
//        }
//        if (jsonObj.has("limitApp")){
//            flowRule.setLimitApp(jsonObj.get("limitApp").getAsString());
//        }
//        // controller 无法序列化
//
//        return flowRule;
//    }
//}
