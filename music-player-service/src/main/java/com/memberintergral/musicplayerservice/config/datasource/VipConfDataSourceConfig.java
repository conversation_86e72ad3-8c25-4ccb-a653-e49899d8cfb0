package com.memberintergral.musicplayerservice.config.datasource;

import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;
import java.util.Properties;

/**
 * 统计库数据源
 */
@Configuration
@MapperScan(basePackages = "com.memberintergral.musicplayerservice.mapper.vipConf", sqlSessionTemplateRef = "vipConfSqlSessionTemplate")
@Slf4j
public class VipConfDataSourceConfig {

    @Value("${spring.vipConf.datasource.url}")
    private String url;
    @Value("${spring.vipConf.datasource.username}")
    private String user;
    @Value("${spring.vipConf.datasource.password}")
    private String password;
    @Value("${spring.vipConf.datasource.driverClassName}")
    private String driverClass;



    /**
     * 数据源配置
     */
    @Value("${spring.druid.initialSize}")
    private Integer initialSize;
    @Value("${spring.druid.minIdle}")
    private Integer minIdle;
    @Value("${spring.druid.maxActive}")
    private Integer maxActive;
    @Value("${spring.druid.maxWait}")
    private Long maxWait;
    @Value("${spring.druid.timeBetweenEvictionRunsMillis}")
    private Long timeBetweenEvictionRunsMillis;
    @Value("${spring.druid.minEvictableIdleTimeMillis}")
    private Long minEvictableIdleTimeMillis;
    @Value("${spring.druid.validationQuery}")
    private String validationQuery;
    @Value("${spring.druid.testWhileIdle}")
    private Boolean testWhileIdle;
    @Value("${spring.druid.testOnBorrow}")
    private Boolean testOnBorrow;
    @Value("${spring.druid.testOnReturn}")
    private Boolean testOnReturn;
    @Value("${spring.druid.poolPreparedStatements}")
    private Boolean poolPreparedStatements;
    @Value("${spring.druid.maxPoolPreparedStatementPerConnectionSize}")
    private Integer maxPoolPreparedStatementPerConnectionSize;
    @Value("${spring.druid.filters}")
    private String filters;
    @Value("${spring.druid.connectionProperties}")
    private Properties connectionProperties;
    @Value("${spring.druid.logSlowSql}")
    private String logSlowSql;


    @Bean(name = "vipConfDataSource")
    public DataSource abConfDataSource() {
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setDriverClassName(driverClass);
        dataSource.setJdbcUrl(url);
        dataSource.setUsername(user);
        dataSource.setPassword(password);
        dataSource.setConnectionTestQuery(validationQuery);
        dataSource.setMinimumIdle(minIdle);
        dataSource.setMaximumPoolSize(maxActive);
        dataSource.setConnectionTimeout(maxWait);
        return dataSource;
    }

    @Bean(name = "vipConfSqlSessionFactory")
    public SqlSessionFactory adminSqlSessionFactory(@Qualifier("vipConfDataSource") DataSource dataSource) throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:mybatis/mapper/vipConf/*.xml"));
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:mybatis/mapper/vipConf/**/*.xml"));
        return bean.getObject();
    }

    @Bean(name = "vipConfTransactionManager")
    public DataSourceTransactionManager adminTransactionManager(@Qualifier("vipConfDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "vipConfSqlSessionTemplate")
    public SqlSessionTemplate adminSqlSessionTemplate(@Qualifier("vipConfSqlSessionFactory") SqlSessionFactory sqlSessionFactory) throws Exception {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

}
