package com.memberintergral.musicplayerservice.config.datasource;

import com.memberintergral.musicplayerservice.util.MybatisInterceptor;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;
import java.util.Properties;

/**
 * 统计库数据源
 */
@Configuration
@MapperScan(basePackages = "com.memberintergral.musicplayerservice.mapper.stasticsRead", sqlSessionTemplateRef = "stasticsReadSqlSessionTemplate")
public class StasticsReadDataSourceConfig {
    private static final Logger logger = LoggerFactory.getLogger(StasticsReadDataSourceConfig.class);

    @Value("${spring.stastics.datasource.url}")
    private String url;
    @Value("${spring.stastics.datasource.username}")
    private String user;
    @Value("${spring.stastics.datasource.password}")
    private String password;
    @Value("${spring.stastics.datasource.driverClassName}")
    private String driverClass;

    /**
     * 数据源配置
     */
    @Value("${spring.druid.initialSize}")
    private Integer initialSize;
    @Value("${spring.druid.minIdle}")
    private Integer minIdle;
    @Value("${spring.druid.maxActive}")
    private Integer maxActive;
    @Value("${spring.druid.maxWait}")
    private Long maxWait;
    @Value("${spring.druid.timeBetweenEvictionRunsMillis}")
    private Long timeBetweenEvictionRunsMillis;
    @Value("${spring.druid.minEvictableIdleTimeMillis}")
    private Long minEvictableIdleTimeMillis;
    @Value("${spring.druid.validationQuery}")
    private String validationQuery;
    @Value("${spring.druid.testWhileIdle}")
    private Boolean testWhileIdle;
    @Value("${spring.druid.testOnBorrow}")
    private Boolean testOnBorrow;
    @Value("${spring.druid.testOnReturn}")
    private Boolean testOnReturn;
    @Value("${spring.druid.poolPreparedStatements}")
    private Boolean poolPreparedStatements;
    @Value("${spring.druid.maxPoolPreparedStatementPerConnectionSize}")
    private Integer maxPoolPreparedStatementPerConnectionSize;
    @Value("${spring.druid.filters}")
    private String filters;
    @Value("${spring.druid.connectionProperties}")
    private Properties connectionProperties;
    @Value("${spring.druid.logSlowSql}")
    private String logSlowSql;


    @Bean(name = "stasticsReadDataSource")
    public DataSource adminDataSource() {
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setDriverClassName(driverClass);
        dataSource.setJdbcUrl(url);
        dataSource.setUsername(user);
        dataSource.setPassword(password);
        dataSource.setConnectionTestQuery(validationQuery);
        dataSource.setMinimumIdle(minIdle);
        dataSource.setMaximumPoolSize(maxActive);
        dataSource.setConnectionTimeout(maxWait);
        return dataSource;
    }

    @Bean(name = "stasticsReadSqlSessionFactory")
    public SqlSessionFactory adminSqlSessionFactory(@Qualifier("stasticsReadDataSource") DataSource dataSource) throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:mybatis/mapper/stasticsRead/*.xml"));
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:mybatis/mapper/stasticsRead/**/*.xml"));
        bean.setPlugins(new Interceptor[]{new MybatisInterceptor()});
        return bean.getObject();
    }

    @Bean(name = "stasticsReadTransactionManager")
    public DataSourceTransactionManager adminTransactionManager(@Qualifier("stasticsReadDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "stasticsReadSqlSessionTemplate")
    public SqlSessionTemplate adminSqlSessionTemplate(@Qualifier("stasticsReadSqlSessionFactory") SqlSessionFactory sqlSessionFactory) throws Exception {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

}
