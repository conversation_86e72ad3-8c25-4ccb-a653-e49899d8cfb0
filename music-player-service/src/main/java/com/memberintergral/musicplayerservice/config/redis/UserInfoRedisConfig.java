package com.memberintergral.musicplayerservice.config.redis;

import org.apache.logging.log4j.util.Strings;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.redisson.config.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;

/**
 * <AUTHOR>
 * @Desc:
 * @date 2023-04-25 18:05:39
 */
@Configuration
@EnableConfigurationProperties(UserInfoRedisProperties.class)
public class UserInfoRedisConfig {

    private static Logger logger = LoggerFactory.getLogger(UserInfoRedisConfig.class);

    @Autowired
    private UserInfoRedisProperties redisActProperties;

    @Bean(destroyMethod = "shutdown",name = "redissonUserInfoClient")
    RedissonClient redisson() throws IOException {
        logger.info("redissionUserInfo client -> init ");
        Config config = new Config();
        SingleServerConfig singleServerConfig = config.useSingleServer().setAddress(redisActProperties.getAddress());
        if (Strings.isNotBlank(redisActProperties.getPassword())) {
            singleServerConfig.setPassword(redisActProperties.getPassword());
        }
        if (redisActProperties.getSelectDb()!=null){
            singleServerConfig.setDatabase(redisActProperties.getSelectDb());
        }
        config.setCodec(new StringCodec());
        config.setUseScriptCache(true);
        return Redisson.create(config);
    }


}
