package com.memberintergral.musicplayerservice.config.redis;

import org.apache.logging.log4j.util.Strings;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.redisson.config.SingleServerConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;

@Configuration
@EnableConfigurationProperties(RedisKeeWiDBProperties.class)
public class RedisKeeWiDB {
    private static Logger logger = LoggerFactory.getLogger(RedisKeeWiDB.class);

    @Autowired
    private RedisKeeWiDBProperties redisKeeWiDBProperties;

    @Bean(destroyMethod = "shutdown",name = "redissonKeeWiDBClient")
    RedissonClient redisson() throws IOException {
        Config config = new Config();
        config.setCodec(new org.redisson.client.codec.StringCodec());
        try {
            logger.info("redissonKeeWiDBClient client -> init ");
            SingleServerConfig singleServerConfig = config.useSingleServer().setAddress(redisKeeWiDBProperties.getAddress());
            if (Strings.isNotEmpty(redisKeeWiDBProperties.getPassword())) {
                singleServerConfig.setPassword(redisKeeWiDBProperties.getPassword());
            }
            if (redisKeeWiDBProperties.getSelectDb() != null) {
                singleServerConfig.setDatabase(redisKeeWiDBProperties.getSelectDb());
            }
            return Redisson.create(config);
        } catch (Exception e){
            logger.info("redissonKeeWiDBClient client -> init error",e);
        }
        return Redisson.create(config);
    }
}
