package com.memberintergral.musicplayerservice.config.redis;

import org.apache.logging.log4j.util.Strings;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.redisson.config.SingleServerConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;

@Configuration
@EnableConfigurationProperties(RedisVipProperties.class)
public class RedisVipConfig {
    private static Logger logger = LoggerFactory.getLogger(RedisVipConfig.class);

    @Autowired
    private RedisVipProperties redisVipProperties;

    @Bean(destroyMethod = "shutdown", name = "redissonVipClient")
    RedissonClient redisson() throws IOException {
        logger.info("redissionVip client -> init ");
        Config config = new Config();
        SingleServerConfig singleServerConfig = config.useSingleServer().setAddress(redisVipProperties.getAddress());
        if (Strings.isNotEmpty(redisVipProperties.getPassword())) {
            singleServerConfig.setPassword(redisVipProperties.getPassword());
        }
        if (redisVipProperties.getSelectDb() != null) {
            singleServerConfig.setDatabase(redisVipProperties.getSelectDb());
        }
        return Redisson.create(config);
    }

}
