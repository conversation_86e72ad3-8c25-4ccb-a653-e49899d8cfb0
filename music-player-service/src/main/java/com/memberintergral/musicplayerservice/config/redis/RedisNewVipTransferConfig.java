package com.memberintergral.musicplayerservice.config.redis;

import org.apache.logging.log4j.util.Strings;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.redisson.config.SingleServerConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;

@Configuration
@EnableConfigurationProperties(RedisNewVipTransferProperties.class)
public class RedisNewVipTransferConfig {
    private static Logger logger = LoggerFactory.getLogger(RedisNewVipTransferConfig.class);

    @Autowired
    private RedisNewVipTransferProperties redisNewVipTransferProperties;

    @Bean(destroyMethod = "shutdown",name = "redissonNewVipTransferClient")
    RedissonClient redisson() throws IOException {
        logger.info("redissionTransfer client -> init ");
        Config config = new Config();
        SingleServerConfig singleServerConfig = config.useSingleServer().setAddress(redisNewVipTransferProperties.getAddress());
        if (Strings.isNotEmpty(redisNewVipTransferProperties.getPassword())){
            singleServerConfig.setPassword(redisNewVipTransferProperties.getPassword());
        }
        if (redisNewVipTransferProperties.getSelectDb()!=null){
            singleServerConfig.setDatabase(redisNewVipTransferProperties.getSelectDb());
        }
        config.setUseScriptCache(true);
        return Redisson.create(config);
    }

}
