package com.memberintergral.musicplayerservice.config.redis;

import org.apache.logging.log4j.util.Strings;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.redisson.config.SingleServerConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;

@Configuration
@EnableConfigurationProperties(RedisActProperties.class)
public class RedisActConfig {
    private static Logger logger = LoggerFactory.getLogger(RedisActConfig.class);

    @Autowired
    private RedisActProperties redisActProperties;

    @Bean(destroyMethod = "shutdown",name = "redissonActClient")
    RedissonClient redisson() throws IOException {
        logger.info("redissionAct client -> init ");
        Config config = new Config();
        SingleServerConfig singleServerConfig = config.useSingleServer().setAddress(redisActProperties.getAddress());
        if (Strings.isNotEmpty(redisActProperties.getPassword())){
            singleServerConfig.setPassword(redisActProperties.getPassword());
        }
        if (redisActProperties.getSelectDb()!=null){
            singleServerConfig.setDatabase(redisActProperties.getSelectDb());
        }
        return Redisson.create(config);
    }

}
