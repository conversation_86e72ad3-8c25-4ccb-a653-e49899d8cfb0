package com.memberintergral.musicplayerservice.config.nacos;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;

@Configuration
@Slf4j
public class NacosClient {

    @Value("${spring.cloud.nacos.discovery.server-addr}")
    private String address;

    @Value("${nacos.config.namespace}")
    private String namespace;


    @Bean
    public ConfigService configService() throws NacosException {
        Properties properties = new Properties();
        properties.setProperty("serverAddr", address);
        if (StringUtils.isNotBlank(namespace)){
            properties.setProperty("namespace", namespace);
        }
        ConfigService configService = NacosFactory.createConfigService(properties);
        log.info("[nacos config client] start ");
        return configService;
    }

}
