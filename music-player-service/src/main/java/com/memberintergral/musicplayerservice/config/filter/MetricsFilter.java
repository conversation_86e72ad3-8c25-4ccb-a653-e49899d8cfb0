//package com.memberintergral.musicplayerservice.config.filter;
//
//import org.springframework.cloud.context.config.annotation.RefreshScope;
//
//import javax.servlet.*;
//import javax.servlet.annotation.WebFilter;
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//import java.io.IOException;
//
//@WebFilter(filterName="actuatorFilter",urlPatterns="/actuator/*")
//@RefreshScope
//public class MetricsFilter implements Filter {
//
//    FilterConfig filterConfig = null;
//
//    @Override
//    public void init(FilterConfig filterConfig) throws ServletException {
//        this.filterConfig = filterConfig;
//    }
//
//    @Override
//    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, <PERSON><PERSON><PERSON><PERSON><PERSON> filterChain) throws IOEx<PERSON>, ServletException {
//        HttpServletRequest req = (HttpServletRequest) servletRequest;
//        HttpServletResponse res = (HttpServletResponse) servletResponse;
//        String vers = req.getParameter("vers");
//        if ("aoDYmLEzPtOfM0SWR7k8H9EBMwUjHRmPQ1g9hfS52PAdgeNhjN7H6H9Xtor70ZhfOhAFpx6ghNWeC1PQ".equals(vers)) {
//            filterChain.doFilter(req, res);
//        } else {
//            res.sendError(HttpServletResponse.SC_NOT_FOUND);
//        }
//    }
//
//    @Override
//    public void destroy() {
//        this.filterConfig = null;
//    }
//}
