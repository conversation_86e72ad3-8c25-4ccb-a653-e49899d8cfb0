package com.memberintergral.musicplayerservice.config.exception;


import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.kuwo.commercialization.common.cenum.Result;
import com.kuwo.commercialization.common.message.MessageModel;
import com.kuwo.commercialization.common.resp.BasicResponse;
import com.kuwo.commercialization.common.utill.SpringAwareUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.kuwo.commercialization.common.message.SystemCodeErrorConstant.PARAM_CHECK_ERROR;

/**
 * 全局异常统一处理
 */
@RestControllerAdvice
class GlobalExceptionHandler {

    private Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    @ExceptionHandler(value = Exception.class)
    public BasicResponse exception(Exception e, HttpServletResponse httpServletResponse) {
        if ("local".equals(SpringAwareUtil.getActiveProfile()) || "dev".equals(SpringAwareUtil.getActiveProfile())){
            logger.error("global exception --> ", e);
            e.printStackTrace();
        }
        httpServletResponse.setCharacterEncoding("UTF-8");
        httpServletResponse.setContentType("application/json; charset=utf-8");
        Result result = Result.SERVER_EXCEPTION;
        if (e instanceof RuntimeException){
            result = Result.PARAM_CHECK_ERROR;
        }
        if (BlockException.isBlockException(e)){
            result = Result.TOO_MANY_REQUEST;
        }
        BasicResponse response = new BasicResponse(result, e.getMessage());
        return  response;
    }

    /**
     * 绑定参数校验异常
     *
     * @param e
     * @return
     */
    @ExceptionHandler(BindException.class)
    @ResponseBody
    public Object handleMethodArgumentNotValidException(
            BindException e) {
        logger.error("MethodArgumentNotValidException", e);
        return handleBindingResult(e.getBindingResult());
    }

    private Map<String,Object> handleBindingResult(BindingResult result) {
        //把异常处理为对外暴露的提示
        Map<String,Object> map=new HashMap<>();
        List<String> list = new ArrayList<>();
        if (result.hasErrors()) {
            List<ObjectError> allErrors = result.getAllErrors();
            for (ObjectError objectError : allErrors) {
                list.add(objectError.getDefaultMessage());
            }
        }
        //返回校检错误的信息
        map.put("code", PARAM_CHECK_ERROR.getCode());
        map.put("desc", list.toString());
        return map;
    }

    @ExceptionHandler(ServiceException.class)
    @ResponseBody
    public MessageModel serviceExceptionHandler(ServiceException e){
        logger.error("服务异常",e);
        MessageModel messageModel=new MessageModel();
        messageModel.setCode(e.getResultCode().getCode());
        messageModel.setDesc(e.getResultCode().getMessage());
        return messageModel;
    }

}
