//package com.memberintergral.musicplayerservice.config.uniqId;
//
//import com.baidu.fsg.uid.impl.CachedUidGenerator;
//import com.baidu.fsg.uid.worker.WorkerIdAssigner;
//import org.springframework.beans.factory.InitializingBean;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.config.ConfigurableBeanFactory;
//import org.springframework.context.annotation.Scope;
//import org.springframework.stereotype.Component;
//
//@Component
//@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
//class MyUidGenerator extends CachedUidGenerator  implements InitializingBean{
//
//    @Autowired
//    private WorkerIdAssigner workerIdAssigner;
//
//    public MyUidGenerator() {
//        setTimeBits(28);
//        setWorkerBits(22);
//        setEpochStr("2022-08-25");
//        setBoostPower(3);
//        setScheduleInterval(60);
//    }
//
//    @Override
//    public void afterPropertiesSet() throws Exception {
//        setWorkerIdAssigner(workerIdAssigner);
//        super.afterPropertiesSet();
//    }
//
//    @Override
//    public void destroy() throws Exception {
//        super.destroy();
//    }
//}
