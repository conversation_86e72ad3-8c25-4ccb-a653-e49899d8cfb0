package com.memberintergral.musicplayerservice.config.sentinel;
import cn.hutool.json.JSONUtil;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.kuwo.commercialization.common.message.MessageModel;
import com.kuwo.commercialization.common.resp.BasicResponse;
import com.memberintergral.musicplayerservice.cache.dressCenter.PlayerTrialUsageNacos;
import com.memberintergral.musicplayerservice.cache.dressCenter.TabListDataNacos;
import com.memberintergral.musicplayerservice.entity.dto.DressCenterDTO;
import com.memberintergral.musicplayerservice.req.RecommendRequestV2;
import com.memberintergral.musicplayerservice.util.SendMsgUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;
@Component
@Slf4j
public class DressCenterLimitHandler {
    @Autowired
    private SendMsgUtils sendMsgUtils;

    @Value("${test.env}")
    private String testProfile;

    @Autowired
    private PlayerTrialUsageNacos playerTrialUsageNacos;

    @Autowired
    private TabListDataNacos tabListDataNacos;

    private static SendMsgUtils sendMsgUtilsStatic;

    private static String testProfileStatic;

    private static PlayerTrialUsageNacos playerTrialUsageNacosStatic;

    private static TabListDataNacos tabListDataNacosStatic;

    @Bean
    public void init(){
        sendMsgUtilsStatic=sendMsgUtils;
        testProfileStatic=testProfile;
        playerTrialUsageNacosStatic=playerTrialUsageNacos;
        tabListDataNacosStatic=tabListDataNacos;
    }

    public static BasicResponse playerTrialUsageHandler(String uid, String sid, String plat, BlockException ex) throws Exception {
        log.error("playerTrialUsageHandler uid={},sid={},plat={}", uid,sid,plat);
        sendMsgUtilsStatic.sendMsg("playerTrialUsage","music-player项目中player/trial/usage接口 ,已限流，环境【"+testProfileStatic+"】",60);
        return BasicResponse.successResponse(playerTrialUsageNacosStatic.getTrialInfoDefaultData());
    }

    public static MessageModel getTabListHandler(DressCenterDTO dressCenterDTO, BlockException ex){
        log.error("getTabListHandler param={}", JSONUtil.toJsonStr(dressCenterDTO));
        sendMsgUtilsStatic.sendMsg("dressCenterGetTabList","music-player项目中dressCenter/getTabList接口 ,已限流，环境【"+testProfileStatic+"】",60);
        return new MessageModel(tabListDataNacosStatic.getTabListDefaultData());
    }

    public static BasicResponse recommendV2PlayersHandler(RecommendRequestV2 recommendRequestV2, BlockException ex){
        log.error("recommendV2PlayersHandler param={}", JSONUtil.toJsonStr(recommendRequestV2));
        sendMsgUtilsStatic.sendMsg("recommendV2Players","music-player项目中recommend/v2/players接口 ,已限流，环境【"+testProfileStatic+"】",60);
        return BasicResponse.successResponse(null);
    }
}