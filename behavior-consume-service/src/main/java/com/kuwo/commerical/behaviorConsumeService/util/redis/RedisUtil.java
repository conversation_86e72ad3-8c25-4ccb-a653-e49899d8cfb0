package com.kuwo.commerical.behaviorConsumeService.util.redis;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kuwo.commercialization.common.utill.RedisTransferConfUtil;
import com.kuwo.commerical.behaviorConsumeService.config.redis.DynamicChoiceRedissonClient;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * @Desc:redis操作工具类
 */
@Component
public class RedisUtil {

    private Logger logger = LoggerFactory.getLogger(RedisUtil.class);

    @Autowired
    private DynamicChoiceRedissonClient dynamicChoiceRedissonClient;

    /*public void setMapObject(String token, Object object,String mapName,Long seconds) {
        RedissonClient redissonClient=dynamicChoiceRedissonClient.getClient("d2");
        RMapCache<String, Object> map = redissonClient.getMapCache(mapName);
        map.put(token, object,seconds, TimeUnit.SECONDS);
    }

    public Object getMapObject(String key, String mapName) {
        RedissonClient redissonClient=dynamicChoiceRedissonClient.getClient("d2");
        RMapCache<String, Object> map = redissonClient.getMapCache(mapName);
        return map.get(key);

    }

    public RMapCache<String, Object> getMap(String mapName) {
        RedissonClient redissonClient=dynamicChoiceRedissonClient.getClient("d2");
        RMapCache<String, Object> map = redissonClient.getMapCache(mapName);
        return map;

    }*/

    //操作 String
    public void setString(String key,String value,Long seconds) {
        RedissonClient redissonClient=dynamicChoiceRedissonClient.getClient("d2");
        RBucket<Object> bucket = redissonClient.getBucket(key);
        bucket.set(value,seconds,TimeUnit.SECONDS);
    }

    /*public String getString(String key) {
        RedissonClient redissonClient=dynamicChoiceRedissonClient.getClient("d2");
        RBucket<String> bucket = redissonClient.getBucket(key);
        return bucket.get();
    }

    public void delBucket(String key) {
        RedissonClient redissonClient=dynamicChoiceRedissonClient.getClient("d2");
        RBucket<Object> bucket = redissonClient.getBucket(key);
        bucket.delete();
    }

    //操作List
    public <T>void setList(String key, List<T> brandCodes,Long seconds) {
        RedissonClient redissonClient=dynamicChoiceRedissonClient.getClient("d2");
        RList<T> list = redissonClient.getList(key);
        brandCodes.forEach( s -> {
            list.add(s);
        });
        list.expire(seconds,TimeUnit.SECONDS);
    }

    public <T>List<T> getList(String key) {
        RedissonClient redissonClient=dynamicChoiceRedissonClient.getClient("d2");
        RList<T> list = redissonClient.getList(key);
        return list.readAll();
    }

    *//**
     * 将对象存入缓存
     *//*
    public void setObject(String key, Object obj, long overTime) {
        try {
            final String value = new ObjectMapper().writeValueAsString(obj);
            setString(key,value,overTime);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public Object getObject(final String key, @SuppressWarnings("rawtypes") Class clazz) {
        final Class classCopy = clazz;
        String value = getString(key);
        try {
            if (!StringUtils.isEmpty(value)) {
                return new ObjectMapper().readValue(value, classCopy);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public Boolean getBit(String key, Long offset) {
        RedissonClient redissonClient=dynamicChoiceRedissonClient.getClient("d2");
        RBitSet bitSet = redissonClient.getBitSet(key);
        return bitSet.get(offset);
    }

    public Boolean setBit(String key, Long offset, Boolean flag) {
        RedissonClient redissonClient=dynamicChoiceRedissonClient.getClient("d2");
        RBitSet bitSet = redissonClient.getBitSet(key);
        return bitSet.set(offset, flag);
    }

    public void increment(String key) {
        RedissonClient redissonClient=dynamicChoiceRedissonClient.getClient("d2");
        RLongAdder longAdder = redissonClient.getLongAdder(key);
        longAdder.increment();

    }

    public <T>void addSet(String key, Set<T> brandCodes) {
        RedissonClient redissonClient=dynamicChoiceRedissonClient.getClient("d2");
        RSet<T> set = redissonClient.getSet(key);
        brandCodes.forEach( s -> {
            set.add(s);
        });
    }*/

    public <T>void addSet(String key, T s) {
        RedissonClient redissonClient=dynamicChoiceRedissonClient.getClient("d2");
        RSet<T> set = redissonClient.getSet(key);
        set.add(s);
    }

    /*public <T>void removeSet(String key, Set<T> brandCodes) {
        RedissonClient redissonClient=dynamicChoiceRedissonClient.getClient("d2");
        RSet<T> set = redissonClient.getSet(key);
        brandCodes.forEach( s -> {
            set.remove(s);
        });
    }*/

    public <T>void removeSet(String key, T s) {
        RedissonClient redissonClient = dynamicChoiceRedissonClient.getClient("d2");
        RSet<T> set = redissonClient.getSet(key);
        set.remove(s);
    }
    public int getSetSize(String key) {
        RedissonClient redissonClient = dynamicChoiceRedissonClient.getClient("d2");
        RSet<Object> set = redissonClient.getSet(key);
        return set.size();
    }

}
