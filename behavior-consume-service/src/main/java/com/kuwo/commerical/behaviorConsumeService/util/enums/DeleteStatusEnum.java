package com.kuwo.commerical.behaviorConsumeService.util.enums;
public enum DeleteStatusEnum {
	deleteno(0,"未删除"),
	deleteyes(1,"已删除");
	private int type;
    private String desc;
    
	public int getType() {
		return type;
	}
	public void setType(int type) {
		this.type = type;
	}
	public String getDesc() {
		return desc;
	}
	public void setDesc(String desc) {
		this.desc = desc;
	}
    private DeleteStatusEnum(int type, String desc){
        this.type = type;
        this.desc = desc;
    }
}
