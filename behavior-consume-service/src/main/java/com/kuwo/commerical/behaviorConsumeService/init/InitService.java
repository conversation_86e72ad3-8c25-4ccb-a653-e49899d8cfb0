package com.kuwo.commerical.behaviorConsumeService.init;

import com.kuwo.commercialization.common.utill.SpringAwareUtil;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.SmartLifecycle;
import org.springframework.stereotype.Component;


@Component
public class InitService implements SmartLifecycle, ApplicationContextAware {

    private volatile boolean running = false;


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        SpringAwareUtil.setApplicationContext(applicationContext);
    }

    @Override
    public void start() {
        running = true;
    }

    @Override
    public void stop() {

    }

    @Override
    public boolean isRunning() {
        return running;
    }
}
