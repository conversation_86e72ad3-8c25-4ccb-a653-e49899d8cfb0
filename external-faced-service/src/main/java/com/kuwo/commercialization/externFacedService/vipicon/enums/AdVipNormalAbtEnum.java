package com.kuwo.commercialization.externFacedService.vipicon.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum AdVipNormalAbtEnum {

    ONLY_NORMAL_VIP("A", "仅普通会员档位"),
    AD_VIP_AND_NORMAL_VIP("B", "广告会员+普通会员"),
    ;

    private String value;
    private String description;

    AdVipNormalAbtEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public static boolean isAdVip(String value) {
        if (Objects.equals(value, AdVipNormalAbtEnum.AD_VIP_AND_NORMAL_VIP.getValue())) {
            return true;
        }
        return false;
    }
}
