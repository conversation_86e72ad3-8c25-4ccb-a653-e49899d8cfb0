package com.kuwo.commercialization.externFacedService.vipicon.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum AdVipFreeModeAbtEnum {

    ONLY_FREE_MODE("A", "仅免模入口"),
    FREE_MODE_AND_AD("B", "免模+广告会员"),
    ONLY_AD("C", "仅广告会员"),
    ;

    private String value;
    private String description;

    AdVipFreeModeAbtEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public static boolean isAdVip(String value) {
        if (Objects.equals(value, AdVipFreeModeAbtEnum.FREE_MODE_AND_AD.getValue())
                || Objects.equals(value, AdVipFreeModeAbtEnum.ONLY_AD.getValue())) {
            return true;
        }
        return false;
    }
}
