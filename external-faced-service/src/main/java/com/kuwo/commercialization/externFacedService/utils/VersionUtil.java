package com.kuwo.commercialization.externFacedService.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Desc: 版本号提取公共类
 * @date 2025-03-17 10:53:55
 */
public class VersionUtil {


    public static final Pattern versionPattern = Pattern.compile("^([0-9]+(\\.[0-9]+)*)$");


    /**
     * 提取app 版本号
     * @param source kwplayer_ar_11.1.8.1_hw.apk
     * @return
     */
    public static Long version(String source){
        if(StringUtils.isNotEmpty(source)){
            String[] channelArr = source.split("_");
            String vers = "";
            for (String l: channelArr){
                // 判断是否
                if (versionPattern.matcher(l).matches()){
                    vers = l;
                    break;
                }
            }
            if (StringUtils.isNotBlank(vers)){
                return MyNumberUtils.toLong(vers.replace(".",""));
            }
        }
        return 0L;
    }

}
