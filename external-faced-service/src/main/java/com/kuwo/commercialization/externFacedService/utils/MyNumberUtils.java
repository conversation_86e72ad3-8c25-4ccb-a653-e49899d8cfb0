/*
 * create By lhh
 */
package com.kuwo.commercialization.externFacedService.utils;

import org.apache.commons.lang3.math.NumberUtils;

import java.math.BigDecimal;

/**
 * 扩展apache的numnerUtils
 * 
 * @version 1.0
 */
public class MyNumberUtils extends NumberUtils {

	/**
	 * 除了正确数字外，其他一切皆返回0
	 *
	 * @param object
	 * @return 除了正确数字外，其他一切皆返回0
	 * <AUTHOR>
	 */
	public static int toINT(Object object){
		String str = String.valueOf(object);
		return toInt(str);
	}

	/**
	 * 保留N位小数
	 * @param scale
	 * @param value
	 * @return
	 */
	public static double formatDecimal(int scale, double value) {
		try {
			BigDecimal bigDecimal = new BigDecimal(value);
			return bigDecimal.setScale(scale, BigDecimal.ROUND_HALF_UP).doubleValue();
		} catch (Exception e) {
			return 0.00;
		}
	}

	public static double formatDecimalPro(int scale, String value) {
		try {
			BigDecimal bigDecimal = new BigDecimal(value);
			return bigDecimal.setScale(scale, BigDecimal.ROUND_HALF_UP).doubleValue();
		} catch (Exception e) {
			return 0.00;
		}
	}


}
