package com.kuwo.commercialization.externFacedService.http;

import com.google.gson.Gson;
import com.kuwo.commercialization.externFacedService.utils.JsonUtils;
import com.kuwo.commercialization.externFacedService.utils.MyNumberUtils;
import io.micrometer.core.annotation.Timed;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Desc: abt星画服务 圈人的
 * @date 2024-07-01 18:47:00
 */
@Component
@Slf4j
public class AbTService {

    @Autowired
    private OkHttpClient httpClient;

    private Gson gson = new Gson();

    /**
     * abt 用户分流调用
     * @param userId
     * @param deviceId
     * @param abtKey
     * @param channelId
     * @return
     * @throws IOException
     */
    public Map getAbTMatch(String userId, String deviceId, String abtKey,int channelId) throws IOException {
        String url = "http://newabapi.kuwo.cn:8081/abtest/kuwo/algorithm/info";
        Map<String, Object> param = new HashMap<>();
        param.put("businessId", 14);
        param.put("uid", userId);
        param.put("device", deviceId);
        param.put("platform", "ar");
        param.put("version", "server_1.0");
        param.put("channelId", channelId);
        List<String> paramList = new ArrayList<>();
        paramList.add(abtKey);
        param.put("moduleKeys", paramList);
        String content = JsonUtils.objectToJson(param);
        RequestBody requestBody = RequestBody.create(MediaType.get("application/json"), content);
        Request request = new Request.Builder()
                .url(url)
                .post(requestBody)
                .build();
        Response response = httpClient.newCall(request).execute();
        String result = "";
        if (response.isSuccessful()){
            result = response.body().string();
            log.info("uid:{}, ab check result:{}",userId, result);
        }
        if (StringUtils.isNotBlank(result)){
            Map map = gson.fromJson(result, Map.class);
            int code = MyNumberUtils.toINT(map.get("code")) ;
            if (0 == code){
                Map linkedHashMap = (Map) map.get("data");
                Map mapTestInfo = (Map) linkedHashMap.get("mapTestInfo");
                Map adu = (Map) mapTestInfo.get(abtKey);
                Map mapParamsJSONObject = (Map) adu.get("mapParams");
                return mapParamsJSONObject;
            }
        }
        response.close();
        return new HashMap();
    }


    /**
     * 星画标签是否命中
     * @param userId
     * @param tagIds
     * @return
     */
    @Timed(value = "self_http_request", percentiles = {0.5, 0.9, 0.95, 0.99})
    public Map<String, Boolean> matchTagStar(String userId,  List<String> tagIds) throws IOException {
        String url = "http://newabapi.kuwo.cn:8081/abtest/kuwo/tagstar/rulecheck";
        Map<String, Object> param = new HashMap<>();
        param.put("sUID", userId);
        param.put("vctRuleIds", tagIds);
        String content = JsonUtils.objectToJson(param);
        RequestBody requestBody = RequestBody.create(MediaType.get("application/json"), content);
        Request request = new Request.Builder()
                .url(url)
                .post(requestBody)
                .build();
        Response response = httpClient.newCall(request).execute();
        String result = "";
        if (response.isSuccessful()){
            result = response.body().string();
            log.info("uid:{}, tagStar check result:{}",userId, result);
        }
        if (StringUtils.isNotBlank(result)){
            Map map = gson.fromJson(result, Map.class);
            int code = MyNumberUtils.toINT(map.get("code")) ;
            if (0 == code){
                Map linkedHashMap = (Map) map.get("data");
                Map mapResult = (Map) linkedHashMap.get("mapResult");
                return mapResult;
            }
        }
        response.close();
        return new HashMap();
    }

    @Timed(value = "self_http_request", percentiles = {0.5, 0.9, 0.95, 0.99})
    public boolean matchTagStar(String userId, String tagId) {
        List<String> tags = new ArrayList<>();
        tags.add(tagId);
        Map<String, Boolean> result = null;
        try {
            result = matchTagStar(userId, tags);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (result != null && result.containsKey(tagId)){
            return result.get(tagId);
        }
        return false;
    }
    

}
