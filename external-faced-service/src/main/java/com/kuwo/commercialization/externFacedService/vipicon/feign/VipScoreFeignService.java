package com.kuwo.commercialization.externFacedService.vipicon.feign;

import com.kuwo.commercialization.common.resp.BasicResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "intergralService", configuration = FeignConfig.class)
public interface VipScoreFeignService {

    @GetMapping(value = "/basic/noauth/info")
    BasicResponse getUserVipLevel(@RequestParam("userId") String userId, @RequestParam("vers") String vers);
}
