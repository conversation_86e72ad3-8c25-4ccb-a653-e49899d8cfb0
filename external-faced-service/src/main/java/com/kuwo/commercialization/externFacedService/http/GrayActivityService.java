package com.kuwo.commercialization.externFacedService.http;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.kuwo.commercialization.externFacedService.bootConfig.util.IntegerTypeAdapter;
import com.kuwo.commercialization.externFacedService.utils.JsonUtils;
import com.kuwo.commercialization.externFacedService.utils.MyNumberUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Desc:
 * @date 2025-03-25 11:36:34
 */
@Component
@Slf4j
public class GrayActivityService {


    @Autowired
    private OkHttpClient httpClient;

    private Gson intGson = new Gson();


    @Value("${vip.domain}")
    private String vipDomain;

    @Value("${vip.wapidomain}")
    private String wapidomain;

    /**
     * 获取灰度活动用户
     * @param code
     * @param page
     * @param size
     * @return
     * @throws IOException
     */
    public List getGrayList(String code,int page, int size) throws IOException {
        String url = vipDomain+"/commercia/vip/activity/list";
        Map<String, Object> params = new HashMap<>();
        params.put("code", code);
        params.put("pageIndex", page);
        params.put("pageSize", size);
        String content = JsonUtils.objectToJson(params);
        RequestBody requestBody = RequestBody.create(MediaType.get("application/json"), content);
        Request request = new Request.Builder()
                .url(url)
                .post(requestBody)
                .build();
        Response response = httpClient.newCall(request).execute();
        String result = "";
        if (response.isSuccessful()){
            result = response.body().string();
            log.info("code:{}, query result:{}",code, result);
        }
        if (StringUtils.isNotBlank(result)){
            Map map = intGson.fromJson(result, Map.class);
            int httpCode = MyNumberUtils.toINT(map.get("code")) ;
            if (0 == httpCode){
                return  (List) map.get("data");
            }
        }
        response.close();
        return null;
    }


    /**
     * 是否全局用户判断
     * @param uid
     * @return
     */
    public boolean invokerAllPay(String uid){
        try{
            if(org.apache.commons.lang.StringUtils.isBlank(uid)|| NumberUtils.toLong(uid)<=0){
                return false;
            }
            String url=wapidomain+"/openapi/v1/operate/homePage/user?loginUid="+uid;
            String res= HttpUtil.get(url,300);
            log.info("invokerAllPay url={} res={}",url,res);
            if(org.apache.commons.lang.StringUtils.isBlank(res)){
                return false;
            }
            JSONObject resJS = JSONObject.parseObject(res);
            JSONObject dataJs= resJS.getJSONObject("data");
            if(dataJs.containsKey("globalfeemode")){
                String globalfeemode=dataJs.getString("globalfeemode") ;
                return org.apache.commons.lang.StringUtils.equals("1",globalfeemode);
            }
        }catch (Exception e){
            log.error("invokerAllPay has error!",e);
        }
        return false;
    }


}
