//package com.kuwo.commercialization.externFacedService.config;
//
//import com.alibaba.fastjson.JSON;
//import com.kuwo.commercialization.common.resp.BasicResponse;
//import org.springframework.core.MethodParameter;
//import org.springframework.http.MediaType;
//import org.springframework.http.converter.HttpMessageConverter;
//import org.springframework.http.server.ServerHttpRequest;
//import org.springframework.http.server.ServerHttpResponse;
//import org.springframework.web.bind.annotation.ControllerAdvice;
//import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;
//
//import javax.servlet.http.HttpServletRequest;
//
//
//@ControllerAdvice
//public class ExternalResponseBodyAdvice implements ResponseBodyAdvice<Object> {
//
//    private final HttpServletRequest request;
//
//    public ExternalResponseBodyAdvice(HttpServletRequest request) {
//        this.request = request;
//    }
//
//    private static final String PROMETHEUS_ACTUATOR = "actuator/prometheus";
//
//    @Override
//    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
//        String requestURI = request.getRequestURI();
//        return !requestURI.contains(PROMETHEUS_ACTUATOR);
//    }
//
//    @Override
//    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType, Class<? extends HttpMessageConverter<?>> selectedConverterType, ServerHttpRequest request, ServerHttpResponse response) {
//        if (body instanceof String || (body == null && returnType.getParameterType() == String.class)) {
//            // 如果是 String 类型，手动转换成 JSON 格式的字符串
//            return JSON.toJSONString(BasicResponse.successResponse(body));
//        }
//
//        if (body instanceof BasicResponse) {
//            return body;
//        }
//        return BasicResponse.successResponse(body);
//    }
//}
