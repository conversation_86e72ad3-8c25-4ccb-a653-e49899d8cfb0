package com.kuwo.commercialization.externFacedService.vipicon.enums;

public enum SecondLevelPopulationEnum {

    AMPHIBIOUS(1, "双栖"),
    NOT_AMPHIBIOUS(2, "非双栖"),
    VIPM(3, "音乐包"),
    VIP(4, "豪v"),
    VEHICLE_VIP(5, "车载"),
    SVIP(6, "超会"),
    one_kind_vip(7, "一种会员"),
    multiple_vip(8, "多种会员"),
    ;

    private Integer value;

    private String desc;

    SecondLevelPopulationEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }
}
