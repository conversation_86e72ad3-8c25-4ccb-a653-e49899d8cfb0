package com.kuwo.commercialization.externFacedService.vipicon.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum VipTypeEnum {

    LUX_VIP(1, "豪v", "LUX_VIP"),
    VEHICLE_VIP(2, "车载", "VEHICLE_VIP"),
    SV<PERSON>(3, "超会", "SUPER_VIP"),
    AD_VIP(4,"广告会员","AD_VIP"),
    ;

    private Integer value;

    private String desc;

    private String name;

    VipTypeEnum(Integer value, String desc, String name) {
        this.value = value;
        this.desc = desc;
        this.name = name;
    }

    public static String getVipName(Integer vipType) {
        for (VipTypeEnum vipTypeEnum : VipTypeEnum.values()) {
            if (Objects.equals(vipTypeEnum.getValue(), vipType)) {
                return vipTypeEnum.getName();
            }
        }
        return "";
    }
}





