package com.kuwo.commercialization.externFacedService.vote;

import cn.hutool.json.JSONUtil;
import com.kuwo.commercialization.common.resp.BasicResponse;
import com.kuwo.commercialization.externFacedService.vote.service.UserSVIPInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/member")
@Slf4j
public class UserSVIPInfoController {

    @Autowired
    private UserSVIPInfoService userSVIPInfoService;

    @GetMapping("/svip")
    public BasicResponse queryUserSVIP(@RequestParam String userId, @RequestParam String sign) {
        BasicResponse userSVIPInfo = userSVIPInfoService.getUserSVIPInfo(userId, sign);
        log.info("queryUserSVIP,userId={},sign={},response={}", userId, sign, JSONUtil.toJsonStr(userSVIPInfo.getData()));
        return userSVIPInfo;
    }
}
