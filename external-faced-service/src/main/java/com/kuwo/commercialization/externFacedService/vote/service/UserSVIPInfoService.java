package com.kuwo.commercialization.externFacedService.vote.service;

import cn.hutool.core.map.MapUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.commercal.uiservice.enity.BasicUInfo;
import com.commercal.uiservice.enity.SignStateInfo;
import com.commercal.uiservice.enity.VipInfo;
import com.google.common.collect.Lists;
import com.kuwo.commercialization.common.cenum.Result;
import com.kuwo.commercialization.common.resp.BasicResponse;
import com.kuwo.commercialization.externFacedService.vipicon.remote.IndicatorService;
import com.kuwo.commercialization.externFacedService.vote.domain.vo.UserSVIPInfoVO;
import com.kuwo.commercialization.externFacedService.vote.enums.YearVIPEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@Slf4j
public class UserSVIPInfoService {

    @Autowired
    private IndicatorService indicatorService;

    private static final String VIP_INFO_METRICS = "uiInfo";

    private static final String SVIP_LARGE_31_EXPIRE_TIME = "svipLarge31ExpireTime";

    private static final String SECRET_KEY = "FuQ&vlKmtSR2";

    public BasicResponse getUserSVIPInfo(String userId, String sign) {
        boolean validateSignResult = validateSign(userId, sign);
        if (!validateSignResult) {
            return new BasicResponse(Result.ILLEGAL_REQUEST, null);
        }
        UserSVIPInfoVO userSVIPInfoVO = new UserSVIPInfoVO();
        userSVIPInfoVO.setIsSVIP(0);
        userSVIPInfoVO.setIsSpecificSVIP(0);
        userSVIPInfoVO.setIsYearSVIP(0);

        List<String> uiInfoMetrics = Lists.newArrayList(VIP_INFO_METRICS, SVIP_LARGE_31_EXPIRE_TIME);
        try {
            Map<String, Object> userMetricsValue = indicatorService.getUserMetricsValue(userId, uiInfoMetrics);
            if (MapUtil.isEmpty(userMetricsValue)) {
                return BasicResponse.successResponse(userSVIPInfoVO);
            }
            VipInfo userVipInfo = getUserVipInfo(userMetricsValue);
            if (Objects.isNull(userVipInfo)) {
                return BasicResponse.successResponse(userSVIPInfoVO);
            }
            BasicUInfo basicUInfo = userVipInfo.getBasicUInfo();
            SignStateInfo signStateInfo = userVipInfo.getSignStateInfo();
            if (Objects.isNull(basicUInfo) && Objects.isNull(signStateInfo)) {
                return BasicResponse.successResponse(userSVIPInfoVO);
            }

            Object svipLarge31ExpireTimeObject = userMetricsValue.get(SVIP_LARGE_31_EXPIRE_TIME);

            Long svipExpireTime = basicUInfo.getSvipExpireTime();
            Integer isSVIPYear = Objects.equals(signStateInfo.getIsYearUser(), YearVIPEnum.SVIP_YEAR.getValue()) ? 1 : 0;

            long svipLarge31ExpireTime = 0L;
            if (Objects.nonNull(svipLarge31ExpireTimeObject)) {
                svipLarge31ExpireTime = Long.valueOf(String.valueOf(svipLarge31ExpireTimeObject));
            }

            long now = new Date().getTime();

            // 超会
            if (svipExpireTime > now) {
                userSVIPInfoVO.setIsSVIP(1);
            }

            // 特定超会
            if (svipLarge31ExpireTime > now) {
                userSVIPInfoVO.setIsSpecificSVIP(1);
            }
            userSVIPInfoVO.setIsYearSVIP(isSVIPYear);
        } catch (Exception e) {
            log.error("getUserSVIPInfo error,", e);
        }
        return BasicResponse.successResponse(userSVIPInfoVO);
    }

    public VipInfo getUserVipInfo(Map<String, Object> userMetricsValue) {
        Object userInfo = userMetricsValue.get(VIP_INFO_METRICS);
        if (Objects.isNull(userInfo)) {
            return null;
        }
        VipInfo vipInfo = JSON.parseObject(String.valueOf(userInfo), VipInfo.class);
        return vipInfo;
    }

    private boolean validateSign(String userId, String sign) {
        String token = SecureUtil.md5(userId + SECRET_KEY);
        if (Objects.equals(token, sign)) {
            return true;
        }
        log.error("validateSign fail,illegal request");
        return false;
    }
}
