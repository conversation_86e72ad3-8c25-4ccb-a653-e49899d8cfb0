package com.kuwo.commercialization.externFacedService.vipicon.feign;

import com.kuwo.commercialization.common.message.MessageModel;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "vipAdvService", configuration = FeignConfig.class)
public interface DataModelFeignService {

    @GetMapping("/vip_adv/datamode/datasetbycode")
    MessageModel getVipStrategy(@RequestParam("codes") String codes, @RequestParam("pageSize") Integer pageSize);
}
