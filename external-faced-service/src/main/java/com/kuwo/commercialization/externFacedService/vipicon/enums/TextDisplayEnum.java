package com.kuwo.commercialization.externFacedService.vipicon.enums;

import lombok.Getter;

@Getter
public enum TextDisplayEnum {

    NEW_CUSTOMER(1,"新客%s元"),
    UPGRADE_LUX(2,"升级豪V"),
    UPGRADE(3,"升级￥%s"),
    RENEW(4,"续费￥%s"),
    OPEN(5,"开通￥%s"),
    DUE(6,"即将到期"),

    ;

    private Integer value;

    private String desc;

    TextDisplayEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }
}
