package com.kuwo.commercialization.externFacedService.vipicon.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum UserIdentityEnum {

    FIRST_OPEN(1, "首开"),
    ONLY_VIPM(2, "仅在期音乐包"),
    ONLY_LUX_VIP(3, "仅在期豪V"),
    ONLY_VEHICLE_VIP(4, "仅在期车载"),
    ONLY_SUPER_VIP(5, "仅在期超会"),
    LUX_VIP_AND_VEHICLE_VIP(6, "在期豪V+在期车载"),
    ONLY_EXPIRING_VIPM(7, "仅临期音乐包"),
    ONLY_EXPIRING_LUX_VIP(8, "仅临期豪V"),
    ONLY_EXPIRING_VEHICLE_VIP(9, "仅临期车载"),
    ONLY_EXPIRING_SUPER_VIP(10, "仅临期超会"),
    EXPIRING_LUX_VIP_AND_VEHICLE_VIP(11, "临期豪V+临期车载"),
    ONLY_VIPM_RETURN(12, "仅音乐包回流"),
    ONLY_LUX_VIP_RETURN(13, "仅豪V回流"),
    ONLY_VEHICLE_VIP_RETURN(14, "仅车载回流"),
    ONLY_SUPER_VIP_RETURN(15, "超会回流"),
    LUX_VIP_AND_VEHICLE_VIP_RETURN(16, "豪V回流+车载回流"),

    DOUDI(17, "兜底"),

    ONLY_AD_VIP(18,"仅在期广告会员"),
    ONLY_EXPIRING_AD_VIP(19,"仅回流广告会员"),
    ;

    private final Integer code;
    private final String description;

    UserIdentityEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    // 如 type2:LUX_VIP:0:VIP_1:
    public static boolean isVipLevelKey(Integer code, Integer actionType) {
        if ((Objects.equals(ONLY_LUX_VIP.getCode(), code) && Objects.equals(ActionTypeEnum.UPGRADE.getValue(), actionType)) ||
                (Objects.equals(LUX_VIP_AND_VEHICLE_VIP.getCode(), code) && Objects.equals(ActionTypeEnum.UPGRADE.getValue(), actionType))) {
            return true;
        }
        return false;
    }

    // 如 type3:LUX_VIP:1::0.88
    public static boolean isPriceKey(Integer code, Integer actionType) {
        if (Objects.equals(FIRST_OPEN.getCode(), code) ||
                Objects.equals(ONLY_VIPM.getCode(), code) ||
                Objects.equals(ONLY_LUX_VIP.getCode(), code) ||
                (Objects.equals(ONLY_VEHICLE_VIP.getCode(), code) && Objects.equals(ActionTypeEnum.UPGRADE.getValue(), actionType)) ||
                Objects.equals(ONLY_SUPER_VIP.getCode(), code) ||
                Objects.equals(LUX_VIP_AND_VEHICLE_VIP.getCode(), code) ||
                Objects.equals(ONLY_EXPIRING_VIPM.getCode(), code) ||
                Objects.equals(ONLY_EXPIRING_LUX_VIP.getCode(), code) ||
                Objects.equals(ONLY_EXPIRING_VEHICLE_VIP.getCode(), code) ||
                Objects.equals(ONLY_EXPIRING_SUPER_VIP.getCode(), code) ||
                Objects.equals(EXPIRING_LUX_VIP_AND_VEHICLE_VIP.getCode(), code) ||
                Objects.equals(ONLY_VIPM_RETURN.getCode(), code) ||
                Objects.equals(ONLY_LUX_VIP_RETURN.getCode(), code) ||
                Objects.equals(ONLY_VEHICLE_VIP_RETURN.getCode(), code) ||
                Objects.equals(ONLY_SUPER_VIP_RETURN.getCode(), code) ||
                Objects.equals(LUX_VIP_AND_VEHICLE_VIP_RETURN.getCode(), code)) {
            return true;
        }
        return false;
    }

    /**
     * 新客判断
     *
     * @param userIdentity
     * @return
     */
    public static boolean isFirstOpenVip(Integer userIdentity) {
        if (Objects.equals(userIdentity, FIRST_OPEN.getCode())) {
            return true;
        }
        return false;
    }

    // 置灰判断
    public static boolean isGrayIcon(Integer userIdentity) {
        if (Objects.equals(userIdentity, ONLY_VIPM_RETURN.getCode()) ||
                Objects.equals(userIdentity, ONLY_LUX_VIP_RETURN.getCode()) ||
                Objects.equals(userIdentity, ONLY_VEHICLE_VIP_RETURN.getCode()) ||
                Objects.equals(userIdentity, ONLY_SUPER_VIP_RETURN.getCode()) ||
                Objects.equals(userIdentity, LUX_VIP_AND_VEHICLE_VIP_RETURN.getCode())) {
            return true;
        }
        return false;
    }

    // 高亮判断
    public static boolean isHighLight(Integer userIdentity) {
        if (Objects.equals(userIdentity, ONLY_VIPM.getCode()) ||
                Objects.equals(userIdentity, ONLY_LUX_VIP.getCode()) ||
                Objects.equals(userIdentity, ONLY_VEHICLE_VIP.getCode()) ||
                Objects.equals(userIdentity, ONLY_SUPER_VIP.getCode()) ||
                Objects.equals(userIdentity, LUX_VIP_AND_VEHICLE_VIP.getCode()) ||
                Objects.equals(userIdentity, ONLY_EXPIRING_VIPM.getCode()) ||
                Objects.equals(userIdentity, ONLY_EXPIRING_LUX_VIP.getCode()) ||
                Objects.equals(userIdentity, ONLY_EXPIRING_VEHICLE_VIP.getCode()) ||
                Objects.equals(userIdentity, ONLY_EXPIRING_SUPER_VIP.getCode()) ||
                Objects.equals(userIdentity, EXPIRING_LUX_VIP_AND_VEHICLE_VIP.getCode())) {
            return true;
        }
        return false;
    }

    /**
     * 非会员
     *
     * @param userIdentity
     * @return
     */
    public static boolean isNotVip(Integer userIdentity) {
        if (Objects.equals(userIdentity, FIRST_OPEN.getCode()) ||
                Objects.equals(userIdentity, ONLY_VIPM_RETURN.getCode()) ||
                Objects.equals(userIdentity, ONLY_LUX_VIP_RETURN.getCode()) ||
                Objects.equals(userIdentity, ONLY_VEHICLE_VIP_RETURN.getCode()) ||
                Objects.equals(userIdentity, ONLY_SUPER_VIP_RETURN.getCode()) ||
                Objects.equals(userIdentity, LUX_VIP_AND_VEHICLE_VIP_RETURN.getCode())) {
            return true;
        }
        return false;
    }
}
