package com.kuwo.commercialization.externFacedService.vipicon.cache;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.cache.Cache;
import com.kuwo.commercialization.common.cache.RedisTTlEventType;
import com.kuwo.commercialization.common.cache.TTLCache;
import com.kuwo.commercialization.common.cache.TTLEvent;
import com.kuwo.commercialization.externFacedService.config.redis.DynamicChoiceRedissonClient;
import com.kuwo.commercialization.externFacedService.vipicon.domain.dto.VipDisplayStrategyDataSetItemDTO;
import com.kuwo.commercialization.externFacedService.vipicon.remote.DataModelService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.kuwo.commercialization.externFacedService.vipicon.constants.CommonConstant.LOW_PRICE_REDIS;
import static com.kuwo.commercialization.externFacedService.vipicon.constants.CommonConstant.VIP_STRATEGY_DATA_SET_KEY;

@Slf4j
@Component
public class VipDisplayStrategyCache extends TTLCache<String, List<VipDisplayStrategyDataSetItemDTO>> {

    private DataModelService dataModelService;

    private DynamicChoiceRedissonClient dynamicChoiceRedissonClient;

    public static final String myChangeKey = "VipDisplayStrategyCache";

    public static final String VIP_STRATEGY_DATA_SET_CACHE = "vip_strategy_data_set_cache";

    public VipDisplayStrategyCache(DynamicChoiceRedissonClient dynamicChoiceRedissonClient, DataModelService dataModelService) {
        // todo 暂时3分钟
        super(dynamicChoiceRedissonClient.getClient(LOW_PRICE_REDIS), myChangeKey, 3, TimeUnit.MINUTES);
        this.dynamicChoiceRedissonClient = dynamicChoiceRedissonClient;
        this.dataModelService = dataModelService;
    }

    @Override
    protected List<VipDisplayStrategyDataSetItemDTO> loadFromDb(String key) {
        RedissonClient redissonClient = dynamicChoiceRedissonClient.getClient(LOW_PRICE_REDIS);
        RBucket<Object> bucket = redissonClient.getBucket(VIP_STRATEGY_DATA_SET_KEY);

        // 如果缓存存在，从缓存获取
        if (bucket.isExists()) {
            try {
                Object vipStrategyStr = bucket.get();
                List<VipDisplayStrategyDataSetItemDTO> result = JSON.parseObject(
                        String.valueOf(vipStrategyStr),
                        new TypeReference<List<VipDisplayStrategyDataSetItemDTO>>() {
                        }
                );

                if (CollectionUtils.isNotEmpty(result)) {
                    return result;
                }
            } catch (Exception e) {
                log.error("解析VIP策略缓存异常", e);
            }
        }

        // 缓存不存在或解析失败，调用服务获取
        List<VipDisplayStrategyDataSetItemDTO> vipDisplayStrategyDataSetItemDTOS = dataModelService.getVipStrategy();

        // 缓存结果
        if (CollectionUtils.isNotEmpty(vipDisplayStrategyDataSetItemDTOS)) {
            cacheVipStrategyDataSet(vipDisplayStrategyDataSetItemDTOS);
        }

        return vipDisplayStrategyDataSetItemDTOS;
    }

    /**
     * 缓存VIP策略数据集
     */
    private void cacheVipStrategyDataSet(List<VipDisplayStrategyDataSetItemDTO> vipStrategyDTOS) {
        RedissonClient redissonClient = dynamicChoiceRedissonClient.getClient(LOW_PRICE_REDIS);
        RBucket<Object> bucket = redissonClient.getBucket(VIP_STRATEGY_DATA_SET_KEY);
        bucket.set(JSON.toJSONString(vipStrategyDTOS), 3, TimeUnit.MINUTES);
    }

    @Override
    protected void initCache(Cache<String, List<VipDisplayStrategyDataSetItemDTO>> cache) {
        loadFromDb(null);
    }

    private void clearCache() {
        getCache().invalidateAll();
        initCache(getCache());
    }

    @Override
    protected void changeCacheValue(TTLEvent event) {
        if (RedisTTlEventType.RELOAD_CACHE == event.getEventType() && myChangeKey.equals(event.getTtlChangeKey())) {
            clearCache();
        }
    }
}
