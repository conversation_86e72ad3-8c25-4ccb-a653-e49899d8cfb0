package com.kuwo.commercialization.externFacedService.config.nacos;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class LowPriceIconConfig extends NacosRepository {

    private static String groupId = "DEFAULT_GROUP";
    private static String dataId = "icon";
    private static long readTimeout = 1000l * 10;

    public LowPriceIconConfig(ConfigService configService) throws NacosException {
        super(configService, groupId, dataId, readTimeout);
    }

    @Override
    protected Object convert(String configData) {
        Map cache = JSON.parseObject(configData, Map.class);
        return cache;
    }

    public Map<String, Map<String, Object>> getIconConfig() {
        Map<String, Map<String, Object>> map = (Map<String, Map<String, Object>>) this.data;
        if (MapUtils.isEmpty(map)) {
            return new HashMap<>();
        }
        return map;
    }
}
