package com.kuwo.commercialization.externFacedService.vipicon.enums;

import lombok.Getter;

@Getter
public enum VipIconEnum {

    OPEN_LUX_VIP(1, "开通豪华"),
    OPEN_VEHICLE_VIP(2, "开通车载"),
    OPEN_SVIP(3, "开通超会"),
    RENEW_LUX_VIP(4, "续费豪华"),
    RENEW_VEHICLE_VIP(5, "续费车载"),
    RENEW_SVIP(6, "续费超会"),
    UPGRADE_LUX_VIP(7, "升级豪华"),
    UPGRADE_SVIP(8, "升级超会"),
    ;


    private Integer value;

    private String desc;


    VipIconEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;

    }
}
