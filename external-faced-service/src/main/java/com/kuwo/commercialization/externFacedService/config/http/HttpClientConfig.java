package com.kuwo.commercialization.externFacedService.config.http;

import com.kuwo.commercialization.common.utill.SSLSocketClient;
import okhttp3.OkHttpClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Desc:
 * @date 2021-07-07 16:01:10
 */
@Configuration
public class HttpClientConfig {


    @Bean
    public OkHttpClient buildOkHttpClient(){
        OkHttpClient client = new OkHttpClient().newBuilder()
                .connectTimeout(1, TimeUnit.SECONDS)
                .readTimeout(1, TimeUnit.SECONDS)
                .writeTimeout(1, TimeUnit.SECONDS)
                .sslSocketFactory(SSLSocketClient.getSSLSocketFactory(), SSLSocketClient.getX509TrustManager())
                .hostnameVerifier(SSLSocketClient.getHostnameVerifier())
                .build();
        return client;
    }

}
