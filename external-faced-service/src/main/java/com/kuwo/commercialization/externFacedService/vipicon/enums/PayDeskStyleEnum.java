package com.kuwo.commercialization.externFacedService.vipicon.enums;

import lombok.Getter;

@Getter
public enum PayDeskStyleEnum {

    UPGRADE_LUX_OTHER_AMPHIBIOUS(1, "第一位升级档位，后面豪V其他档位（双栖样式）"),
    UPGRADE_LUX_OTHER_NORMAL(2, "第一位升级档位，后面豪V其他档位（正常样式）"),
    UPGRADE_VEHICLE_OTHER_AMPHIBIOUS(3, "第一位升级档位，后面车载其他档位（双栖样式）"),
    UPGRADE_VEHICLE_OTHER_NORMAL(4, "第一位升级档位，后面车载其他档位（正常样式）"),
    UPGRADE_LUX_VIP_LUX_OTHER_NORMAL(5, "第一位升级豪V档位，后面豪V其他档位（正常样式）"),
    LUX_NORMAL(6, "纯豪V档位（正常样式）"),
    VEHICLE_AMPHIBIOUS(7, "纯车载档位（双栖样式）"),
    SVIP_NORMAL(8, "纯超会档位（正常样式）"),
    SVIP_AMPHIBIOUS(9, "纯超会档位（双栖样式）"),
    VEHICLE_NORMAL(10, "纯车载档位（正常样式）"),
    FIRST_AD_VIP(11, "首位广告会员"),
    ;

    private Integer value;

    private String desc;

    PayDeskStyleEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }
}
