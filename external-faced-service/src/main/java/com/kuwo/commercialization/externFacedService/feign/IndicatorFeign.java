package com.kuwo.commercialization.externFacedService.feign;

import com.kuwo.commercialization.common.resp.BasicResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @Desc:
 * @date 2025-03-15 16:34:18
 */
@FeignClient(value = "indicator-service")
public interface IndicatorFeign {

    @GetMapping(value = "/freeModel/jd")
    BasicResponse isFreeModelUser(@RequestParam("userId")String userId,
                                  @RequestParam("q36")String q36,
                                  @RequestParam("notrace")String notrace,
                                  @RequestParam("deviceId")String deviceId,
                                  @RequestParam("platfrom")String platfrom,
                                  @RequestParam("source")String source,
                                  @RequestParam("page")String page);

    @GetMapping(value = "/freeModel/jdNew")
    BasicResponse isFreeModelUserNew(@RequestParam("userId") String userId,
                                     @RequestParam("q36") String q36,
                                     @RequestParam("notrace") String notrace,
                                     @RequestParam("deviceId") String deviceId,
                                     @RequestParam("platfrom") String platfrom,
                                     @RequestParam("source") String source,
                                     @RequestParam("page") String page);

}
