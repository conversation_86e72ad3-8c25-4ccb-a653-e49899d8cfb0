package com.kuwo.commercialization.externFacedService.common;

import com.kuwo.commercialization.common.cache.RedisTTlEventType;
import com.kuwo.commercialization.common.cache.TTLEvent;
import com.kuwo.commercialization.common.cenum.RedisConstant;
import com.kuwo.commercialization.common.resp.BasicResponse;
import com.kuwo.commercialization.externFacedService.config.redis.DynamicChoiceRedissonClient;
import com.kuwo.commercialization.externFacedService.utils.Const;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RTopic;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Desc: help工具类集合
 * @date 2025-03-15 15:38:37
 */
@RestController
@RequestMapping("/help")
@Slf4j
public class HelperController {

    private DynamicChoiceRedissonClient dynamicChoiceRedissonClient;

    public HelperController(DynamicChoiceRedissonClient dynamicChoiceRedissonClient) {
        this.dynamicChoiceRedissonClient = dynamicChoiceRedissonClient;
    }


    @RequestMapping("/clear/ttl/cache")
    public BasicResponse clearTTLCache(String cacheKey) {
        RedissonClient redissonClient = dynamicChoiceRedissonClient.getClient(Const.CACHE_REDIS_SHORT_NAME);
        RTopic topic = redissonClient.getTopic(RedisConstant.LOCAL_CACHE_TOPIC.getKey());
        long publish = topic.publish(new TTLEvent(RedisTTlEventType.RELOAD_CACHE, cacheKey));
        log.info("clear cache, receive client num -> {}", publish);
        return BasicResponse.successResponse(publish);
    }

}
