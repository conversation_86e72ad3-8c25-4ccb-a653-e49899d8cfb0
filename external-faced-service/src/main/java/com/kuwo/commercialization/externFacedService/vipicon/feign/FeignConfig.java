package com.kuwo.commercialization.externFacedService.vipicon.feign;

import feign.Request;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * icon接口查询的 feign配置
 */
@Configuration
public class FeignConfig {

    @Bean
    public Request.Options options() {
        //连接超时时间，请求处理超时时间
        return new Request.Options(200, 200);
    }

}

