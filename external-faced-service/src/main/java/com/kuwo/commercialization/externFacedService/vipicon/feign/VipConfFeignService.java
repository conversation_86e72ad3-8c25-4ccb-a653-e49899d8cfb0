package com.kuwo.commercialization.externFacedService.vipicon.feign;

import com.kuwo.commercialization.common.message.MessageModel;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "vipConf", configuration = FeignConfig.class)
public interface VipConfFeignService {

    @GetMapping(value = "/price/getSingleLuxSVIPPrice")
    MessageModel getSVIPLowPrice(@RequestParam("userId") String userId, @RequestParam("platform") String platform,
                                 @RequestParam("deviceId") String deviceId, @RequestParam("vers") String vers);
}
