<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false">
    <!--日志文件主目录：这里${user.home}为当前服务器用户主目录-->
    <property name="LOG_HOME" value="/home/<USER>/tomcat-9.0.33/logs"/>
<!--    <property name="LOG_HOME" value="${user.home}/logs"/>-->
    <!--日志文件名称：这里spring.application.name表示工程名称-->
    <springProperty scope="context" name="APP_NAME" source="spring.application.name"/>

    <!--默认配置-->
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <!--配置控制台(Console)-->
    <include resource="org/springframework/boot/logging/logback/console-appender.xml"/>

    <!--配置日志文件(File)-->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <append>true</append>
        <!--设置策略-->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--日志文件路径：这里%d{yyyyMMdd}表示按天分类日志-->
            <FileNamePattern>${LOG_HOME}/%d{yyyyMMdd}/${APP_NAME}.log</FileNamePattern>
            <!--日志保留天数-->
            <MaxHistory>72</MaxHistory>
        </rollingPolicy>
        <!--设置格式-->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] [%X{traceId}] %-5level %logger{50} - %msg%n</pattern>
            <!-- 或者使用默认配置 -->
            <!--<pattern>${FILE_LOG_PATTERN}</pattern>-->
            <charset>utf8</charset>
        </encoder>
    </appender>

    <!-- 配置专门用来打印耗时的异步日志 -->
    <appender name="TIME_INFO_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${LOG_HOME}/%d{yyyyMMdd}/time_cost.%d{"yyyy-MM-dd"}
            </FileNamePattern>
            <MaxHistory>72</MaxHistory>
        </rollingPolicy>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] [%X{traceId}] %-5level %logger{50} - %msg%n</Pattern>
        </layout>
    </appender>

    <appender name="async" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="TIME_INFO_FILE" />
    </appender>

    <appender name="FAIL_BACK_INFO_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${LOG_HOME}/%d{yyyyMMdd}/failBack.%d{"yyyy-MM-dd"}
            </FileNamePattern>
            <MaxHistory>72</MaxHistory>
        </rollingPolicy>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] [%X{traceId}] %-5level %logger{50} - %msg%n</Pattern>
        </layout>
    </appender>


    <appender name="failBackAsync" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="FAIL_BACK_INFO_FILE" />
    </appender>


    <logger name= "timeLogger" level="INFO" additivity="false">
        <appender-ref ref="async"/>
    </logger>

    <logger name= "failBackLogger" level="INFO" additivity="false">
        <appender-ref ref="failBackAsync"/>
    </logger>


    <!-- 多环境配置 按照active profile选择分支 -->
    <springProfile name="local">
        <!--root节点 全局日志级别，用来指定最基础的日志输出级别-->
        <root level="INFO">
            <appender-ref ref="FILE"/>
            <appender-ref ref="CONSOLE"/>
        </root>

        <!-- 子节点向上级传递 局部日志级别-->
        <logger level="WARN" name="org.springframework"/>
        <logger level="WARN" name="com.netflix"/>
        <logger level="DEBUG" name="org.hibernate.SQL"/>
    </springProfile>

    <springProfile name="dev">
        <!--root节点 全局日志级别，用来指定最基础的日志输出级别-->
        <root level="INFO">
            <appender-ref ref="FILE"/>
            <appender-ref ref="CONSOLE"/>
        </root>

        <!-- 子节点向上级传递 局部日志级别-->
        <logger level="WARN" name="org.springframework"/>
        <logger level="WARN" name="com.netflix"/>
        <logger level="DEBUG" name="org.hibernate.SQL"/>
    </springProfile>




    <springProfile name="prod">
        <!--root节点 全局日志级别，用来指定最基础的日志输出级别-->
        <root level="INFO">
            <appender-ref ref="FILE"/>
            <appender-ref ref="CONSOLE"/>
        </root>
    </springProfile>
</configuration>