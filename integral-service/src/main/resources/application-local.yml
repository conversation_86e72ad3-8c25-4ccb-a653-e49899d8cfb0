spring:
  integral:
    datasource:
      url: *****************************************************************************************************************************
      username: root
      password:
      driverClassName: com.mysql.cj.jdbc.Driver

  druid:
    initialSize: 5
    minIdle: 5
    maxActive: 20
    maxWait: 60000
    timeBetweenEvictionRunsMillis: 60000
    minEvictableIdleTimeMillis: 60000
    validationQuery: SELECT 1
    testWhileIdle: true
    testOnBorrow: false
    testOnReturn: false
    poolPreparedStatements: true
    maxPoolPreparedStatementPerConnectionSize: 20
    filters: stat,wall
    connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
    logSlowSql: true
  cache:
    ehcache:
      config:
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
        namespace1: a9c73607-c549-4ff6-8cd5-0b4f3e475ba1
        instance-enabled: ${register_enabled:false}
    sentinel:
      transport:
        dashboard: ************:8080
server:
  port: 8081
login:
  check:
    url: http://loginserver.kuwo.cn/u.s?type=validate&uid=%s&sid=%s&req_enc=utf8&res_enc=utf8
vip:
  domain: http://testvip.kuwo.cn
redis:
  config:
    d1:
      address: redis://127.0.0.1:6379
    d2:
      address: redis://127.0.0.1:6379
  names: d1,d2
test:
  env: local
sentinel:
  nacos:
    namespace: 8b40d906-fa9d-4fc9-b164-f43ad92e3835
    groupId: local
    vip-rule-dataId: integralService-flow-rules

kafka:
  address: ************:9091,************:9092,************:9093
  userSystemAddress: ************:9091,************:9092,************:9093
  ascribeAddress: *********:9092,*********:9092,*********:9092,*********:9092,***********:9092

kuwo:
  monitor:
    enabled: true
    sentinel:
      address: 127.0.0.1:8848
      namespace: 8b40d906-fa9d-4fc9-b164-f43ad92e3835
      groupId: local
      dataId: ${spring.application.name}-flow-rules
      degradeRulesDataId: ${spring.application.name}-degrade-rules
      flowRulesDataId: ${spring.application.name}-flow-rules
      paramRulesDataId: ${spring.application.name}-param-rules
      systemRulesDataId: ${spring.application.name}-system-rules

dubbo:
  application:
    name: integral-provider
  protocol:
    name: dubbo
    port: -1
  scan:
    base-packages: com.kuwo.integralservice.service
  registries:
    local_1:
      address: nacos://localhost:8848?namespace=a62ae846-2e12-424a-9d71-bf58f12accf1
      group: local
  provider:
    filter: tracing
  cloud:
    subscribed-services: integral-provider
