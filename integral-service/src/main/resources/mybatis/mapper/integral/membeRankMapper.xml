<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kuwo.integralservice.mapper.integral.MemberRankMapper" >

    <select id="getAllRank" resultType="com.kuwo.integralservice.enity.MemberRank">
        select
            ID id,
            VIP_TAG vipTag,
            ICON icon,
            MINI_SCORE miniScore,
            MAX_SCORE maxScore,
            RANK_TYPE rankType,
            SORT_VALUE sortValue,
            CREATE_TIME createTime,
            UPDATE_TIME updateTime,
            IDENTIFY_KEY identifyKey,
            STATE state
        from member_rank
        <where>
            STATE = 1
        </where>
        order by SORT_VALUE, CREATE_TIME
    </select>
</mapper>