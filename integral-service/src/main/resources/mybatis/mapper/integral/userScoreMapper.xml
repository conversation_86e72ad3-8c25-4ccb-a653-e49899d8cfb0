<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kuwo.integralservice.mapper.integral.UserScoreMapper" >
    <update id="updateAddUserScore">
        update user_score
        <set>
            UPDATE_TIME = now(), SCORE = SCORE + #{score}, VERSION = VERSION + 1
        </set>
        <where>
            USER_ID = #{userId} and USER_TYPE = #{userType} and VERSION = #{version}
        </where>
    </update>

    <update id="updateSubUserScore">
        update user_score
        <set>
            UPDATE_TIME = now(), SCORE = SCORE - #{score}, VERSION = VERSION + 1
        </set>
        <where>
            USER_ID = #{userId} and USER_TYPE = #{userType} and VERSION = #{version}
        </where>
    </update>

    <update id="initScore">
        update user_score
        <set>
            UPDATE_TIME = now(), SCORE = #{score}
        </set>
        <where>
            USER_ID = #{userId}
        </where>
    </update>

    <insert id="createDefaultUser">
        insert into user_score (`MEMBER_SCORE_ID`,`USER_ID`,`VERSION`,`SCORE`, `CREATE_TIME`, `UPDATE_TIME`, `USER_TYPE`, `SVIP_FLAG`)
        values (#{memberScoreId}, #{userId}, 0,0,now(),now(),1, 0)
    </insert>

    <select id="getUserScore" resultType="com.kuwo.integralservice.enity.UserScore" timeout="1">
        select
            ID id,
            MEMBER_SCORE_ID memberScoreId,
            USER_ID userId,
            VERSION version,
            SCORE score,
            CREATE_TIME createTime,
            UPDATE_TIME updateTime,
            USER_TYPE userType,
            SVIP_FLAG svipFlag
        from user_score
        <where>
            USER_ID = #{userId} and USER_TYPE = #{userType}
        </where>
        limit 1
    </select>

</mapper>