<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kuwo.integralservice.mapper.integral.MemberGiveRightsRecordMapper" >

    <insert id="saveRightRecord" parameterType="com.kuwo.integralservice.enity.MemberGiveRightsRecord">
        <selectKey resultType="java.lang.Long" order="AFTER" keyProperty="id">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into member_give_rights_record (`MEMBER_SCORE_ID`,`GIVE_STATUS`, `RIGHT_TYPE`, `RIGHT_PARAMS`, `REMARK`, `SVIP_TYPE`, `CREATE_TIME`, `UPDATE_TIME`)
        values (#{memberScoreId}, 0, #{rightType}, #{rightParams}, "",#{svipType}, now(), now())
    </insert>

    <update id="updateGiveRightStatus">
        update member_give_rights_record
        <set>
            GIVE_STATUS = #{status},
            REMARK = #{remark},
            UPDATE_TIME = now()
        </set>
        <where>
            ID = #{id}
        </where>
    </update>

    <select id="getGiveRightsRecord" resultType="com.kuwo.integralservice.enity.MemberGiveRightsRecord">
           select
            ID id,
            MEMBER_SCORE_ID memberScoreId,
            GIVE_STATUS giveStatus,
            RIGHT_TYPE rightType,
            RIGHT_PARAMS rightParams,
            REMARK remark,
            SVIP_TYPE svipType,
            CREATE_TIME createTime,
            UPDATE_TIME updateTime
           from member_give_rights_record
           <where>
               MEMBER_SCORE_ID = #{memberScoreId} and RIGHT_TYPE = #{rightIdentifyId}  and SVIP_TYPE = #{memberRankIdentify}
           </where>
           limit 1
    </select>


</mapper>