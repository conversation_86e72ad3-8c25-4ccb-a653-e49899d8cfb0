<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kuwo.integralservice.mapper.integral.NewWorkNodeMapper" >
    <insert id="save" parameterType="com.kuwo.integralservice.enity.NewWorkerNodeEntity">
        <selectKey resultType="java.lang.Long" order="AFTER" keyProperty="id">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into worker_node (`HOST_NAME`,`PORT`,`TYPE`,`LAUNCH_DATE`, `MODIFIED`, `CREATED`)
        values (#{hostName}, #{port}, #{type},now(), now(), now())
    </insert>
</mapper>