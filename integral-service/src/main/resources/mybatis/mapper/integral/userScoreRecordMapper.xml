<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kuwo.integralservice.mapper.integral.UserScoreRecordMapper" >

    <insert id="saveScoreRecord" parameterType="com.kuwo.integralservice.enity.UserScoreRecord">
        insert into user_score_record (`MEMBER_SCORE_ID`, `ACTION_TYPE`, `SCORE`, `FROM_PARENT_TASK`, `FROM_TASK`, `REMARK`, `CREATE_TIME`)
        values (#{memberScoreId}, #{actionType}, #{score}, #{fromParentTask}, #{fromTask}, #{remark}, now())
    </insert>

    <select id="getScoreRecords" resultType="com.kuwo.integralservice.enity.UserScoreRecord">
        SELECT
            ID id,
            MEMBER_SCORE_ID memberScoreId,
            ACTION_TYPE actionType,
            SCORE score,
            FROM_PARENT_TASK fromParentTask,
            FROM_TASK fromTask,
            REMARK remark,
            CREATE_TIME createTime
        FROM user_score_record
        <where>
            MEMBER_SCORE_ID = #{memberScoreId}
        </where>
        order by CREATE_TIME desc
        limit #{start}, #{size}
    </select>

    <select id="getScoreByRange" resultType="java.lang.Integer">
        select COALESCE(sum(SCORE), 0)  from user_score_record
        <where>
            MEMBER_SCORE_ID = #{memberScoreId}
            and ACTION_TYPE = #{type}
            and CREATE_TIME <![CDATA[ >= ]]> #{beginDay}
            and CREATE_TIME <![CDATA[ < ]]> #{endDay}
        </where>
    </select>

    <select id="findUserScoreRecord" resultType="com.kuwo.integralservice.enity.UserScoreRecord">
        select
            ID id,
            MEMBER_SCORE_ID memberScoreId,
            ACTION_TYPE actionType,
            SCORE score,
            FROM_PARENT_TASK fromParentTask,
            FROM_TASK fromTask,
            REMARK remark,
            CREATE_TIME createTime
        FROM user_score_record
        <where>
            MEMBER_SCORE_ID = #{memberScoreId}
            and CREATE_TIME <![CDATA[ >= ]]> #{beginDay}
            and CREATE_TIME <![CDATA[ < ]]> #{endDay}
            and FROM_PARENT_TASK = #{parentTaskId}
            and FROM_TASK = #{taskId}
        </where>
        limit 1

    </select>

    <select id="getNormalTaskScoreByRange" resultType="java.lang.Integer">
        select COALESCE(sum(SCORE), 0)  from user_score_record u, member_task t
        <where>
            u.MEMBER_SCORE_ID = #{memberScoreId}
            and  u.ACTION_TYPE = #{type}
            and  u.CREATE_TIME <![CDATA[ >= ]]> #{beginDay}
            and  u.CREATE_TIME <![CDATA[ < ]]> #{endDay}
            and u.FROM_TASK = t.ID
            and t.TASK_TYPE = 'normal'
        </where>

    </select>

</mapper>