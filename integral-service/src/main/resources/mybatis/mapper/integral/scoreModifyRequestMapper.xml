<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kuwo.integralservice.mapper.integral.ModifyRequestRecordMapper" >

    <insert id="saveRecord" parameterType="com.kuwo.integralservice.enity.ModifyRequestRecord">
        insert  into   modify_request_record (`INVOKE_SOURCE`,`REQUEST_ID`,`CREATE_DATE`,`UPDATE_DATE`,`REQUEST_STATUS`)
        values (#{invokeSource}, #{requestId}, now(), now(), #{requestStatus})
    </insert>
    <update id="updateRequestInvokeStatus">
        update  modify_request_record
        <set>
            REQUEST_STATUS = #{status},
            UPDATE_DATE = now()
        </set>
        <where>
            INVOKE_SOURCE = #{invokeSource}
            and  REQUEST_ID =#{requestId}
        </where>
    </update>

    <select id="getRequestRecord" resultType="com.kuwo.integralservice.enity.ModifyRequestRecord">
            select
                ID id,
                INVOKE_SOURCE invokeSource,
                REQUEST_ID requestId,
                CREATE_DATE createDate,
                UPDATE_DATE updateDate,
                REQUEST_STATUS requestStatus
            from modify_request_record
            <where>
                 INVOKE_SOURCE = #{invokeSource}
                 and  REQUEST_ID =#{requestId}
            </where>
            limit 1
    </select>



</mapper>