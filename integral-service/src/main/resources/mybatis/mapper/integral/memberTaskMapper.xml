<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kuwo.integralservice.mapper.integral.MemberTaskMapper" >

    <update id="updateMemberTaskByParentIdHide">
        update  member_task
        <set>
            IS_HIDE = 1
        </set>
        <where>
            PARENT_ID = #{parentId}
        </where>
    </update>

    <update id="updateMemberTaskDisPlay">
        update  member_task
        <set>
            IS_HIDE = 0
        </set>
        <where>
            ID in
            <foreach collection="taskIds" open="(" close=")" separator="," item="taskId">
                #{taskId}
            </foreach>
        </where>

    </update>


    <select id="getAvailableTasks" resultType="com.kuwo.integralservice.enity.MemberTask">
        select
            ID id,
            PARENT_ID parentId,
            TASK_TYPE taskType,
            TASK_NAME taskName,
            TASK_ICON taskIcon,
            TASK_DESC taskDesc,
            ADD_SCORE addScore,
            STATUS status,
            TASK_IDENTIFY taskIdentify,
            CREATE_TIME createTime,
            UPDATE_TIME updateTime,
            SORE_VALUE sortValue,
            IS_HIDE isHide,
            RELATE_DISPLAY_ID relateDisplayId,
            AD_PLAT adPlat
        from member_task
        <where>
            STATUS = 1
        </where>
        order by SORE_VALUE, ID
    </select>

    <select id="getAvailableTaskByIdentifyKey" resultType="com.kuwo.integralservice.enity.MemberTask">
        select
            ID id,
            PARENT_ID parentId,
            TASK_TYPE taskType,
            TASK_NAME taskName,
            TASK_ICON taskIcon,
            TASK_DESC taskDesc,
            ADD_SCORE addScore,
            STATUS status,
            TASK_IDENTIFY taskIdentify,
            CREATE_TIME createTime,
            UPDATE_TIME updateTime,
            SORE_VALUE sortValue,
            IS_HIDE isHide,
            RELATE_DISPLAY_ID relateDisplayId,
            AD_PLAT adPlat
        from member_task
        <where>
            STATUS = 1 and TASK_IDENTIFY = #{identifyKey}
        </where>
        order by ID
    </select>

    <select id="getAllTaskByParentTaskId" resultType="com.kuwo.integralservice.enity.MemberTask" timeout="1">
        select
            ID id,
            PARENT_ID parentId,
            TASK_TYPE taskType,
            TASK_NAME taskName,
            TASK_ICON taskIcon,
            TASK_DESC taskDesc,
            ADD_SCORE addScore,
            STATUS status,
            TASK_IDENTIFY taskIdentify,
            CREATE_TIME createTime,
            UPDATE_TIME updateTime,
            SORE_VALUE sortValue,
            IS_HIDE isHide,
            RELATE_DISPLAY_ID relateDisplayId,
            AD_PLAT adPlat
        from member_task
        <where>
            PARENT_ID = #{parentId}
        </where>


    </select>


</mapper>