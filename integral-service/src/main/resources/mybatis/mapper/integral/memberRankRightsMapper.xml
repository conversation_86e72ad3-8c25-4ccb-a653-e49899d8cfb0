<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kuwo.integralservice.mapper.integral.MemberRankRightsMapper" >
    <select id="loadAllRankRights" resultType="com.kuwo.integralservice.enity.MemberRankRights">
         select
            ID id,
            RANK_IDENTIFY_ID rankIdentifyId,
            RIGHTS_ID rightsId,
            RIGHT_IDENTIFY_ID rightIdentifyId,
            DEVELOPER_PARAMS developerParams,
            CREATE_TIME createTime,
            UPDATE_TIME updateTime,
            RANK_ID rankId
        from member_rank_rights
    </select>

    <select id="getRightsByRankIdentfy" resultType="com.kuwo.integralservice.enity.MemberRankRights" timeout="1">
        select
        ID id,
        RANK_IDENTIFY_ID rankIdentifyId,
        RIGHTS_ID rightsId,
        RIGHT_IDENTIFY_ID rightIdentifyId,
        DEVE<PERSON><PERSON>ER_PARAMS developerParams,
        CREATE_TIME createTime,
        UPDATE_TIME updateTime,
        RANK_ID rankId
        from member_rank_rights
        <where>
            RANK_IDENTIFY_ID = #{memberRankIdentify}
        </where>
    </select>



</mapper>