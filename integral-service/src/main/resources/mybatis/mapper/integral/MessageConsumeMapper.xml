<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kuwo.integralservice.mapper.integral.MessageConsumeMapper" >

    <update id="updateMessageStatus">
        update message_consume set STATUS = #{status}, UPDATE_TIME= now(),MSG_RETRY_NUM = MSG_RETRY_NUM +1, ERROR_DESC = #{errorDesc}
        <where>
            ID = #{id}
        </where>
    </update>

    <insert id="saveNewMessageConsume" parameterType="com.kuwo.integralservice.enity.MessageConsume">
        <selectKey resultType="java.lang.Long" order="AFTER" keyProperty="id">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into message_consume (`MESSAGE_ID`, `CONTENT`,`STATUS`, `ERROR_DESC`, `MSG_RETRY_NUM`, `CREATE_TIME`, `UPDATE_TIME`)
        values (#{messageId}, #{content}, #{status}, "", 0, now(), now())
    </insert>

    <select id="findMessageConsume" resultType="com.kuwo.integralservice.enity.MessageConsume">
        select
        ID id,
        MESSAGE_ID messageId,
        CONTENT content,
        STATUS status,
        ERROR_DESC errorDesc,
        MSG_RETRY_NUM msgRetryNum,
        CREATE_TIME createTime,
        UPDATE_TIME updateTime
        from message_consume
        <where>
          MESSAGE_ID = #{messageId}
        </where>
    </select>


</mapper>