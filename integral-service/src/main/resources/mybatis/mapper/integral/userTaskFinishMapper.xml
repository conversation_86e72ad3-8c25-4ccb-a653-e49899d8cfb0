<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kuwo.integralservice.mapper.integral.UserTaskFinishDetailMapper" >

    <insert id="saveUserTaskDetail" parameterType="com.kuwo.integralservice.enity.UserTaskFinishDetail">
        insert into user_task_finish_detail (`MEMBER_SCORE_ID`, `PARENT_TASK_ID`, `TASK_ID`, `STATUS`, `CREATE_TIME`, `UPDATE_TIME`, `REMARK`)
        values (#{memberScoreId}, #{parentTaskId},#{taskId}, #{status}, now(),now(),#{remark})
    </insert>

    <update id="updateCurrentTaskRecordFinish">
        update user_task_finish_detail
        <set>
            STATUS = 2,
            UPDATE_TIME = now()
        </set>
        <where>
            MEMBER_SCORE_ID = #{memberScoreId}
            and TASK_ID = #{taskId}
            and PARENT_TASK_ID = #{parentTaskId}
            and CREATE_TIME <![CDATA[ >= ]]> #{begin}
            and CREATE_TIME <![CDATA[ < ]]> #{end}
        </where>
    </update>

    <select id="getUserTaskDetail" resultType="com.kuwo.integralservice.enity.UserTaskFinishDetail">
        select
        ID id,
        MEMBER_SCORE_ID memberScoreId,
        PARENT_TASK_ID parentTaskId,
        TASK_ID taskId,
        STATUS status,
        CREATE_TIME createTime,
        UPDATE_TIME updateTime,
        REMARK remark
        from  user_task_finish_detail
        <where>
            MEMBER_SCORE_ID = #{memberScoreId}
            and TASK_ID = #{taskId}
            and CREATE_TIME <![CDATA[ >= ]]> #{begin}
            and CREATE_TIME <![CDATA[ < ]]> #{end}
        </where>
        limit 1
    </select>

    <select id="findUserTaskFinishDetails" resultType="com.kuwo.integralservice.enity.UserTaskFinishDetail">
        select
            ID id,
            MEMBER_SCORE_ID memberScoreId,
            PARENT_TASK_ID parentTaskId,
            TASK_ID taskId,
            STATUS status,
            CREATE_TIME createTime,
            UPDATE_TIME updateTime,
            REMARK remark
        from  user_task_finish_detail
        <where>
            MEMBER_SCORE_ID = #{memberScoreId}
            and CREATE_TIME <![CDATA[ >= ]]> #{begin}
            and CREATE_TIME <![CDATA[ < ]]> #{end}
        </where>
    </select>


</mapper>