spring:
  profiles:
    active: prod
  application:
    name: intergralService
  mvc:
    throw-exception-if-no-handler-found: true
    static-path-pattern: /statics/**
  resources:
    add-mappings: false
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
  web:
    resources:
      add-mappings: false
member:
  client:
    md5key: 35lp2TIwMMHLh3MfM7qpfsWLXjgwiwiY
    base64Key: yeelion20131111kuwogame
  score:
    md5key: w1yAHe!&V2$!@%D0e&4VyuhaYda90s2K

management:
  endpoints:
    web:
      exposure:
        include: "prometheus"
      base-path: "/actuator"
  metrics:
    web:
      server:
        request:
          autotime:
            percentiles-histogram: true
    distribution:
      slo:
        http:
          server:
            requests: PT0.005S, PT0.010S, PT0.025S, PT0.05S, PT0.075S, PT0.1S, PT0.25S, PT0.5S, PT0.75S, PT1S, PT2.5S, PT5S, PT7.5S, PT10S
      minimum-expected-value:
        http:
          server:
            requests:  PT0.005S
      maximum-expected-value:
        http:
          server:
            requests:  PT0.005S
mybatis:
  configuration:
    default-statement-timeout: 3