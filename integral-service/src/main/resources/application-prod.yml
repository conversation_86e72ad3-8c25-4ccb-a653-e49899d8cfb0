spring:
  integral:
    datasource:
      url: ********************************************************************************************************************************
      username: mi_write
      password: vip^_^_dr521
      driverClassName: com.mysql.cj.jdbc.Driver

  druid:
    initialSize: 5
    minIdle: 5
    maxActive: 20
    maxWait: 60000
    timeBetweenEvictionRunsMillis: 60000
    minEvictableIdleTimeMillis: 60000
    validationQuery: SELECT 1
    testWhileIdle: true
    testOnBorrow: false
    testOnReturn: false
    poolPreparedStatements: true
    maxPoolPreparedStatementPerConnectionSize: 20
    filters: stat,wall
    connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
    logSlowSql: true
  cloud:
    nacos:
      discovery:
        server-addr: ************:8848
        namespace1: c88af10c-113d-416e-b32a-9f78c7af1d89
    sentinel:
      transport:
        dashboard: *************:8080
server:
  port: 8080

redis:
  config:
    d1:
      address: redis://************:6380
      password: 2019yyFF$%
      selectDb: 1
    d2:
      address: redis://************:6380
      password: 2019yyFF$%
      selectDb: 1
  names: d1,d2

sentinel:
  nacos:
    namespace: df1616de-b65e-44b6-ab0e-debdc7cca419
    groupId: prod
    vip-rule-dataId: integralService-flow-rules

login:
  check:
    url: http://loginserver.kuwo.cn/u.s?type=validate&uid=%s&sid=%s&req_enc=utf8&res_enc=utf8
vip:
  domain: http://vip1.kuwo.cn
kafka:
  address: ***********:9092,************:9092,************:9092
  userSystemAddress: ***********:9092,************:9092,************:9092
  ascribeAddress: ***********:9092
test:
  env: prod

kuwo:
  monitor:
    enabled: true
    sentinel:
      address: ************:8848
      namespace: df1616de-b65e-44b6-ab0e-debdc7cca419
      groupId: prod
      dataId: ${spring.application.name}-flow-rules
      degradeRulesDataId: ${spring.application.name}-degrade-rules
      flowRulesDataId: ${spring.application.name}-flow-rules
      paramRulesDataId: ${spring.application.name}-param-rules
      systemRulesDataId: ${spring.application.name}-system-rules

dubbo:
  application:
    name: integral-provider
  protocol:
    name: dubbo
    port: -1
  scan:
    base-packages: com.kuwo.integralservice.service
  registries:
    p_1:
      address: nacos://************:8848?namespace=4566ca54-8e14-4749-a4c5-4ffc028d6377
      group: prod
  provider:
    filter: tracing
  cloud:
    subscribed-services: integral-provider