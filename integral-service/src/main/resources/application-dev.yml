spring:
  integral:
    datasource:
      url: ******************************************************************************************************************************
      username: pay_vip_write
      password: vip^_^_dr521
      driverClassName: com.mysql.cj.jdbc.Driver

  druid:
    initialSize: 5
    minIdle: 5
    maxActive: 20
    maxWait: 60000
    timeBetweenEvictionRunsMillis: 60000
    minEvictableIdleTimeMillis: 60000
    validationQuery: SELECT 1
    testWhileIdle: true
    testOnBorrow: false
    testOnReturn: false
    poolPreparedStatements: true
    maxPoolPreparedStatementPerConnectionSize: 20
    filters: stat,wall
    connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
    logSlowSql: true
  cloud:
    nacos:
      discovery:
        server-addr: **********:8848
        namespace1: a9c73607-c549-4ff6-8cd5-0b4f3e475ba1
    sentinel:
      transport:
        dashboard: *********:8081
server:
  port: 8080


login:
  check:
    url: http://loginserver.kuwo.cn/u.s?type=validate&uid=%s&sid=%s&req_enc=utf8&res_enc=utf8
vip:
  domain: http://testvip.kuwo.cn
redis:
  config:
    d1:
      address: redis://*************:6380
      password: 2019yyFF$%
      selectDb: 3
    d2:
      address: redis://*************:6380
      password: 2019yyFF$%
      selectDb: 4
  names: d1,d2

sentinel:
  nacos:
    namespace: 6f61394b-0c40-42af-bb2c-72944d8439c3
    groupId: dev
    vip-rule-dataId: integralService-flow-rules

kafka:
  address: ************:9092
  userSystemAddress: *********:9092,*********:9092,*********:9092,*********:9092,***********:9092
  ascribeAddress: *********:9092,*********:9092,*********:9092,*********:9092,***********:9092
test:
  env: test

kuwo:
  monitor:
    enabled: true
    sentinel:
      address: **********:8848
      namespace: 6f61394b-0c40-42af-bb2c-72944d8439c3
      groupId: dev
      dataId: ${spring.application.name}-flow-rules
      degradeRulesDataId: ${spring.application.name}-degrade-rules
      flowRulesDataId: ${spring.application.name}-flow-rules
      paramRulesDataId: ${spring.application.name}-param-rules
      systemRulesDataId: ${spring.application.name}-system-rules

dubbo:
  application:
    name: integral-provider
  protocol:
    name: dubbo
    port: -1
  scan:
    base-packages: com.kuwo.integralservice.service
  registries:
    local_1:
      address: nacos://**********:8848?namespace=67f2182d-0b31-444c-8746-054c171d7a11
      group: local
  provider:
    filter: tracing
  cloud:
    subscribed-services: integral-provider