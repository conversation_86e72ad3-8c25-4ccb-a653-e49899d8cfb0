package com.kuwo.integralservice.vo;

import com.kuwo.integralservice.enity.UserTaskFinishDetail;

public class UserTaskFinishVo {
    /**
     * 判断当前vo是新创建的还是已经有了
     */
    private int status = 0;

    private UserTaskFinishDetail taskFinishDetail;


    public UserTaskFinishVo(UserTaskFinishDetail taskFinishDetail) {
        this.taskFinishDetail = taskFinishDetail;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public UserTaskFinishDetail getTaskFinishDetail() {
        return taskFinishDetail;
    }
}
