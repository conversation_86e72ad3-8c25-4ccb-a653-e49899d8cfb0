package com.kuwo.integralservice.vo;

public class MemberRankVo implements Comparable{
    private String vipTag;
    private String icon;
    private Integer miniScore;
    private Integer maxScore;
    private int order;
    private String lv;
    public String getVipTag() {
        return vipTag;
    }

    public void setVipTag(String vipTag) {
        this.vipTag = vipTag;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public Integer getMiniScore() {
        return miniScore;
    }

    public void setMiniScore(Integer miniScore) {
        this.miniScore = miniScore;
    }

    public Integer getMaxScore() {
        return maxScore;
    }

    public void setMaxScore(Integer maxScore) {
        this.maxScore = maxScore;
    }

    public int getOrder() {
        return order;
    }

    public void setOrder(int order) {
        this.order = order;
    }
    public String getLv() {
        return lv;
    }

    public void setLv(String lv) {
        this.lv = lv;
    }

    @Override
    public int compareTo(Object o) {
        MemberRankVo other = (MemberRankVo) o;
        if (this.order == other.order){
            return Math.toIntExact(other.miniScore- this.miniScore);
        }
        return this.order-other.order;
    }
}
