package com.kuwo.integralservice.vo;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Desc: 用户任务列表完成状况vo
 * @date 2022-02-17 20:42:50
 */
public class TaskVo implements Serializable {

    private Long userId;
    private String taskIdentify;
    private int score;
    private String taskName;
    // 正常情况和taskName一致,当score -1时,这个值不一样
    private String taskDesc;
    private String taskIcon;

    // 1 未完成。2 完成，未领取。3 领取
    private int status;


    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getTaskIdentify() {
        return taskIdentify;
    }

    public void setTaskIdentify(String taskIdentify) {
        this.taskIdentify = taskIdentify;
    }

    public int getScore() {
        return score;
    }

    public void setScore(int score) {
        this.score = score;
    }

    public String getTaskIcon() {
        return taskIcon;
    }

    public void setTaskIcon(String taskIcon) {
        this.taskIcon = taskIcon;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getTaskDesc() {
        return taskDesc;
    }

    public void setTaskDesc(String taskDesc) {
        this.taskDesc = taskDesc;
    }
}
