package com.kuwo.integralservice.util;

import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class ClientActionTransform {

    public static Map<Integer, String> convertMap = new ConcurrentHashMap<Integer, String>();

    @PostConstruct
    public void init(){
        // 更换会员皮肤
        convertMap.put(6001, "switch_member_skin");
        // 创建歌单
        convertMap.put(6002, "create_song_sheet");
        //头像挂件
        convertMap.put(6003, "head_widget");
        //ai音效
        convertMap.put(6004, "ai_voice");
        //动态分享
        convertMap.put(6005, "dynamic_share");
        // 个性化铃声
        convertMap.put(6006, "personalized_voice");
        // 更换背景图片
        convertMap.put(6007, "switch_background_image");
        // 体验云盘
        convertMap.put(6008, "experience_cloud_disk");
        // 播放器换肤
        convertMap.put(6009, "player_switch_skin");
        // 发表评论
        convertMap.put(7001, "create_comment_task");
        // 听歌
        convertMap.put(7002, "listen_vip_song");
        // 下载
        convertMap.put(7003, "download_vip_song_task");
    }


    public  String getTaskIdentify(int action){
        return convertMap.get(action);
    }

}
