package com.kuwo.integralservice.util;

import com.google.gson.Gson;
import com.kuwo.integralservice.cenum.ActionType;
import com.kuwo.integralservice.config.kafka.KafkaTopicEnum;
import com.kuwo.integralservice.req.ScoreModifyRequest;
import com.kuwo.integralservice.service.KafkaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class KafkaMessageHelper {

    @Autowired
    private KafkaService kafkaService;

    private Gson gson = new Gson();

    public void broadMessage(Long userId, int score, ActionType actionType, ScoreModifyRequest request) {
        Map message = new HashMap();
        message.put("clientRequests", request);
        message.put("userId", userId);
        message.put("score", score);
        message.put("actionType", actionType.getValue());
        kafkaService.sendMessages(KafkaTopicEnum.SCORE_CHANGE_MESSAGE, gson.toJson(message));
    }
}
