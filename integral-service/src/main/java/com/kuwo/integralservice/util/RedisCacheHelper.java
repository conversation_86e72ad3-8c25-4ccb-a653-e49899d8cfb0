package com.kuwo.integralservice.util;

import com.kuwo.commercialization.common.cenum.RedisConstant;
import com.kuwo.integralservice.cache.RedisReplaceConfig;
import com.kuwo.integralservice.config.redis.DynamicChoiceRedissonClient;
import com.kuwo.integralservice.vo.TaskList;
import org.redisson.api.RLock;
import org.redisson.api.RMapCache;
import org.redisson.api.RReadWriteLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Desc:
 * @date 2022-03-23 10:10:13
 */
@Component
public class RedisCacheHelper {

    private Logger logger = LoggerFactory.getLogger(RedisCacheHelper.class);

    @Autowired
    private DynamicChoiceRedissonClient dynamicChoiceRedissonClient;

    @Autowired
    private RedisReplaceConfig redisReplaceConfig;



    /**
     * 清空user_id 的task列表缓存
     * @param userId
     */
    public void cleanUserTaskListCache(String userId){
        RedissonClient redissonClient = dynamicChoiceRedissonClient.getClient(redisReplaceConfig.getDefaultRedisName());
        RMapCache<String, List<TaskList>> taskCache = redissonClient.getMapCache(RedisConstant.USER_TASK_LIST_CACHE.getKey());
        String cacheKey1 = String.format("%s_%s", userId, "ar");
        String cacheKey2 = String.format("%s_%s", userId, "ios");
        String cacheKey3 = String.format("%s_%s", userId, "pc");
        if (taskCache.containsKey(cacheKey1)){
            taskCache.remove(cacheKey1);
        }
        if (taskCache.containsKey(cacheKey2)){
            taskCache.remove(cacheKey2);
        }
        if (taskCache.containsKey(cacheKey3)){
            taskCache.remove(cacheKey3);
        }
    }

    /**
     * 清空用户的积分信息
     * @param userId
     */
    public void cleanUserBasicScoreInfo(String userId){
        RedissonClient redissonClient = dynamicChoiceRedissonClient.getClient(redisReplaceConfig.getDefaultRedisName());
        String lockKey = String.format(RedisConstant.READ_SCORE_INFO_CACHE_LOCK.getKey(), userId);
        RReadWriteLock readWriteLock = redissonClient.getReadWriteLock(lockKey);
        RLock rLock = readWriteLock.writeLock();
        // 这块儿需要根据userid进行分组
        RMapCache<Object, Object> scoreInfoCache = dynamicChoiceRedissonClient.getClient(redisReplaceConfig.useTencentRedis(userId)?"d2":"d1")
                .getMapCache(RedisConstant.USER_BASE_SCORE_INFO_CACHE.getKey());
        try {
            if (rLock.tryLock(1, TimeUnit.SECONDS)){
                if (scoreInfoCache.containsKey(userId)){
                    Object remove = scoreInfoCache.remove(userId);
                    logger.info("user info cache clean, user_id -> {}, removed value ->{}", userId, remove);
                }
            }
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } finally {
            if (rLock.isLocked()){
                rLock.unlock();
            }
        }
    }

}
