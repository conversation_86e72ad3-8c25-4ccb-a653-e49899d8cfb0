package com.kuwo.integralservice.util;

import com.kuwo.integralservice.cache.RedisReplaceConfig;
import com.kuwo.integralservice.config.redis.DynamicChoiceRedissonClient;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component
public class LockHelper {

    private Logger logger = LoggerFactory.getLogger(LockHelper.class);

    private final DynamicChoiceRedissonClient dynamicChoiceRedissonClient;

    private final RedisReplaceConfig redisReplaceConfig;

    public LockHelper(DynamicChoiceRedissonClient dynamicChoiceRedissonClient, RedisReplaceConfig redisReplaceConfig) {
        this.dynamicChoiceRedissonClient = dynamicChoiceRedissonClient;
        this.redisReplaceConfig = redisReplaceConfig;
    }

    public  <T> T lockAndExecute(String lockKey, long waitTime,long leaseTime,  Execute<T> execute, String exceptionMsg){
       RedissonClient redissonClient = dynamicChoiceRedissonClient.getClient(redisReplaceConfig.getDefaultRedisName());
       RLock lock = redissonClient.getLock(lockKey);
       T t = null;
       try {
           if (lock.tryLock(waitTime,leaseTime, TimeUnit.SECONDS)){
                t = execute.execute();
           }
       } catch (Exception e) {
           logger.error("exception msg {}, lock acquire exception --> ",exceptionMsg, e);
       } finally {
           if (lock.isLocked() && lock.isHeldByCurrentThread()){
               lock.unlock();
           }
       }
       return t;
   }

    /**
     * 对比上面那个方法，增加类对于异常情况添加默认值的处理
     * @param lockKey
     * @param waitTime
     * @param leaseTime
     * @param execute
     * @param exceptionMsg
     * @param exceptionValue
     * @param <T>
     * @return
     */
   public  <T> T lockAndExecute(String lockKey, long waitTime,long leaseTime,  Execute<T> execute, String exceptionMsg, T exceptionValue){
       RedissonClient redissonClient = dynamicChoiceRedissonClient.getClient(redisReplaceConfig.getDefaultRedisName());
       RLock lock = redissonClient.getLock(lockKey);
       T t = null;
       try {
           if (lock.tryLock(waitTime,leaseTime, TimeUnit.SECONDS)){
                t = execute.execute();
           }
       } catch (Exception e) {
           logger.error("exception msg {}, lock acquire exception --> ",exceptionMsg, e);
           t = exceptionValue;
       } finally {
           if (lock.isLocked() && lock.isHeldByCurrentThread()){
               lock.unlock();
           }
       }
       return t;
   }

    /**
     * 执行体
     * @param <T>
     */
   public static interface Execute<T>{
       T execute();
   }


}
