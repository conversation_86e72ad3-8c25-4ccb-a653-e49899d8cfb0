package com.kuwo.integralservice.util;

import com.kuwo.integralservice.req.ScoreModifyRequest;

public class ScoreChangeRequestHolder {

    private static final ThreadLocal <ScoreModifyRequest> threadLocal = new ThreadLocal<>();

    public static void addRequest(ScoreModifyRequest request){
        threadLocal.set(request);
    }

    public static ScoreModifyRequest getRequest(){
        return threadLocal.get();
    }

    public static void clean(){
        threadLocal.remove();
    }

}
