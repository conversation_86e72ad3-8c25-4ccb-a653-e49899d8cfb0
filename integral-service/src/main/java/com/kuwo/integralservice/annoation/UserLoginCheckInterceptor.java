package com.kuwo.integralservice.annoation;

import com.google.common.base.Preconditions;
import com.kuwo.integralservice.http.LoginHttpService;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.UUID;

/**
 * <AUTHOR>
 * @Desc:
 * @date 2021-12-31 16:54:31
 */
@Component
public class UserLoginCheckInterceptor implements HandlerInterceptor {

    @Autowired
    private LoginHttpService loginHttpService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        MDC.put("traceId", UUID.randomUUID().toString());
        if (handler instanceof HandlerMethod){
            HandlerMethod method = (HandlerMethod) handler;
            CheckUserLogin userLogin = method.getMethodAnnotation(CheckUserLogin.class);
            if (userLogin!=null){
                String uidKey = userLogin.uidKey();
                String sessionIdKey = userLogin.sessionIdKey();
                String userId = request.getParameter(uidKey);
                String sessionId = request.getParameter(sessionIdKey);
                Preconditions.checkState(loginHttpService.checkLogin(userId, sessionId), "USER_NOT_LOGIN");
            }

        }
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        MDC.clear();
    }
}
