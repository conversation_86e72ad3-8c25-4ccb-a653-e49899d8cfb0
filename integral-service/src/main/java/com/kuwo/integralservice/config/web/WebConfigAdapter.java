package com.kuwo.integralservice.config.web;

import com.commerical.mointor.config.filter.ResponseTimeExposeInterceptor;
import com.kuwo.integralservice.annoation.UserLoginCheckInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 * @Desc:
 * @date 2021-12-31 17:02:41
 */
@Configuration
public class WebConfigAdapter implements WebMvcConfigurer {

    @Autowired
    private UserLoginCheckInterceptor userLoginCheck;

    @Autowired
    private ResponseTimeExposeInterceptor responseTimeExposeInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(userLoginCheck)
                .addPathPatterns("/**")
                .order(2);

        registry.addInterceptor(responseTimeExposeInterceptor)
                .addPathPatterns("/**")
                .order(3);
    }

    @Override
    public void addCorsMappings(CorsRegistry registry) {
//        registry.addMapping("/basic/**")
//                .allowedOriginPatterns("*")
//                .allowCredentials(true)
//                .allowedMethods("GET", "HEAD", "POST", "PUT", "DELETE", "OPTIONS")
//                .allowedHeaders("*");
//
//        registry.addMapping("/page/**")
//                .allowedOriginPatterns("*")
//                .allowCredentials(true)
//                .allowedMethods("GET", "HEAD", "POST", "PUT", "DELETE", "OPTIONS")
//                .allowedHeaders("*");
//
//        registry.addMapping("/score/**")
//                .allowedOriginPatterns("*")
//                .allowCredentials(true)
//                .allowedMethods("GET", "HEAD", "POST", "PUT", "DELETE", "OPTIONS")
//                .allowedHeaders("*");
    }
}
