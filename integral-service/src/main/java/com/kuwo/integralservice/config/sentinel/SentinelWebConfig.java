//package com.kuwo.integralservice.config.sentinel;
//
//import com.alibaba.csp.sentinel.adapter.spring.webmvc.callback.BlockExceptionHandler;
//import com.google.gson.Gson;
//import com.kuwo.commercialization.common.cenum.Result;
//import com.kuwo.commercialization.common.resp.BasicResponse;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
//import java.io.PrintWriter;
//
//@Configuration
//public class SentinelWebConfig {
//
//    private Gson gson = new Gson();
//
//    @Bean
//    public BlockExceptionHandler sentinelBlockExceptionHandler(){
//        return (httpServletRequest, httpServletResponse, e) -> {
//            httpServletResponse.setContentType("application/json");
//            PrintWriter writer = httpServletResponse.getWriter();
//            writer.print( gson.toJson(new BasicResponse(Result.TOO_MANY_REQUEST, "")));
//            writer.flush();
//            writer.close();
//        };
//    }
//
//
//}
