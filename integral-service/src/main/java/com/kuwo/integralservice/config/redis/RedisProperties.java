package com.kuwo.integralservice.config.redis;

public class RedisProperties {

    private String address;

    private String password;

    private Integer selectDb;

    private String userStringCodec;

    private String userJsonCodec;

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Integer getSelectDb() {
        return selectDb;
    }

    public void setSelectDb(Integer selectDb) {
        this.selectDb = selectDb;
    }


    public String getUserStringCodec() {
        return userStringCodec;
    }

    public void setUserStringCodec(String userStringCodec) {
        this.userStringCodec = userStringCodec;
    }

    public String getUserJsonCodec() {
        return userJsonCodec;
    }

    public void setUserJsonCodec(String userJsonCodec) {
        this.userJsonCodec = userJsonCodec;
    }
}
