package com.kuwo.integralservice.config.nacos;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;

/**
 * <AUTHOR>
 * @Desc:
 * @date 2023-12-14 10:13:52
 */

@Configuration
public class NacosClientConfig {

    @Value("${spring.cloud.nacos.discovery.server-addr}")
    private String address;

    @Value("${spring.cloud.nacos.discovery.namespace1}")
    private String namespace;


    @Bean
    public ConfigService configService() throws NacosException {
        Properties properties = new Properties();
        properties.setProperty("serverAddr", address);
        if (StringUtils.isNotBlank(namespace)){
            properties.setProperty("namespace", namespace);
        }
        ConfigService configService = NacosFactory.createConfigService(properties);
        return configService;
    }


}
