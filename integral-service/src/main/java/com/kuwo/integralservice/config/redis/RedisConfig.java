package com.kuwo.integralservice.config.redis;

import org.apache.logging.log4j.util.Strings;
import org.redisson.Redisson;
import org.redisson.client.codec.StringCodec;
import org.redisson.codec.JsonJacksonCodec;
import org.redisson.config.Config;
import org.redisson.config.SingleServerConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

@Configuration
@EnableConfigurationProperties(RedisPlusProperties.class)
public class RedisConfig {
    private static Logger logger = LoggerFactory.getLogger(RedisConfig.class);


    @Autowired
    private RedisPlusProperties redisPlusProperties;


    @Bean(destroyMethod = "destroy",name = "dynamicChoiceRedisssionClient")
    public DynamicChoiceRedissonClient dynamicChoiceRedissonClient(){
        DynamicChoiceRedissonClient dynamicChoiceRedissonClient = new DynamicChoiceRedissonClient();
        String names = redisPlusProperties.getNames();
        Map<String, RedisProperties> config = redisPlusProperties.getConfig();
        for (String name: config.keySet()){
            Config newConfig =  new Config();
            RedisProperties redisProperties = config.get(name);
            SingleServerConfig singleServerConfig = newConfig.useSingleServer().setAddress(redisProperties.getAddress());
            if (Strings.isNotEmpty(redisProperties.getPassword())){
                singleServerConfig.setPassword(redisProperties.getPassword());
            }
            if (redisProperties.getSelectDb()!=null){
                singleServerConfig.setDatabase(redisProperties.getSelectDb());
            }
            if ("true".equals(redisProperties.getUserStringCodec())){
                newConfig.setCodec(new StringCodec());
            }
            if ("true".equals(redisProperties.getUserJsonCodec())){
                newConfig.setCodec(new JsonJacksonCodec());
            }
            newConfig.setUseScriptCache(true);
            dynamicChoiceRedissonClient.addRedissonClient(name, Redisson.create(newConfig));
            logger.info("初始化redis -> {}", name);
        }
        return dynamicChoiceRedissonClient;
    }

}
