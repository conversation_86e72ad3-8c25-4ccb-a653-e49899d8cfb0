package com.kuwo.integralservice.config.thread;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @Desc:
 * @date 2022-03-09 11:22:31
 */
@Configuration
public class ThreadPoolConfig {

    @Bean(name = "rightCheckValidate")
    public ThreadPoolExecutor rightCheckThreadPool(){
        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(10, 20, 5, TimeUnit.SECONDS, new ArrayBlockingQueue<>(1000),new ThreadPoolExecutor.CallerRunsPolicy());
        threadPoolExecutor.setThreadFactory(new ThreadFactory() {
            int i =0;
            @Override
            public Thread newThread(Runnable r) {
                Thread t = new Thread(r);
                t.setName("[right-check-validate]"+ (i++));
                return t;
            }
        });
        return threadPoolExecutor;
    }



}
