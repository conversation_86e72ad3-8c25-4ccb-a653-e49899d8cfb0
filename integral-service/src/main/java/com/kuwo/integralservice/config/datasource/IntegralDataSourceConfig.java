package com.kuwo.integralservice.config.datasource;

import com.alibaba.druid.pool.DruidDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;
import java.util.Properties;

/**
 * 统计库数据源
 */
@Configuration
@MapperScan(basePackages = "com.kuwo.integralservice.mapper.integral", sqlSessionTemplateRef = "integralSqlSessionTemplate")
public class IntegralDataSourceConfig {
    private static final Logger logger = LoggerFactory.getLogger(IntegralDataSourceConfig.class);

    @Value("${spring.integral.datasource.url}")
    private String url;
    @Value("${spring.integral.datasource.username}")
    private String user;
    @Value("${spring.integral.datasource.password}")
    private String password;
    @Value("${spring.integral.datasource.driverClassName}")
    private String driverClass;

    /**
     * 数据源配置
     */
    @Value("${spring.druid.initialSize}")
    private Integer initialSize;
    @Value("${spring.druid.minIdle}")
    private Integer minIdle;
    @Value("${spring.druid.maxActive}")
    private Integer maxActive;
    @Value("${spring.druid.maxWait}")
    private Long maxWait;
    @Value("${spring.druid.timeBetweenEvictionRunsMillis}")
    private Long timeBetweenEvictionRunsMillis;
    @Value("${spring.druid.minEvictableIdleTimeMillis}")
    private Long minEvictableIdleTimeMillis;
    @Value("${spring.druid.validationQuery}")
    private String validationQuery;
    @Value("${spring.druid.testWhileIdle}")
    private Boolean testWhileIdle;
    @Value("${spring.druid.testOnBorrow}")
    private Boolean testOnBorrow;
    @Value("${spring.druid.testOnReturn}")
    private Boolean testOnReturn;
    @Value("${spring.druid.poolPreparedStatements}")
    private Boolean poolPreparedStatements;
    @Value("${spring.druid.maxPoolPreparedStatementPerConnectionSize}")
    private Integer maxPoolPreparedStatementPerConnectionSize;
    @Value("${spring.druid.filters}")
    private String filters;
    @Value("${spring.druid.connectionProperties}")
    private Properties connectionProperties;
    @Value("${spring.druid.logSlowSql}")
    private String logSlowSql;


    @Bean(name = "integralDataSource")
    @Primary
    public DataSource adminDataSource() {
        DruidDataSource dataSource = new DruidDataSource();
        dataSource.setDriverClassName(driverClass);
        dataSource.setUrl(url);
        dataSource.setUsername(user);
        dataSource.setPassword(password);

        dataSource.setMinIdle(minIdle);
        dataSource.setMaxActive(maxActive);
        dataSource.setMaxWait(maxWait);
        dataSource.setTimeBetweenEvictionRunsMillis(timeBetweenEvictionRunsMillis);
        dataSource.setMinEvictableIdleTimeMillis(minEvictableIdleTimeMillis);
        dataSource.setValidationQuery(validationQuery);
        dataSource.setTestWhileIdle(testWhileIdle);
        dataSource.setTestOnBorrow(testOnBorrow);
        dataSource.setTestOnReturn(testOnReturn);
        dataSource.setPoolPreparedStatements(poolPreparedStatements);
        dataSource.setMaxPoolPreparedStatementPerConnectionSize(maxPoolPreparedStatementPerConnectionSize);
        try {
            dataSource.setFilters(filters);
        } catch (Exception e) {
            logger.warn("获取数据源时设置监控拦截器失败");
        }
        dataSource.setConnectProperties(connectionProperties);
        return dataSource;
    }

    @Bean(name = "integralSqlSessionFactory")
    @Primary
    public SqlSessionFactory adminSqlSessionFactory(@Qualifier("integralDataSource") DataSource dataSource) throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:mybatis/mapper/integral/*.xml"));
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:mybatis/mapper/integral/**/*.xml"));
        return bean.getObject();
    }

    @Bean(name = "integralTransactionManager")
    @Primary
    public DataSourceTransactionManager adminTransactionManager(@Qualifier("integralDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "integralSqlSessionTemplate")
    @Primary
    public SqlSessionTemplate adminSqlSessionTemplate(@Qualifier("integralSqlSessionFactory") SqlSessionFactory sqlSessionFactory) throws Exception {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

}
