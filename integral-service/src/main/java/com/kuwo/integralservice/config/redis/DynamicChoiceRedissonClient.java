package com.kuwo.integralservice.config.redis;

import org.redisson.api.RedissonClient;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class DynamicChoiceRedissonClient {

    private Map<String, RedissonClient> clients = new ConcurrentHashMap<>();

    public void destroy() throws Exception {
        clients.values().forEach(RedissonClient::shutdown);
    }

    public void addRedissonClient(String name, RedissonClient redissonClient){
        clients.put(name, redissonClient);
    }

    public RedissonClient getClient(String name){
        return clients.get(name);
    }

}
