package com.kuwo.integralservice.config.uniqId;

import com.baidu.fsg.uid.utils.NetUtils;
import com.baidu.fsg.uid.worker.WorkerIdAssigner;
import com.baidu.fsg.uid.worker.WorkerNodeType;
import com.kuwo.integralservice.config.uniqId.service.NewWorkerNodeService;
import com.kuwo.integralservice.enity.NewWorkerNodeEntity;
import org.apache.commons.lang.math.RandomUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class MyWorkerIdAssigner implements WorkerIdAssigner {

    @Autowired
    private NewWorkerNodeService newWorkerNodeService;

    @Override
    public long assignWorkerId() {
        NewWorkerNodeEntity workerNodeEntity = new NewWorkerNodeEntity();
        workerNodeEntity.setType(WorkerNodeType.ACTUAL.value());
        workerNodeEntity.setHostName(NetUtils.getLocalAddress());
        workerNodeEntity.setPort(System.currentTimeMillis() + "-" + RandomUtils.nextInt(100000));
        NewWorkerNodeEntity newNode = newWorkerNodeService.addWorkerNode(workerNodeEntity);
        return newNode.getId();
    }
}
