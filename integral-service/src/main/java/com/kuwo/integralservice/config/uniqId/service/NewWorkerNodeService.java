package com.kuwo.integralservice.config.uniqId.service;

import com.kuwo.integralservice.enity.NewWorkerNodeEntity;
import com.kuwo.integralservice.mapper.integral.NewWorkNodeMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @Desc:
 * @date 2021-12-28 11:33:24
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class NewWorkerNodeService {

    @Autowired
    private NewWorkNodeMapper newWorkNodeMapper;


    public NewWorkerNodeEntity addWorkerNode(NewWorkerNodeEntity entity){
        int rows = newWorkNodeMapper.save(entity);
        return entity;
    }
}
