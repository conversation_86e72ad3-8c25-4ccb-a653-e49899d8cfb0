package com.kuwo.integralservice.config.redis;

import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Map;

@ConfigurationProperties(prefix = "redis", ignoreUnknownFields = true)
public class RedisPlusProperties {

    private String names;

    private Map<String, RedisProperties> config;


    public String getNames() {
        return names;
    }

    public void setNames(String names) {
        this.names = names;
    }

    public Map<String, RedisProperties> getConfig() {
        return config;
    }

    public void setConfig(Map<String, RedisProperties> config) {
        this.config = config;
    }
}
