package com.kuwo.integralservice.config.nacos;

import com.alibaba.cloud.nacos.registry.NacosAutoServiceRegistration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.stereotype.Component;

import javax.management.MBeanServer;
import javax.management.ObjectName;
import javax.management.Query;
import java.lang.management.ManagementFactory;
import java.util.Set;

/**
 * 解决tomcat 容器部署 nacos不进行注册的问题
 */
@ConditionalOnExpression("#{'prod'.equals(environment.getProperty('spring.profiles.active'))}")
@Component
public class NacosConfig implements ApplicationRunner {

    @Autowired(required = false)
    private NacosAutoServiceRegistration registration;

    @Value("${server.port}")
    Integer port;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        if (registration!=null && port!=null){
            Integer tomcatPort = port;
            tomcatPort = new Integer(getTomcatPort());
            registration.setPort(tomcatPort);
            registration.start();
        }
    }


    public String getTomcatPort() throws Exception{
        MBeanServer platformMBeanServer = ManagementFactory.getPlatformMBeanServer();
        Set<ObjectName> ob = platformMBeanServer.queryNames(new ObjectName("*:type=Connector,*"), Query.match(Query.attr("protocol"), Query.value("HTTP/1.1")));
        String port = ob.iterator().next().getKeyProperty("port");
        return port;
    }

}
