package com.kuwo.integralservice.cache;

import com.google.common.cache.Cache;
import com.kuwo.commercialization.common.cache.TTLCache;
import com.kuwo.integralservice.config.redis.DynamicChoiceRedissonClient;
import com.kuwo.integralservice.enity.MemberRankRights;
import com.kuwo.integralservice.mapper.integral.MemberRankRightsMapper;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
public class MemberRankRightsCache extends TTLCache<String, List<MemberRankRights>> {

    private static String myChangeKey = "memberRankRights";

    private MemberRankRightsMapper memberRankRightsMapper;

    private RedisReplaceConfig redisReplaceConfig;

    public MemberRankRightsCache(DynamicChoiceRedissonClient dynamicChoiceRedissonClient,
                                 MemberRankRightsMapper memberRankRightsMapper,
                                 RedisReplaceConfig redisReplaceConfig) {
        super(dynamicChoiceRedissonClient.getClient(redisReplaceConfig.getDefaultRedisName()), myChangeKey, 10, TimeUnit.MINUTES);
        this.memberRankRightsMapper = memberRankRightsMapper;
        this.redisReplaceConfig = redisReplaceConfig;
    }

    // key: memberRankIdentify
    @Override
    protected List<MemberRankRights> loadFromDb(String memberRankIdentify) {
        return memberRankRightsMapper.getRightsByRankIdentfy(memberRankIdentify);
    }

    // 关联权益的数据不易过多,这里有个Bug,就是没有加载的话加载不上来了，看看怎么优化
    @Override
    protected void initCache(Cache<String, List<MemberRankRights>> cache) {
        List<MemberRankRights> rankRights = memberRankRightsMapper.loadAllRankRights();
        if(rankRights!=null && !rankRights.isEmpty()){
            Map<String, List<MemberRankRights>> rightsMap = rankRights.stream().collect(Collectors.toMap(MemberRankRights::getRankIdentifyId, p -> {
                List<MemberRankRights> getNameList = new ArrayList<>();
                getNameList.add(p);
                return getNameList;
            }, (List<MemberRankRights> value1, List<MemberRankRights> value2) -> {
                value1.addAll(value2);
                return value1;
            }));
            for (String key: rightsMap.keySet()){
                cache.put(key, rightsMap.get(key));
                addCacheKey(key);
            }
        }
    }
}
