package com.kuwo.integralservice.cache;

import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Map;

/**
 * <AUTHOR>
 * @Desc: redis替换部分
 * @date 2023-12-13 21:01:55
 */
@Component
public class RedisReplaceConfig extends NacosRepository{

    private Logger log = LoggerFactory.getLogger(RedisReplaceConfig.class);
    private static String groupId = "common_conf";

    private static String dataId = "redis_switch";

    private static long readTimeout = 1000l * 10;

    private Gson gson;


    public RedisReplaceConfig(ConfigService configService) throws NacosException {
        super(configService, groupId, dataId, readTimeout);
    }

    /**
     * 默认d1，动态切换d2
     * @return
     */
    public String getDefaultRedisName(){
        if(this.data!=null){
            Map cache = (Map) this.data;
            return String.valueOf(cache.getOrDefault("defaultRedisName", "d1")) ;
        }
        return "d1";
    }


    /**
     * 用户尾号判断
     * @param userId
     * @return
     */
    public boolean useTencentRedis(String userId){
        if(this.data!=null){
            Map cache = (Map) this.data;
            String gray = String.valueOf(cache.getOrDefault("gray", "0"));
            if ("1".equals(gray.trim())){
                String endsValue = String.valueOf(cache.getOrDefault("endsValue", ""));
                return Arrays.stream(endsValue.split(",")).anyMatch(userId::endsWith);
            }
        }
        return false;
    }


    @Override
    protected Object convert(String configData) {
        if (this.gson == null){
            this.gson = new Gson();
        }
        Map cache = gson.fromJson(configData, Map.class);
        return cache;
    }
}
