package com.kuwo.integralservice.cache;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.api.config.listener.Listener;
import com.alibaba.nacos.api.exception.NacosException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

public abstract class NacosRepository implements Listener {

    private Logger log = LoggerFactory.getLogger(NacosRepository.class);

    private ConfigService configService;

    private String groupId;

    private String dataId;

    protected Object data;

    public NacosRepository(ConfigService configService, String groupId, String dataId, long readTimeout) throws NacosException {
        this.configService = configService;
        this.groupId = groupId;
        this.dataId = dataId;
        String configData = this.configService.getConfig(dataId, groupId, readTimeout);
        if (StringUtils.isNotBlank(configData)){
            this.data = convert(configData);
        }
        this.configService.addListener(dataId, groupId, this);
    }


    protected abstract Object convert(String configData);


    @Override
    public void receiveConfigInfo(String configInfo) {
        log.info("receive change by nacos, data -> {}", configInfo);
        if (StringUtils.isNotBlank(configInfo)){
            this.data = convert(configInfo);
        }
    }

    @Override
    public Executor getExecutor() {
        return new ThreadPoolExecutor(10, 20,
                15L, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(2000));
    }

    public <T> Boolean addJsonArrayConfig(List<T> increment) throws NacosException {
        if (CollectionUtils.isEmpty(increment)) {
            return Boolean.TRUE;
        }
        List<T> original = null;
        if (this.data != null) {
            original = (List<T>)this.data;
        }

        if (CollectionUtils.isNotEmpty(original)) {
            increment.addAll(original);
        }

        List<T> collect = increment.stream().distinct().collect(Collectors.toList());
        String publishContent = JSON.toJSONString(collect);
        return this.configService.publishConfig(dataId, groupId, publishContent, ConfigType.JSON.getType());
    }

    public <T> Boolean overwriteConfig(String content) throws NacosException {
        if (StringUtils.isBlank(content)) {
            return this.configService.publishConfig(dataId, groupId, "");
        }
        return this.configService.publishConfig(dataId, groupId, content);
    }
}
