package com.kuwo.integralservice.cache;

import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @Desc:
 * @date 2024-01-18 14:02:58
 */
@Component
public class ExceptionMockConfig extends NacosRepository implements ExceptionMockSense{

    private static String groupId = "mock";

    private static String dataId = "exception_config";

    private static long readTimeout = 1000l * 10;

    private volatile String cpu100 = "0";

    private Gson gson;
    private Logger log = LoggerFactory.getLogger(ExceptionMockConfig.class);

    public ExceptionMockConfig(ConfigService configService) throws NacosException {
        super(configService, groupId, dataId, readTimeout);
    }

    @Override
    protected Object convert(String configData) {
        if (this.gson == null){
            this.gson = new Gson();
        }
        Map cache = gson.fromJson(configData, Map.class);
        this.cpu100= String.valueOf(cache.getOrDefault("cpu100Switch", "0"));
        return cache;
    }


    public void slow_exec() {
        if(this.data!=null){
            Map cache = (Map) this.data;
            String slowRespSwitch = String.valueOf(cache.getOrDefault("slowRespSwitch", "0"));
            if ("1".equals(slowRespSwitch.trim())){
                String slowRespTimeOut = String.valueOf(cache.getOrDefault("slowRespTimeOut", "0"));
                slow_exec(Long.parseLong(slowRespTimeOut));
            }
        }
    }




    @Override
    public void cpu_100() {
        double random = 0;
        while ("1".equals(cpu100.trim())){
            random = Math.random();
        }
    }

    @Override
    public void makeException() throws Exception {
        ExceptionMockSense.super.makeException();
    }
}
