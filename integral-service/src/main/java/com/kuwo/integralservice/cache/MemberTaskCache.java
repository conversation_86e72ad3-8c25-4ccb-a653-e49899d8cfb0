package com.kuwo.integralservice.cache;

import com.google.common.cache.Cache;
import com.kuwo.commercialization.common.cache.RedisTTlEventType;
import com.kuwo.commercialization.common.cache.TTLCache;
import com.kuwo.commercialization.common.cache.TTLEvent;
import com.kuwo.integralservice.config.redis.DynamicChoiceRedissonClient;
import com.kuwo.integralservice.enity.MemberTask;
import com.kuwo.integralservice.mapper.integral.MemberTaskMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class MemberTaskCache extends TTLCache<String, MemberTask> {

    private final static Logger logger = LoggerFactory.getLogger(MemberTaskCache.class);

    public static final String myChangeKey = "memberTask";

    private MemberTaskMapper memberTaskMapper;

    // task id --> task
    private Map<String, MemberTask> otherMap = new ConcurrentHashMap<>();

    private RedisReplaceConfig redisReplaceConfig;

    public MemberTaskCache(DynamicChoiceRedissonClient dynamicChoiceRedissonClient,
                           MemberTaskMapper memberTaskMapper,
                           RedisReplaceConfig redisReplaceConfig) {
        super(dynamicChoiceRedissonClient.getClient(redisReplaceConfig.getDefaultRedisName()), myChangeKey);
        this.memberTaskMapper = memberTaskMapper;
        this.redisReplaceConfig = redisReplaceConfig;
    }

    @Override
    protected MemberTask loadFromDb(String key) {
        logger.info("load key -->{} ", key);
        MemberTask memberTask = memberTaskMapper.getAvailableTaskByIdentifyKey(key);
        if (memberTask!=null){
            otherMap.put(String.valueOf(memberTask.getId()), memberTask);
        }
        return  memberTask;
    }

    @Override
    protected void initCache(Cache<String, MemberTask> cache) {
        List<MemberTask> availableTasks = memberTaskMapper.getAvailableTasks();
        if (availableTasks!=null && !availableTasks.isEmpty()){
            availableTasks.forEach(k-> { cache.put(k.getTaskIdentify(), k); addCacheKey(k.getTaskIdentify()); otherMap.put(String.valueOf(k.getId()), k); });
        }
    }

    public MemberTask getTaskById(String taskId){
        return otherMap.get(taskId);
    }

    public List<MemberTask> getAllAvailableTasks(){
       return new ArrayList<>(otherMap.values());
    }

    /**
     * 初始化加载cache
     */
    public void clearCache(){
        getCache().invalidateAll();
        otherMap.clear();
        initCache(getCache());
    }

    @Override
    protected void changeCacheValue(TTLEvent event) {
        if (RedisTTlEventType.RELOAD_CACHE == event.getEventType() && myChangeKey.equals(event.getTtlChangeKey())){
            clearCache();
        }
    }
}
