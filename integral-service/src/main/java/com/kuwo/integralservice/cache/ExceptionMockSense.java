package com.kuwo.integralservice.cache;

/**
 * 异常模拟场景
 */
public interface ExceptionMockSense {

    default void slow_exec(long timeout){
        try {
            Thread.sleep(timeout);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }


    default void cpu_100(){

    }


    default void makeException() throws Exception {
        throw new Exception("MOCK EXCEPTION!");
    }






}
