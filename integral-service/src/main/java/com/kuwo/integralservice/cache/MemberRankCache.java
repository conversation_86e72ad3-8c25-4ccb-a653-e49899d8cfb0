package com.kuwo.integralservice.cache;

import com.google.common.cache.Cache;
import com.kuwo.commercialization.common.cache.TTLCache;
import com.kuwo.integralservice.config.redis.DynamicChoiceRedissonClient;
import com.kuwo.integralservice.enity.MemberRank;
import com.kuwo.integralservice.http.MemberGiveService;
import com.kuwo.integralservice.mapper.integral.MemberRankMapper;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
public class MemberRankCache extends TTLCache<String, List<MemberRank>> {

    private static String myChangeKey = "memberTask";

    public final String DEFAULT_KEY = "DEFAULT";

    private MemberRankMapper memberRankMapper;

    private MemberGiveService memberGiveService;

    private List<MemberRank> costMoneyVipRank;

    private MemberRank endNotCostMoneyMemberRank;

    private Map<String, Integer> scoreMap ;

    private RedisReplaceConfig redisReplaceConfig;

    public MemberRankCache(DynamicChoiceRedissonClient dynamicChoiceRedissonClient,
                           MemberRankMapper memberRankMapper,
                           MemberGiveService memberGiveService,
                           RedisReplaceConfig redisReplaceConfig) {
        super(dynamicChoiceRedissonClient.getClient(redisReplaceConfig.getDefaultRedisName()), myChangeKey, 30, TimeUnit.MINUTES);
        this.memberRankMapper = memberRankMapper;
        this.memberGiveService = memberGiveService;
        this.redisReplaceConfig = redisReplaceConfig;
    }

    @Override
    protected List<MemberRank> loadFromDb(String key) {
        List<MemberRank> allRank = memberRankMapper.getAllRank();
        if (allRank!=null&& !allRank.isEmpty()){
            this.costMoneyVipRank = allRank.stream().filter(memberRank ->memberRank.getRankType() == 2 && memberRank.getState()==1).collect(Collectors.toList());
            this.endNotCostMoneyMemberRank = allRank.stream().filter(memberRank -> memberRank.getRankType() == 1 && memberRank.getState()==1).reduce((first, end)-> end).get();
            this.scoreMap = allRank.stream().collect(HashMap::new, (m, e) -> m.put(e.getVipTag(), e.getMiniScore()), HashMap::putAll);
        }
        return allRank;
    }

    @Override
    protected void initCache(Cache<String, List<MemberRank>> cache) {
        List<MemberRank> memberRankList = memberRankMapper.getAllRank();
        if (memberRankList!=null && !memberRankList.isEmpty()){
            cache.put(DEFAULT_KEY, memberRankList);
            addCacheKey(DEFAULT_KEY);
            this.costMoneyVipRank = memberRankList.stream().filter(memberRank ->memberRank.getRankType() == 2 && memberRank.getState()==1).collect(Collectors.toList());
            this.endNotCostMoneyMemberRank = memberRankList.stream().filter(memberRank -> memberRank.getRankType() == 1 && memberRank.getState()==1).reduce((first, end)-> end).get();
            this.scoreMap = memberRankList.stream().collect(HashMap::new, (m, e) -> m.put(e.getVipTag(), e.getMiniScore()), HashMap::putAll);
        }
    }

    /**
     * 获取 identify key
     * @param score
     * @param userId
     * @return
     */
    public String getMemberRankIdentify(int score, Long userId){
        return getMemberRank(score, userId).getIdentifyKey();
    }

    /**
     * 获取 付费的vip 等级
     * @return
     */
    public List<MemberRank> getCostMoneyVipRank(){
        return this.costMoneyVipRank;
    }


    /**
     * 获取小于当前积分的几个会员等级，用来异步校验检查补权益
     * @param score
     * @return
     */
    public List<String> getLowerMemberRankIdentify(int score){
        List<String> tasks = new ArrayList<>();
        List<MemberRank> memberRanks = getValue(DEFAULT_KEY);
        if (memberRanks!=null&& !memberRanks.isEmpty()){
            tasks = memberRanks.stream().filter(memberRank ->
                            memberRank.getRankType() == 1 && memberRank.getMaxScore() < score && memberRank.getState() == 1)
                    .map(MemberRank::getIdentifyKey)
                    .collect(Collectors.toList());
        }
        return tasks;
    }


    /**
     * 获取当前会员等级部分
     * 1. 看是否匹配默认的等级 （不消费就能到达的等级）
     * 2. 如果积分超过限制 然后 查看当前会员是否是超级vip
     * 3. 从付费的等级中查找当前会员的等级是否有匹配的
     * @param score
     * @param userId
     * @return
     */
    public  MemberRank getMemberRank(int score, Long userId){
        List<MemberRank> memberRanks = getValue(DEFAULT_KEY);
        if (memberRanks!=null&& !memberRanks.isEmpty()){
            // 这块儿还得改下 豪华vip只能升级到V6满级后不再升级，开通超级会员后升级到V7
            MemberRank memberRankOptional = memberRanks.stream().filter(memberRank ->
                    memberRank.getRankType() == 1&& memberRank.getMaxScore() > score && score <= memberRank.getMaxScore() && memberRank.getState()==1)
                    .findFirst().orElseGet(()->{
                // 这里一般匹配不到是由于 超过积分限制了,此时
                List<MemberRank> costMoneyVipRank = getCostMoneyVipRank();
                if (costMoneyVipRank!=null&& !costMoneyVipRank.isEmpty()){
                    // 判断当前会员是否是超级会员
                    if (memberGiveService.isSuperVip(userId)){
                        Optional<MemberRank> rank = costMoneyVipRank.stream().filter(memberRank -> memberRank.getMaxScore() > score && score <= memberRank.getMaxScore()&& memberRank.getState() == 1).findFirst();
                        if (rank.isPresent()){
                            return rank.get();
                        }
                    }else {
                        // 默认取 rank type 为1 的最后一个等级
                        return endNotCostMoneyMemberRank;
                    }
                }
                return MemberRank.getDefault();
            });
            return memberRankOptional;
        }
        return MemberRank.getDefault();
    }

    /**
     * 获取所有可用的member rank
     * @return
     */
    public List<MemberRank> getAllAvailableRank(){
        return getValue(DEFAULT_KEY);
    }

    /**
     * 获取最小的积分
     * @param tag
     * @return
     */
    public Integer getMiniScore(String tag){
        if (this.scoreMap.containsKey(tag)){
            return this.scoreMap.get(tag);
        }
        return  -1;
    }
}
