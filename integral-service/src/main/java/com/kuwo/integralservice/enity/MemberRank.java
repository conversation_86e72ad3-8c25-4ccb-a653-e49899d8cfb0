package com.kuwo.integralservice.enity;

import com.kuwo.integralservice.vo.MemberRankVo;

import java.util.Date;

public class MemberRank {
    private Long id;
    private String vipTag;
    private String icon;
    private Integer miniScore;
    private Integer maxScore;
    // 1: 积分会员 2.直接充钱的这种
    private Integer rankType;
    private Integer sortValue;
    private Date createTime;
    private Date updateTime;

    private String identifyKey;

    private Integer state;

    private static MemberRank memberRank = new MemberRank();

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getVipTag() {
        return vipTag;
    }

    public void setVipTag(String vipTag) {
        this.vipTag = vipTag;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public Integer getMiniScore() {
        return miniScore;
    }

    public void setMiniScore(Integer miniScore) {
        this.miniScore = miniScore;
    }

    public Integer getMaxScore() {
        return maxScore;
    }

    public void setMaxScore(Integer maxScore) {
        this.maxScore = maxScore;
    }

    public Integer getRankType() {
        return rankType;
    }

    public void setRankType(Integer rankType) {
        this.rankType = rankType;
    }

    public Integer getSortValue() {
        return sortValue;
    }

    public void setSortValue(Integer sortValue) {
        this.sortValue = sortValue;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getIdentifyKey() {
        return identifyKey;
    }

    public void setIdentifyKey(String identifyKey) {
        this.identifyKey = identifyKey;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public MemberRankVo toVo(){
        MemberRankVo vo = new MemberRankVo();
        vo.setIcon(this.icon);
        vo.setMaxScore(this.maxScore);
        vo.setMiniScore(this.miniScore);
        vo.setVipTag(this.vipTag);
        vo.setOrder(this.sortValue);
        vo.setLv(this.vipTag.replace("VIP", ""));
        return vo;
    }

    public static MemberRank getDefault(){
        memberRank.setVipTag("");
        memberRank.setIcon("");
        return memberRank;
    }
}
