package com.kuwo.integralservice.enity;

import java.util.Date;

/**
 * 用户积分记录信息实体
 */
public class UserScoreRecord {
    private Long id;
    private Long memberScoreId;
    private Integer actionType;
    private Integer score;
    private String fromParentTask;
    private String fromTask;
    private String remark;
    private Date createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getMemberScoreId() {
        return memberScoreId;
    }

    public void setMemberScoreId(Long memberScoreId) {
        this.memberScoreId = memberScoreId;
    }

    public Integer getActionType() {
        return actionType;
    }

    public void setActionType(Integer actionType) {
        this.actionType = actionType;
    }

    public Integer getScore() {
        return score;
    }

    public void setScore(Integer score) {
        this.score = score;
    }

    public String getFromParentTask() {
        return fromParentTask;
    }

    public void setFromParentTask(String fromParentTask) {
        this.fromParentTask = fromParentTask;
    }

    public String getFromTask() {
        return fromTask;
    }

    public void setFromTask(String fromTask) {
        this.fromTask = fromTask;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
