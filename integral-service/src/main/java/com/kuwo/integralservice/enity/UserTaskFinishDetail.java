package com.kuwo.integralservice.enity;

import java.util.Date;

/**
 * <AUTHOR>
 * @Desc: 用户任务完成情况
 * @date 2022-02-17 17:53:16
 */
public class UserTaskFinishDetail {

    private Long id;
    private Long memberScoreId;
    private Long parentTaskId;
    private Long taskId;
    private int status;
    private Date createTime;
    private Date updateTime;

    private String remark;
    // 原始任务id
    private Long originTaskId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getMemberScoreId() {
        return memberScoreId;
    }

    public void setMemberScoreId(Long memberScoreId) {
        this.memberScoreId = memberScoreId;
    }

    public Long getParentTaskId() {
        return parentTaskId;
    }

    public void setParentTaskId(Long parentTaskId) {
        this.parentTaskId = parentTaskId;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 修改下任务状态
     * @param taskStatus
     */
    public void setTaskStatus(TASK_STATUS taskStatus){
        switch (taskStatus){
            case FINISH_TASK:
                this.status = 1;
                break;
            case LINK_SCORE_TASK:
                this.status = 2;
                break;
            default:
                this.status = -1;
                break;
        }
    }


    public static enum TASK_STATUS{
        FINISH_TASK,
        WAIT_FINISH_TASK,
        LINK_SCORE_TASK,
        ;
    }

    public Long getOriginTaskId() {
        return originTaskId;
    }

    public void setOriginTaskId(Long originTaskId) {
        this.originTaskId = originTaskId;
    }
}
