package com.kuwo.integralservice.enity;

import com.kuwo.integralservice.cache.MemberRankCache;
import com.kuwo.integralservice.vo.UserScoreVo;

import java.util.Date;
import java.util.Optional;

/**
 * 用户积分实体类
 */
public class UserScore {

    private Long id;

    // 使用唯一id生成器生成id,待实现
    private Long memberScoreId;

    private Long userId;

    private Integer version;

    // 积分
    private int score;

    private Date createTime;

    private Date updateTime;

    // 用户类型, 1 酷我用户
    private Integer userType;

    // 荣耀vip标识
    private Integer svipFlag;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getMemberScoreId() {
        return memberScoreId;
    }

    public void setMemberScoreId(Long memberScoreId) {
        this.memberScoreId = memberScoreId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public int getScore() {
        return score;
    }

    public void setScore(int score) {
        this.score = score;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    public Integer getSvipFlag() {
        return svipFlag;
    }

    public void setSvipFlag(Integer svipFlag) {
        this.svipFlag = svipFlag;
    }

    public UserScoreVo toVo(MemberRankCache memberRankCache) {
        UserScoreVo userScoreVo =new UserScoreVo();
        userScoreVo.setScore(score);
        MemberRank memberRank = memberRankCache.getMemberRank(score, userId);
        userScoreVo.setVipTag(memberRank.getVipTag());
        return userScoreVo;
    }
}
