package com.kuwo.integralservice.enity;

import java.util.Date;

/**
 * 会员任务相关实体
 */
public class MemberTask  implements Comparable{
    private Long id;
    // 父类型 id ,没有的话 -1
    private Long parentId;
    // 任务类型： 消费类任务
    private String taskType;
    private String taskName;
    private String taskIcon;
    private String taskDesc;
    private Integer addScore;
    private Integer status;
    private String taskIdentify;
    private Date updateTime;
    private Date createTime;
    private int sortValue;
    // 是否隐藏 1 前台隐藏任务
    private int isHide;
    // 隐藏任务所关联的显示任务id, 用来完成相应的任务
    private Long relateDisplayId;
    // 适用平台
    private String adPlat;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getTaskType() {
        return taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getTaskIcon() {
        return taskIcon;
    }

    public void setTaskIcon(String taskIcon) {
        this.taskIcon = taskIcon;
    }

    public String getTaskDesc() {
        return taskDesc;
    }

    public void setTaskDesc(String taskDesc) {
        this.taskDesc = taskDesc;
    }

    public Integer getAddScore() {
        return addScore;
    }

    public void setAddScore(Integer addScore) {
        this.addScore = addScore;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getTaskIdentify() {
        return taskIdentify;
    }

    public void setTaskIdentify(String taskIdentify) {
        this.taskIdentify = taskIdentify;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public int getSortValue() {
        return sortValue;
    }

    public void setSortValue(int sortValue) {
        this.sortValue = sortValue;
    }

    public int getIsHide() {
        return isHide;
    }

    public void setIsHide(int isHide) {
        this.isHide = isHide;
    }

    public boolean notHide(){
       return isHide==1?false:true;
    }

    @Override
    public int compareTo(Object o) {
        MemberTask other = (MemberTask) o;
        if (this.sortValue == other.sortValue){
            return Math.toIntExact(other.id- this.id);
        }
        return this.sortValue-other.sortValue;
    }

    public Long getRelateDisplayId() {
        return relateDisplayId;
    }

    public void setRelateDisplayId(Long relateDisplayId) {
        this.relateDisplayId = relateDisplayId;
    }

    public String getAdPlat() {
        return adPlat;
    }

    public void setAdPlat(String adPlat) {
        this.adPlat = adPlat;
    }
}
