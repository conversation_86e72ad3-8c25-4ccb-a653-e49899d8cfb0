package com.kuwo.integralservice.http;

import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixProperty;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Desc:
 * @date 2022-03-24 10:38:13
 */
@Component
public class TestService {
    @HystrixCommand(fallbackMethod = "testB", commandProperties = {
            @HystrixProperty(name="execution.isolation.thread.timeoutInMilliseconds", value = "1000")
    })
    public String testA(){

        try {
            Thread.sleep(5000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return "11111";
    }

    public String testB(){
        return "22222";
    }

    public String testC(){
        return ((TestService)AopContext.currentProxy()).testA();
    }

}
