package com.kuwo.integralservice.http;

import com.google.gson.Gson;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixProperty;
import io.micrometer.core.annotation.Timed;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.Map;

/**
 * http请求会员相关服务
 */
@Component
public class MemberGiveService {

    private Logger logger = LoggerFactory.getLogger(MemberGiveService.class);

    @Autowired
    private OkHttpClient httpClient;

    private Gson gson = new Gson();
    @Value("${vip.domain}")
    private String vipDomain;

    /**
     * 赠送会员卡接口
     * @param uid 用户id
     * @param cnt  会员卡张数
     * @param days 天数
     * @return
     * @throws IOException
     */
    @Timed(value = "MemberGiveService#giveMemberCard", percentiles = {0.5, 0.9, 0.95, 0.99}, histogram = true)
    public String giveMemberCard(Long uid, int cnt, int days) throws IOException {
        long begin = System.currentTimeMillis();
        String url = String.format("%s/vip/manage/new?op=giveVipCardByDays&days=%s&cnt=%s&uid=%s&productTypeId=21", vipDomain,days,cnt, uid);
        Request request = new Request.Builder().url(url).build();
        Response response = httpClient.newCall(request).execute();
        String result = "";
        if (response.isSuccessful()){
            result = response.body().string();
        }
        logger.info("give memberCard, url ->{}, result->{}, cost:{}ms", url, result, System.currentTimeMillis()-begin);
        response.close();
        return result;
    }

    /**
     * 赠送会员天数
     * @param uid
     * @param days
     * @return
     * @throws IOException
     */
    @Timed(value = "MemberGiveService#giveMemberDay", percentiles = {0.5, 0.9, 0.95, 0.99},histogram = true)
    public String giveMemberDay(Long uid, int days) throws IOException {
        // pid 是会员类型
        long begin = System.currentTimeMillis();
        //fromsrc
        String url = String.format("%s/vip/manage/new?op=giveVipDaysByUid&uid=%s&pid=12&fromSrc=%s&days=%s", vipDomain,uid,"vipRank",days);
        Request request = new Request.Builder().url(url).build();
        Response response = httpClient.newCall(request).execute();
        String result = "";
        if (response.isSuccessful()){
            result = response.body().string();
        }
        logger.info("give memberDay, url ->{}, result->{}, cost:{}ms", url, result, System.currentTimeMillis()-begin);
        response.close();
        return result;
    }

    /**
     * 查询用户vip信息
     * @param uid
     */
    @HystrixCommand(fallbackMethod = "getUserVipFallBack", commandProperties = {
           @HystrixProperty(name="execution.isolation.thread.timeoutInMilliseconds", value = "3000")
    })
    @Timed(value = "MemberGiveService#getUserVipInfo", percentiles = {0.5, 0.9, 0.95, 0.99})
    public String getUserVipInfo(Long uid) throws IOException {
        long begin = System.currentTimeMillis();
        String url = String.format("%s/vip/v2/user/vip?uid=%s&op=ui", vipDomain,uid);
        Request request = new Request.Builder().url(url).build();
        Response response = httpClient.newCall(request).execute();
        String result = "";
        if (response.isSuccessful()){
            result = response.body().string();
        }
        logger.debug("get user member info, url ->{}, result->{}, cost:{}ms", url, result, System.currentTimeMillis()-begin);
        response.close();
        return result;
    }

    public String getUserVipFallBack(Long uid)throws IOException{
        logger.info("enter in fail back method -> {}", uid);
        return "";
    }


    /**
     * 判断用户是否为豪华vip
     * @param uid
     * @return
     */
    public boolean isLuxVip(Long uid){
        String vipInfo = "";
        for (int i=0;i<3; i++){
            try {
                vipInfo = ((MemberGiveService)AopContext.currentProxy()).getUserVipInfo(uid);
                if (StringUtils.isNotBlank(vipInfo)){
                    break;
                }
            } catch (Exception e) {
                continue;
            }
        }
        if (StringUtils.isNotBlank(vipInfo)){
            Map map = gson.fromJson(vipInfo, Map.class);
            Map linkedHashMap = (Map) map.get("data");
            if (linkedHashMap.containsKey("vipLuxuryExpire")){
                String vipLuxuryExpire = (String) linkedHashMap.get("vipLuxuryExpire");
                if (Long.parseLong(vipLuxuryExpire) - System.currentTimeMillis() >0 ){
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 判断用户是不是超级会员,
     * @return
     */
    public boolean isSuperVip(Long uid){
        String vipInfo = "";
        for (int i=0;i<3; i++){
            try {
                vipInfo = ((MemberGiveService)AopContext.currentProxy()).getUserVipInfo(uid);
                if (StringUtils.isNotBlank(vipInfo)){
                    break;
                }
            } catch (Exception e) {
                continue;
            }
        }
        if (StringUtils.isNotBlank(vipInfo)){
            Map map = gson.fromJson(vipInfo, Map.class);
            Map linkedHashMap = (Map) map.get("data");
            if (linkedHashMap.containsKey("svipExpire")){
                String svipExpire = (String) linkedHashMap.get("svipExpire");
                if (Long.parseLong(svipExpire) - System.currentTimeMillis() >0 ){
                    return true;
                }
            }
        }
        return false;
    }

    /**
     *  3 超级会员
     *  2. 豪华vip
     *  1. 音乐包
     *  0. 非会员
     * @param uid
     * @return
     */
    public int getUserMemberType(Long uid){
        String vipInfo = "";
        for (int i=0;i<3; i++){
            try {
                vipInfo = ((MemberGiveService)AopContext.currentProxy()).getUserVipInfo(uid);
                if (StringUtils.isNotBlank(vipInfo)){
                    break;
                }
            } catch (Exception e) {
                continue;
            }
        }
        if (StringUtils.isNotBlank(vipInfo)){
            Map map = gson.fromJson(vipInfo, Map.class);
            Map linkedHashMap = (Map) map.get("data");
            if (linkedHashMap.containsKey("svipExpire")){
                String svipExpire = (String) linkedHashMap.get("svipExpire");
                if (Long.parseLong(svipExpire) - System.currentTimeMillis() >0 ){
                    return 3;
                }
            }
            if (linkedHashMap.containsKey("vipLuxuryExpire")){
                String vipLuxuryExpire = (String) linkedHashMap.get("vipLuxuryExpire");
                if (Long.parseLong(vipLuxuryExpire) - System.currentTimeMillis() >0 ){
                    return 2;
                }
            }
            if (linkedHashMap.containsKey("vipmExpire")){
                String vipmExpire = (String) linkedHashMap.get("vipmExpire");
                if (Long.parseLong(vipmExpire) - System.currentTimeMillis() >0 ){
                    return 1;
                }
            }
        }
        return 0;
    }



    /**
     * 通知用户行为数据
     * @param uid
     * @param action
     * @param extra
     * @throws IOException
     */
    public void notifyUserActions(String uid, String action, String extra, String msg) throws IOException {
        long begin = System.currentTimeMillis();
        String url = String.format("%s/commercia/vip/player/handlePri?op=handlePriType&uid=%s&action=%s&extra=%s", vipDomain,uid,action, URLEncoder.encode("","UTF-8"));
        Request request = new Request.Builder().url(url)
                .post(RequestBody.create(MediaType.parse("application/json"), msg))
                .build();
        Response response = httpClient.newCall(request).execute();
        String result = "";
        if (response.isSuccessful()){
            result = response.body().string();
        }
        logger.debug("notify user action, url ->{}, result->{}, cost:{}ms", url, result, System.currentTimeMillis()-begin);
        response.close();
    }

}
