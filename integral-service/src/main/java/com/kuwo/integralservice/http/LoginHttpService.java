package com.kuwo.integralservice.http;

import io.micrometer.core.annotation.Timed;
import io.micrometer.core.instrument.MeterRegistry;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Desc:
 * @date 2021-12-21 15:58:55
 */
@Component
public class LoginHttpService {

    @Autowired
    private MeterRegistry meterRegistry;
    private Logger logger = LoggerFactory.getLogger(LoginHttpService.class);

    @Value("${login.check.url}")
    private String loginUrl;

    @Autowired
    private OkHttpClient httpClient;

    @Timed(value = "LoginHttpService#checkLogin", percentiles = {0.5, 0.9, 0.95, 0.99}, histogram = true)
    public boolean checkLogin(String userId, String webSid) throws Exception {
        logger.info("校验用户登录 -> 入参： userId: {}, sid:{}", userId, webSid);
        boolean hasLogin = false;
        boolean isUserIdValid = true;
        String newSid = "";
        try {
            Long uidLong = Long.valueOf(userId);
            if (uidLong == 0) {
                isUserIdValid = false;
            }
        } catch (Exception e) {
            isUserIdValid = false;
        }
        if (!isUserIdValid) {
            logger.error("noValidUid,  userId: " + userId + " ,webSid: " + webSid);
            return false;
        }
        long start = System.currentTimeMillis();
        String url = String.format(loginUrl, userId, webSid);
        String checkLoginUrl = url.replaceAll(" ", "");
        Request request = new Request.Builder().url(url).build();
        Response response = httpClient.newCall(request).execute();
        if (response.isSuccessful()){
             newSid = Objects.requireNonNull(response.body().string());
            int pos = newSid.indexOf("result=ok");
            if (pos < 0) {
                //验证表明没有登录，需要重新登录
                hasLogin = false;
            } else {
                hasLogin = true;
            }
        }
        // 客户端主动关闭连接试试
        response.close();
        logger.info(String.format(" checkLogin, url:%s\tcost:%s\thasLogin:%s\tresult:%s", checkLoginUrl, (System.currentTimeMillis() - start), hasLogin, newSid));
        return hasLogin;
    }



}
