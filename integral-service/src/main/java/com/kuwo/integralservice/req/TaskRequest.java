package com.kuwo.integralservice.req;


import com.google.gson.Gson;
import com.kuwo.commercialization.common.message.BasicMessage;
import com.kuwo.commercialization.common.message.MessageType;
import com.kuwo.commercialization.common.utill.MD5;

import java.util.StringJoiner;

public class TaskRequest {
    private String uid;
    private String sid;
    private Integer action;
    private String extra="";
    private Long ts;
    private String nonce;
    private String reqId;
    private String sign;



    public boolean validateSign(String privateKey){
        StringJoiner joiner = new StringJoiner("");
        joiner.add(uid);
        joiner.add(sid);
        joiner.add(String.valueOf(action));
        joiner.add(extra);
        joiner.add(String.valueOf(ts));
        joiner.add(nonce);
        joiner.add(reqId);
        String waitData = joiner.toString();
        String md5Sign = MD5.getMD5ofStr(waitData + privateKey).toUpperCase();
        return this.sign.equals(md5Sign);
    }

    public boolean checkTime(int delta){
        int n = String.valueOf(ts).length();
        if (n == 10){
            ts = ts * 1000;
        }
        if (System.currentTimeMillis() - ts > delta *60* 1000){
            return false;
        }
        return true;
    }

    public String toMessage(){
        BasicMessage basicMessage = new BasicMessage(MessageType.CLIENT_FINISHED_TASK, this);
        return basicMessage.toKafkaMessage();
    }

    public String toJson(){
        Gson gson = new Gson();
        return gson.toJson(this);
    }


    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getSid() {
        return sid;
    }

    public void setSid(String sid) {
        this.sid = sid;
    }

    public Integer getAction() {
        return action;
    }

    public void setAction(Integer action) {
        this.action = action;
    }

    public String getExtra() {
        return extra;
    }

    public void setExtra(String extra) {
        this.extra = extra;
    }

    public Long getTs() {
        return ts;
    }

    public void setTs(Long ts) {
        this.ts = ts;
    }

    public String getNonce() {
        return nonce;
    }

    public void setNonce(String nonce) {
        this.nonce = nonce;
    }

    public String getReqId() {
        return reqId;
    }

    public void setReqId(String reqId) {
        this.reqId = reqId;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

}
