package com.kuwo.integralservice.req;

import com.kuwo.commercialization.common.utill.MD5;
import com.kuwo.integralservice.cenum.ActionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * 修改score相关请求
 */
public class ScoreModifyRequest implements Serializable {


    private final static Logger logger = LoggerFactory.getLogger(ScoreModifyRequest.class);

    private String userId;
    // 完成某个任务
    private String taskIdentify;

    // 积分操作类型, 1 增加 0减少
    private int actionType;

    private String remark;

    /**
     * 这个是为了防止重复调用导致积分重复增加
     */
    private String requestId;

    /**
     * 调用来源，用来区分从哪儿调用的
     */
    private String invokeSource;

    /**
     * 特殊类任务，此时才取这个值
     *  taskType: special
     */
    private int score;

    private long ts;

    private String nonce;

    private String sign;

    /**
     * 修改类型
     * normal 表示正常的加
     * upGrade 直接升级
     */
    private String  modifyType = "normal";

    /**
     * 其它参数
     */
    private String otherParams = "";

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getTaskIdentify() {
        return taskIdentify;
    }

    public void setTaskIdentify(String taskIdentify) {
        this.taskIdentify = taskIdentify;
    }

    public int getActionType() {
        return actionType;
    }

    public void setActionType(int actionType) {
        this.actionType = actionType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public ActionType getsActionType(){
       return actionType == 1?ActionType.ADD:ActionType.SUB;
    }

    public long getTs() {
        return ts;
    }

    public void setTs(long ts) {
        this.ts = ts;
    }

    public String getNonce() {
        return nonce;
    }

    public void setNonce(String nonce) {
        this.nonce = nonce;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getInvokeSource() {
        return invokeSource;
    }

    public void setInvokeSource(String invokeSource) {
        this.invokeSource = invokeSource;
    }

    public String getModifyType() {
        return modifyType;
    }

    public void setModifyType(String modifyType) {
        this.modifyType = modifyType;
    }

    public String getOtherParams() {
        return otherParams;
    }

    public void setOtherParams(String otherParams) {
        this.otherParams = otherParams;
    }

    public boolean validateRequest(String privateKey){
        StringJoiner joiner = new StringJoiner("&@#!3194");
        joiner.add(userId);
        joiner.add(requestId);
        joiner.add(invokeSource);
        joiner.add(taskIdentify);
        joiner.add(String.valueOf(actionType));
        joiner.add(String.valueOf(score));
        joiner.add(remark);
        joiner.add(String.valueOf(ts));
        joiner.add(nonce);
        String waitData = joiner.toString();
        String md5Sign = MD5.getMD5ofStr(waitData + privateKey).toUpperCase();
        logger.info("request sign ->{}, our sign is ->{}", sign, md5Sign);
        if (!this.sign.equals(md5Sign)){
            return false;
        }
        if (Math.abs(System.currentTimeMillis() - ts)  < 1*60*1000){
            logger.info("time validate ok!");
            return true;
        }
        return false;
    }

    public int getScore() {
        return score;
    }

    public void setScore(int score) {
        this.score = score;
    }
}
