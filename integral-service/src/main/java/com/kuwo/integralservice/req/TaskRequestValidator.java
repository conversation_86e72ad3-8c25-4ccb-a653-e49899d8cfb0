package com.kuwo.integralservice.req;

import com.kuwo.commercialization.common.cenum.Result;
import com.kuwo.integralservice.http.LoginHttpService;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;

public class TaskRequestValidator {

    private TaskRequest taskRequest;

    private LoginHttpService loginHttpService;

    private RedissonClient redissonClient;

    private String limitKey;


    private TaskRequestValidator(TaskRequest taskRequest) {
        this.taskRequest = taskRequest;
    }

    private void setLoginHttpService(LoginHttpService loginHttpService) {
        this.loginHttpService = loginHttpService;
    }

    private void setRedissonClient(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }

    private void setLimitKey(String limitKey) {
        this.limitKey = limitKey;
    }


    public static class Builder{

        private TaskRequest taskRequest;

        private LoginHttpService loginHttpService;

        private RedissonClient redissonClient;

        private String limitKey;



        public Builder taskRequest(TaskRequest t){
            taskRequest = t;
            return this;
        }

        public Builder loginHttpService(LoginHttpService t){
            loginHttpService = t;
            return this;
        }
        public Builder redissonClient(RedissonClient t){
            redissonClient = t;
            return this;
        }

        public Builder limitKey(String t){
            limitKey = t;
            return this;
        }

        public TaskRequestValidator build(){
            TaskRequestValidator validator = new TaskRequestValidator(taskRequest);
            validator.setLimitKey(limitKey);
            validator.setLoginHttpService(loginHttpService);
            validator.setRedissonClient(redissonClient);
            return validator;
        }

    }


    public ValidatorResult validate()throws Exception{
        Result result = Result.SUCCESS;
        // 1. check sessionId
        boolean isLogin = loginHttpService.checkLogin(taskRequest.getUid(), taskRequest.getSid());
        if (!isLogin){
            result = Result.USER_NOT_LOGIN;
        }
       // RMapCache<Object, Object> mapCache = redissonClient.getMapCache(limitKey);
//        String reqId = taskRequest.getReqId();
//        // 包含,说明之前已经处理过了
//        if (mapCache.containsKey(reqId)){
//            result =Result.HANDLE_ALREADY;
//        }
        if (!taskRequest.checkTime(1)){
            result = Result.EXPIRE_REQUEST;
        }
        ValidatorResult validatorResult = new ValidatorResult();
       // validatorResult.mapCache = mapCache;
        validatorResult.result = result;
        return validatorResult;
    }

    public static class ValidatorResult{
        public Result result;
        public RMapCache<Object, Object> mapCache;
    }


}
