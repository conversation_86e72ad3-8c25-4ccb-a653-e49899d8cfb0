package com.kuwo.integralservice.handler.task;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class TaskHandlerHelper implements InitializingBean {

    @Autowired
    private List<TaskHandle> handlers;

    private final Map<String, TaskHandle> container = new HashMap<>();


    @Override
    public void afterPropertiesSet() throws Exception {
        if (handlers!=null){
            handlers.forEach(taskHandler -> container.put(taskHandler.supportTaskType().getTaskTypeName(), taskHandler));
        }
    }

    public TaskHandle getTaskHandleByTaskType(String taskType){
        return container.get(taskType);
    }


}