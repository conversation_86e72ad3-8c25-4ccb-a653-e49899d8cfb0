package com.kuwo.integralservice.handler.rights.member;

import com.google.gson.Gson;
import com.google.gson.internal.LinkedTreeMap;
import com.kuwo.integralservice.handler.rights.RightContext;
import com.kuwo.integralservice.handler.rights.RightHandler;
import com.kuwo.integralservice.handler.rights.RightIdentifyConstant;
import com.kuwo.integralservice.http.MemberGiveService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 赠送会员时长
 */
@Component
public class MemberDayRightHandler implements RightHandler {

    private Logger logger = LoggerFactory.getLogger(MemberDayRightHandler.class);

    @Autowired
    private MemberGiveService memberGiveService;

    private Gson gson = new Gson();

    @Override
    public String getRightIdentifyKey() {
        return RightIdentifyConstant.ADD_MEMBER_DAY.getKey();
    }

    @Override
    public boolean addRight(RightContext rightContext) {
        String developerParams = rightContext.getDeveloperParams();
        Map map = gson.fromJson(developerParams, Map.class);
        long uid =rightContext.getUserScore().getUserId();
        LinkedTreeMap memberMap = new LinkedTreeMap();
        // 判断当前用户类型,然后取相应的用户类型的权益进行发放
        int userMemberType = memberGiveService.getUserMemberType(uid);
        if (userMemberType == 0){
            // 当当前用户是非会员时,暂时不发放权益,等是会员的时候在进行发放
            return false;
        }
        if (userMemberType == 3){
            if (map.containsKey("svip")){
                memberMap = (LinkedTreeMap) map.get("svip");
            }
        }
        if (userMemberType == 2){
            if (map.containsKey("luxVip")){
                memberMap = (LinkedTreeMap) map.get("luxVip");
            }
        }
        int days = -1;
        if (memberMap.containsKey("day")){
            days = new BigDecimal(String.valueOf(memberMap.get("day"))).intValue();;
        }
        if (days<0){
            logger.error("add member day, config day error -> {}, user type {}", days, userMemberType);
            return true;
        }
        try {
            memberGiveService.giveMemberDay(uid,days);
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    @Override
    public String getRemark() {
        return RightIdentifyConstant.ADD_MEMBER_DAY.getDesc();
    }
}
