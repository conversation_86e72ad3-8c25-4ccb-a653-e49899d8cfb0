package com.kuwo.integralservice.handler.task.sp;

import com.kuwo.commercialization.common.utill.DateUtil;
import com.kuwo.integralservice.cenum.ActionType;
import com.kuwo.integralservice.cenum.TaskType;
import com.kuwo.integralservice.enity.MemberTask;
import com.kuwo.integralservice.enity.UserScore;
import com.kuwo.integralservice.enity.UserScoreRecord;
import com.kuwo.integralservice.handler.task.TaskContext;
import com.kuwo.integralservice.handler.task.TaskHandle;
import com.kuwo.integralservice.service.MemberScoreService;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 普通类型任务处理
 */
@Component
public class NormalTaskHandler implements TaskHandle {

    /**
     * 用来限制是否可以完成任务
     * @return
     */
    @Override
    public boolean canFinishTask(TaskContext taskContext){
        return true;
    }


    /**
     *  用来先这是否可以加积分
     * @return
     */
    @Override
    public boolean needLimitAddScore(TaskContext taskContext, ActionType actionType, Date time, MemberScoreService memberScoreService){
        UserScore userScore = taskContext.getUserScore();
        MemberTask memberTask = taskContext.getMemberTask();
        Long memberScoreId = userScore.getMemberScoreId();
        Integer addScore = memberTask.getAddScore();
        // 查看之前这个任务是否完成过，如果完成的话就直接返回false
        UserScoreRecord userScoreRecord = memberScoreService.getUserScoreRecord(memberScoreId, memberTask.getParentId(), memberTask.getId(), time);
        if (userScoreRecord!=null){
            return true;
        }
        String numAfterOneDay = DateUtil.getNumAfterDay(time, 1);
        String curDay = DateUtil.getNumAfterDay(time, 0);
        String monthFirstDay = DateUtil.getMonthFirstDay(time);
        String monthEndDay = DateUtil.getMonthEndDay(time);
        int oneDayScore = memberScoreService.getNormalTaskUserScoreByRange(curDay,numAfterOneDay, memberScoreId, actionType);
        int monthScore = memberScoreService.getNormalTaskUserScoreByRange(monthFirstDay,monthEndDay, memberScoreId, actionType);
        if (actionType == ActionType.ADD){
            // 经过确认，如果这里加的积分大于目标值，这里就会直接进行限制
            // 判断条件1： 每日上限15点
            if (oneDayScore + addScore > 15){
                return true;
            }
            // 判断条件2： 一个月450点上限
            if (monthScore + addScore > 400){
                return true;
            }
        }
        return false;
    }
    

    @Override
    public TaskType supportTaskType(){
        return TaskType.NORMAL_TASK;
    }

}
