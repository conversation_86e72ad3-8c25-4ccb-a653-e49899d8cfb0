package com.kuwo.integralservice.handler.message;

import com.kuwo.commercialization.common.message.BasicMessage;
import com.kuwo.integralservice.enity.MessageConsume;
import com.kuwo.integralservice.service.MessageConsumeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class MessageHandlerAdapter {

    @Autowired
    private MessageHandlerHelper messageHandlerHelper;

    @Autowired
    private MessageConsumeService messageConsumeService;

    public boolean  handle(String msgId, BasicMessage basicMessage){
        boolean msgHandleResult = false;
        // 数据库消息记录
        MessageConsume messageConsume = messageConsumeService.getOrCreateMessage(msgId, basicMessage);
        if (messageConsume ==null){
            return false;
        }
        // 消息幂等处理
        if (messageConsume.getStatus() == 1){
            return true;
        }
        Long id = messageConsume.getId();
        MessageHandler handler = messageHandlerHelper.matchHandler(basicMessage.getMessageType());
        if (handler!=null){
            MessageHandler.HandleResult handleResult = handler.handleMessage(basicMessage);
            msgHandleResult = handleResult.businessResult;
            String errorMessage = "";
            int status = 1;
            if (!handleResult.businessResult){
                status = 0 ;
                errorMessage = handleResult.errorMessage;
            }
            messageConsumeService.updateMessageStatus(id, status, errorMessage);
        }else {
            messageConsumeService.updateMessageStatus(id, 0, "KAFKA_MESSAGE_HANDLER_NOT_FOUND");
        }
        return msgHandleResult;
    }

}
