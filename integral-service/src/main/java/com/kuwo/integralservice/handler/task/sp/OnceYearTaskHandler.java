package com.kuwo.integralservice.handler.task.sp;

import com.kuwo.integralservice.cenum.ActionType;
import com.kuwo.integralservice.cenum.TaskType;
import com.kuwo.integralservice.enity.MemberTask;
import com.kuwo.integralservice.enity.UserScore;
import com.kuwo.integralservice.handler.task.TaskContext;
import com.kuwo.integralservice.handler.task.TaskHandle;
import com.kuwo.integralservice.service.MemberScoreService;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
public class OnceYearTaskHandler implements TaskHandle {

    @Override
    public boolean canFinishTask(TaskContext taskContext) {
        UserScore userScore = taskContext.getUserScore();
        MemberTask memberTask = taskContext.getMemberTask();
        // 查询一年内是否有相应的任务完成



        return false;
    }

    @Override
    public boolean needLimitAddScore(TaskContext taskContext, ActionType actionType, Date time, MemberScoreService memberScoreService) {
        // todo: 再加个任务类型进行限制， 比如生日这种的只能完成1次都任务 (数据看里面查询之前是否完成，如果完成就不能进行领取)
        return false;
    }


    @Override
    public TaskType supportTaskType() {
        return TaskType.ONCE_YEAR_TASK;
    }
}
