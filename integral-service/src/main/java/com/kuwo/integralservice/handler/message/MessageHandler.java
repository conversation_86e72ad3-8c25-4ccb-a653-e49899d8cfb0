package com.kuwo.integralservice.handler.message;

import com.kuwo.commercialization.common.message.BasicMessage;
import com.kuwo.commercialization.common.message.MessageType;

public interface MessageHandler {

    /**
     * 获取消息类型
     * @return
     */
    public MessageType getMessageType();

    /**
     * 处理kafka消息
     * @param basicMessage
     * @return
     */
    public HandleResult handleMessage(BasicMessage basicMessage);



    public static class HandleResult{
        public boolean businessResult;
        public String errorMessage;

    }


}
