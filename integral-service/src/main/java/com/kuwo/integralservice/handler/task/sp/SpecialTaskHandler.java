package com.kuwo.integralservice.handler.task.sp;

import com.kuwo.integralservice.cenum.ActionType;
import com.kuwo.integralservice.cenum.TaskType;
import com.kuwo.integralservice.handler.task.TaskContext;
import com.kuwo.integralservice.handler.task.TaskHandle;
import com.kuwo.integralservice.service.MemberScoreService;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
public class SpecialTask<PERSON>and<PERSON> implements TaskHandle {

    @Override
    public boolean canFinishTask(TaskContext taskContext) {
        return false;
    }

    @Override
    public boolean needLimitAddScore(TaskContext taskContext, ActionType actionType, Date time, MemberScoreService memberScoreService) {
        return false;
    }


    @Override
    public TaskType supportTaskType() {
        return TaskType.SPECIAL_TASK;
    }
}
