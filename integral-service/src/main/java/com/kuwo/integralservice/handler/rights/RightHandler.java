package com.kuwo.integralservice.handler.rights;

/**
 * 权益handler处理类
 */
public interface RightHandler {

    /**
     * 根据权益描述符判断是否需要匹配权益
     * @param
     * @return
     */
    String getRightIdentifyKey();

    /**
     * 增加权益
     * @param rightContext
     * @return
     */
    boolean addRight(RightContext rightContext);


    /**
     * 返回一个备注,记录在发放记录表中
     * @return
     */
    String getRemark();

}
