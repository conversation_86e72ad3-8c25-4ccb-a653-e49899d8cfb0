package com.kuwo.integralservice.handler.task;

import com.kuwo.integralservice.cenum.ActionType;
import com.kuwo.integralservice.cenum.TaskType;
import com.kuwo.integralservice.service.MemberScoreService;

import java.util.Date;

public interface TaskHandle {

    /**
     * 用来限制是否可以完成任务
     * @return
     */
    public boolean canFinishTask(TaskContext taskContext);


    /**
     *  用来先这是否可以加积分
     * @return
     */
    public boolean needLimitAddScore(TaskContext taskContext, ActionType actionType, Date time, MemberScoreService memberScoreService);


    /**
     * 支持的task类型
     * @return
     */
    public TaskType supportTaskType();

}
