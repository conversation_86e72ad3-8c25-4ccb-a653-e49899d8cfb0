package com.kuwo.integralservice.handler.message.clientTask;

import com.google.gson.Gson;
import com.kuwo.commercialization.common.message.BasicMessage;
import com.kuwo.commercialization.common.message.MessageType;
import com.kuwo.integralservice.handler.message.MessageHandler;
import com.kuwo.integralservice.http.MemberGiveService;
import com.kuwo.integralservice.req.TaskRequest;
import com.kuwo.integralservice.service.MemberScoreService;
import com.kuwo.integralservice.service.UserTaskFinishService;
import com.kuwo.integralservice.util.ClientActionTransform;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 客户段完成的积分任务加积分处理
 */
@Component
public class ClientFinishTaskMessageHandler implements MessageHandler {

    private Gson gson = new Gson();

    private final static Logger logger = LoggerFactory.getLogger(ClientFinishTaskMessageHandler.class);

    @Autowired
    private MemberGiveService memberGiveService;

    @Autowired
    private UserTaskFinishService userTaskFinishService;

    @Autowired
    private ClientActionTransform clientActionTransform;

    @Override
    public MessageType getMessageType() {
        return MessageType.CLIENT_FINISHED_TASK;
    }

    @Override
    public HandleResult handleMessage(BasicMessage basicMessage) {
        HandleResult handleResult = new HandleResult();
        try {
            TaskRequest taskRequest = gson.fromJson(gson.toJson(basicMessage.getMessage()), TaskRequest.class);
            String taskIdentify = clientActionTransform.getTaskIdentify(taskRequest.getAction());
            String remark = "客户端任务积分获取";
            String extra = taskRequest.getExtra();
            if (StringUtils.isNotBlank(extra)){
                remark = extra;
                if (extra.length()>250){
                    remark ="";
                }
            }
            // 同步数据给huihui服务
            //memberGiveService.notifyUserActions(taskRequest.getUid(), String.valueOf(taskRequest.getAction()),extra, taskRequest.toJson());
            userTaskFinishService.finishTask(Long.valueOf(taskRequest.getUid()), taskIdentify, remark);
            handleResult.businessResult = true;
            handleResult.errorMessage = "";
        } catch (Exception e) {
            logger.error("client finish task exception --> ", e);
            handleResult.businessResult = false;
            handleResult.errorMessage = e.getMessage();
        }
        return handleResult;
    }
}
