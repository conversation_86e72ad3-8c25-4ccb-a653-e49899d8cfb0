package com.kuwo.integralservice.handler.message;

import com.kuwo.commercialization.common.message.MessageType;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class MessageHandlerHelper implements InitializingBean {

    @Autowired
    private List<MessageHandler> handlers;

    private final Map<MessageType, MessageHandler> container = new HashMap<>();


    @Override
    public void afterPropertiesSet() throws Exception {
        if (handlers!=null){
            handlers.forEach(k-> container.put(k.getMessageType(), k));
        }
    }

    public MessageHandler matchHandler(MessageType messageType){
        return container.get(messageType);
    }


}
