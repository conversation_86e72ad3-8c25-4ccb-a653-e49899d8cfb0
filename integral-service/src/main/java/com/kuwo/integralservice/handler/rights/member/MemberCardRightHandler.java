package com.kuwo.integralservice.handler.rights.member;

import com.google.gson.Gson;
import com.google.gson.internal.LinkedTreeMap;
import com.kuwo.integralservice.handler.rights.RightContext;
import com.kuwo.integralservice.handler.rights.RightHandler;
import com.kuwo.integralservice.handler.rights.RightIdentifyConstant;
import com.kuwo.integralservice.http.MemberGiveService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 赠送会员卡
 */
@Component
public class MemberCardRightHandler implements RightHandler {

    private Logger logger = LoggerFactory.getLogger(MemberCardRightHandler.class);

    @Autowired
    private MemberGiveService memberGiveService;

    private Gson gson = new Gson();

    @Override
    public String getRightIdentifyKey() {
        return RightIdentifyConstant.MEMBER_CARD.getKey();
    }

    @Override
    public boolean addRight(RightContext rightContext) {
        String developerParams = rightContext.getDeveloperParams();
        Map map = gson.fromJson(developerParams, Map.class);
        // 定义下,这里面有几个参数  n天会员, 几张的 ,大致是这个样子
        Long userId = rightContext.getUserScore().getUserId();
        LinkedTreeMap memberMap = new LinkedTreeMap();
        // 判断当前用户类型,然后取相应的用户类型的权益进行发放
        int userMemberType = memberGiveService.getUserMemberType(userId);
        if (userMemberType == 0){
            // 当当前用户是非会员时,暂时不发放权益,等是会员的时候在进行发放
            return false;
        }
        if (userMemberType == 3){
            if (map.containsKey("svip")){
                memberMap = (LinkedTreeMap) map.get("svip");
            }
        }
        if (userMemberType == 2){
            if (map.containsKey("luxVip")){
                memberMap = (LinkedTreeMap) map.get("luxVip");
            }
        }
        // 然后调用接口赠送会员卡就可以了
        int cnt = -1;
        int days = -1;
        if (memberMap.containsKey("day")){
            days = new BigDecimal(String.valueOf(memberMap.get("day"))).intValue();
        }
        if (memberMap.containsKey("cnt")){
            cnt = new BigDecimal(String.valueOf(memberMap.get("cnt"))).intValue();
        }
        if (days<0 || cnt<0){
            // 当没有配置权益的时候就不进行记录和赠送调用,等会员省份满足之后在进行统一发放
            logger.error("cur user no member give member card, config error -> day: {}, cnt:{}, userType： {}", days, cnt, userMemberType);
            return false;
        }
        try {
            String result = memberGiveService.giveMemberCard(userId, cnt, days);
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    @Override
    public String getRemark() {
       return RightIdentifyConstant.MEMBER_CARD.getDesc();
    }
}
