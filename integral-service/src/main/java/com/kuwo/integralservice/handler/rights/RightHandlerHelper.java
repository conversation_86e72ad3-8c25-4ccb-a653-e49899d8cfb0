package com.kuwo.integralservice.handler.rights;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class RightHandlerHelper implements InitializingBean {

    @Autowired
    private List<RightHandler> handlers;

    private final Map<String, RightHandler> container = new HashMap<>();


    @Override
    public void afterPropertiesSet() throws Exception {
        if (handlers!=null){
            handlers.forEach(rightHandler -> container.put(rightHandler.getRightIdentifyKey(), rightHandler));
        }
    }

    public RightHandler getRightHandlerByIdentifyKey(String identifyKey){
        return container.get(identifyKey);
    }


}
