package com.kuwo.integralservice.controller;

import com.google.common.base.Preconditions;
import com.kuwo.commercialization.common.cenum.Result;
import com.kuwo.commercialization.common.resp.BasicResponse;
import com.kuwo.integralservice.annoation.CheckUserLogin;
import com.kuwo.integralservice.service.TaskService;
import com.kuwo.integralservice.vo.TaskList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Desc: 任务列表相关
 * @date 2022-02-17 16:55:44
 */
@RestController
@RequestMapping("/task")
public class TaskController {

    @Autowired
    private TaskService taskService;

    // 获取当前任务列表及详情
    @GetMapping("/list")
    @CheckUserLogin
    public BasicResponse getUserTasks(String userId, String sessionId, String plat){
        Preconditions.checkNotNull(plat, Result.PARAM_CHECK_ERROR.getMessage());
        Preconditions.checkState("ar".equals(plat)||"ios".equals(plat)||"pc".equals(plat), Result.PARAM_CHECK_ERROR.getMessage());
        List<TaskList> tasks = taskService.getUserTaskList(userId, plat);
        return BasicResponse.successResponse(tasks);
    }

    /**
     * 每天随机任务实现
     * @return
     */
    @GetMapping("/random")
    public BasicResponse randomTask(String token){
        if (!"slkoTpvOXfhlKqwPyeZaDO5ZyiEL06Be".equals(token)){
            return BasicResponse.failResponse(Result.ILLEGAL_REQUEST);
        }
        taskService.randomCurrentTask();
        return BasicResponse.successResponse(null);
    }


}
