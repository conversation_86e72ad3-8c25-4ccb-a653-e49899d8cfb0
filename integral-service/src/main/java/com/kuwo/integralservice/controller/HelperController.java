package com.kuwo.integralservice.controller;

import com.google.common.base.Preconditions;
import com.google.gson.Gson;
import com.kuwo.commercialization.common.cenum.Result;
import com.kuwo.commercialization.common.controller.BaseController;
import com.kuwo.commercialization.common.resp.BasicResponse;
import com.kuwo.commercialization.common.utill.IpUtil;
import com.kuwo.commercialization.common.utill.SpringAwareUtil;
import com.kuwo.integralservice.cache.RedisReplaceConfig;
import com.kuwo.integralservice.config.kafka.KafkaTopicEnum;
import com.kuwo.integralservice.req.TaskRequest;
import com.kuwo.integralservice.service.KafkaService;
import com.kuwo.integralservice.service.RightService;
import com.kuwo.integralservice.service.UserScoreService;
import com.kuwo.integralservice.util.RedisCacheHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @Desc:  针对测试环境创建用户等操作使用
 * @date 2022-03-07 13:47:58
 */
@RestController
@RequestMapping("/help")
public class HelperController extends BaseController {
    @Autowired
    private UserScoreService userScoreService;

    @Autowired
    private KafkaService kafkaService;

    @Autowired
    private RightService rightService;

    @Autowired
    private RedisCacheHelper redisCacheHelper;

    private Logger logger = LoggerFactory.getLogger(HelperController.class);

    @Autowired
    private RedisReplaceConfig redisReplaceConfig;

    private Gson gson = new Gson();

    /**
     * 这个只有初始化的时候有用
     * @param uid
     * @param score
     * @return
     */
    @GetMapping("/score/init")
    public BasicResponse initMemberScore(String uid, int score, String vers){
        if (!"yIrpdXxPBo9khHsG1FJKZ80AMTCRlSQw".equals(vers) && "prod".equals(SpringAwareUtil.getActiveProfile())){
            return BasicResponse.failResponse(Result.ILLEGAL_REQUEST);
        }
        return BasicResponse.successResponse(userScoreService.initUserScore(Long.valueOf(uid), score));
    }

    /**
     * 发送评论消息 测试用
     * @param uid
     * @return
     */
    @GetMapping("/comment/sendComment")
    public BasicResponse sendCommentMessage(String uid,  String vers){
        if ("prod".equals(SpringAwareUtil.getActiveProfile()) && !"yIrpdXxPBo9khHsG1FJKZ80AMTCRlSQw".equals(vers)){
            return BasicResponse.failResponse(Result.ILLEGAL_REQUEST);
        }
        Preconditions.checkNotNull(uid);
        Map map = new HashMap<>();
        map.put("event_type", "POST_COMMENT");
        map.put("user_id", uid);
        String message = gson.toJson(map);
        kafkaService.sendMessages(KafkaTopicEnum.COMMENT_TOPIC, message);
        return BasicResponse.successResponse(null);
    }

    @GetMapping("/ip")
    public BasicResponse getIp(HttpServletRequest request){
       return BasicResponse.successResponse(IpUtil.getUserIp(request));
    }


    @GetMapping("/right/check")
    public BasicResponse rightCheck(String uid,  String vers){
        if ("prod".equals(SpringAwareUtil.getActiveProfile()) && !"yIrpdXxPBo9khHsG1FJKZ80AMTCRlSQw".equals(vers)){
            return BasicResponse.failResponse(Result.ILLEGAL_REQUEST);
        }
        rightService.addRightsIfNeed(Long.parseLong(uid));
        return BasicResponse.successResponse(null);
    }

    @GetMapping("/test/send")
    public BasicResponse sendTestMsg(){
        TaskRequest taskRequest = new TaskRequest();
        taskRequest.setTs(System.currentTimeMillis());
        taskRequest.setReqId(UUID.randomUUID().toString());
        taskRequest.setUid("435952249");
        taskRequest.setSid("153211234");
        taskRequest.setAction(6010);
        taskRequest.setExtra("{\"version\":\"1.1\",\"plat\":\"ar\",\"useDesc\":[{\"code\":\"18\",\"num\":\"1\"},{\"code\":\"19\",\"num\":\"-1\"}]}");
        taskRequest.setSign("11111sadas1111");
        kafkaService.sendMessages(KafkaTopicEnum.CLIENT_TASK_REPORT_TOPIC, taskRequest.toMessage());
        return BasicResponse.successResponse(null);
    }


    @GetMapping("/clean/task")
    public BasicResponse cleanCache(String userId){
        redisCacheHelper.cleanUserTaskListCache(userId);
        redisCacheHelper.cleanUserBasicScoreInfo(userId);
        return BasicResponse.successResponse(userId);
    }

    @RequestMapping("/notifyOrder")
    public BasicResponse notifyOrder(HttpServletRequest request) throws IOException {
        String requestBody = getRequestBody(request);
        logger.info("req body -> {}", requestBody);
        Enumeration<String> headers = request.getParameterNames();
        while (headers.hasMoreElements()){
            String key = headers.nextElement();
            logger.info("params -> {}:{}", key, request.getParameter(key));
        }
        return BasicResponse.successResponse(null);
    }

    @GetMapping("/testRedis")
    public BasicResponse test(String key){
        logger.info("use tencent key -> {}",redisReplaceConfig.useTencentRedis(key));
        logger.info("default redis name -> {}",redisReplaceConfig.getDefaultRedisName());
        return BasicResponse.successResponse("1111");
    }

}
