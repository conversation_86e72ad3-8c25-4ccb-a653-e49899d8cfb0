package com.kuwo.integralservice.controller.fallback;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.kuwo.commercialization.common.resp.BasicResponse;
import com.kuwo.integralservice.vo.UserScoreVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Desc: 降级部分的方法
 * @date 2024-01-11 15:11:44
 */

public class MemberScoreFallBack extends BaseFailBack {

    private final static Logger logger = LoggerFactory.getLogger("failBackLogger");

    public static BasicResponse getUserScoreNoSessionId(String userId, String vers, BlockException ex){
        logger.info("[failBack]-getUserScoreNoSessionId-{}", userId);
        recordNum("MemberScoreFallBack_getUserScoreNoSessionId");
        UserScoreVo userScoreVo = new UserScoreVo();
        userScoreVo.setDayScore(0);
        userScoreVo.setVipTag("VIP1");
        userScoreVo.setScore(0);
        return BasicResponse.successResponse(userScoreVo);
    }

    public static BasicResponse getUserScore(String userId, String sessionId,  BlockException ex){
        logger.info("[failBack]-getUserScoreNoSessionId-{}", userId);
        recordNum("MemberScoreFallBack_getUserScore");
        UserScoreVo userScoreVo = new UserScoreVo();
        userScoreVo.setDayScore(0);
        userScoreVo.setVipTag("VIP1");
        userScoreVo.setScore(0);
        return BasicResponse.successResponse(userScoreVo);
    }


}
