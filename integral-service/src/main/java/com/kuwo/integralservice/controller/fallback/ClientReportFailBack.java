package com.kuwo.integralservice.controller.fallback;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.kuwo.commercialization.common.resp.BasicResponse;
import com.kuwo.commercialization.common.utill.SpringAwareUtil;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @Desc:
 * @date 2024-01-12 16:31:58
 */
public class ClientReportFailBack extends BaseFailBack{
    private final static Logger logger = LoggerFactory.getLogger("failBackLogger");
    public static BasicResponse completeTask(HttpServletRequest request, BlockException ex){
        logger.info("[failBack]-completeTask");
        recordNum("ClientReportFailBack_completeTask");
        return BasicResponse.failResponse(null);
    }

}
