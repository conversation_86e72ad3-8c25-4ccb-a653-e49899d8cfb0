package com.kuwo.integralservice.controller;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.google.gson.Gson;
import com.kuwo.commercialization.common.cenum.RedisConstant;
import com.kuwo.commercialization.common.cenum.Result;
import com.kuwo.commercialization.common.controller.BaseController;
import com.kuwo.commercialization.common.resp.BasicResponse;
import com.kuwo.commercialization.common.utill.SecurityCoder;
import com.kuwo.integralservice.config.kafka.KafkaTopicEnum;
import com.kuwo.integralservice.config.redis.DynamicChoiceRedissonClient;
import com.kuwo.integralservice.controller.fallback.MessageProxyFailBack;
import com.kuwo.integralservice.http.LoginHttpService;
import com.kuwo.integralservice.req.TaskRequest;
import com.kuwo.integralservice.req.TaskRequestValidator;
import com.kuwo.integralservice.service.KafkaService;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Desc: 客户端上报相关接口
 * @date 2021-12-21 10:20:16
 */
@RestController
@RequestMapping("/member")
public class ClientReportController extends BaseController {


    private Logger logger = LoggerFactory.getLogger(ClientReportController.class);

    private Gson gson = new Gson();

    @Value("${member.client.base64Key}")
    private String base64Key;

    @Value("${member.client.md5key}")
    private String md5Key;

    @Autowired
    private LoginHttpService loginHttpService;

//    @Autowired
//    private DynamicChoiceRedissonClient dynamicChoiceRedissonClient;

    @Autowired
    private KafkaService kafkaService;

    @RequestMapping(value = "/task")
    @SentinelResource(value = "ClientReportController#completeTask",
            fallbackClass = MessageProxyFailBack.class,
            blockHandlerClass = MessageProxyFailBack.class,
            blockHandler = "completeTask",fallback = "completeTask")
    public BasicResponse completeTask(HttpServletRequest request) throws IOException {
        Result result = Result.FAIL;
        BasicResponse basicResponse = new BasicResponse();
        String requestBody = getRequestBody(request);
        try {
            String decryptText = SecurityCoder.decrypt(requestBody, base64Key);
            TaskRequest taskRequest = gson.fromJson(decryptText,TaskRequest.class);
            //RedissonClient redissonClient = dynamicChoiceRedissonClient.getClient("d1");
            logger.info(taskRequest.toMessage());
            if (taskRequest!=null && taskRequest.validateSign(md5Key)){
                TaskRequestValidator taskRequestValidator = new TaskRequestValidator.Builder()
                        .taskRequest(taskRequest)
                        .loginHttpService(loginHttpService)
                        //.redissonClient(redissonClient)
                        .limitKey(RedisConstant.CLIENT_MEMBER_TASK_LOCK.getKey())
                        .build();
                TaskRequestValidator.ValidatorResult validateResult = taskRequestValidator.validate();
                if (validateResult.result ==Result.SUCCESS){
                    //4. kafka客户端 发送消息到消息队列中, kafka参数部分也的需要进行校验
                    kafkaService.sendMessages(KafkaTopicEnum.CLIENT_TASK_REPORT_TOPIC, taskRequest.toMessage());
                    //validateResult.mapCache.put(taskRequest.getReqId(), 1, 1, TimeUnit.MINUTES);
                }
                result = validateResult.result;
            }else {
                result = Result.SIGN_ERROR;
            }
            logger.info("send result -> {}", result.getMessage());
        } catch (Exception e) {
            logger.error(e.getMessage());
            result = Result.PARAM_CHECK_ERROR;
        }
        basicResponse.setCode(result.getCode());
        basicResponse.setMsg(result.getMessage());
        return basicResponse;
    }


}
