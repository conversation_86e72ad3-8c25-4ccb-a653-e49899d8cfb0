package com.kuwo.integralservice.controller;

import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.kuwo.commercialization.common.cenum.Result;
import com.kuwo.commercialization.common.resp.BasicResponse;
import com.kuwo.integralservice.annoation.CheckUserLogin;
import com.kuwo.integralservice.cache.MemberRankCache;
import com.kuwo.integralservice.enity.MemberRank;
import com.kuwo.integralservice.service.MemberScoreService;
import com.kuwo.integralservice.service.TaskService;
import com.kuwo.integralservice.vo.MemberRankVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 成长值页面合并列表接口
 */
@RestController
@RequestMapping("/page")
public class ScorePageController {

    @Autowired
    private MemberScoreService memberScoreService;

    @Autowired
    private TaskService taskService;


    @Autowired
    private MemberRankCache memberRankCache;

    @CheckUserLogin
    @GetMapping("/main")
    public BasicResponse getScorePageInfo(String userId, String sessionId, String plat){
        Preconditions.checkState("ar".equals(plat)||"ios".equals(plat)||"pc".equals(plat), Result.PARAM_CHECK_ERROR.getMessage());
        Map returnResults = CompletableFuture.supplyAsync(() -> memberScoreService.getUserScoreVo(userId))
                .thenCombine(CompletableFuture.supplyAsync(() -> taskService.getUserTaskList(userId, plat)),
                        (userScoreVo, taskLists) -> {
                            Map results = new HashMap();
                            Map scoreVo = new HashMap();
                            String rank = userScoreVo.getVipTag().replace("VIP", "");
                            scoreVo.put("vipTag", userScoreVo.getVipTag());
                            scoreVo.put("score", userScoreVo.getScore());
                            scoreVo.put("dayScore", userScoreVo.getDayScore());
                            scoreVo.put("lv", rank);
                            results.put("userScoreInfo", scoreVo);

                            results.put("tasks", taskLists);
                            return results;
                        })
                .thenCombine(CompletableFuture.supplyAsync(() -> {
                            List<MemberRank> memberRanks = memberRankCache.getAllAvailableRank();
                            if (memberRanks != null && memberRanks.size() > 0) {
                                List<MemberRankVo> rankVos = memberRanks.stream().map(MemberRank::toVo).sorted().collect(Collectors.toList());
                                return rankVos;
                            } else {
                                return Lists.newArrayList();
                            }
                        }),
                        (map, memberRankVos) -> {
                            map.put("availableRanks", memberRankVos);
                            return map;
                        })
                .join();
        return BasicResponse.successResponse(returnResults);
    }

}
