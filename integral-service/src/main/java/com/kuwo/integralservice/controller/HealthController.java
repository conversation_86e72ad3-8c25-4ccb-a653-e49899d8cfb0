package com.kuwo.integralservice.controller;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Desc: 健康检查相关接口
 * @date 2021-12-16 15:24:11
 */
@RestController
@RequestMapping("/health")
public class HealthController {

    @Value("${test.env}")
    private String testProfile;

    @RequestMapping("/status")
    public String status(){
        return testProfile;
    }
}
