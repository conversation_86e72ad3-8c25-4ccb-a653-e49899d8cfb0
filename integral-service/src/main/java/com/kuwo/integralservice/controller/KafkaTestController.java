package com.kuwo.integralservice.controller;

import com.kuwo.commercialization.common.controller.BaseController;
import com.kuwo.integralservice.http.TestService;
import com.kuwo.integralservice.req.TaskRequest;
import com.kuwo.integralservice.service.KafkaService;
import com.kuwo.integralservice.service.RightService;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.UUID;

/**
 * <AUTHOR>
 * @Desc:
 * @date 2021-12-15 10:40:10
 */
@RestController
@RequestMapping("/test")
public class KafkaTestController extends BaseController {

    @Autowired
    private KafkaService kafkaService;
    @Autowired
    private RightService rightService;

    @Autowired
    private TestService testService;

    @RequestMapping("/sendMessage")
    public void sendKafkaMessage(){
        kafkaService.sendMessage("11111");
    }

    @RequestMapping("/sendClientMessage")
    public void sendTemplateMessage(){
        TaskRequest taskRequest = new TaskRequest();
        taskRequest.setAction(6001);
        //String uid = RandomStringUtils.randomNumeric(10);
        String uid = "2522400520";
        String sid = RandomStringUtils.randomNumeric(10);
        taskRequest.setUid(uid);
        taskRequest.setSid(sid);
        taskRequest.setExtra("tess1111");
        taskRequest.setReqId(UUID.randomUUID().toString());
        taskRequest.setTs(System.currentTimeMillis());
        kafkaService.sendMessage(taskRequest.toMessage());
    }

    @RequestMapping("/proxy")
    public String proxyTest(HttpServletRequest request) throws IOException {
        String requestBody = getRequestBody(request);
        return requestBody;
    }

    @RequestMapping("/failback")
    public String test111(){
        return testService.testC();
    }

}
