package com.kuwo.integralservice.controller.fallback;

import com.kuwo.commercialization.common.utill.SpringAwareUtil;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.context.ApplicationContext;

/**
 * <AUTHOR>
 * @Desc:
 * @date 2024-01-23 20:20:01
 */
public class BaseFailBack {

    protected static void recordNum(String method){
        ApplicationContext applicationContext = SpringAwareUtil.getApplicationContext();
        if (applicationContext!=null){
            MeterRegistry meterRegistry = applicationContext.getBean(MeterRegistry.class);
            Counter counter = Counter
                    .builder("fail-back")
                    .description("a fail back make count") // optional
                    .tags("method", method) // optional
                    .register(meterRegistry);
            counter.increment();
        }
    }

}
