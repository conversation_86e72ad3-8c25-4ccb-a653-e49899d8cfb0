package com.kuwo.integralservice.controller;

import com.google.common.base.Preconditions;
import com.kuwo.commercialization.common.cenum.Result;
import com.kuwo.commercialization.common.resp.BasicResponse;
import com.kuwo.integralservice.service.RemoteCacheService;
import com.kuwo.integralservice.vo.RankScoreVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 排行榜controller
 */
@RestController
@RequestMapping("/rank")
public class RankController {

    @Autowired
    private RemoteCacheService remoteCacheService;

    /**
     * area:
     *  1:  全国
     *  2:  北京
     *  3:  上海
     *  4:  广州
     *  5:  深圳
     * @param area
     */
    @GetMapping("/{area}")
    public BasicResponse getScoreRank(@PathVariable(value = "area")  Integer area){
        Preconditions.checkState(area==1 || area==2||area == 3|| area == 4||area==5, Result.ILLEGAL_REQUEST.getMessage());
        List<RankScoreVo> ranks = remoteCacheService.getRankTop10ByArea(area, 10);
        return BasicResponse.successResponse(ranks);
    }



}
