package com.kuwo.integralservice.controller;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.google.common.base.Preconditions;
import com.kuwo.commercialization.common.resp.BasicResponse;
import com.kuwo.integralservice.controller.fallback.MemberScoreFallBack;
import com.kuwo.integralservice.controller.fallback.MessageProxyFailBack;
import com.kuwo.integralservice.req.KafkaProxyRequest;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.util.concurrent.ListenableFutureCallback;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.UUID;

/**
 * 消息代理controller
 * 系统边界处增加报错和降级措施
 */
@RestController
@RequestMapping("/msg")
public class MessageProxyController {

    private Logger logger = LoggerFactory.getLogger(MessageProxyController.class);

    private final KafkaTemplate kafkaTemplate;
    private final KafkaTemplate ascribekafkaTemplate;

    public MessageProxyController(@Qualifier("userSystemKafkaTemplate") KafkaTemplate kafkaTemplate,
                                  @Qualifier("ascribeKafkaTemplate") KafkaTemplate ascribeKafkaTemplate) {
        this.kafkaTemplate = kafkaTemplate;
        this.ascribekafkaTemplate = ascribeKafkaTemplate;
    }

    /**
     * kafka相关消息代理
     * @return
     */
    @PostMapping("/kafka/proxy")
    @SentinelResource(value = "MessageProxyController#sendKafkaMessage",
            fallbackClass = MessageProxyFailBack.class,
            blockHandlerClass = MessageProxyFailBack.class,
            blockHandler = "sendKafkaMessage",fallback = "sendKafkaMessage")
    public BasicResponse sendKafkaMessage(@RequestBody KafkaProxyRequest kafkaProxyRequest){
        Preconditions.checkNotNull(kafkaProxyRequest);
        Preconditions.checkState(StringUtils.isNotBlank(kafkaProxyRequest.getTopic()) && StringUtils.isNotBlank(kafkaProxyRequest.getMsg()));
        String uniqId = UUID.randomUUID().toString();
        String msg = kafkaProxyRequest.getMsg();
        ListenableFuture future = kafkaTemplate.send(kafkaProxyRequest.getTopic(), uniqId, msg);
        future.addCallback(new ListenableFutureCallback() {
            @Override
            public void onFailure(Throwable ex) {
                logger.info("fail msg => {}", msg);
            }

            @Override
            public void onSuccess(Object result) {
                logger.info("send msg => {}", msg);
            }
        });
        return BasicResponse.successResponse(null);
    }

    /**
     * 发送代理到李帅峰 kafka里
     * @param kafkaProxyRequest
     * @return
     */
    @PostMapping("/kafka/proxy/dest/2")
    @SentinelResource(value = "MessageProxyController#sendKafkaMessageToShuaiFeng",
            fallbackClass = MessageProxyFailBack.class,
            blockHandlerClass = MessageProxyFailBack.class,
            blockHandler = "sendKafkaMessageToShuaiFeng",fallback = "sendKafkaMessageToShuaiFeng")
    public BasicResponse sendKafkaMessageToShuaiFeng(@RequestBody KafkaProxyRequest kafkaProxyRequest){
        Preconditions.checkNotNull(kafkaProxyRequest);
        Preconditions.checkState(StringUtils.isNotBlank(kafkaProxyRequest.getTopic()) && StringUtils.isNotBlank(kafkaProxyRequest.getMsg()));
        String uniqId = UUID.randomUUID().toString();
        String msg = kafkaProxyRequest.getMsg();
        ListenableFuture future = ascribekafkaTemplate.send(kafkaProxyRequest.getTopic(), uniqId, msg);
        future.addCallback(new ListenableFutureCallback() {
            @Override
            public void onFailure(Throwable ex) {
                logger.info("fail [dest 2] msg => {}", msg);
            }

            @Override
            public void onSuccess(Object result) {
                logger.info("send [dest 2] msg => {}", msg);
            }
        });
        return BasicResponse.successResponse(null);
    }



}
