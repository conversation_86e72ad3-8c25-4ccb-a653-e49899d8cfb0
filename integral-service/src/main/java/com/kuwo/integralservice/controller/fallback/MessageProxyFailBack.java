package com.kuwo.integralservice.controller.fallback;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.kuwo.commercialization.common.resp.BasicResponse;
import com.kuwo.integralservice.req.KafkaProxyRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @Desc:
 * @date 2024-01-12 10:07:33
 */
public class MessageProxyFailBack extends BaseFailBack{
    private final static Logger logger = LoggerFactory.getLogger("failBackLogger");

    public static BasicResponse sendKafkaMessage(KafkaProxyRequest kafkaProxyRequest,  BlockException ex){
        logger.info("[failBack]-sendKafkaMessage-{},{}", kafkaProxyRequest.getTopic(),kafkaProxyRequest.getMsg());
        recordNum("MessageProxyFailBack_sendKafkaMessage");
        return BasicResponse.successResponse(null);
    }

    public static BasicResponse sendKafkaMessageToShuaiFeng(@RequestBody KafkaProxyRequest kafkaProxyRequest,  BlockException ex){
        logger.info("[failBack]-sendKafkaMessageToShuaiFeng-{},{}", kafkaProxyRequest.getTopic(),kafkaProxyRequest.getMsg());
        recordNum("MessageProxyFailBack_sendKafkaMessageToShuaiFeng");
        return BasicResponse.successResponse(null);
    }

}
