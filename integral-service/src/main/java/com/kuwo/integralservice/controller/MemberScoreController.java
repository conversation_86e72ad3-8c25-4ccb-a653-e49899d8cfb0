package com.kuwo.integralservice.controller;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.google.common.collect.Lists;
import com.kuwo.commercialization.common.cenum.Result;
import com.kuwo.commercialization.common.resp.BasicResponse;
import com.kuwo.integralservice.annoation.CheckUserLogin;
import com.kuwo.integralservice.cache.ExceptionMockConfig;
import com.kuwo.integralservice.cache.MemberRankCache;
import com.kuwo.integralservice.controller.fallback.MemberScoreFallBack;
import com.kuwo.integralservice.enity.MemberRank;
import com.kuwo.integralservice.enity.UserScore;
import com.kuwo.integralservice.service.MemberScoreService;
import com.kuwo.integralservice.service.UserScoreService;
import com.kuwo.integralservice.vo.MemberRankVo;
import com.kuwo.integralservice.vo.UserScoreDetailVo;
import com.kuwo.integralservice.vo.UserScoreVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 会员积分相关
 * 基础信息
 */
@RestController
@RequestMapping("/basic")
public class MemberScoreController {

    private Logger logger = LoggerFactory.getLogger(MemberScoreController.class);

    @Autowired
    private UserScoreService userScoreService;

    @Autowired
    private MemberScoreService memberScoreService;

    @Autowired
    private MemberRankCache memberRankCache;

    @Autowired
    private ExceptionMockConfig exceptionMockConfig;

    //check一下用户是否登录
    @GetMapping("/detail")
    @CheckUserLogin
    public BasicResponse getScoreDetail(String userId, String sessionId,
                                        @RequestParam(required = false,defaultValue = "1") int page,
                                        @RequestParam(required = false, defaultValue = "10")int pn) throws Exception {
        // 转换成memrchatid
        UserScore userScore = userScoreService.getOrCreateDefaultUserScore(Long.valueOf(userId));
        // 返回list列表
        Long memberScoreId = userScore.getMemberScoreId();
        // 查询列表返回
        List<UserScoreDetailVo> scoreDetails = memberScoreService.getUserScoreDetail(memberScoreId, page, pn);
        return BasicResponse.successResponse(scoreDetails);
    }


    // 查询当前可用的会员等级
    @GetMapping("/rank/list")
    public BasicResponse getMemberRank(){
        List<MemberRank> memberRanks = memberRankCache.getAllAvailableRank();
        if (memberRanks!=null && memberRanks.size()>0){
            List<MemberRankVo> rankVos = memberRanks.stream().map(MemberRank::toVo).sorted().collect(Collectors.toList());
            return BasicResponse.successResponse(rankVos);
        }else {
            return BasicResponse.successResponse(Lists.newArrayList());
        }
    }

    @CheckUserLogin
    @GetMapping("/info")
    @SentinelResource(value = "MemberScoreController#getUserScore",
            fallbackClass = MemberScoreFallBack.class,
            blockHandlerClass = MemberScoreFallBack.class,
            blockHandler = "getUserScore",fallback = "getUserScore")
    public BasicResponse getUserScore(String userId, String sessionId){
        UserScoreVo userScoreVo = memberScoreService.getUserScoreVo(userId);
        if (userScoreVo == null){
            logger.info("get user score null, userId -> {}", userId);
            return BasicResponse.failResponse(Result.GET_SCORE_INFO_FAIL);
        }
        return BasicResponse.successResponse(userScoreVo);
    }


    /**
     * 供内部接口调用的一个接口
     * @param userId
     * @param vers
     * @return
     */
    @RequestMapping("/noauth/info")
    @SentinelResource(value = "MemberScoreController#getUserScoreNoSessionId",
            fallbackClass = MemberScoreFallBack.class,
            blockHandlerClass = MemberScoreFallBack.class,
            blockHandler = "getUserScoreNoSessionId",fallback = "getUserScoreNoSessionId")
    public BasicResponse getUserScoreNoSessionId(String userId, String vers){
        if (!"wBnPCy64ws09dFkzkD0Po0TTBeexomeF".equals(vers)){
            return BasicResponse.failResponse(Result.ILLEGAL_REQUEST);
        }
        UserScoreVo userScoreVo = memberScoreService.getUserScoreVo(userId);
        if (userScoreVo == null){
            logger.info("get user score null, userId -> {}", userId);
            return BasicResponse.failResponse(Result.GET_SCORE_INFO_FAIL);
        }
        return BasicResponse.successResponse(userScoreVo);
    }

}
