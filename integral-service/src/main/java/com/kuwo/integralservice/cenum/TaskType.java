package com.kuwo.integralservice.cenum;

/**
 * 任务类型
 */
public enum  TaskType {
    NORMAL_TASK("normal", "普通任务,每天能够完成1次且有完成积分限制的任务"),
    ONCE_YEAR_TASK("onceYear", "普通任务,每年只能完成1次的任务,比如填生日"),
    COST_MONEY_TASK("costMoney", "消费类任务,用钱来进行完成任务获取积分，没有限制"),
    SPECIAL_TASK("special","特殊类任务,可以加减积分不受限制的任务"),
    ;
    private String taskTypeName;
    private String desc;

    TaskType(String taskTypeName, String desc) {
        this.taskTypeName = taskTypeName;
        this.desc = desc;
    }


    public String getTaskTypeName() {
        return taskTypeName;
    }

    public String getDesc() {
        return desc;
    }
}
