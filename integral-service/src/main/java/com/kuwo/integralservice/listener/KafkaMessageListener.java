//package com.kuwo.integralservice.listener;
//
//import com.google.common.base.Preconditions;
//import com.google.gson.Gson;
//import com.kuwo.commercialization.common.message.BasicMessage;
//import com.kuwo.integralservice.cache.MemberTaskCache;
//import com.kuwo.integralservice.config.kafka.KafkaTopicEnum;
//import com.kuwo.integralservice.enity.MemberTask;
//import com.kuwo.integralservice.handler.message.MessageHandlerAdapter;
//import com.kuwo.integralservice.service.UserTaskFinishService;
//import com.kuwo.integralservice.util.ClientActionTransform;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.kafka.clients.consumer.ConsumerRecord;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.kafka.annotation.KafkaListener;
//import org.springframework.kafka.support.Acknowledgment;
//import org.springframework.stereotype.Component;
//
//import java.lang.reflect.Method;
//import java.util.Arrays;
//import java.util.List;
//import java.util.Map;
//
///**
// * <AUTHOR>
// * @Desc:
// * @date 2021-12-15 15:58:14
// */
//@Component
//public class KafkaMessageListener {
//
//    private final static Logger logger = LoggerFactory.getLogger(KafkaMessageListener.class);
//
//    private Gson gson = new Gson();
//
//    @Autowired
//    private MessageHandlerAdapter messageHandlerAdapter;
//
//    @Autowired
//    private ClientActionTransform clientActionTransform;
//
//    @Autowired
//    private UserTaskFinishService userTaskFinishService;
//
//    @Autowired
//    private MemberTaskCache memberTaskCache;
//
//    @KafkaListener(topics = KafkaTopicEnum.CLIENT_TASK_REPORT_TOPIC,
//            autoStartup = "${listen.auto.start:true}", concurrency = "${listen.concurrency:3}" ,containerFactory = "kafkaListenerContainerFactory")
//    public void listen(List<ConsumerRecord> consumerRecords, Acknowledgment ack) {
//        // 写个批量分发程序，需要分发到相关线程池进行处理,
//        // 需要避免重复消费, 幂等处理
//        boolean isError = false;
//        int index = 0;
//        logger.info("会员等级消息接受到消息个数: {}", consumerRecords.size());
//        for (ConsumerRecord record: consumerRecords){
//            BasicMessage basicMessage = gson.fromJson(String.valueOf(record.value()), BasicMessage.class);
//            String key = (String) record.key();
//            boolean result = false;
//            try {
//                result = messageHandlerAdapter.handle(key, basicMessage);
//                logger.info("消费数据 --> {}| result -> {}" ,record.value(), result);
//                if (!result){
//                    isError = true;
//                    ack.nack(index, 30*1000);
//                    break;
//                }
//            } catch (Exception e) {
//                logger.error("message consume exception --> {}, message --> {}", e, basicMessage);
//                isError = true;
//                ack.nack(index, 30*1000);
//                break;
//            }
//            ++index;
//        }
//        if (!isError){
//            ack.acknowledge();
//        }
//    }
//
//
//    /**
//     * 评论相关topic
//     * @param consumerRecords
//     * @param ack
//     */
//    @KafkaListener(topics = KafkaTopicEnum.COMMENT_TOPIC,
//            autoStartup = "${listen.auto.start:true}", concurrency = "${listen.concurrency:3}" ,containerFactory = "kafkaListenerContainerFactory")
//    public void comment_message_listen(List<ConsumerRecord> consumerRecords, Acknowledgment ack){
//        logger.info("评论信息接受到消息个数: {}", consumerRecords.size());
//        boolean isError = false;
//        int index = 0;
//        for (ConsumerRecord record: consumerRecords){
//            Map map = gson.fromJson(String.valueOf(record.value()), Map.class);
//            if (map.containsKey("event_type") && String.valueOf(map.get("event_type")).equals("POST_COMMENT")){
//                try {
//                    if (map.containsKey("user_id")){
//                        String user_id = String.valueOf(map.get("user_id"));
//                        String taskIdentify = clientActionTransform.getTaskIdentify(7001);
//                        String remark = "评论积分获取";
//                        userTaskFinishService.finishTask(Long.valueOf(user_id), taskIdentify, remark);
//                    }
//                } catch (Exception e) {
//                    logger.error("comment consume message --> "+  record.value()+ " exception ->", e);
//                    isError = true;
//                    ack.nack(index, 30*1000);
//                    break;
//                }
//            }
//            ++index;
//            if (!isError){
//                ack.acknowledge();
//            }
//        }
//
//    }
//
//
//    @KafkaListener(topics = KafkaTopicEnum.PLAY_MUSIC_TOPIC,
//            autoStartup = "${listen.auto.start:true}", concurrency = "${listen.concurrency:3}" ,containerFactory = "kafkaListenerContainerFactory")
//    public void play_music_message_listen(List<ConsumerRecord> consumerRecords, Acknowledgment ack){
//        logger.info("播放歌曲日志接受到消息个数: {}", consumerRecords.size());
//        try {
//            String taskIdentify = clientActionTransform.getTaskIdentify(7002);
//            MemberTask memberTask = Preconditions.checkNotNull(memberTaskCache.getValue(taskIdentify),"INVALID_TASK");
//        } catch (Exception e) {
//            // 当任务没启用的时候直接ack
//            ack.acknowledge();
//            return;
//        }
//        boolean isError = false;
//        int index = 0;
//        for (ConsumerRecord record: consumerRecords){
//            String message = String.valueOf(record.value());
//            String msg = Arrays.stream(message.split("\\|")).filter(k -> k.startsWith("UI:")).findFirst().orElse("");
//            if (StringUtils.isNotBlank(msg)){
//                try {
//                    String[] lines = msg.split(":");
//                    if (lines.length>=2){
//                        String uid = lines[1];
//                        String taskIdentify = clientActionTransform.getTaskIdentify(7002);
//                        String remark = "播放歌曲积分获取";
//                        userTaskFinishService.finishTask(Long.valueOf(uid), taskIdentify, remark);
//                    }
//
//                } catch (Exception e) {
//                    logger.error("play song topic error --> " +message, e);
//                   continue;
//                }
//            }
//            ++index;
//            if (!isError){
//                ack.acknowledge();
//            }
//        }
//    }
//
//
//
//    @KafkaListener(topics = KafkaTopicEnum.DOWNLOAD_MUSIC_TOPIC,
//            autoStartup = "${listen.auto.start:true}", concurrency = "${listen.concurrency:3}" ,containerFactory = "kafkaListenerContainerFactory")
//    public void download_music_message_listen(List<ConsumerRecord> consumerRecords, Acknowledgment ack){
//        logger.info("下载歌曲接受到消息个数: {}", consumerRecords.size());
//        boolean isError = false;
//        int index = 0;
//        for (ConsumerRecord record: consumerRecords){
//            String message = String.valueOf(record.value());
//            String msg = Arrays.stream(message.split("\\|")).filter(k -> k.startsWith("UI:")).findFirst().orElse("");
//            if (StringUtils.isNotBlank(msg)){
//                try {
//                    String[] lines = msg.split(":");
//                    if (lines.length>=2){
//                        String uid = lines[1];
//                        String taskIdentify = clientActionTransform.getTaskIdentify(7003);
//                        String remark = "下载歌曲积分获取";
//                        userTaskFinishService.finishTask(Long.valueOf(uid), taskIdentify, remark);
//                    }
//                } catch (Exception e) {
//                    logger.error("download song topic error --> "+ message, e);
//                }
//            }
//            ++index;
//            if (!isError){
//                ack.acknowledge();
//            }
//        }
//    }
//
//
//
//
//}
