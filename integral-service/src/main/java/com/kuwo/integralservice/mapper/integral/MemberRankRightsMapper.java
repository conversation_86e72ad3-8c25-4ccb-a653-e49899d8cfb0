package com.kuwo.integralservice.mapper.integral;

import com.kuwo.integralservice.enity.MemberRankRights;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MemberRankRightsMapper {

    List<MemberRankRights> getRightsByRankIdentfy(@Param("memberRankIdentify") String memberRankIdentify);

    List<MemberRankRights> loadAllRankRights();
}
