package com.kuwo.integralservice.mapper.integral;

import com.kuwo.integralservice.enity.ModifyRequestRecord;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface ModifyRequestRecordMapper {

    ModifyRequestRecord getRequestRecord(@Param("requestId") String requestId, @Param("invokeSource")String invokeSource);

    int saveRecord(ModifyRequestRecord modifyRequestRecord);

    int updateRequestInvokeStatus(@Param("requestId") String requestId,@Param("invokeSource") String invokeSource, @Param("status") int statuses);
}
