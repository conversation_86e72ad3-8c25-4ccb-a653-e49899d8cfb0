package com.kuwo.integralservice.mapper.integral;

import com.kuwo.integralservice.enity.UserScore;
import io.micrometer.core.annotation.Timed;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface UserScoreMapper {

    @Timed(value = "UserScoreMapper#getUserScore", percentiles = {0.5, 0.9, 0.95, 0.99})
    UserScore getUserScore(@Param("userId") Long userId, @Param("userType")int userType);

    @Timed(value = "UserScoreMapper#updateAddUserScore", percentiles = {0.5, 0.9, 0.95, 0.99})
    int updateAddUserScore(@Param("userId") Long userId, @Param("userType")int userType, @Param("score") int score, @Param("version")int version);

    // 减积分的时候需要判断下 socre是否会减为0
    int updateSubUserScore(@Param("userId") Long userId, @Param("userType")int userType, @Param("score") int score, @Param("version")int version);

    int createDefaultUser(@Param("memberScoreId") Long memberScoreId, @Param("userId")Long userId);

    int initScore(@Param("userId")Long userId, @Param("score")int score);
}
