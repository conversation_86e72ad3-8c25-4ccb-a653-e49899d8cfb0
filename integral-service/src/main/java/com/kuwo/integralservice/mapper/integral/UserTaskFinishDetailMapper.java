package com.kuwo.integralservice.mapper.integral;

import com.kuwo.integralservice.enity.UserTaskFinishDetail;
import io.micrometer.core.annotation.Timed;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface UserTaskFinishDetailMapper {

    @Timed(value = "UserTaskFinishDetailMapper#updateCurrentTaskRecordFinish", percentiles = {0.5, 0.9, 0.95, 0.99})
    int updateCurrentTaskRecordFinish(@Param("memberScoreId") Long memberScoreId,
                                      @Param("taskId")Long taskId,
                                      @Param("parentTaskId")Long parentTaskId,
                                      @Param("begin")Date begin,
                                      @Param("end")Date end);

    int saveUserTaskDetail(UserTaskFinishDetail detail);

    UserTaskFinishDetail getUserTaskDetail(@Param("memberScoreId")Long memberScoreId,
                                           @Param("taskId")Long taskId,
                                           @Param("begin")Date begin,
                                           @Param("end")Date end);

    List<UserTaskFinishDetail> findUserTaskFinishDetails(@Param("memberScoreId") Long memberScoreId,
                                                         @Param("begin")Date begin,
                                                         @Param("end") Date end);
}
