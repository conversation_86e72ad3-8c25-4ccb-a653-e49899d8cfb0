package com.kuwo.integralservice.mapper.integral;

import com.kuwo.integralservice.enity.MemberGiveRightsRecord;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface MemberGiveRightsRecordMapper {

    MemberGiveRightsRecord getGiveRightsRecord(@Param("memberScoreId") Long memberScoreId, @Param("rightIdentifyId") String rightIdentifyId,  @Param("memberRankIdentify")String memberRankIdentify);

    int saveRightRecord(MemberGiveRightsRecord memberGiveRightsRecord);

    int updateGiveRightStatus(@Param("id")Long id, @Param("status")int status, @Param("remark")String remark);
}
