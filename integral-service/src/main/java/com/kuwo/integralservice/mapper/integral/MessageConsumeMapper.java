package com.kuwo.integralservice.mapper.integral;

import com.kuwo.integralservice.enity.MessageConsume;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface MessageConsumeMapper {

    MessageConsume findMessageConsume(@Param("messageId") String messageId);

    int updateMessageStatus(@Param("id") long id, @Param("status") int status, @Param("errorDesc")String errorDesc);

    int saveNewMessageConsume(MessageConsume messageConsume);
}
