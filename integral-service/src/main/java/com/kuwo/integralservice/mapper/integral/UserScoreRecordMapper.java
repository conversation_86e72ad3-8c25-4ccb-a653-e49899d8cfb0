package com.kuwo.integralservice.mapper.integral;

import com.kuwo.integralservice.enity.UserScoreRecord;
import io.micrometer.core.annotation.Timed;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UserScoreRecordMapper {


    int saveScoreRecord(UserScoreRecord record);


    List<UserScoreRecord> getScoreRecords(@Param("memberScoreId") Long memberScoreId, @Param("start")int start, @Param("size")int pageSize);


    @Timed(value = "UserScoreRecordMapper#getScoreByRange", percentiles = {0.5, 0.9, 0.95, 0.99})
    int getScoreByRange(@Param("beginDay") String beginDay, @Param("endDay")String endDay, @Param("memberScoreId")Long memberScoreId, @Param("type") int type);

    UserScoreRecord findUserScoreRecord(@Param("memberScoreId") Long memberScoreId, @Param("parentTaskId")Long parentTaskId, @Param("taskId")Long fromTaskId,
                                        @Param("beginDay") String curDay, @Param("endDay") String nextDay);

    int getNormalTaskScoreByRange(@Param("beginDay") String beginDay, @Param("endDay")String endDay, @Param("memberScoreId")Long memberScoreId, @Param("type") int type);
}
