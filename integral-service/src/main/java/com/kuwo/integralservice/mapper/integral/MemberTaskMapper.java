package com.kuwo.integralservice.mapper.integral;

import com.kuwo.integralservice.enity.MemberTask;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MemberTaskMapper {

    List<MemberTask> getAvailableTasks();

    MemberTask getAvailableTaskByIdentifyKey(@Param("identifyKey") String identifyKey);


    List<MemberTask> getAllTaskByParentTaskId(@Param("parentId") Long parentTypeTaskId);

    int updateMemberTaskByParentIdHide(@Param("parentId") Long parentTypeTaskId);

    int updateMemberTaskDisPlay(@Param("taskIds") List<Long> randomTaskIds);

}
