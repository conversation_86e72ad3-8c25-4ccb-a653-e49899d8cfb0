package com.kuwo.integralservice.service;

import com.google.common.base.Preconditions;
import com.kuwo.commercialization.common.cenum.RedisConstant;
import com.kuwo.commercialization.common.utill.DateUtil;
import com.kuwo.commercialization.service.api.TaskApi;
import com.kuwo.integralservice.cache.MemberTaskCache;
import com.kuwo.integralservice.cenum.ActionType;
import com.kuwo.integralservice.enity.MemberTask;
import com.kuwo.integralservice.enity.UserScore;
import com.kuwo.integralservice.enity.UserTaskFinishDetail;
import com.kuwo.integralservice.handler.task.TaskContext;
import com.kuwo.integralservice.handler.task.TaskHandle;
import com.kuwo.integralservice.handler.task.TaskHandlerHelper;
import com.kuwo.integralservice.http.MemberGiveService;
import com.kuwo.integralservice.mapper.integral.UserTaskFinishDetailMapper;
import com.kuwo.integralservice.util.LockHelper;
import com.kuwo.integralservice.util.RedisCacheHelper;
import com.kuwo.integralservice.vo.UserTaskFinishVo;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Desc: 处理用户任务积分状态改变
 * @date 2022-02-17 18:13:45
 */
@DubboService
public class UserTaskFinishService implements TaskApi {

    @Autowired
    private UserTaskFinishDetailMapper userTaskFinishDetailMapper;

    @Autowired
    private UserScoreService userScoreService;

    @Autowired
    private MemberTaskCache memberTaskCache;

    @Autowired
    private LockHelper lockHelper;

    @Autowired
    private MemberGiveService memberGiveService;

    @Autowired
    private TaskHandlerHelper taskHandlerHelper;

    @Autowired
    private RedisCacheHelper redisCacheHelper;

    private final static Logger logger = LoggerFactory.getLogger(UserTaskFinishService.class);

    public UserTaskFinishVo getOrCreateUserFinishTask(Long taskId, Long parentTaskId, String remark, Long memberScoreId, Long originTaskId){
        String key = String.format(RedisConstant.USER_TASK_FINISH_LOCK.getKey(), memberScoreId, taskId);
        UserTaskFinishVo userTaskFinishDetail =  lockHelper.lockAndExecute(key,1,5,()->{
            Date now =  new Date();
            Date begin = DateUtil.getDayBegin(now);
            String afterDay = DateUtil.getNumAfterDay(now, 1);
            Date end= DateUtil.parseDate(afterDay, DateUtil.STANDARD_DAY_FORMAT);
            UserTaskFinishDetail detail  =userTaskFinishDetailMapper.getUserTaskDetail(memberScoreId, taskId, begin, end);
            UserTaskFinishVo userTaskFinishVo = null;
            if (detail == null){
                detail = new UserTaskFinishDetail();
                detail.setTaskId(taskId);
                detail.setParentTaskId(parentTaskId);
                detail.setCreateTime(now);
                detail.setUpdateTime(now);
                detail.setRemark(remark);
                if (null!=originTaskId){
                    detail.setOriginTaskId(originTaskId);
                }
                detail.setTaskStatus(UserTaskFinishDetail.TASK_STATUS.FINISH_TASK);
                detail.setMemberScoreId(memberScoreId);
                int i = userTaskFinishDetailMapper.saveUserTaskDetail(detail);
                userTaskFinishVo = new UserTaskFinishVo(detail);
                userTaskFinishVo.setStatus(1);
            }else{
                userTaskFinishVo = new UserTaskFinishVo(detail);
            }
            return userTaskFinishVo;
        },"create user fail!");
        return userTaskFinishDetail;
    }


    /**
     * 客户端完成任务接口
     * 状态更新为完成任务
     * @param userId
     * @param taskIdentify
     * @param remark
     */
    @Override
    public void finishTask(Long userId, String taskIdentify,  String remark) {
        if (userId <= 0){
            return;
        }
        UserScore userScore = Preconditions.checkNotNull(userScoreService.getOrCreateDefaultUserScore(userId), "CREATE_USER_EXCEPTION");
        MemberTask memberTask = null;
        try {
            memberTask = Preconditions.checkNotNull(memberTaskCache.getValue(taskIdentify),"INVALID_TASK");
            if(!memberTask.notHide() && memberTask.getParentId() == 2L){
                logger.error("member task, now is disabled , task --> "+taskIdentify);
                return;
            }
        } catch (Exception e) {
            // 这块儿配置的任务状态当前不可用,直接返回
            logger.error("task now is disabled , task --> "+taskIdentify);
            return;
        }
        if (!memberGiveService.isLuxVip(userId)){
            logger.debug("user_id: {}, is not a luxury vip", userId);
            return;
        }
        String taskType = memberTask.getTaskType();
        TaskContext taskContext = new TaskContext();
        taskContext.setMemberTask(memberTask);
        taskContext.setUserScore(userScore);
        TaskHandle taskHandle = taskHandlerHelper.getTaskHandleByTaskType(taskType);
        if (taskHandle == null || !taskHandle.canFinishTask(taskContext)) return;

        Long parentTaskId = memberTask.getParentId();
        Long taskId = memberTask.getId();
        Long originTaskId = memberTask.getId();
        Long memberScoreId = userScore.getMemberScoreId();
        Long relateDisplayId = memberTask.getRelateDisplayId();
        int isHide = memberTask.getIsHide();
        // 针对隐藏任务 且 有关联的前台任务
        if (isHide==1 && relateDisplayId!=null){
            MemberTask relatedTask = Preconditions.checkNotNull(memberTaskCache.getTaskById(String.valueOf(relateDisplayId)),"INVALID_RELATION_TASK");
            taskId = relatedTask.getId();
            parentTaskId = relatedTask.getParentId();
        }
        // 加一个查询是否有完成状态
        UserTaskFinishVo taskFinishVo = getOrCreateUserFinishTask(taskId, parentTaskId, remark, memberScoreId, originTaskId);
        if (taskFinishVo.getStatus() == 1){
            redisCacheHelper.cleanUserTaskListCache(String.valueOf(userId));
        }
    }

    /**
     * 将任务当日的状态更新为已领取完成
     * 积分任务完成，领取完成
     * @param userId
     * @param taskIdentify
     * @param now
     */
    public void updateTaskCompletedIfNeed(Long userId, String taskIdentify, Date now, ActionType actionType) {
        // 只有加积分时，才需要完成任务
        if (ActionType.ADD == actionType){
            UserScore userScore = Preconditions.checkNotNull(userScoreService.getOrCreateDefaultUserScore(userId), "CREATE_USER_EXCEPTION");
            MemberTask memberTask = Preconditions.checkNotNull(memberTaskCache.getValue(taskIdentify),"INVALID_TASK");
            Date begin = DateUtil.getDayBegin(now);
            String afterDay = DateUtil.getNumAfterDay(now, 1);
            Date end= DateUtil.parseDate(afterDay, DateUtil.STANDARD_DAY_FORMAT);
            Long memberScoreId = userScore.getMemberScoreId();
            Long taskId = memberTask.getId();
            Long parentTaskId = memberTask.getParentId();
            int rows = userTaskFinishDetailMapper.updateCurrentTaskRecordFinish(memberScoreId, taskId, parentTaskId, begin, end);
        }
    }

    /**
     * 获取用户在某天任务完成状况
     * @param userId
     * @param date
     * @return
     */
    public List<UserTaskFinishDetail> getUserTaskFinishStatus(String userId, Date date) {
        UserScore userScore = Preconditions.checkNotNull(userScoreService.getOrCreateDefaultUserScore(Long.valueOf(userId)), "CREATE_USER_EXCEPTION");
        Long memberScoreId = userScore.getMemberScoreId();
        Date begin = DateUtil.getDayBegin(date);
        String afterDay = DateUtil.getNumAfterDay(date, 1);
        Date end= DateUtil.parseDate(afterDay, DateUtil.STANDARD_DAY_FORMAT);
        List<UserTaskFinishDetail> taskFinishDetails =userTaskFinishDetailMapper.findUserTaskFinishDetails(memberScoreId, begin, end);
        return taskFinishDetails;
    }
}
