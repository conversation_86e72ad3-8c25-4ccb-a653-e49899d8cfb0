package com.kuwo.integralservice.service;

import com.kuwo.integralservice.enity.ModifyRequestRecord;
import com.kuwo.integralservice.mapper.integral.ModifyRequestRecordMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Desc:
 * @date 2022-03-18 11:04:42
 */

@Service
public class ScoreModifyRequestService {

    @Autowired
    private ModifyRequestRecordMapper modifyRequestRecordMapper;


    /**
     * 这里面需要check and create record
     * @param requestId
     * @param invokeSource
     * @return
     */
    public boolean checkRequestInvokeSuccessed(String requestId, String invokeSource) {
        boolean isSuccess = false;
        ModifyRequestRecord modifyRequestRecord = modifyRequestRecordMapper.getRequestRecord(requestId, invokeSource);
        if (modifyRequestRecord == null){
            modifyRequestRecord = new ModifyRequestRecord();
            modifyRequestRecord.setRequestId(requestId);
            modifyRequestRecord.setInvokeSource(invokeSource);
            modifyRequestRecord.setRequestStatus(0);
            modifyRequestRecordMapper.saveRecord(modifyRequestRecord);
        }else {
            isSuccess = modifyRequestRecord.getRequestStatus()==1;
        }
        return isSuccess;
    }

    /**
     * 更新状态为调用成功
     * @param requestId
     * @param invokeSource
     */
    public void updateSuccessInvoke(String requestId, String invokeSource) {
        int i = modifyRequestRecordMapper.updateRequestInvokeStatus(requestId, invokeSource, 1);
    }
}
