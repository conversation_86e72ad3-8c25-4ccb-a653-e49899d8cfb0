package com.kuwo.integralservice.service;

import com.google.common.base.Preconditions;
import com.kuwo.integralservice.cache.MemberTaskCache;
import com.kuwo.integralservice.cenum.ActionType;
import com.kuwo.integralservice.enity.MemberTask;
import com.kuwo.integralservice.enity.UserScore;
import com.kuwo.integralservice.handler.task.TaskContext;
import com.kuwo.integralservice.handler.task.TaskHandle;
import com.kuwo.integralservice.handler.task.TaskHandlerHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class LimitService {

    @Autowired
    private MemberTaskCache memberTaskCache;

    @Autowired
    private TaskHandlerHelper taskHandlerHelper;

    /**
     * 是否需要限制领取积分
     * @param userScore
     * @param actionType
     * @param taskIdentify
     * @return
     */
    public boolean needLimitModifyScore(MemberScoreService memberScoreService, UserScore userScore, ActionType actionType, String taskIdentify, Date time) {
        // 获取当前月的时间范围
        //获取当前天的时间范围
        // 查询用户指定时间范围内的积分,查看是否大袋限制
        MemberTask memberTask = Preconditions.checkNotNull(memberTaskCache.getValue(taskIdentify), "INVALID_TASK");
        TaskContext taskContext = new TaskContext();
        taskContext.setUserScore(userScore);
        taskContext.setMemberTask(memberTask);
        TaskHandle taskHandle = taskHandlerHelper.getTaskHandleByTaskType(memberTask.getTaskType());
        if (taskHandle==null || taskHandle.needLimitAddScore(taskContext, actionType, time, memberScoreService)){
            return true;
        }
        return false;
    }
}
