package com.kuwo.integralservice.service;

import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuwo.commercialization.common.cache.RedisTTlEventType;
import com.kuwo.commercialization.common.cache.TTLEvent;
import com.kuwo.commercialization.common.cenum.RedisConstant;
import com.kuwo.integralservice.cache.MemberTaskCache;
import com.kuwo.integralservice.cache.RedisReplaceConfig;
import com.kuwo.integralservice.config.redis.DynamicChoiceRedissonClient;
import com.kuwo.integralservice.enity.MemberTask;
import com.kuwo.integralservice.enity.UserTaskFinishDetail;
import com.kuwo.integralservice.mapper.integral.MemberTaskMapper;
import com.kuwo.integralservice.util.LockHelper;
import com.kuwo.integralservice.vo.TaskList;
import com.kuwo.integralservice.vo.TaskVo;
import org.redisson.api.RMapCache;
import org.redisson.api.RTopic;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Desc:
 * @date 2022-02-17 20:53:15
 */
@Service
public class TaskService {

    @Autowired
    private MemberTaskCache memberTaskCache;

    @Autowired
    private UserTaskFinishService userTaskFinishService;

    @Autowired
    private DynamicChoiceRedissonClient dynamicChoiceRedissonClient;

    @Autowired
    private MemberTaskMapper memberTaskMapper;

    @Autowired
    private LockHelper lockHelper;

    @Autowired
    private RedisReplaceConfig redisReplaceConfig;

    private void buildTaskTree(Map<Long, List<MemberTask>>  taskMap, MemberTask task){
        if (task == null){
            return;
        }
        if (task.getParentId()== -1 && !taskMap.containsKey(task.getId())){
            taskMap.put(task.getId(), Lists.newArrayList());
            return;
        }
        List<MemberTask> memberTasks = taskMap.get(task.getParentId());
        if (CollectionUtils.isEmpty(memberTasks)){
            buildTaskTree(taskMap, memberTaskCache.getTaskById(String.valueOf(task.getParentId())));
            memberTasks = taskMap.get(task.getParentId());
        }
        if (memberTasks==null){
            return;
        }
        memberTasks.add(task);
        taskMap.put(task.getParentId(), memberTasks);
    }

    public List<TaskList> getUserTaskList(String userId, String plat) {
        RedissonClient redissonClient = dynamicChoiceRedissonClient.getClient(redisReplaceConfig.getDefaultRedisName());
        // 加上0.5 小时缓存,在完成任务和领取积分时清空缓存
        RMapCache<String, List<TaskList>> taskCache = redissonClient.getMapCache(RedisConstant.USER_TASK_LIST_CACHE.getKey());
        String cacheKey = String.format("%s_%s", userId, plat);
        if (taskCache.containsKey(cacheKey)){
            return taskCache.get(cacheKey);
        }

        List<MemberTask> availableTasks = memberTaskCache.getAllAvailableTasks();
      
        List<UserTaskFinishDetail> taskFinishDetails =  userTaskFinishService.getUserTaskFinishStatus(userId, new Date());
        if (CollectionUtils.isEmpty(availableTasks)){
            return Collections.emptyList();
        }
        //构建子任务map列表
        Map<Long, UserTaskFinishDetail> finishTask = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(taskFinishDetails)){
            finishTask = taskFinishDetails.stream().collect(Collectors.toMap(UserTaskFinishDetail::getTaskId, k -> k));
        }
        List<TaskList> taskLists = new LinkedList<>();
        Map<Long, List<MemberTask>>  taskMap = new HashMap<>();
        // 把不显示的过滤调
        for (MemberTask task: availableTasks){
            if (task.notHide()){
                buildTaskTree(taskMap, task);
            }
        }
        // 获取所有的父任务项目,排序后的
        List<MemberTask> allParentTask = taskMap.keySet().stream().map(k -> memberTaskCache.getTaskById(String.valueOf(k)))
                .filter(MemberTask::notHide).sorted().collect(Collectors.toList());
        for (MemberTask task: allParentTask){
            TaskList taskList = new TaskList();
            taskList.setParentTaskIdentify(task.getTaskIdentify());
            taskList.setCategoryName(task.getTaskName());
            List<MemberTask> childTasks = taskMap.get(task.getId());
            List<TaskVo> taskVos = Lists.newArrayList();
            if (!CollectionUtils.isEmpty(childTasks)){
                for (MemberTask childTask: childTasks){
                    // 返回适合当前平台的 task
                    if ("all".equals(childTask.getAdPlat()) || plat.equals(childTask.getAdPlat())){
                        TaskVo taskVo = new TaskVo();
                        UserTaskFinishDetail userTaskFinishDetail = finishTask.get(childTask.getId());
                        if (userTaskFinishDetail!=null){
                            int status = userTaskFinishDetail.getStatus();
                            switch (status){
                                case 1:
                                    taskVo.setStatus(2);
                                    break;
                                case 2:
                                    taskVo.setStatus(3);
                                    break;
                            }
                        }else {
                            taskVo.setStatus(1);
                        }
                        taskVo.setTaskIcon(childTask.getTaskIcon());
                        taskVo.setTaskIdentify(childTask.getTaskIdentify());
                        taskVo.setScore(childTask.getAddScore());
                        taskVo.setUserId(Long.valueOf(userId));
                        taskVo.setTaskName(childTask.getTaskName());
                        taskVo.setTaskDesc(childTask.getTaskDesc());
                        taskVos.add(taskVo);
                    }
                }
            }
            taskList.setTaskVos(taskVos);
            taskLists.add(taskList);
        }
        taskCache.put(cacheKey, taskLists, 30, TimeUnit.MINUTES);
        return taskLists;
    }


    public void randomCurrentTask(){
        randomTask(2l, 2);
    }


    /**
     * 随机任务，避免手工随机
     *  select * from member_task where PARENT_ID = 2;
     *  update member_task set STATUS = 0 where PARENT_ID = 2;
     *  随机2个值
     *  update member_task set STATUS = 1 where ID in (27,29);
     *
     * @param  parentTypeTaskId 进行随机的父任务id
     * @param  randomDisplayNum 随机展示任务的数量
     *
     */
    public void randomTask(Long parentTypeTaskId, int randomDisplayNum){
        String lockKey = RedisConstant.RANDOM_TASK_LOCK.getKey();
        RedissonClient redissonClient = dynamicChoiceRedissonClient.getClient(redisReplaceConfig.getDefaultRedisName());
        lockHelper.lockAndExecute(lockKey,5, 10,() ->{
            List<MemberTask> tasks = memberTaskMapper.getAllTaskByParentTaskId(parentTypeTaskId);
            if (tasks!=null && tasks.size()>0){
                Preconditions.checkState(tasks.size()>= randomDisplayNum, "随机展示任务数量异常");
                List<Long> childTaskIds = tasks.stream().map(MemberTask::getId).collect(Collectors.toList());
                // 1. 更新改类任务状态为禁用
                int n = memberTaskMapper.updateMemberTaskByParentIdHide(parentTypeTaskId);
                // 2. 随机展示n 个数据
                List<Long> randomTaskIds =  choiceNData(childTaskIds, randomDisplayNum);
                // 3.更新其状态可用
                int m = memberTaskMapper.updateMemberTaskDisPlay(randomTaskIds);
            }
            //清空下本地cache缓存
            RTopic topic = redissonClient.getTopic(RedisConstant.LOCAL_CACHE_TOPIC.getKey());
            topic.publish(new TTLEvent(RedisTTlEventType.RELOAD_CACHE, MemberTaskCache.myChangeKey));
            return null;
        } ,"随机任务获取锁异常");
    }

    /**
     *
     * @param childTaskIds
     * @param randomDisplayNum
     */
    public static List<Long> choiceNData(List<Long> childTaskIds, int randomDisplayNum) {
        if (childTaskIds.size() < randomDisplayNum){
            return childTaskIds;
        }
        int n = childTaskIds.size();
        Random random = new Random();
        Set<Integer> s = new HashSet<>();
        while (s.size()<randomDisplayNum){
            s.add(random.nextInt(n));
        }
        return s.stream().map(childTaskIds::get).collect(Collectors.toList());
    }
}
