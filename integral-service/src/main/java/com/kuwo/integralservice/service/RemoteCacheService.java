package com.kuwo.integralservice.service;

import com.google.common.base.Preconditions;
import com.kuwo.commercialization.common.cenum.RedisConstant;
import com.kuwo.commercialization.common.utill.IpUtil;
import com.kuwo.integralservice.cache.RedisReplaceConfig;
import com.kuwo.integralservice.cenum.ActionType;
import com.kuwo.integralservice.config.redis.DynamicChoiceRedissonClient;
import com.kuwo.integralservice.req.ScoreModifyRequest;
import com.kuwo.integralservice.util.KafkaMessageHelper;
import com.kuwo.integralservice.util.RedisCacheHelper;
import com.kuwo.integralservice.util.ScoreChangeRequestHolder;
import com.kuwo.integralservice.vo.RankScoreVo;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RedissonClient;
import org.redisson.client.protocol.ScoredEntry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Desc:
 * @date 2021-12-31 15:56:43
 */
@Service
public class RemoteCacheService {

    @Autowired
    private DynamicChoiceRedissonClient dynamicChoiceRedissonClient;

    @Autowired
    private RedisCacheHelper redisCacheHelper;

    @Autowired
    private KafkaMessageHelper kafkaMessageHelper;

    @Autowired
    private RedisReplaceConfig redisReplaceConfig;

    private String queueName = "";

    // todo: 待实现， 这里主要就是将积分更新到redis或其它存储，方便快速查询数据,先提交下,之后考虑下redis挂了怎么办
    // 理论上是需要 redis集群来保证数据到可用性，和不会丢失，避免启动后在重新加载内存数据
    // 还有排行榜用户丢数据量有多大，避免成为big key
    // 判断用户是否在某个区域
    public void broadcastScore(Long userId, int score, ActionType actionType) {
        //获取用户所在的区域
        String area = getUserArea(userId);
        if(StringUtils.isNotBlank(area)){
            addAreaScore(userId, score, actionType, area);
        }
        addAreaScore(userId, score, actionType, "all");

        // 每次积分变更的时候清空下cache
        redisCacheHelper.cleanUserBasicScoreInfo(String.valueOf(userId));
        redisCacheHelper.cleanUserTaskListCache(String.valueOf(userId));
        ScoreModifyRequest request = ScoreChangeRequestHolder.getRequest();
        kafkaMessageHelper.broadMessage(userId, score, actionType, request);
        // 获取队列
//        RBlockingDeque<Object> blockingDeque = redissonClient.getBlockingDeque(queueName);
//        RDelayedQueue<Object> delayedQueue = redissonClient.getDelayedQueue(blockingDeque);
//        delayedQueue.offer(null, 5,TimeUnit.SECONDS);

    }

    /**
     * 根据接口，返回 当前id
     * 这块看看这个是直接按那个
     * todo: 待确定
     * @param userId
     * @return  beijing, shanghai,guangzhou,shenzheng
     */
    private String getUserArea(Long userId) {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String userIp = IpUtil.getUserIp(request);

        return null;
    }


    /**
     * 根据 地区 将积分加到相关的Redis里面
     * @param userId
     * @param score
     * @param actionType
     * @param area
     * @return 积分倒排rank
     */
    public Integer addAreaScore(Long userId, int score, ActionType actionType, String area) {
        Integer scoreRank;
        String key = String.format(RedisConstant.RANK_LIST_SCORE.getKey(), area);
        RedissonClient redissonClient = dynamicChoiceRedissonClient.getClient(redisReplaceConfig.getDefaultRedisName());
        RScoredSortedSet<Object> scoredSortedSet = redissonClient.getScoredSortedSet(key);
        if (actionType == ActionType.ADD){
            scoreRank = scoredSortedSet.addScoreAndGetRevRank(userId, score);
        }else {
            scoreRank = scoredSortedSet.addScoreAndGetRevRank(userId, -1*score);
        }
        return scoreRank;
    }

    // 直接在这里加一个吧
    public List<RankScoreVo> getRankTop10ByArea(Integer areaFlag , int n){
        String area = "";
        switch (areaFlag){
            case 1:
                area= "all";
                break;
            case 2:
                area= "beijing";
                break;
            case 3:
                area= "shanghai";
                break;
            case 4:
                area= "guangzhou";
                break;
            case 5:
                area= "shenzheng";
                break;
        }
        Preconditions.checkState(n>0, "ILLEGAL_TOP_N");
        Preconditions.checkState(StringUtils.isNotBlank(area), "ILLEGAL_AREA");
        String key = String.format(RedisConstant.RANK_LIST_SCORE.getKey(), area);
        RedissonClient redissonClient = dynamicChoiceRedissonClient.getClient(redisReplaceConfig.getDefaultRedisName());
        RScoredSortedSet<Object> scoredSortedSet = redissonClient.getScoredSortedSet(key);
        Collection<ScoredEntry<Object>> scoredEntries = scoredSortedSet.entryRangeReversed(0, n - 1);
        if (scoredEntries==null || scoredEntries.isEmpty()){
            return new ArrayList<>();
        }
        AtomicInteger i = new AtomicInteger(1);
        List<RankScoreVo> rankScoreVos = scoredEntries.stream().map(entry -> {
            RankScoreVo rankScoreVo = new RankScoreVo();
            rankScoreVo.setUserId(String.valueOf(entry.getValue()));
            rankScoreVo.setScore(entry.getScore());
            rankScoreVo.setRank(i.getAndIncrement());
            return rankScoreVo;
        }).collect(Collectors.toList());
        return rankScoreVos;
    }

}
