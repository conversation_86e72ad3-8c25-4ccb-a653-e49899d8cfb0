package com.kuwo.integralservice.service;

import com.kuwo.commercialization.common.cenum.RedisConstant;
import com.kuwo.commercialization.common.message.BasicMessage;
import com.kuwo.integralservice.enity.MessageConsume;
import com.kuwo.integralservice.mapper.integral.MessageConsumeMapper;
import com.kuwo.integralservice.util.LockHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class MessageConsumeService {

    private final static Logger logger = LoggerFactory.getLogger(MessageConsumeService.class);

    @Autowired
    private MessageConsumeMapper messageConsumeMapper;

    @Autowired
    private LockHelper lockHelper;

    public MessageConsume getOrCreateMessage(String messageId, BasicMessage message)  {
        String lock_key = String.format(RedisConstant.KAFKA_MESSAGE_LOCK.getKey(), messageId);
        MessageConsume messageConsume1 = lockHelper.lockAndExecute(lock_key, 2, 60, () -> {
            MessageConsume messageConsume = messageConsumeMapper.findMessageConsume(messageId);
            if (messageConsume==null){
                messageConsume = new MessageConsume();
                messageConsume.setStatus(0);
                messageConsume.setMessageId(messageId);
                messageConsume.setContent(message.toKafkaMessage());
                int i = messageConsumeMapper.saveNewMessageConsume(messageConsume);
                logger.debug("create kafka msg, id --> {}, message id --> {}, insert results {}", messageConsume.getId(),messageId, i);
            }
            return messageConsume;
        },"kafka message get or create message exception");
        return messageConsume1;
    }


    public void updateMessageStatus(Long id, int status, String errorDesc){
        int rows = messageConsumeMapper.updateMessageStatus(id, status, errorDesc);
        logger.debug("update kafka msg, id --> {}, update results {}", id, rows);
    }


}
