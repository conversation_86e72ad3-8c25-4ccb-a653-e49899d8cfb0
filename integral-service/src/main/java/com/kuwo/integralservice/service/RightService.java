package com.kuwo.integralservice.service;

import com.google.common.base.Preconditions;
import com.kuwo.commercialization.common.cenum.RedisConstant;
import com.kuwo.integralservice.cache.MemberRankCache;
import com.kuwo.integralservice.cache.MemberRankRightsCache;
import com.kuwo.integralservice.enity.MemberGiveRightsRecord;
import com.kuwo.integralservice.enity.MemberRankRights;
import com.kuwo.integralservice.enity.UserScore;
import com.kuwo.integralservice.handler.rights.RightContext;
import com.kuwo.integralservice.handler.rights.RightHandler;
import com.kuwo.integralservice.handler.rights.RightHandlerHelper;
import com.kuwo.integralservice.util.LockHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;

@Service
public class RightService {

    @Autowired
    private MemberRankCache memberRankCache;

    @Autowired
    private UserScoreService userScoreService;

    @Autowired
    private MemberRankRightsCache memberRankRightsCache;

    @Autowired
    private RightHandlerHelper rightHandlerHelper;

    @Autowired
    private MemberGiveRightsRecordService memberGiveRightsRecordService;

    @Autowired
    private LockHelper lockHelper;

    private ThreadPoolExecutor rightCheckthreadPoolExecutor;

    private final static Logger logger = LoggerFactory.getLogger(RightService.class);

    public RightService( @Qualifier("rightCheckValidate") ThreadPoolExecutor rightCheckthreadPoolExecutor) {
        this.rightCheckthreadPoolExecutor = rightCheckthreadPoolExecutor;
    }

    /**
     *  检查是否需要加权限 如果需要加权限的话就加,否则就不做操作
     *  1. 获取当前用户积分所处于的vip等级
     *  2. 查看指定等级vip是否具有权限赠送的
     *  3. 框架侧 判断之前是否已经发送过，发送过直接返回 (发放权益的过程中需要进行加锁, 没有获取到锁就说明正在加该组权限)
     *  4. 根据right 标识 和权益参数来进行来调用发放权限
     *  5. 记录已发放权益
     *
     * @param userId
     */
//    @Transactional(rollbackFor = Exception.class, readOnly = false)
    public void addRightsIfNeed(Long userId) {
        UserScore userScore = userScoreService.getOrCreateDefaultUserScore(userId);
        String memberRankIdentify = Preconditions.checkNotNull(memberRankCache.getMemberRankIdentify(userScore.getScore(), userId), "MEMBER_RANK_IDENTIFY_NULL");
        rightCheckthreadPoolExecutor.submit(()->{
            // 建立权益member, 根据 当前会员等级 去获取权益
            try {
                giveUserRightsByRank(userId, userScore, memberRankIdentify);
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        // 异步check下其它数据是否加上
        rightCheckthreadPoolExecutor.submit(() -> {
            try {
                List<String> lowerMemberRankIdentify = memberRankCache.getLowerMemberRankIdentify(userScore.getScore());
                if (!CollectionUtils.isEmpty(lowerMemberRankIdentify)){
                    lowerMemberRankIdentify.stream().forEach(key-> giveUserRightsByRank(userId,userScore,key));
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }

    /**
     * 赠送用户给指定的等级的会员权益
     * @param userId
     * @param userScore
     * @param memberRankIdentify
     */
    private void giveUserRightsByRank(Long userId, UserScore userScore, String memberRankIdentify) {
        List<MemberRankRights> memberRankRights = Preconditions.checkNotNull(memberRankRightsCache.getValue(memberRankIdentify), "NO_RIGHTS_HAVE_FOUND");
        for (MemberRankRights rankRights: memberRankRights){
            String developerParams = rankRights.getDeveloperParams();
            String rightIdentifyId = rankRights.getRightIdentifyId();
            RightContext rightContext = new RightContext();
            rightContext.setUserScore(userScore);
            rightContext.setDeveloperParams(developerParams);
            rightContext.setRightIdentifyKey(rightIdentifyId);
            RightHandler rightHandler = rightHandlerHelper.getRightHandlerByIdentifyKey(rightIdentifyId);
            if (rightHandler!=null){
                // 这个应该封装一个service取记录这个发放情况
                MemberGiveRightsRecord record = memberGiveRightsRecordService.getOrCreateRightRecord(userScore.getMemberScoreId(),rightIdentifyId, memberRankIdentify, developerParams);
                if (record.getGiveStatus() == 0){
                    // 上锁这部分
                    String lockKey = String.format(RedisConstant.MEMBER_GIVE_RIGHTS_LOCK.getKey(), userScore.getMemberScoreId(), memberRankIdentify,rightIdentifyId);
                    lockHelper.lockAndExecute(lockKey, 1,5, (LockHelper.Execute<Void>) () -> {
                        boolean addResult = rightHandler.addRight(rightContext);
                        String remark = rightHandler.getRemark();
                        int status = 0;
                        if(addResult){
                            status = 1;
                        }
                        memberGiveRightsRecordService.updateGiveRightStatus(record.getId(), status, remark);
                        return null;
                    }, String.format("add right exception, uid:%s, right:%s, member:%s", userId,rightIdentifyId, memberRankIdentify));
                }
            }
        }
    }
}
