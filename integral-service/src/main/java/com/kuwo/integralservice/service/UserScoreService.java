package com.kuwo.integralservice.service;

import com.google.common.base.Preconditions;
import com.kuwo.commercialization.common.cenum.RedisConstant;
import com.kuwo.commercialization.common.utill.TimeLogger;
import com.kuwo.integralservice.cenum.ActionType;
import com.kuwo.integralservice.config.uniqId.IdGenerator;
import com.kuwo.integralservice.enity.UserScore;
import com.kuwo.integralservice.mapper.integral.UserScoreMapper;
import com.kuwo.integralservice.util.LockHelper;
import com.kuwo.integralservice.util.RedisCacheHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class UserScoreService {

    private Logger logger = LoggerFactory.getLogger(UserScoreService.class);

    @Autowired
    private UserScoreMapper userScoreMapper;

    @Autowired
    private IdGenerator idGenerator;

    @Autowired
    private LockHelper lockHelper;

    @Autowired
    private RedisCacheHelper redisCacheHelper;

    private static final String BUSSINESS_TAG = "SCORE_USER";

    public UserScore getOrCreateDefaultUserScore(Long userId){
        String lock_key = String.format(RedisConstant.USER_SCORE_LOCK.getKey(), userId);
        UserScore userScore1 = lockHelper.lockAndExecute(lock_key, 2,60, () -> {
            UserScore userScore = userScoreMapper.getUserScore(userId, 1);
            if (userScore == null) {
                Long newId = idGenerator.getId(BUSSINESS_TAG);
                int rows = userScoreMapper.createDefaultUser(newId, userId);
                if (rows > 0) {
                    userScore = userScoreMapper.getUserScore(userId, 1);
                }
            }
            return userScore;
        }, String.format("user_id:", userId));
        return userScore1;

    }

    public UserScore getOrCreateDefaultUserScore(Long userId, TimeLogger timeLogger){
        String lock_key = String.format(RedisConstant.USER_SCORE_LOCK.getKey(), userId);
        UserScore userScore1 = lockHelper.lockAndExecute(lock_key, 2,60, () -> {
            timeLogger.step("(子模块) 查询userScore 1");
            timeLogger.begin();
            UserScore userScore = userScoreMapper.getUserScore(userId, 1);
            timeLogger.end();
            if (userScore == null) {
                Long newId = idGenerator.getId(BUSSINESS_TAG);
                timeLogger.step("(子模块) 创建userScore");
                timeLogger.begin();
                int rows = userScoreMapper.createDefaultUser(newId, userId);
                timeLogger.end();
                if (rows > 0) {
                    timeLogger.step("(子模块) 查询userScore 2");
                    timeLogger.begin();
                    userScore = userScoreMapper.getUserScore(userId, 1);
                    timeLogger.end();
                }
            }
            return userScore;
        }, String.format("user_id:", userId));
        return userScore1;

    }

    public boolean modifyScore(ActionType actionType, Long userId, int score) {
        UserScore userScore = Preconditions.checkNotNull(userScoreMapper.getUserScore(userId, 1), "USER_NOT_FOUND");
        Preconditions.checkState(actionType == ActionType.ADD||(actionType == ActionType.SUB && userScore.getScore() - score >=0), "SCORE_NOT_ENOUGH");
        int rows = 0;
        if (ActionType.SUB == actionType){
            rows =userScoreMapper.updateSubUserScore(userId,1, score, userScore.getVersion());
        }else {
            rows =userScoreMapper.updateAddUserScore(userId,1, score, userScore.getVersion());
        }
        return rows > 0 ;
    }

    /**
     * 初始化积分
     * @param userId
     * @param score
     * @return
     */
    private boolean initSetScore(Long userId, int score){
        UserScore userScore = Preconditions.checkNotNull(userScoreMapper.getUserScore(userId, 1), "USER_NOT_FOUND");
        boolean addResult = userScoreMapper.initScore(userId, score) > 0;
        // redis 清理下
        redisCacheHelper.cleanUserBasicScoreInfo(String.valueOf(userId));
        redisCacheHelper.cleanUserTaskListCache(String.valueOf(userId));
        return addResult;
    }


    /**
     * 初始化用户积分操作
     * @param userId
     * @param score
     * @return
     */
    public boolean initUserScore(Long userId, int score){
        getOrCreateDefaultUserScore(userId);
        return initSetScore(userId, score);
    }

}
