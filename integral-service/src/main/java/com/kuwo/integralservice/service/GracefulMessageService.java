package com.kuwo.integralservice.service;

import com.kuwo.commercialization.common.message.BasicMessage;
import com.kuwo.commercialization.service.api.GracefulMessageTaskApi;
import com.kuwo.integralservice.enity.MessageConsume;
import com.kuwo.integralservice.handler.message.MessageHandler;
import com.kuwo.integralservice.handler.message.MessageHandlerHelper;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Desc:
 * @date 2024-03-27 09:56:13
 */
@DubboService
public class GracefulMessageService implements GracefulMessageTaskApi {

    @Autowired
    private MessageHandlerHelper messageHandlerHelper;

    @Autowired
    private MessageConsumeService messageConsumeService;

    @Override
    public boolean handle(String msgId, BasicMessage basicMessage) {
        boolean msgHandleResult = false;
        // 数据库消息记录
        MessageConsume messageConsume = messageConsumeService.getOrCreateMessage(msgId, basicMessage);
        if (messageConsume ==null){
            return false;
        }
        // 消息幂等处理
        if (messageConsume.getStatus() == 1){
            return true;
        }
        Long id = messageConsume.getId();
        MessageHandler handler = messageHandlerHelper.matchHandler(basicMessage.getMessageType());
        if (handler!=null){
            MessageHandler.HandleResult handleResult = handler.handleMessage(basicMessage);
            msgHandleResult = handleResult.businessResult;
            String errorMessage = "";
            int status = 1;
            if (!handleResult.businessResult){
                status = 0 ;
                errorMessage = handleResult.errorMessage;
            }
            messageConsumeService.updateMessageStatus(id, status, errorMessage);
        }else {
            messageConsumeService.updateMessageStatus(id, 0, "KAFKA_MESSAGE_HANDLER_NOT_FOUND");
        }
        return msgHandleResult;
    }
}
