package com.kuwo.integralservice.service;

import com.kuwo.commercialization.common.cenum.RedisConstant;
import com.kuwo.integralservice.enity.MemberGiveRightsRecord;
import com.kuwo.integralservice.mapper.integral.MemberGiveRightsRecordMapper;
import com.kuwo.integralservice.util.LockHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class MemberGiveRightsRecordService {

    private final static Logger logger = LoggerFactory.getLogger(MemberGiveRightsRecordService.class);

    @Autowired
    private MemberGiveRightsRecordMapper memberGiveRightsRecordMapper;

    @Autowired
    private LockHelper lockHelper;

    public MemberGiveRightsRecord getOrCreateRightRecord(Long memberScoreId, String rightIdentifyId, String memberRankIdentify, String developerParams) {
        String lock_key = String.format(RedisConstant.CREATE_RIGHT_RECORD_LOCK.getKey(), memberScoreId, rightIdentifyId, memberRankIdentify);
        MemberGiveRightsRecord memberGiveRightsRecord1 = lockHelper.lockAndExecute(lock_key, 2, 60, () -> {
            MemberGiveRightsRecord memberGiveRightsRecord = memberGiveRightsRecordMapper.getGiveRightsRecord(memberScoreId, rightIdentifyId, memberRankIdentify);
            if (memberGiveRightsRecord == null){
                int rows = 0;
                memberGiveRightsRecord = new MemberGiveRightsRecord();
                memberGiveRightsRecord.setMemberScoreId(memberScoreId);
                memberGiveRightsRecord.setSvipType(memberRankIdentify);
                memberGiveRightsRecord.setRightType(rightIdentifyId);
                memberGiveRightsRecord.setRightParams(developerParams);
                memberGiveRightsRecord.setGiveStatus(0);
                rows = memberGiveRightsRecordMapper.saveRightRecord(memberGiveRightsRecord);
                logger.info("create member give rights , score_user_id --> {}, rank id --> {}, right id -->{} , insert results {}", memberScoreId,memberRankIdentify,rightIdentifyId, rows);
            }
            return memberGiveRightsRecord;
        },"right give right status --> get or create exception");
        return memberGiveRightsRecord1;
    }

    public void updateGiveRightStatus(Long id, int status, String remark) {
        memberGiveRightsRecordMapper.updateGiveRightStatus(id, status, remark);
    }
}
