package com.kuwo.integralservice.service;

import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.google.common.base.Preconditions;
import com.kuwo.commercialization.common.cenum.RedisConstant;
import com.kuwo.commercialization.common.cenum.Result;
import com.kuwo.commercialization.common.utill.DateUtil;
import com.kuwo.commercialization.common.utill.TimeLogger;
import com.kuwo.integralservice.cache.MemberRankCache;
import com.kuwo.integralservice.cache.MemberTaskCache;
import com.kuwo.integralservice.cache.RedisReplaceConfig;
import com.kuwo.integralservice.cenum.ActionType;
import com.kuwo.integralservice.cenum.TaskType;
import com.kuwo.integralservice.config.redis.DynamicChoiceRedissonClient;
import com.kuwo.integralservice.enity.MemberTask;
import com.kuwo.integralservice.enity.UserScore;
import com.kuwo.integralservice.enity.UserScoreRecord;
import com.kuwo.integralservice.enity.UserTaskFinishDetail;
import com.kuwo.integralservice.mapper.integral.UserScoreRecordMapper;
import com.kuwo.integralservice.util.LockHelper;
import com.kuwo.integralservice.vo.UserScoreDetailVo;
import com.kuwo.integralservice.vo.UserScoreVo;
import io.micrometer.core.annotation.Timed;
import org.redisson.api.RLock;
import org.redisson.api.RMapCache;
import org.redisson.api.RReadWriteLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.Span;
import org.springframework.cloud.sleuth.Tracer;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 会员积分操作相关service
 */
@Service
public class MemberScoreService {

    @Autowired
    private UserScoreService userScoreService;

    @Autowired
    private UserScoreRecordMapper userScoreRecordMapper;

    @Autowired
    private MemberTaskCache memberTaskCache;

    @Autowired
    private LimitService limitService;

    @Autowired
    private RightService rightService;

    @Autowired
    private RemoteCacheService remoteCacheService;

    @Autowired
    private UserTaskFinishService userTaskFinishService;

    @Autowired
    private LockHelper lockHelper;

    @Autowired
    private DynamicChoiceRedissonClient dynamicChoiceRedissonClient;

    @Autowired
    private MemberRankCache memberRankCache;

    @Autowired
    private Tracer tracer;

    @Autowired
    private RedisReplaceConfig redisReplaceConfig;


    private final static Logger logger = LoggerFactory.getLogger(MemberScoreService.class);

    private final static Logger timeLogger = LoggerFactory.getLogger("timeLogger");

    /**
     * 1. 查询有没有用户，没有的话创建一个用户
     * 2. 参数校验,获取具体任务的积分
     * 3. 保存积分流水
     * 4. 更新用户积分值
     * 5. 可能将积分同步到redis里面
     * 6. 更新任务完成状态
     * @param userId
     * @param taskIdentify 任务描述符号
     * @param remark
     */
    @Transactional(rollbackFor = Exception.class, propagation= Propagation.REQUIRED)
    public Result modifyScore(Long userId, ActionType actionType, String taskIdentify, String remark, int clientGiveScore){
        UserScore userScore = Preconditions.checkNotNull(userScoreService.getOrCreateDefaultUserScore(userId), "CREATE_USER_EXCEPTION");
        // 直接将积分提取出来
        if (userScore.getScore() == 0 && ActionType.SUB == actionType){
            return Result.SCORE_NOT_ENOUGH;
        }
        // 加一个锁 对 统一用户 领取积分,同一任务区进行加锁
        String lockKey = String.format(RedisConstant.MODIFY_SCORE_LOCK.getKey(), userId, taskIdentify);
        Result result = lockHelper.lockAndExecute(lockKey, 1, 3, () -> {
            MemberTask memberTask = Preconditions.checkNotNull(memberTaskCache.getValue(taskIdentify), "INVALID_TASK");
            // 根据taskIdentify 获取score值
            int score = memberTask.getAddScore();
            // 特殊类任务可以做到直接扣减指定积分
            if (TaskType.SPECIAL_TASK.getTaskTypeName().equals(memberTask.getTaskType())){
                score = clientGiveScore;
            }
            String parentTaskId = String.valueOf(memberTask.getParentId());
            String fromTaskId = String.valueOf(memberTask.getId());
            // check 一下用户的积分,这块儿暂时先不考虑，后面在改
            Preconditions.checkState(actionType == ActionType.ADD || (actionType == ActionType.SUB && userScore.getScore() - score >= 0), "SCORE_NOT_ENOUGH");
            Long memberScoreId = userScore.getMemberScoreId();
            if (!limitService.needLimitModifyScore(this, userScore, actionType, taskIdentify, new Date())) {
                // check 一下任务完成进度,只有真正完成的任务才能进行加积分
                if (actionType == ActionType.ADD && TaskType.NORMAL_TASK.getTaskTypeName().equals(memberTask.getTaskType())) {
                    List<UserTaskFinishDetail> userTaskFinishStatus = userTaskFinishService.getUserTaskFinishStatus(String.valueOf(userId), new Date());
                    Preconditions.checkState(!CollectionUtils.isEmpty(userTaskFinishStatus), "INVALID_ADD_SCORE_REQUEST");
                    // 校验完成任务的 task 数量大于0
                    Preconditions.checkState(userTaskFinishStatus.stream().filter(s -> s.getStatus() == 1 && s.getTaskId() == memberTask.getId()).count() > 0, "DUP_ADD_SCORE_REQUEST");
                    // 这里还需要对任务进行转换taksid 和 parentTaskId
                    UserTaskFinishDetail userTaskFinishDetail = userTaskFinishStatus.get(0);
                    if (null != userTaskFinishDetail.getOriginTaskId()) {
                        Long originTaskId = userTaskFinishDetail.getOriginTaskId();
                        MemberTask relatedTask = Preconditions.checkNotNull(memberTaskCache.getTaskById(String.valueOf(originTaskId)), "INVALID_ORIGIN_TASK");
                        fromTaskId = String.valueOf(relatedTask.getId());
                        parentTaskId = String.valueOf(relatedTask.getParentId());
                    }

                }
                // 加流水记录
                Preconditions.checkState(saveUserScoreRecord(memberScoreId, actionType, score, parentTaskId, fromTaskId, remark), "RECORD_ADD_FAIL");
                // 修改积分
                Preconditions.checkState(userScoreService.modifyScore(actionType, userId, score), "MODIFY_USER_SCORE_FAIL");
                rightService.addRightsIfNeed(userId);
                // 更新用户积分任务完成情况
                userTaskFinishService.updateTaskCompletedIfNeed(userId, taskIdentify, new Date(), actionType);
                // 将积分变更信息广播到其它快速存储
                remoteCacheService.broadcastScore(userId, score, actionType);
            } else {
                logger.info("userId:{}, taskIdentify:{}, was limit to add score!", userId, taskIdentify);
                return Result.LINK_SCORE_LIMIT;
            }
            return Result.SUCCESS;
        }, "SCORE_REQUEST_FAIL", Result.LINK_FAIL);
        return result;
    }

    /**
     * 查询当前任务是否已经完成，避免重复加积分
     * @param memberScoreId
     * @param parentTaskId
     * @param fromTaskId
     * @return
     */
    public UserScoreRecord getUserScoreRecord(Long memberScoreId, Long parentTaskId, Long fromTaskId, Date time){
        String numAfterOneDay = DateUtil.getNumAfterDay(time, 1);
        String curDay = DateUtil.getNumAfterDay(time, 0);
        return userScoreRecordMapper.findUserScoreRecord(memberScoreId, parentTaskId, fromTaskId, curDay, numAfterOneDay);

    }


    public boolean saveUserScoreRecord(Long memberScoreId, ActionType actionType, int score, String parentTaskId, String fromTaskId, String remark) {
        UserScoreRecord record = new UserScoreRecord();
        record.setActionType(actionType.getValue());
        record.setCreateTime(new Date());
        record.setFromParentTask(parentTaskId);
        record.setFromTask(fromTaskId);
        record.setMemberScoreId(memberScoreId);
        record.setScore(score);
        record.setRemark(remark);
        int rows = userScoreRecordMapper.saveScoreRecord(record);
        return rows>0;
    }

    /**
     * 查询指定时间范围内,某个用户某种类型的总积分
     * @param beginDay
     * @param endDay
     * @param memberScoreId
     * @param actionType
     */
    public int getUserScoreByRange(String beginDay, String endDay, Long memberScoreId, ActionType actionType){
        int type = 0;
        if (actionType == ActionType.ADD){
            type = 1;
        }
        return userScoreRecordMapper.getScoreByRange(beginDay, endDay, memberScoreId, type);
    }

    public int getNormalTaskUserScoreByRange(String beginDay, String endDay, Long memberScoreId, ActionType actionType){
        int type = 0;
        if (actionType == ActionType.ADD){
            type = 1;
        }
        return userScoreRecordMapper.getNormalTaskScoreByRange(beginDay, endDay, memberScoreId, type);
    }

    /**
     * 分页查询用户积分明细
     * @param memberScoreId
     * @param page
     * @param pagesize
     * @return
     */
    public List<UserScoreDetailVo> getUserScoreDetail(Long memberScoreId, int page, int pagesize){
        List<UserScoreDetailVo> scoreLists = new ArrayList<>();
        // 页数格式规整写
        pagesize= Math.min(pagesize, 100);
        pagesize= pagesize<0?1:pagesize;
        page = page<=0 ?1 : page;
        List<UserScoreRecord> records = userScoreRecordMapper.getScoreRecords(memberScoreId, (page-1)*pagesize, pagesize);
        for (UserScoreRecord userScoreRecord: records){
            UserScoreDetailVo userScoreDetailVo = new UserScoreDetailVo();
            String parentTaskName = "unknown", taskName = "unknown";
            MemberTask parentTask = memberTaskCache.getTaskById(userScoreRecord.getFromParentTask());
            MemberTask task = memberTaskCache.getTaskById(userScoreRecord.getFromTask());
            if (parentTask!=null){
                parentTaskName = parentTask.getTaskName();
            }
            if (task!=null){
                taskName = task.getTaskName();
            }
            userScoreDetailVo.setScore(userScoreRecord.getScore());
            userScoreDetailVo.setCategory(parentTaskName);
            userScoreDetailVo.setTaskName(taskName);
            userScoreDetailVo.setScoreType(userScoreRecord.getActionType());
            userScoreDetailVo.setDay(DateUtil.formatDate(userScoreRecord.getCreateTime(),DateUtil.STANDARD_DAY_HOUR_FORMAT));
            scoreLists.add(userScoreDetailVo);
        }
        return scoreLists;
    }

    /**
     *  获取当前用户当天的用户积分
     * @return
     */
    public int getCurDayUserScore(Long memberScoreId){
        Date time = new Date();
        String numAfterOneDay = DateUtil.getNumAfterDay(time, 1);
        String curDay = DateUtil.getNumAfterDay(time, 0);
        int addScore = getUserScoreByRange(curDay,numAfterOneDay, memberScoreId, ActionType.ADD);
        int subScore = getUserScoreByRange(curDay,numAfterOneDay, memberScoreId, ActionType.SUB);
        return addScore - subScore;
    }


    @Timed(value = "MemberScoreService#getUserScoreFromCache", percentiles = {0.5, 0.9, 0.95, 0.99})
    public Object getScoreFromRedis(RMapCache<Object, Object> scoreInfoCache, String userId){
        return scoreInfoCache.get(userId);
    }

    /**
     * 查询积分详情
     * @param userId
     * @return
     */
    public UserScoreVo getUserScoreVo(String userId){
        Span span = tracer.currentSpan();
        String traceId = "";
        if (span!=null){
           traceId = span.context().traceId();
        }
        TimeLogger instance = TimeLogger.getInstance("查询积分详情", traceId, 500);
        instance.setLogger(timeLogger);
        instance.step("redis缓存查询 -> "+ userId);
        instance.begin();
        RedissonClient redissonClient = dynamicChoiceRedissonClient.getClient(redisReplaceConfig.useTencentRedis(userId)?"d2":"d1");
        RMapCache<Object, Object> scoreInfoCache = redissonClient.getMapCache(RedisConstant.USER_BASE_SCORE_INFO_CACHE.getKey());
        if (scoreInfoCache.containsKey(userId)){
            instance.end();
            instance.printMS();
            logger.info("get user score info from cache, userid: {}", userId);
            // 这块儿抽离是用来进行耗时统计
            return (UserScoreVo) ((MemberScoreService)AopContext.currentProxy()).getScoreFromRedis(scoreInfoCache, userId);
        }
        instance.end();
        instance.step("redis锁获取时长");
        instance.begin();
        String lockKey = String.format(RedisConstant.READ_SCORE_INFO_CACHE_LOCK.getKey(), userId);
        RReadWriteLock readWriteLock = dynamicChoiceRedissonClient.getClient(redisReplaceConfig.getDefaultRedisName())
                .getReadWriteLock(lockKey);
        RLock rLock = readWriteLock.readLock();
        try {
            if (rLock.tryLock(1, TimeUnit.SECONDS)){
                instance.end();
                logger.info("load user score info from db, userid: {}", userId);
                UserScore userScore = userScoreService.getOrCreateDefaultUserScore(Long.valueOf(userId),instance);
                // 查询今日获取了n个积分
                Long memberScoreId = userScore.getMemberScoreId();
                instance.step("数据库查询curDay score");
                instance.begin();
                int curDayScore = this.getCurDayUserScore(memberScoreId);
                instance.end();
                instance.step("scoreVo对象转换耗时");
                instance.begin();
                UserScoreVo userScoreVo = userScore.toVo(memberRankCache);
                instance.end();
                userScoreVo.setDayScore(curDayScore);
                // 改成缓存1个小时,1天有点儿长
                scoreInfoCache.put(userId, userScoreVo, 1, TimeUnit.HOURS);
                instance.printMS();
                return userScoreVo;
            }
            instance.end();
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } finally {
            instance.printMS();
            if (rLock.isLocked() && rLock.isHeldByCurrentThread()){
                rLock.unlock();
            }
        }
        return null;
    }

}
