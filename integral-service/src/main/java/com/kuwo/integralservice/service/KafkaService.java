package com.kuwo.integralservice.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.util.concurrent.ListenableFutureCallback;

import java.util.UUID;

/**
 * <AUTHOR>
 * @Desc:
 * @date 2021-12-15 10:41:13
 */
@Service
public class KafkaService {

    @Autowired
    private KafkaTemplate kafkaTemplate;

    private Logger logger = LoggerFactory.getLogger(KafkaService.class);


    // 注意key不能位null，否则拒绝发送消息
    public void sendMessage(String msg) {
        UUID uuid = UUID.randomUUID();
        ListenableFuture future = kafkaTemplate.send("test1", uuid.toString(),msg);
        future.addCallback(new ListenableFutureCallback() {
            @Override
            public void onFailure(Throwable ex) {
                System.out.println("发送失败");
            }

            @Override
            public void onSuccess(Object result) {
                System.out.println("发送成功 --> "+ result);
            }
        });
    }


    public void sendMessages(String topic, String msg){
        String uniqId = UUID.randomUUID().toString();
        ListenableFuture future = kafkaTemplate.send(topic, uniqId,msg);
        future.addCallback(new ListenableFutureCallback() {
            @Override
            public void onFailure(Throwable ex) {
                logger.info("fail msg => {}", msg);
            }

            @Override
            public void onSuccess(Object result) {
                logger.info("send msg => {}", msg);
            }
        });

    }

}
