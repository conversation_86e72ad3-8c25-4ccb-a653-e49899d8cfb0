package com.kuwo.integralservice;

import com.google.common.base.Joiner;
import com.google.gson.Gson;
import com.kuwo.commercialization.common.utill.MD5;
import com.kuwo.commercialization.common.utill.SecurityCoder;
import com.kuwo.integralservice.req.TaskRequest;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.concurrent.ConcurrentSkipListMap;
import java.util.concurrent.ThreadLocalRandom;

public class test {
    public static void main(String[] args) {
//        String decrypt = SecurityCoder.decrypt("AkcQBQ1NVBAEAAsEAAcGX0dVQ0USBAFbX0ddUFZcAAAFAAYDEx1JFBQbDg4DR0NTVVxYQ0xXSEVBUBMLE0lZVRsUQ1dUT1FVWVBfWgsGBAIDHRNfBBsUCkVbTzxANlEkH01CEEJUQnhVEwtZRUBDRRIEAhdHX05cWFpxCXIDdQl1cyk2Tl5RUFonOlxdKC0rX3ECcgRyCRNM", "yeelion20131111kuwogame");
//        System.out.println(decrypt);
//
//        String encryptValue="{\"uid\":\"418516742\",\"sid\":\"1805696638\",\"action\":6001,\"ts\":1640588503934,\"nonce\":\"Na3O2e\",\"reqId\":955,\"sign\":\"211a0f3905e491b8240bd95b5fe4369d\"}";
//        String encrypt = SecurityCoder.encrypt(encryptValue, "yeelion20131111kuwogame");
//        System.out.println(encrypt);
//
//        List l = new ArrayList();
//        l.add("418516742");
//        l.add("1805696638");
//        l.add(String.valueOf("6001"));
//        l.add("");
//        l.add(String.valueOf(1640588503934l));
//        l.add("Na3O2e");
//        l.add("955");
//        String waitData = Joiner.on("").join(l);
//        String md5Sign = MD5.getMD5ofStr(waitData + "35lp2TIwMMHLh3MfM7qpfsWLXjgwiwiY").toUpperCase();
//        System.out.println(md5Sign);

//        Gson gson = new Gson();
//        TaskRequest taskRequest = gson.fromJson(decrypt,TaskRequest.class);
//        System.out.println(taskRequest.validateSign("35lp2TIwMMHLh3MfM7qpfsWLXjgwiwiY"));

        // function 接口 本质上就是lambaba表达式，根据function的定义来做判断 f.apply 就是调用函数式方法
//        ConcurrentSkipListMap c = new ConcurrentSkipListMap<>();
//        c.computeIfAbsent("1", k->30);
//        System.out.println(c.get("1"));
//        Random random = new Random();
//        for (int i= 0;i<100;i++){
//            System.out.println(random.nextInt(10));
//        }
        
        BigDecimal bigDecimal = new BigDecimal("0.01");
        System.out.println(bigDecimal.intValue());



    }

}
