package com.commercal.uiservice.enity;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Desc: 标准 简单的ui信息
 * @date 2023-11-02 10:30:13
 */
@Data
@NoArgsConstructor
public class BasicUInfo implements Serializable {

  private Long vipmExpireTime = 0L;

  private Long vipLuxuryExpireTime= 0L;

  private Long experienceExpireTime= 0L;

  private Long svipExpireTime= 0L;

  private Long vipWatch1ExpireTime= 0L;

  private Long vipOverSeasExpireTime= 0L;

  // 车载过期时间
  private Long vehicleExpireTime = 0L;

  // 广告会员过期时间
  private Long vipAdExpireTime = 0L;

  // 广告会员阶段的开始时间
  private Long vipAdStartTime = 0L;

  // 成长值
  private Long score = 0L;

}
