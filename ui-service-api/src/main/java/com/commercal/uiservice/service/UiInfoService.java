package com.commercal.uiservice.service;

import com.commercal.uiservice.enity.BasicUInfo;
import com.commercal.uiservice.enity.SignStateInfo;
import com.commercal.uiservice.enity.VipInfo;

import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @Desc: 用户信息服务
 * @date 2023-11-02 10:26:20
 */
public interface UiInfoService {


    /**
     * 获取基础ui信息
     * @param userId
     * @return
     */
    CompletableFuture<BasicUInfo> getBasicUiInfo(String userId);

    /**
     * 获取签约信息
     * @param userId
     * @return
     */
    CompletableFuture<SignStateInfo> getUISignInfo(String userId);


    /**
     * 获取较为全部的vip信息
     * @param userId
     * @return
     */
    CompletableFuture<VipInfo> getVipInfo(String userId);


    /**
     * 清除uid cache信息
     * @param userId
     */
    void cleanCache(String userId);


    /**
     * 查询并校验ui信息结果
     * 和老ui接口查询并进行校验，不一致时更新缓存
     * @param userId
     * @return
     */
    CompletableFuture<BasicUInfo> getAndValidateBasicUiInfo(String userId);


    /**
     * 查询并校验签约信息，不一致时更新缓存
     * @param userId
     * @return
     */
    CompletableFuture<SignStateInfo> getAndValidateUISignInfo(String userId);

    /**
     * 查询并校验所有接口
     * @param userId
     * @return
     */
    CompletableFuture<VipInfo> getAndValidateUIAllInfo(String userId);

}
