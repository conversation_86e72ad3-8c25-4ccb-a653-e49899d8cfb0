package com.kuwo.backgroundmanager.model.vo;

import com.kuwo.backgroundmanager.model.dto.RefundLogInfoDTO;
import lombok.Getter;
import lombok.Setter;

/**
* <AUTHOR>
* @version V1.0
* <p>Description: RefundLogInfo VO</p>
* @date 2022-11-01 18:43:02
*/
@Getter
@Setter
public class RefundLogInfoVO {

    /**
    * 主键
    */
    private Long id;

    /**
    * 退款单id
    */
    private Long refundDetailId;

    /**
    * 退款审批状态
    */
    private Integer status;

    /**
    * 是否完成
    */
    private Integer isFinished;

    /**
    * 数据镜像
    */
    private String dataImage;

    /**
    * 内容描述
    */
    private String content;

    /**
    * 操作人工号
    */
    private String createUser;

    /**
    * 操作人名称
    */
    private String createUserName;

    /**
    * 创建时间
    */
    private java.util.Date createTime;


    public void convertToVO(RefundLogInfoDTO refundLogInfoDTO) {
        this.id = refundLogInfoDTO.getId();
        this.refundDetailId = refundLogInfoDTO.getRefundDetailId();
        this.status = refundLogInfoDTO.getStatus();
        this.isFinished = refundLogInfoDTO.getIsFinished();
        this.dataImage = refundLogInfoDTO.getDataImage();
        this.content = refundLogInfoDTO.getContent();
        this.createUser = refundLogInfoDTO.getCreateUser();
        this.createUserName = refundLogInfoDTO.getCreateUserName();
        this.createTime = refundLogInfoDTO.getCreateTime();
    }

    public RefundLogInfoDTO convertToDTO() {
        RefundLogInfoDTO refundLogInfoDTO = new RefundLogInfoDTO();
        refundLogInfoDTO.setId(id);
        refundLogInfoDTO.setRefundDetailId(refundDetailId);
        refundLogInfoDTO.setStatus(status);
        refundLogInfoDTO.setIsFinished(isFinished);
        refundLogInfoDTO.setDataImage(dataImage);
        refundLogInfoDTO.setContent(content);
        refundLogInfoDTO.setCreateUser(createUser);
        refundLogInfoDTO.setCreateUserName(createUserName);
        refundLogInfoDTO.setCreateTime(createTime);
        return refundLogInfoDTO;
    }
}
