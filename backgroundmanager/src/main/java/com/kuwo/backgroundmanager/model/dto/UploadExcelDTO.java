package com.kuwo.backgroundmanager.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.kuwo.backgroundmanager.entity.dto
 * @date:2022/9/29
 */
@Getter
@Setter
@EqualsAndHashCode
public class UploadExcelDTO {

    @ExcelProperty({"基本信息", "用户id"})
    private String uid;

    @ExcelProperty({"基本信息", "业务线"})
    private String service;

    @ExcelProperty({"基本信息", "平台"})
    private String plat;

    @ExcelProperty({"基本信息", "类别"})
    private String catalog;

    @ExcelProperty({"基本信息", "商品描述"})
    private String goodsDesc;

    @ExcelProperty({"订单信息", "商家订单号"})
    private String payOrderId;

    @ExcelProperty({"订单信息", "酷我订单号"})
    private String customerId;

    @ExcelProperty({"订单信息", "订单金额"})
    private String orderFee;

    @ExcelProperty({"订单信息", "退款金额"})
    private String refundFee;

    /**
     * 系统内部虚拟金额, 没有传0
     */
    @ExcelProperty({"订单信息", "酷我币金额"})
    private String virtualFee;

    @ExcelProperty({"订单信息", "生效时间"})
    private Date orderStartTime;

    @ExcelProperty({"订单信息", "结束时间"})
    private Date orderEndTime;

    /**
     * 退款类型 （1 自动退款， 2 手动退款）
     */
    @ExcelProperty({"基本信息", "退款类型"})
    private String refundType;

    @ExcelProperty({"退款详情", "退款原因"})
    private String refundReason;

    @ExcelProperty({"退款详情", "文字描述"})
    private String refundText;

    @ExcelProperty({"退款详情", "图片描述"})
    private String refundImages;

    @ExcelProperty({"退款详情", "手机号"})
    private String refundPhone;

    @ExcelProperty({"退款详情", "QQ号"})
    private String refundQQ;

    @ExcelProperty({"退款详情", "微信号"})
    private String refundWechat;

    @ExcelProperty({"退款渠道", "退款渠道类型"})
    private String refundChannel;

    @ExcelProperty({"退款渠道", "银行账户户名"})
    private String bankUserName;

    @ExcelProperty({"退款渠道", "开户账户"})
    private String bankAccount;

    @ExcelProperty({"退款渠道", "开户行"})
    private String bankName;

    @ExcelProperty({"退款渠道", "开户地"})
    private String bankLocation;

    @ExcelProperty({"操作人", "操作人"})
    private String promoter;

}
