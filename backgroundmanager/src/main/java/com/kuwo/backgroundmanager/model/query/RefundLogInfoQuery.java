package com.kuwo.backgroundmanager.model.query;

import com.kuwo.backgroundmanager.common.base.BaseQuery;
import lombok.Getter;
import lombok.Setter;

/**
* <AUTHOR>
* @version V1.0
* <p>Description: RefundLogInfo query</p>
* @date 2022-11-01 18:43:02
*/
@Getter
@Setter
public class RefundLogInfoQuery extends BaseQuery {

    /**
    * 主键
    */
    private Long id;

    /**
    * 退款单id
    */
    private Long refundDetailId;

    /**
    * 退款审批状态
    */
    private Integer status;

    /**
    * 是否完成
    */
    private Integer isFinished;

    /**
    * 数据镜像
    */
    private String dataImage;

    /**
    * 内容描述
    */
    private String content;

    /**
    * 操作人工号
    */
    private String createUser;

    /**
    * 操作人名称
    */
    private String createUserName;

    /**
    * 创建时间
    */
    private java.util.Date createTime;

}
