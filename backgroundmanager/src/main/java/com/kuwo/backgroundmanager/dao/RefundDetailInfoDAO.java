package com.kuwo.backgroundmanager.dao;

import com.kuwo.backgroundmanager.common.base.BaseDAO;
import com.kuwo.backgroundmanager.model.dto.RefundDetailInfoDTO;
import com.kuwo.backgroundmanager.model.query.RefundDetailInfoQuery;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
* <AUTHOR>
* @version V1.0
* <p>Description: RefundDetailInfo DAO</p>
* @date 2022-11-01 18:43:02
*/
@Repository(value = "refundDetailInfoDAO")
public interface RefundDetailInfoDAO extends BaseDAO<RefundDetailInfoDTO, RefundDetailInfoQuery> {

    List<RefundDetailInfoDTO> selectPageByRetry(@Param("service") String service, @Param("status") Integer status, @Param("retryCount") Integer retryCount, @Param("retryGapTime") Integer retryGapTime);
}
