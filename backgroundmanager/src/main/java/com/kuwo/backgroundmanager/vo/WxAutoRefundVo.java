package com.kuwo.backgroundmanager.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.kuwo.backgroundmanager.common.enumeration.WxAutoRefundType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WxAutoRefundVo {
    // 微信订单ID
    private String wOid;
    // 退款金额
    private String amount;
    // 退款通道
    private String refundChannel;
    // 退款原因
    private String refundReason;

    // 类型
    private WxAutoRefundType reType;
    private String reason;

}
