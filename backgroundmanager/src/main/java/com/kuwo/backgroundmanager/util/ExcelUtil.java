package com.kuwo.backgroundmanager.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.event.AnalysisEventListener;

import java.io.File;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.kuwo.backgroundmanager.util
 * @date:2022/9/29
 */
public class ExcelUtil {

    public static void read2Clazz(File file, Class clazz, AnalysisEventListener listener){
        EasyExcel.read(file, clazz, listener)
                .xlsxSAXParserFactoryName("com.sun.org.apache.xerces.internal.jaxp.SAXParserFactoryImpl").sheet().doRead();
    }


}
