package com.kuwo.backgroundmanager.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;

import java.util.UUID;
import java.util.concurrent.Callable;
import java.util.function.Consumer;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.memberintergral.musicplayerservice.util
 * @date:2022/9/26
 */
@Slf4j
public class FrameTimer {

    public static final ThreadLocal<StopWatch> FRAME_TIMER = new InheritableThreadLocal();

    public static final ThreadLocal<String> THREAD_UUID = new InheritableThreadLocal();

    private StopWatch stopWatch;

    public FrameTimer(StopWatch stopWatch) {
        this.stopWatch = stopWatch;
    }

    public static String getUUID() {
        String uuid = THREAD_UUID.get();
        if (StringUtils.isBlank(uuid)) {
            uuid = UUID.randomUUID().toString();
            THREAD_UUID.set(uuid);
        }
        return uuid;
    }

    public static void start() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        FRAME_TIMER.set(stopWatch);
    }

    public static Long getSplitTime() {
        StopWatch stopWatch = FRAME_TIMER.get();
        if (stopWatch == null) {
            log.error("{}, get ThreadLocal StopWatch error, StopWatch is null", getUUID());
            return 0L;
        }
        stopWatch.split();
        return stopWatch.getSplitTime();
    }

    public static void reset() {
        StopWatch stopWatch = FRAME_TIMER.get();
        if (stopWatch == null) {
            start();
            return;
        }
        stopWatch.reset();
        stopWatch.start();
    }

    public static void stop() {
        StopWatch stopWatch = FRAME_TIMER.get();
        if (stopWatch == null) {
            log.error("{}, get ThreadLocal StopWatch error, StopWatch is null", getUUID());
            return;
        }
        stopWatch.stop();
    }

    public void stop0() {
        stopWatch.stop();
    }

    public Long getSplitTime0() {
        stopWatch.split();
        return stopWatch.getSplitTime();
    }

    public static FrameTimer getInstance() {
        FrameTimer frameTimer = new FrameTimer(new StopWatch());
        frameTimer.stopWatch.start();
        return frameTimer;
    }

    /**
     *
     * @param callable 调用的主流程
     * @param consumer 入参为执行时间，然后根据执行实现调用 回调方法
     * @return
     */
    public static Object takeTime(Callable<Object> callable, Consumer<Long> consumer) {
        Object result = null;
        try {
            FrameTimer frameTimer = FrameTimer.getInstance();
            Long startTime = frameTimer.getSplitTime0();
            result = callable.call();
            Long endTime = frameTimer.getSplitTime0();
            frameTimer.stop0();
            consumer.accept(endTime - startTime);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }
}
