package com.kuwo.backgroundmanager.util;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.kuwo.backgroundmanager.common.PropertiesValue;
import com.kuwo.backgroundmanager.common.RefundConstant;

import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.SignatureException;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.kuwo.backgroundmanager.util
 * @date:2024/10/19
 */

public class TencentSignUtil {
    
    public static void main(String[] args) {
        String str = "{\"out_trade_no\":\"880889917\",\"offer_id\":\"1450025008\",\"ts\":\"1729679962\"}";
        // Test case1 普通场景
        String encodeResult = doEncode(RefundConstant.TENCENT_VIDEO_KEY_PRIVATE_ONLINE, str);
        boolean verityResult = doVerify(RefundConstant.TENCENT_VIDEO_KEY_PUBLIC_ONLINE, encodeResult, str);

        System.out.print("\n签名结果1 = " + encodeResult + "\n");
        System.out.print("\n验签结果1 = " + verityResult + "\n");

        // Test case2 签名内容被篡改
        String encodeResult2 = doEncode(RefundConstant.TENCENT_VIDEO_KEY_PRIVATE_ONLINE, str);
        boolean verityResult2 = doVerify(RefundConstant.TENCENT_VIDEO_KEY_PUBLIC_ONLINE, encodeResult2, str + "s");

        System.out.print("\n签名结果2 = " + encodeResult2 + "\n");
        System.out.print("\n验签结果2 = " + verityResult2 + "\n");
    }

    /**
     * 获取签名信息
     *
     * @param json
     * @return
     */
    public static String sign(JSONObject json) {

        String KEY_PRIVATE = PropertiesValue.isOnline() ? RefundConstant.TENCENT_VIDEO_KEY_PRIVATE_ONLINE : RefundConstant.TENCENT_VIDEO_KEY_PRIVATE_TEST;
        String APPKEY = PropertiesValue.isOnline() ? RefundConstant.ONLINE_TENCENT_APPKEY : RefundConstant.TEST_TENCENT_APPKEY;

        Map<String, Object> map = Maps.newTreeMap();
        for (String key : json.keySet()) {
            map.put(key, json.get(key));
        }

        String param = map.entrySet().stream().map(e -> e.getKey() + "=" + e.getValue()).collect(Collectors.joining("&")) + "&" + APPKEY;

        return doEncode(KEY_PRIVATE, param);
    }

    /**
     * 验证签名信息
     *
     * @param publicKey
     * @param encodeResult
     * @param contentToVerify
     * @return
     */
    public static boolean doVerify(String publicKey, String encodeResult, String contentToVerify) {
        try {
            publicKey = publicKey.replaceAll("-----END PUBLIC KEY-----", "").
                    replaceAll("-----BEGIN PUBLIC KEY-----", "").
                    replaceAll("\n", "");
            byte[] b2 = Base64.getDecoder().decode(publicKey);
            X509EncodedKeySpec xspec = new X509EncodedKeySpec(b2);

            KeyFactory kf = KeyFactory.getInstance("RSA");
            java.security.Signature privateSignature = java.security.Signature.getInstance("SHA256withRSA");

            privateSignature.initVerify(kf.generatePublic(xspec));
            privateSignature.update(contentToVerify.getBytes("UTF-8"));

            byte[] verifyBytes = Base64.getDecoder().decode(encodeResult);

            return privateSignature.verify(verifyBytes);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (InvalidKeyException e) {
            e.printStackTrace();
        } catch (InvalidKeySpecException e) {
            e.printStackTrace();
        } catch (SignatureException e) {
            e.printStackTrace();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        return false;
    }

    private static String doEncode(String privateKey, String contentToEncode) {
        privateKey = privateKey.replaceAll("-----END PRIVATE KEY-----", "").
                replaceAll("-----BEGIN PRIVATE KEY-----", "").
                replaceAll("\n", "");

        // 私钥需要进行Base64解密
        byte[] b1 = Base64.getDecoder().decode(privateKey);

        try {
            // 将字节数组转换成PrivateKey对象
            PKCS8EncodedKeySpec spec = new PKCS8EncodedKeySpec(b1);
            KeyFactory kf = KeyFactory.getInstance("RSA");
            java.security.Signature privateSignature = java.security.Signature.getInstance("SHA256withRSA");
            privateSignature.initSign(kf.generatePrivate(spec));

            // 输入需要签名的内容
            privateSignature.update(contentToEncode.getBytes("UTF-8"));
            // 拿到签名后的字节数组
            byte[] s = privateSignature.sign();
            // 将签名后拿到的字节数组做一个Base64编码，以便以字符串的形式保存
            return Base64.getEncoder().encodeToString(s);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (InvalidKeyException e) {
            e.printStackTrace();
        } catch (InvalidKeySpecException e) {
            e.printStackTrace();
        } catch (SignatureException e) {
            e.printStackTrace();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        return "";
    }
}
