package com.kuwo.backgroundmanager.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import java.io.ByteArrayOutputStream;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.spec.PKCS8EncodedKeySpec;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.kuwo.backgroundmanager.util
 * @date:2024/10/19
 */

@Slf4j
public class DdmcSignUtil {
    
    /**
     * 用私钥对内容进行加密
     *
     * @param content
     * @return
     * @throws Exception
     */
    public static String encryptByPrivateKey(String content, String privateKeyStr) {
        String cipherText = "";
        try {
            PrivateKey privateKey = getPrivateKey(privateKeyStr);
            Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
            cipher.init(Cipher.ENCRYPT_MODE, privateKey); //
            byte[] bytes = content.getBytes();
            int inputLen = bytes.length;
            int offLen = 0;//
            int i = 0;
            ByteArrayOutputStream bops = new ByteArrayOutputStream();
            while (inputLen - offLen > 0) {
                byte[] cache;
                if (inputLen - offLen > 117) {
                    cache = cipher.doFinal(bytes, offLen, 117);
                } else {
                    cache = cipher.doFinal(bytes, offLen, inputLen - offLen);
                }
                bops.write(cache);
                i++;
                offLen = 117 * i;
            }
            bops.close();
            byte[] encryptedData = bops.toByteArray();
            cipherText = Base64.encodeBase64String(encryptedData);
        } catch (Exception e) {
            log.error("使用私钥加密错误", e);
        }
        return cipherText;
    }

    /**
     * 根据私钥字符串生成私钥对象
     *
     * @param privateKey
     * @return
     * @throws Exception
     */
    public static PrivateKey getPrivateKey(String privateKey) throws Exception {
        byte[] data = Base64.decodeBase64(privateKey);
        PKCS8EncodedKeySpec pkcs8 = new PKCS8EncodedKeySpec(data);
        KeyFactory factory = KeyFactory.getInstance("RSA");
        PrivateKey key = factory.generatePrivate(pkcs8);
        return key;
    }
    
}
