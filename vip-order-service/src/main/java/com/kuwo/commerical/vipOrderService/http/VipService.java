package com.kuwo.commerical.vipOrderService.http;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.internal.LinkedTreeMap;
import com.google.gson.reflect.TypeToken;
import com.kuwo.commercialization.common.utill.MD5;
import com.kuwo.commerical.vipOrderService.util.ChinaUnicomUtil;
import com.kuwo.commerical.vipOrderService.util.MapTypeAdapter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;

@Component
@Slf4j
public class VipService {

    @Value("${vip.domain}")
    private String vipDomain;

    @Autowired
    private OkHttpClient httpClient;

    private Gson gson = new GsonBuilder()
            .registerTypeAdapter(new TypeToken<Map<String,Object>>(){}.getType(),new MapTypeAdapter()).create();



    /**
     * 调用vip 赠送vip并发送消息接口
     *
     * @param luxVip
     * @param days
     * @param gamesName
     * @return
     */
    public boolean sendVipAndPushMessage(String uid,String luxVip, int days, String gamesName) {
        try {
            long ts = System.currentTimeMillis();
            StringJoiner joiner = new StringJoiner("&@#!3194");
            joiner.add(uid);
            joiner.add(String.valueOf(days));
            joiner.add(luxVip);
            joiner.add(String.valueOf(ts));
            String sign = MD5.getMD5ofStr(joiner.toString() + "gycxW8BiKsf2mfkrJXGWwSNRF8yzexl3").toUpperCase();
            String url = String.format("%s/vip/v2/userbase/vip?op=giveGameVipAndPushMessage&uid=%s&vipType=%s&days=%s&gameName=%s&ts=%s&sign=%s&vers=rvQlmmEd1dDluXS", vipDomain, uid, luxVip, days, URLEncoder.encode(gamesName, "utf-8"), ts, sign);
            Request request = new Request.Builder().url(url).build();
            Response response = httpClient.newCall(request).execute();
            if (response.isSuccessful()) {
                String result = response.body().string();
                log.info("request:{}, response -> {}", url, result);
                Map map = gson.fromJson(result, Map.class);
                String data = (String) map.get("data");
                Map metaMap = (Map) map.get("meta");
                int statusCode = new BigDecimal(String.valueOf(metaMap.get("code"))).intValue();
                if (statusCode == 200) {
                   if ("success".equals(data)){
                       return true;
                   }
                }
            }else {
                log.info("request:{}, response -> {}", url, "fail");
            }
        } catch (IOException e) {
            log.error("invoke fail!",e);
        }
        return false;
    }


    public Map drawAwardsSystem(String activitySign, String code, String userId){
        Map mapR = new HashMap();
        String url = String.format("%s/vip_adv/v2/liveBooking/award/drawAwardsSystem?activitySign=%s&code=%s&skey=boxAwardSystem&userId=%s&timeSign=%s", vipDomain, activitySign, code, userId, System.currentTimeMillis());
        String result = "";
        Request request = new Request.Builder().url(url).build();
        try {
            Response response = httpClient.newCall(request).execute();
            if (response.isSuccessful()) {
                result = response.body().string();
                Map map = gson.fromJson(result, Map.class);
                ArrayList dataMapList = (ArrayList) map.get("data");
                int statusCode = new BigDecimal(String.valueOf(map.get("code"))).intValue();
                if (statusCode == 200) {
                    if (dataMapList != null && dataMapList.size() > 0) {
                        LinkedTreeMap dataMap = (LinkedTreeMap) dataMapList.get(0);
                        int days = NumberUtils.toInt(String.valueOf(dataMap.get("awardName")), 0);
                        mapR.put("days", days);
                    }
                }
            }
        } catch (IOException e) {
            log.error("invoke draw award fail!");
        }
        log.info("request:{}, response -> {}", url, result);
        return mapR;
    }


    /**
     * 发送会员接口
     * @return orderId  返回订单id
     */
    public String sendVip(SendVipContext sendVipContext){
        String orderId = "";
        long ts = System.currentTimeMillis();
        String url = String.format("%s/vip/spi/businessCooperate?service=getVip&timestamp=%s&channel=%s&product=%s&days=%s&mobile=%s&businessOrderId=%s",
                vipDomain, ts, sendVipContext.getChannel(), sendVipContext.getProductCode(), sendVipContext.getDays(),sendVipContext.getPhone(), sendVipContext.getBussinessOrderId());
        // 生成签名
        TreeMap treeMap = new TreeMap();
        treeMap.put("service","getVip");
        treeMap.put("timestamp",ts);
        treeMap.put("channel",sendVipContext.getChannel());
        treeMap.put("product",sendVipContext.getProductCode());
        treeMap.put("days",sendVipContext.getDays());
        treeMap.put("mobile",sendVipContext.getPhone());
        treeMap.put("businessOrderId",sendVipContext.getBussinessOrderId());
        String sign = ChinaUnicomUtil.makeMd5Sign(treeMap, sendVipContext.getKey());
        url +="&sign="+sign;
        String result = "";
        Request request = new Request.Builder().url(url).build();
        try {
            Response response = httpClient.newCall(request).execute();
            if (response.isSuccessful()) {
                result = response.body().string();
                Map map = gson.fromJson(result, new TypeToken<Map<String, Object>>() {
                }.getType());
                Map dataMap = (Map) map.get("data");
                Map meta = (Map) map.get("meta");
                int statusCode = new BigDecimal(String.valueOf(meta.get("code"))).intValue();
                if (statusCode == 200) {
                    if (dataMap != null && dataMap.containsKey("kwOrderId")) {
                        orderId = String.valueOf(dataMap.get("kwOrderId"));
                    }
                }
            }
        } catch (IOException e) {
            log.error("business send vip fail!");
        }
        log.info("request:{}, response -> {}", url, result);
        return orderId;
    }
    /**
     * 发送会员接口(给测试环境发送)
     * @return orderId  返回订单id
     */
    public String sendVipTest(SendVipContext sendVipContext){
        String orderId = "";
        long ts = System.currentTimeMillis();
        String url = String.format("%s/vip/spi/businessCooperate?service=getVip&timestamp=%s&channel=%s&product=%s&days=%s&mobile=%s&businessOrderId=%s",
                "http://testvip.kuwo.cn", ts, sendVipContext.getChannel(), sendVipContext.getProductCode(), sendVipContext.getDays(),sendVipContext.getPhone(), sendVipContext.getBussinessOrderId());
        // 生成签名
        TreeMap treeMap = new TreeMap();
        treeMap.put("service","getVip");
        treeMap.put("timestamp",ts);
        treeMap.put("channel",sendVipContext.getChannel());
        treeMap.put("product",sendVipContext.getProductCode());
        treeMap.put("days",sendVipContext.getDays());
        treeMap.put("mobile",sendVipContext.getPhone());
        treeMap.put("businessOrderId",sendVipContext.getBussinessOrderId());
        String sign = ChinaUnicomUtil.makeMd5Sign(treeMap, sendVipContext.getKey());
        url +="&sign="+sign;
        String result = "";
        Request request = new Request.Builder().url(url).build();
        try {
            Response response = httpClient.newCall(request).execute();
            if (response.isSuccessful()) {
                result = response.body().string();
                Map map = gson.fromJson(result, new TypeToken<Map<String, Object>>() {
                }.getType());
                Map dataMap = (Map) map.get("data");
                Map meta = (Map) map.get("meta");
                int statusCode = new BigDecimal(String.valueOf(meta.get("code"))).intValue();
                if (statusCode == 200) {
                    if (dataMap != null && dataMap.containsKey("kwOrderId")) {
                        orderId = String.valueOf(dataMap.get("kwOrderId"));
                    }
                }
            }
        } catch (IOException e) {
            log.error("business send vip fail!");
        }
        log.info("request:{}, response -> {}", url, result);
        return orderId;
    }


    /**
     * 发送车载会员接口
     * @param sendVehicleVipContext
     * @return
     */
    public String sendVehicleVip(SendVehicleVipContext sendVehicleVipContext){
        String orderId = "";
        long ts = System.currentTimeMillis();
        String url = String.format("%s/vip_adv/vehicle/directCharge", vipDomain);
        VehicleDirectRequest vehicleDirectRequest = new VehicleDirectRequest();
        vehicleDirectRequest.setBusinessId(sendVehicleVipContext.getBussinessOrderId());
        vehicleDirectRequest.setDays(String.valueOf(sendVehicleVipContext.getDays()));
        vehicleDirectRequest.setDirectType(sendVehicleVipContext.getDirectType());
        vehicleDirectRequest.setPhone(sendVehicleVipContext.getPhone());
        vehicleDirectRequest.setChannel(sendVehicleVipContext.getChannel());
        vehicleDirectRequest.setPrivateKey(sendVehicleVipContext.getKey());
        vehicleDirectRequest.setTimestamp(String.valueOf(ts));
        String requestParams = vehicleDirectRequest.getRequestParams();
        url +="?"+requestParams;
        String result = "";
        Request request = new Request.Builder().url(url).build();
        try {
            Response response = httpClient.newCall(request).execute();
            if (response.isSuccessful()) {
                result = response.body().string();
                Map map = gson.fromJson(result, new TypeToken<Map<String, Object>>() {
                }.getType());
                Map dataMap = (Map) map.get("data");
                int statusCode = new BigDecimal(String.valueOf(map.get("code"))).intValue();
                if (statusCode == 200) {
                    if (dataMap != null && dataMap.containsKey("orderId")) {
                        orderId = String.valueOf(dataMap.get("orderId"));
                    }
                }
            }
        } catch (IOException e) {
            log.error("business send vehicle vip fail!");
        }
        log.info("request:{}, response -> {}", url, result);
        return orderId;
    }
    /**
     * 发送车载会员接口
     * @param sendVehicleVipContext
     * @return
     */
    public String sendVehicleVipTest(SendVehicleVipContext sendVehicleVipContext){
        String orderId = "";
        long ts = System.currentTimeMillis();
        String url = String.format("%s/vip_adv/vehicle/directCharge", "http://testvip.kuwo.cn");
        VehicleDirectRequest vehicleDirectRequest = new VehicleDirectRequest();
        vehicleDirectRequest.setBusinessId(sendVehicleVipContext.getBussinessOrderId());
        vehicleDirectRequest.setDays(String.valueOf(sendVehicleVipContext.getDays()));
        vehicleDirectRequest.setDirectType(sendVehicleVipContext.getDirectType());
        vehicleDirectRequest.setPhone(sendVehicleVipContext.getPhone());
        vehicleDirectRequest.setChannel(sendVehicleVipContext.getChannel());
        vehicleDirectRequest.setPrivateKey(sendVehicleVipContext.getKey());
        vehicleDirectRequest.setTimestamp(String.valueOf(ts));
        String requestParams = vehicleDirectRequest.getRequestParams();
        url +="?"+requestParams;
        String result = "";
        Request request = new Request.Builder().url(url).build();
        try {
            Response response = httpClient.newCall(request).execute();
            if (response.isSuccessful()) {
                result = response.body().string();
                Map map = gson.fromJson(result, new TypeToken<Map<String, Object>>() {
                }.getType());
                Map dataMap = (Map) map.get("data");
                int statusCode = new BigDecimal(String.valueOf(map.get("code"))).intValue();
                if (statusCode == 200) {
                    if (dataMap != null && dataMap.containsKey("orderId")) {
                        orderId = String.valueOf(dataMap.get("orderId"));
                    }
                }
            }
        } catch (IOException e) {
            log.error("business send vehicle vip fail!");
        }
        log.info("request:{}, response -> {}", url, result);
        return orderId;
    }







}