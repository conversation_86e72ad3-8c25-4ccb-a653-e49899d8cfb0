package com.kuwo.commerical.vipOrderService.http;

import com.google.gson.Gson;
import com.google.gson.internal.LinkedTreeMap;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Map;
import java.util.Optional;

/**
 * 数据集相关service
 */
@Component
@Slf4j
public class DataSetService {

    @Autowired
    private OkHttpClient httpClient;

    @Value("${vip.domain}")
    private String vipDomain;

    private Gson gson = new Gson();

    /**
     * 根据code 查询相应数据集列表
     * @param code
     * @param page 默认1
     * @param size 默认100
     * @return
     */
    public Optional getDataSetByCode(String code, int page, int size) throws IOException {
        if (StringUtils.isEmpty(code)){
            return Optional.empty();
        }
        if (page<=1){
            page = 1;
        }
        if (size<=0){
            size = 100;
        }
        String url = String.format("%s/vip_adv/datamode/datasetbycode?codes=%s&pageSize=%s&page=%s", vipDomain, code,size, page);
        Request request = new Request.Builder().url(url).build();
        Response response = httpClient.newCall(request).execute();
        if (response.isSuccessful()){
            String result = response.body().string();
            Map map = gson.fromJson(result, Map.class);
            Map dataMap = (Map) map.get("data");
            int statusCode =  new BigDecimal(String.valueOf(map.get("code"))).intValue();
            if (statusCode == 200) {
                LinkedTreeMap codeRecodes = (LinkedTreeMap) dataMap.get(code);
                if (codeRecodes!= null){
                    ArrayList<String> recordes = (ArrayList) codeRecodes.get("record");
                    ArrayList<Object> newResults = new ArrayList<>();
                    for (String record:recordes){
                        newResults.add(gson.fromJson(record, Map.class));
                    }
                    return Optional.ofNullable(newResults);
                }
            }
        }
        return Optional.empty();
    }





}
