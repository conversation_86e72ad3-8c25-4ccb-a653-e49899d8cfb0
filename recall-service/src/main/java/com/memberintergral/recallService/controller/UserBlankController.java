package com.memberintergral.recallService.controller;

import com.google.common.collect.Lists;
import com.kuwo.commercialization.common.resp.BasicResponse;
import com.kuwo.commercialization.common.resp.PageResponse;
import com.memberintergral.recallService.entity.dto.UserBlankDTO;
import com.memberintergral.recallService.entity.query.UserBlankPageQuery;
import com.memberintergral.recallService.service.UserBlankService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * @author: <EMAIL>
 * @version: v1.0
 * @description: com.memberintergral.recallService.controller
 * @date:2022/12/28
 */
@RestController
@RequestMapping("/activity")
@Slf4j
public class UserBlankController {

    @Autowired
    private UserBlankService userBlankService;

    @PostMapping("list")
    public PageResponse list(@RequestBody UserBlankPageQuery userBlankPageQuery){

        List<UserBlankDTO> list = userBlankService.list(userBlankPageQuery.getCode());
        PageResponse result = new PageResponse();
        int totalCount = list.size();
        int totalPage = (totalCount + userBlankPageQuery.getPageSize() - 1) / userBlankPageQuery.getPageSize();
        int pageNum = userBlankPageQuery.getPageIndex();
        int pageSize = userBlankPageQuery.getPageSize();
        Integer offset = (pageNum - 1) * pageSize;
        userBlankPageQuery.setOffset(offset);
        userBlankPageQuery.setLimit(pageSize);

        if (CollectionUtils.isNotEmpty(list)) {
            list = list.stream().skip(offset).limit(pageSize).
                    collect(Collectors.toList());
        }

        result.setCurrentPage(pageNum);
        result.setPageSize(pageSize);
        result.setPages(totalPage);
        result.setTotalCount(totalCount);
        result.setData(list);
        result.setMsg("OK");
        return result;
    }

    @PostMapping("adds")
    public BasicResponse addList(@RequestBody List<UserBlankDTO> userBlankDTOS){
        return BasicResponse.successResponse(userBlankService.addList(userBlankDTOS));
    }

    @PostMapping("isExist")
    public BasicResponse isExist(@RequestBody UserBlankDTO userBlankDTO){
        return BasicResponse.successResponse(userBlankService.isExist(userBlankDTO));
    }

}
